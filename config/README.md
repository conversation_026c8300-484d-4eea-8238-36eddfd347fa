# 环境配置系统

本项目使用环境变量`APP_ENV`来区分不同的环境配置，支持原生Android和Flutter共享同一套配置系统。

## 支持的环境

- `develop`: 开发环境 (默认)
- `pre`: 预发布环境
- `prd`: 生产环境

## 配置文件

配置文件位于`config`目录下，按环境命名：

- `config.develop.json`: 开发环境配置
- `config.pre.json`: 预发布环境配置
- `config.prd.json`: 生产环境配置

## 如何使用

### 推荐的构建方式

使用项目根目录的`prepare_build.sh`脚本准备环境配置，然后进行构建：

```bash
# 开发环境构建
APP_ENV=develop ./prepare_build.sh
flutter build apk

# 预发布环境构建
APP_ENV=pre ./prepare_build.sh
flutter build apk

# 生产环境构建
APP_ENV=prd ./prepare_build.sh
flutter build apk --release
```

### 直接构建Android应用

```bash
# 开发环境构建
APP_ENV=develop ./gradlew assembleDebug

# 预发布环境构建
APP_ENV=pre ./gradlew assembleDebug

# 生产环境构建
APP_ENV=prd ./gradlew assembleRelease
```

## 在代码中访问配置

### 原生Android (Kotlin)

```kotlin
// 获取API基址
val apiBaseUrl = ConfigHelper.getString("api_base_url")
```

### Flutter (Dart)

```dart
// 获取API基址
final apiBaseUrl = ConfigBridge.getString("api_base_url");
```

## 配置项说明

- `app_env`: 当前环境标识
- `api_base_url`: API服务器地址
- `oss_endpoint`: 阿里云OSS端点
- `oss_bucket`: OSS存储桶名称
- `oss_region`: OSS区域
- `oss_sts_auth_url`: OSS STS认证URL
- `wechat_app_id`: 微信应用ID
- `app_download_url`: 应用下载链接

## 添加新配置项

1. 在所有环境的配置文件中添加新的配置项
2. 使用`ConfigHelper`或`ConfigBridge`访问新的配置项

## 注意事项

- 打包发布版本时，请确保使用`APP_ENV=prd`环境变量
- 所有环境的配置文件结构应保持一致，只是值不同
- 不要在代码中硬编码环境特定的值，应始终从配置中读取
