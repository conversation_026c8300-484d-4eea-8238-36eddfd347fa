<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ai.rtasr.RTASRActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        >
        <TextView
            android:id="@+id/ai_rtasr_testAudioPath"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:text="识别音频路径:"
            />

        <TextView
            android:id="@+id/ai_rtasr_asrResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="10"
            android:scrollbars="vertical"
            android:text="识别结果："
            />

        <TextView
            android:id="@+id/ai_rtasr_translateResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="10"
            android:scrollbars="vertical"
            android:text="翻译结果："
            />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id = "@+id/ai_rtasr_language_layout"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                >
                <TextView
                    android:id="@+id/ai_rtasr_language_info"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="选择语种："
                    android:textColor="#333333"
                    android:textSize="15dp" />
                <Spinner
                    android:id="@+id/ai_rtasr_language"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent" />

            </LinearLayout>
        </LinearLayout>
        <Button
            android:id="@+id/ai_rtasr_audio_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:text="麦克风转写测试"/>
        <Button
            android:id="@+id/ai_rtasr_file_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_weight="0.5"
            android:text="文件转写测试"/>
        <Button
            android:id="@+id/ai_rtasr_btn_stop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_weight="0.5"
            android:text="停止转写"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>