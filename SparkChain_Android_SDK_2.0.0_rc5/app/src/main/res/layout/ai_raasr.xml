<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ai.raasr.RAASRActivity">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/ai_raasr_notification"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:scrollbars="vertical"
            android:layout_above="@+id/ai_raasr_ll_audiopath" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/ai_raasr_ll_audiopath"
            android:layout_above="@+id/ai_raasr_start"
            android:orientation="horizontal"
            >
            <Button
                android:id="@+id/ai_raasr_audiopath"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="10dp"
                android:text="选择音频"/>
            <TextView
                android:id="@+id/ai_raasr_pathinfo"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="10dp"
                android:gravity="left|center"
                android:text="当前音频路径为:/sdcard/iflytek/asr/cn_test.pcm"
                />
        </LinearLayout>
        <Button
            android:id="@+id/ai_raasr_start"
            android:layout_above="@+id/ai_raasr_getResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:text="上传音频"/>

        <Button
            android:id="@+id/ai_raasr_getResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/ai_raasr_stop"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:visibility="gone"
            android:text="查询结果"/>

        <Button
            android:id="@+id/ai_raasr_stop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:text="停止上传"/>


    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>