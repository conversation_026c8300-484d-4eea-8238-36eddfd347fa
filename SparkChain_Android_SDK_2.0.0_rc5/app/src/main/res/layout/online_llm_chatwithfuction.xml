<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".llm.online_llm.function.ChatWithFuctionCallActivity">

    <TextView
        android:id="@+id/online_llm_function_notification"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/online_llm_function_input"
        android:layout_alignParentTop="true"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="0dp"
        android:text="如果想测试fuction功能，请询问天气或者税率查询相关的问题。例如：今天合肥的天气怎么样？"
        android:scrollbars="vertical" />

    <EditText
        android:layout_above="@+id/online_llm_function_linearLayout"
        android:id="@+id/online_llm_function_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="合肥今天的天气怎么样？"/>

    <LinearLayout
        android:id="@+id/online_llm_function_linearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">
        <Button
            android:id="@+id/online_llm_function_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="SEND"/>
        <Button
            android:id="@+id/online_llm_function_stop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="STOP"/>
    </LinearLayout>


</RelativeLayout>