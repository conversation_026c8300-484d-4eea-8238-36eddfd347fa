apply plugin: 'com.android.application'

android {
    compileSdkVersion 32
    buildToolsVersion "29.0.3"

    defaultConfig {
        applicationId "com.iflytek.edgeAI"
        minSdkVersion 21
        targetSdkVersion 32
        versionCode 1
        versionName "1.0.0"
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
//        ndk {
//            abiFilters "armeabi-v7a"
////            abiFilters "arm64-v8a"
//        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    ndkVersion '20.0.5594570'
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.6.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.hjq:xxpermissions:8.2'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation files('libs/SparkChain.aar')
    api 'com.google.code.gson:gson:2.8.8'

}