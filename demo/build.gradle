// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"

buildscript {
    repositories {
//        jcenter()
        mavenCentral()
        google()
        maven{ url 'http://mirrors.tencent.com/nexus/repository/maven-public' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
//        classpath 'com.android.tools.build:gradle:2.2.2'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.8.0'
        classpath 'com.google.protobuf:protobuf-gradle-plugin:0.8.8'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        flatDir {
            dirs 'libs'
        }
        maven{ url 'http://mirrors.tencent.com/nexus/repository/maven-public' }
//        jcenter()
        mavenCentral()
        google()
//        maven {
//            url "http://maven.oa.com/nexus/content/repositories/jcenter/"
//        }
//        maven {
//            url 'https://jcenter.bintray.com/'
//            name 'Google'
//        }

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
