apply plugin: 'com.android.application'

project.ext.configPath = "../speech-demo-config.json"

apply from: "../parse_json.gradle"

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion '28.0.3'

    defaultConfig {
        applicationId "com.tencent.iot.speech.app"
        minSdkVersion rootProject.ext.android.minSdkVersion
        //noinspection ExpiredTargetSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode 2
        versionName "1.1"

        buildConfigField 'String', 'AppId', "\"${getValueById('APP_ID')}\""
        buildConfigField 'String', 'Secretkey', "\"${getValueById('SECRET_KEY')}\""
        buildConfigField 'String', 'SecretId', "\"${getValueById('SECRET_ID')}\""

    }
    buildTypes {
        debug{
            ndk {
                abiFilters 'armeabi-v7a','x86', 'arm64-v8a', 'x86_64'
            }
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    android {
        lintOptions {
            abortOnError false
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    testImplementation 'junit:junit:4.12'//
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'com.victor:lib:1.0.4'
    implementation 'com.squareup.okhttp3:okhttp:4.2.2'
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
}
