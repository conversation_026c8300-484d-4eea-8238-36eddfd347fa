<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.tencent.iot.speech.app">
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.INTERNET"/>
<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />


    <application
        android:allowBackup="false"
        tools:replace="android:allowBackup"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true">
        <activity
            android:exported="true"
            android:name=".OptionActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".asr.sentence.OneSentenceRecognizeActivity" android:label="一句话识别"></activity>
        <activity android:name=".asr.flash_file.FileFlashRecognizeActivity" android:label="录音文件识别极速版"></activity>
        <activity android:name=".asr.realtime.MainActivity" android:label="实时语音识别"></activity>

    </application>

</manifest>