<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:text="回声消除测试选项" />

        <Switch
            android:id="@+id/aec_enable"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginLeft="12dp"
            android:layout_marginBottom="5dp"
            android:minHeight="48dp"
            android:text="回声消除" />

        <Switch
            android:id="@+id/play_sound"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginLeft="12dp"
            android:layout_marginBottom="5dp"
            android:minHeight="48dp"
            android:text="播放声音" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginStart="12dp"
            android:layout_marginLeft="12dp"
            android:layout_marginBottom="5dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="音频保存路径"
                android:textColor="@android:color/black" />

            <EditText
                android:id="@+id/file_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="0"
                android:ems="10"
                android:inputType="textPersonName"
                android:minHeight="48dp"
                android:text="record"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin = "10dp"
        android:orientation="vertical">

        <LinearLayout android:id="@+id/l1"
            android:layout_marginTop="30dp"
            android:layout_marginLeft="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <TextView android:id="@+id/recognize_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="识别状态："/>
            <TextView android:id="@+id/volume"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="音量："/>

        </LinearLayout>

        <LinearLayout android:id="@+id/l1_1"
            android:layout_marginTop="30dp"
            android:layout_marginLeft="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:orientation="horizontal">
            <TextView android:id="@+id/voice_db"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="分贝："/>

            <Switch
                android:id="@+id/silent_switch"
                android:checked="false"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="静音检测" />
        </LinearLayout>
        <LinearLayout android:id="@+id/l2"
            android:layout_weight="1"
            android:layout_marginBottom="25dp"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button android:id="@+id/start"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="15dp"
                android:layout_weight="1"
                android:text="start" />

            <Button android:id="@+id/cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="15dp"
                android:layout_weight="1"
                android:text="cancel" />

            <Switch
                android:id="@+id/switch1"
                android:checked="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="音频压缩" />
        </LinearLayout>
    </LinearLayout>

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp">

        <TextView
            android:id="@+id/recognize_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minLines="3"
            android:scrollbars="vertical" />
    </ScrollView>


</LinearLayout>
