#!/bin/bash

# Flutter应用高级混淆构建脚本
# 使用方法: ./build_obfuscated.sh [环境] [平台]
# 环境: develop, pre, prd
# 平台: android, ios, all

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
ENV=${1:-prd}
PLATFORM=${2:-android}

echo -e "${BLUE}========== Flutter高级混淆构建脚本 ==========${NC}"
echo -e "${YELLOW}构建环境: $ENV${NC}"
echo -e "${YELLOW}目标平台: $PLATFORM${NC}"

# 验证环境参数
if [[ ! "$ENV" =~ ^(develop|pre|prd)$ ]]; then
    echo -e "${RED}错误: 无效的环境参数 '$ENV'${NC}"
    echo -e "${YELLOW}支持的环境: develop, pre, prd${NC}"
    exit 1
fi

# 验证平台参数
if [[ ! "$PLATFORM" =~ ^(android|ios|all)$ ]]; then
    echo -e "${RED}错误: 无效的平台参数 '$PLATFORM'${NC}"
    echo -e "${YELLOW}支持的平台: android, ios, all${NC}"
    exit 1
fi

# 设置环境变量
export APP_ENV=$ENV

# 清理之前的构建
echo -e "${BLUE}清理之前的构建文件...${NC}"
flutter clean
flutter pub get

# 创建符号表目录
mkdir -p build/app/outputs/symbols
mkdir -p build/ios/symbols

# 构建函数
build_android() {
    echo -e "${GREEN}开始构建Android应用...${NC}"
    
    if [ "$ENV" = "prd" ]; then
        echo -e "${YELLOW}生产环境: 启用完整代码混淆${NC}"
        flutter build apk --release \
            --obfuscate \
            --split-debug-info=build/app/outputs/symbols \
            --dart-define=APP_ENV=$ENV \
            --target-platform android-arm64
    else
        echo -e "${YELLOW}非生产环境: 使用标准混淆${NC}"
        flutter build apk --release \
            --dart-define=APP_ENV=$ENV \
            --target-platform android-arm64
    fi
    
    echo -e "${GREEN}Android构建完成!${NC}"
    echo -e "${BLUE}APK位置: build/app/outputs/flutter-apk/app-release.apk${NC}"
}

build_ios() {
    echo -e "${GREEN}开始构建iOS应用...${NC}"
    
    if [ "$ENV" = "prd" ]; then
        echo -e "${YELLOW}生产环境: 启用完整代码混淆${NC}"
        flutter build ios --release \
            --obfuscate \
            --split-debug-info=build/ios/symbols \
            --dart-define=APP_ENV=$ENV
    else
        echo -e "${YELLOW}非生产环境: 使用标准混淆${NC}"
        flutter build ios --release \
            --dart-define=APP_ENV=$ENV
    fi
    
    echo -e "${GREEN}iOS构建完成!${NC}"
}

# 执行构建
case $PLATFORM in
    android)
        build_android
        ;;
    ios)
        build_ios
        ;;
    all)
        build_android
        build_ios
        ;;
esac

# 显示混淆信息
if [ "$ENV" = "prd" ]; then
    echo -e "${GREEN}========== 混淆构建完成 ==========${NC}"
    echo -e "${YELLOW}注意事项:${NC}"
    echo -e "1. 符号表已保存到 build/app/outputs/symbols (Android) 或 build/ios/symbols (iOS)"
    echo -e "2. 请妥善保存符号表文件，用于后续调试和崩溃分析"
    echo -e "3. 混淆后的应用无法直接调试，请使用符号表进行错误追踪"
    echo -e "4. 建议在发布前进行充分测试"
else
    echo -e "${GREEN}========== 标准构建完成 ==========${NC}"
fi

echo -e "${BLUE}构建脚本执行完成!${NC}"
