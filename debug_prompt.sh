#!/bin/bash

# 调试提示词效果的脚本
# 使用 GPT-4.1 mini 模型测试提示词

# 配置
API_URL="https://api.gptsapi.net/v1/chat/completions"
API_KEY="sk-gY862e5b9a04ee26941ac843faead2bfbcc3e10c9eelGS92"
MODEL="gpt-4.1-mini"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 系统提示词
SYSTEM_PROMPT="你是一位专业H5网页生成 AI，任务是根据用户提供的内容，生成 H5 HTML 页面代码。

请严格遵循以下规则生成代码：

.用户设置了笔记标题，根据用户的标题，提取并总结用户提供的内容中的相关信息，比如用户当前的笔记标题是“副业”那么就从用户提供的内容中提炼出跟“副业”相关的内容。用户此次提供的标题是: 自媒体
.用户会提供一个笔记标题（如“自媒体”）和一段内容，笔记标题代表本页面的**总主题**。
.请从用户提供的内容中提取出与该笔记标题最相关的子主题（例如“买量与投流”），作为该内容的**子模块标题**。子主题只有一个，子主题的字数不超过5个字
.总标题下面是一个精美的tab，将当前子主题作为一个tab放到其中，这样方便后续做拓展，tab的选中样式就是tab下面有一个短横线
.tab下面是子标题的目录，点击可以跳转子标题的不同内容区域，子标题的目录不要换行，而且目录内容不超过5个字
.先根据用户提供的内容，自己总结并生成笔记的文本内容，不展示在回答内容中。笔记内容必须极度精炼，只保留对笔记主题最关键的信息点，每条观点尽量压缩成一句话，避免任何冗余或重复。
.页面的内容设计一定要适合笔记内容，能够方便阅读和理解，不要像流水账
.页面的内容块用优雅的卡片展示，区分不同区域的内容
.生成的页面中的JavaScript 必须使用标准 DOM API（如：document.querySelector、innerHTML、appendChild 等）。
.设计的时候假设用户只会使用手机查看，设计的整体必须考虑是手机的使用场景
.生成的代码中，所有的js行为必须封装到script标签中，并且script带上id属性，类似于data-id=\"behavior\"，标识当前script标签的作用
.不要使用 window.alert 或 console.log
.返回的内容严禁包含其他任何内容，只需要html代码。我需要直接复制并展示，不需要任何解释
.使用模板生成，模板是note.html，模板内容如下：
<!DOCTYPE html>
<html lang=\"zh\">
<head>
  <meta charset=\"UTF-8\">
  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
  <title>商业笔记滑动 Tab</title>
  <style>
    body {
      margin: 0;
      background: #f4f5f7;
      height: 100vh;
      font-family: 'Inter', sans-serif;
      display: flex;
      flex-direction: column;
    }
    .header {
      background-color: #ffffff;
      padding: 20px;
      padding-bottom: 0;
      font-size: 22px;
      font-weight: 600;
      text-align: center;
    }
    .tab-bar {
      display: flex;
      overflow-x: auto;
      background-color: #fff;
      border-bottom: 1px solid #eee;
      position: relative;
    }
    .tab-item {
      padding: 12px 20px;
      white-space: nowrap;
      font-size: 15px;
      font-weight: 500;
      color: #666;
      cursor: pointer;
      position: relative;
    }
    .tab-item.active {
      color: #2d72ff;
    }
    .tab-item.active::after {
      content: \"\";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 2px;
      background-color: #2d72ff;
      border-radius: 1px;
    }
    .table-of-contents {
      background: #fff;
      margin-bottom: 16px;
      border-radius: 8px;
      padding: 16px 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }
    .toc-title {
      font-size: 16px;
      font-weight: 600;
      color: #222;
      margin-bottom: 12px;
    }
    .toc-item {
      padding: 8px 0;
      font-size: 14px;
      color: #2d72ff;
      cursor: pointer;
      border-bottom: 1px solid #f5f5f5;
      transition: color 0.2s;
    }
    .toc-item:last-child {
      border-bottom: none;
    }
    .toc-item:hover {
      color: #1a5ce6;
    }
    .scroll-area {
      flex: 1;
      overflow: hidden;
      position: relative;
    }
    .tab-content {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      display: none;
      padding: 16px;
      box-sizing: border-box;
      -webkit-overflow-scrolling: touch;
    }
    .tab-content.active {
      display: block;
    }
    .card {
      background: #fff;
      margin-bottom: 16px;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }
    .card h3 {
      margin: 0 0 10px;
      font-size: 18px;
      font-weight: 600;
      color: #222;
    }
    .card p {
      margin: 0;
      color: #444;
      font-size: 14px;
      line-height: 1.6;
    }
  </style>
</head>
<body>
    <div class=\"header\">商业笔记</div>
    <div class=\"tab-bar\" id=\"tabBar\">
      <div class=\"tab-item active\">概览</div>
      <div class=\"tab-item\">技术</div>
      <div class=\"tab-item\">市场</div>
      <div class=\"tab-item\">运营</div>
      <div class=\"tab-item\">设计</div>
    </div>
    <div class=\"scroll-area\" id=\"scrollArea\">
      <div class=\"tab-content active\">
        <div class=\"table-of-contents\" id=\"tableOfContents\">
          <div class=\"toc-title\">目录</div>
          <div class=\"toc-item\" data-target=\"card-0\">商业模式解析</div>
          <div class=\"toc-item\" data-target=\"card-1\">用户画像与痛点</div>
        </div>
        <div class=\"card\" id=\"card-0\">
          <h3>商业模式解析</h3>
          <p>介绍了SaaS平台的营收结构，包括订阅、增值服务及合作分成模型。</p>
        </div>
        <div class=\"card\" id=\"card-1\">
          <h3>用户画像与痛点</h3>
          <p>基于调研数据，识别目标用户行为特征和痛点。</p>
        </div>
      </div>
      <div class=\"tab-content\">
        <div class=\"card\"><h3>敬请期待</h3><p>内容正在筹备中，请稍后再来。</p></div>
      </div>
      <div class=\"tab-content\">
        <div class=\"card\"><h3>敬请期待</h3><p>内容正在筹备中，请稍后再来。</p></div>
      </div>
      <div class=\"tab-content\">
        <div class=\"card\"><h3>敬请期待</h3><p>内容正在筹备中，请稍后再来。</p></div>
      </div>
      <div class=\"tab-content\">
        <div class=\"card\"><h3>敬请期待</h3><p>内容正在筹备中，请稍后再来。</p></div>
      </div>
    </div>
  <script data-id=\"tab-behavior\">
    const tabs = document.querySelectorAll('.tab-item');
    const contents = document.querySelectorAll('.tab-content');
    const scrollArea = document.getElementById('scrollArea');

    function activateTab(index) {
      tabs.forEach(t => t.classList.remove('active'));
      contents.forEach(c => c.classList.remove('active'));
      tabs[index].classList.add('active');
      contents[index].classList.add('active');
    }

    tabs.forEach((tab, index) => {
      tab.addEventListener('click', () => activateTab(index));
    });

    // 目录点击跳转功能
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('toc-item')) {
        const targetId = e.target.getAttribute('data-target');
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    });

    let startX = 0;
    let startY = 0;
    scrollArea.addEventListener('touchstart', e => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    });
    scrollArea.addEventListener('touchend', e => {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      const diffX = endX - startX;
      const diffY = endY - startY;

      // 只有当水平滑动距离大于垂直滑动距离时才切换tab
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        const currentIndex = [...tabs].findIndex(t => t.classList.contains('active'));
        if (diffX < -50 && currentIndex < tabs.length - 1) activateTab(currentIndex + 1);
        else if (diffX > 50 && currentIndex > 0) activateTab(currentIndex - 1);
      }
    });
  </script>
</body>
</html>
"

# 函数：打印带颜色的文本
print_colored() {
    local color=$1
    local text=$2
    echo -e "${color}${text}${NC}"
}

# 函数：打印分隔线
print_separator() {
    echo "=================================================="
}

# 函数：调用OpenAI API
call_openai() {
    local user_content="$1"

    # 桌面a.html文件路径
    local desktop_html="$HOME/Desktop/a.html"

    # 使用jq来构建正确的JSON请求体
    local request_body=$(jq -n \
        --arg model "$MODEL" \
        --arg system_prompt "$SYSTEM_PROMPT" \
        --arg user_content "【用户提供的内容如下】：\n$user_content\n\n【网页 HTML 内容】" \
        '{
            "model": $model,
            "messages": [
                {
                    "role": "system",
                    "content": $system_prompt
                },
                {
                    "role": "user",
                    "content": $user_content
                }
            ],
            "stream": false
        }')

    print_colored $BLUE "正在调用 OpenAI API..."
    print_colored $YELLOW "模型: $MODEL"
    print_colored $YELLOW "用户内容: ${user_content:0:100}..."

    # 发送请求
    local response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $API_KEY" \
        -d "$request_body")

    # 检查响应
    if [ $? -eq 0 ]; then
        # 解析响应中的content字段
        local ai_content=$(echo "$response" | jq -r '.choices[0].message.content // empty')

        if [ -n "$ai_content" ] && [ "$ai_content" != "null" ]; then
            print_colored $GREEN "✓ API调用成功"

            # 去除代码块标记（```html 和 ```）
            local cleaned_content=$(echo "$ai_content" | sed 's/^```html//g' | sed 's/^```$//g' | sed '/^```$/d' | sed '/^```html$/d')

            # 替换桌面的a.html文件
            echo "$cleaned_content" > "$desktop_html"
            print_colored $GREEN "✓ HTML内容已替换到桌面的a.html文件"
            print_colored $BLUE "文件路径: $desktop_html"

            # 显示内容预览
            print_separator
            print_colored $BLUE "生成的HTML内容预览:"
            echo "$cleaned_content" | head -20
            if [ $(echo "$cleaned_content" | wc -l) -gt 20 ]; then
                print_colored $YELLOW "... (内容较长，已截断，完整内容请查看桌面a.html文件)"
            fi
            print_separator

            return 0
        else
            print_colored $RED "✗ API响应中没有找到有效内容"
            echo "完整响应: $response"
            return 1
        fi
    else
        print_colored $RED "✗ API调用失败"
        return 1
    fi
}

# 注意：用户输入已写死，以下函数保留但不使用

# 函数：交互式输入内容
interactive_input() {
    print_colored $BLUE "请输入要转换为H5页面的内容 (输入 'END' 结束):"
    local content=""
    while IFS= read -r line; do
        if [ "$line" = "END" ]; then
            break
        fi
        content="$content$line\n"
    done
    echo -e "$content"
}

# 函数：从文件读取内容
read_from_file() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        cat "$file_path"
    else
        print_colored $RED "文件不存在: $file_path"
        return 1
    fi
}

# 主函数
main() {
    print_colored $GREEN "=== 提示词调试工具 ==="
    print_colored $YELLOW "使用模型: $MODEL"
    print_separator

    # 检查jq是否安装
    if ! command -v jq &> /dev/null; then
        print_colored $RED "错误: 需要安装 jq 来解析JSON响应"
        print_colored $YELLOW "安装命令: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
        exit 1
    fi

    # 写死的用户输入内容
    local user_content="我现在在真的是不会再花一分钱去买流量了，无论你是哪一个平台，看见的我这个账号啊，我都是一分钱没花把这个账号做起来的，不是不想投，也不是不会投，是现在投了真的没有用。我每天的后台说到无数的提问，就是关于抖加和薯条这样的工具怎么用，但是要搞清楚这个问题，我们首先要讲一下，在算法平台我们发布了一个内容之后，这个内容传播的这个逻辑是什么？在我们发出来一个内容之后，算法会把这个内容小批量的曝光给一些人，比如说很多人觉得我小红书流量特别差，就可能有100个小眼睛，这个流量就停了，对吧？就是系统帮助你把内容分发出去之后，我进行了一个测试，这个测试的结果是有100个人点进来看了我的内容，但是我统计了我的这个点击率、互动率、完播率等等的指标之后，我发现你的数据不是很好，所以我就不会再继续给你去推了，但是如果你的数据还不错，你在发完内容的几个小时之后，会在后台看见一个数据分析，会有一个五边形就长这个样子啊，如果这个五边形的五个数据都是拉满的，就是全部都超越了同类数据的99%的人，你这个东西就会持续去传播，但是每个平台对于数据的考核标准都不太一样啊，这也是为什么。有人说，诶，我小红书火的东西怎么发的抖音没人看，或者我抖音火的东西发到视频号怎么没人看，如果是小红书的话，他因为有那个双列的信息流嘛，他非常考核创作者有没有能力说用户在看到我这个双列的屏幕的时候，我对你的东西就非常感兴趣，我就一下点进去了，所以你的封面和标题的点击率非常重要的，等用户点进去了之后，我又会考核你的这个互动率和完播率的这个数据，如果是互动的话，那现在的指标是点赞和收藏的权重肯定是小于评论和关注的，如果是看王播率的话，各个平台考核的标准就不太一样，抖音是看5秒王播率，视频号是看3秒完播率，小红书是看2秒的跳出率，在两秒跳出率就是两秒完播嘛，30%的人在两秒钟内跳出了，那其实你的2秒完播率就是70%对吧，那不管考核标准什么测量方法都大差不差，就你发了内容之后，给你点流量测试一下，如果你是个新号呢，就给你点垃圾流量测试一下，毕竟你没有证明过你有创作能力嘛，我也没有这个必要把好的流量给你，如果你是一个老账号的话，就让你的老粉丝来测试一下这个内容好不好，如果好的话就持续传播，不好的话这个事儿就拉倒了，但如果好的话，这个持续传播的路径是怎么样的呢？如果你的内容。很好，你会在一开始收获一个非常饱满的五边形，如果是他抖音的话，它就是一条完玻璃曲线啊，然后这个数据它是系统给你推算出来的，最精准的一帮人帮你打造出来的，就是你可以理解为全网最喜欢你这个内容的人在这个时候已经出现了，所以随着继续给你推流，你的这个数据是一定会崩塌的，你的那些非常精准的老观众看完你的内容之后，又是给你点赞，又是给你评论，它证明你的内容是一个好内容，所以他就会直去传播出去，传播出去之后来的第二茬观众，第三场观众，他就不是那么的精准了，然后你的五边形就会崩，崩完之后可能就长这个样子，它已经变成四边形了，对吧，甚至再传播可能变成三角形了，等这个五边形崩的不能再崩，崩的这个数据已经一塌糊涂的时候，它就会停止传播，所以薯条和抖架这种东西什么时候可以用啊，假如说我是一个新账号，我发的这个内容明明很好，在任何一个平台都火了，就在你这儿不火，我觉得是你一开始给我推送的那批人有问题，这个时候你可以花99块钱或者75块钱，花点钱去投一下，然后你投的时候呢，你要选一些你觉得比较精准的那些人群，纠正一下算法，就我觉得你给我误判了，这个时候你可以。存点钱仅用来校正使用啊，并不是拿这个钱去买流量，或者说你这是一个老账号的转型，你的账号其实没有问题，但是你发的这个内容呢，你的老粉丝不愿意看，我觉得这个系统对我测试的人群也有问题，你也可以投点钱试一下，但是依然是为了数据校正去使用，所以在你的各项指标都达标，你的内容没有问题的情况下，你不花钱，这个内容也是可以被传播出去的，也是可以被别人看见的，我们不不需要怀疑这个算法的技术层面的有效性，但是会有人跟我说，你这个观点我不同意，我今天花了500块钱，这个流量哗哗就来了，明天这500块钱我不花了，这流量就停了，这到底是怎么回事儿？我跟你说，你这个500块钱，你改变的是流速，但是你没有改变流量，只要你的内容是OK的，你的互动率高，你的完播率高，你的点击率高，你的内容就是会持续传播出去，你现在花的那笔钱，它的真正意义是本来这笔流量是要慢慢的给你的，可能未来3~7天我一点一点给你，你现在说了，我花了500块钱，这个平台拿钱得办事儿，对吧？所以我就把七天的流量作为一天，甚至作为一下午一个小时，我一下我就怼给你了，所以你就会觉得流量爆炸，那流量爆炸的结果是。什么呢？7天的饭你一顿吃完了，那下一顿你就没得吃了，所以明天你不花钱，你就会觉得这个流量停了，但实际上给你停了，不是因为你没花钱，是因为你昨天花钱了，你昨天花钱了，把本来要细水长流的这个东西一次性把子弹打光了，所以明天就没有流量可用了，所以你不花钱，这个流量也是你的，你花了钱只不过是改变了这个流量流动的速度，把未来几天的东西提前预知了一下，仅此而已。那这个时候又会有人不同意，他说我的内容已经自然流量都跑光了，我觉得该看我的内容都看完了，已经平台上都没有更多的人喜欢我的内容了，这个时候我再花钱，依然还会再有流量来，这个东西到底是怎么回事儿，这时候我们就要想一下，这些算法平台推送这些内容的目的是什么？就无论是小红书、抖音、快手、视频号，还是B站什么各种各样的平台，它所有的平台一定是希望用户打开我的平台，琳琅满目的东西，我看起来都是很喜欢的，我是上瘾的，我刷到我可以停不下来。那么如果你发的那个内容是评估出来点赞率有10%，点击率有10%的一个超级爆款，我肯定会把你排上去，但你的那个内容如果传播了一段时间。它的五边形已经萎缩了，它只有点赞率3%，点击率2%，我当然就不给你传播了，但是如果你能接受2%的点击率，甚至1%的点击率，1‰点击率，你说老子就是有钱，1000个人里面就只有一个人给我点赞，我还是要花这笔钱，那流量还是有的，我还是能给你推的，挤一挤还是能出水的，但是你要想一想，这笔钱花的有意义吗？曾经你的内容推的很好的时候，100个人里面有10个人给你点赞，这个东西都是免费送给你的，现在你要花钱去买，买过来的还是1000个人里面就一个人给你点赞的，你觉得这个钱花出去有什么价值呢？所以这就是为什么这些账号上我现在一分钱都不投了，就是该给我的流量，那些愿意看我内容的人，系统已经免费都推给我了，剩下一查不太愿意看我的那些人，你非要花笔钱摁着他头让他看，这件事情有什么意义呢？我花了这笔钱，我能回本吗？肯定是回不了的，你真有这个预算去买流量的话，你应该换一换你的设备，买个麦克风，多打几个灯，把钱投入到新内容的制作上，而不是拿去买那些垃圾的、2等的、三流的流量。这时候又有人说了，说我们不是做博主，我们就纯电商卖货的。也就拿一个素材，我投100块钱，能赚150块钱，我就开心了，这个事儿有没有问题，没有问题，一点儿问题都没有，你要是卖货的，你这个账能算得过来，你投流能回本，该投你就投，但是你要知道，只有这件事情才叫投流。我们刚刚讲的这个薯条和抖加什么的，这个东西不叫投流，这个叫内容加热服务。什么是投流？拿营业执照要开户的，受广告法约束的那个东西才叫投流，你发出去的那个素材，是要有一个广告部门单独去审核的，这个才叫投流。你用抖加和薯条买的那个东西，它不叫信息流广告投放，它叫平台内部的内容加热服务，它不让你挂链接，也不让你隐私域，它就只是帮你把内容提供一个更大的一个曝光而已，可是如果我的内容好的话，我不花钱也会有流量送过来，我的内容不好的话，我花了钱也无利可图，那你说平台要赚钱，对呀，我理解啊，所以我在你这儿卖货嘛，我也不会引流到其他平台去变现，我就在你这儿卖，我卖的东西你可以抽成，但是你说让我花钱买一个所谓的内容加热服务，把我未来7天的流量一次性打给我，然后我未来6天断流，那不是来搞笑的吗？"

    print_colored $BLUE "使用写死的飞机反推检查内容进行测试..."
    print_colored $YELLOW "内容预览: ${user_content:0:100}..."

    # 调用API
    call_openai "$user_content"

    if [ $? -eq 0 ]; then
        print_colored $GREEN "✓ 调试完成"
        print_colored $BLUE "可以在浏览器中打开桌面的a.html查看效果"
        print_colored $YELLOW "文件路径: $HOME/Desktop/a.html"
    else
        print_colored $RED "✗ 调试失败"
        exit 1
    fi
}

# 运行主函数
main "$@"
