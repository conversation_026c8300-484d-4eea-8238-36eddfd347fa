#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示带颜色的信息
info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

error() {
    echo -e "${RED}[错误]${NC} $1"
    exit 1
}

# 获取当前版本号
get_current_version() {
    if [ ! -f "pubspec.yaml" ]; then
        error "未找到 pubspec.yaml 文件"
    fi

    # 从pubspec.yaml中提取版本号
    CURRENT_VERSION=$(grep "^version:" pubspec.yaml | sed 's/version: //' | tr -d ' ')

    if [ -z "$CURRENT_VERSION" ]; then
        error "无法从 pubspec.yaml 中读取版本号"
    fi

    echo "$CURRENT_VERSION"
}

# 验证版本号格式
validate_version() {
    local version=$1

    # 检查版本号格式：major.minor.patch (简化格式，不要+build)
    if [[ ! $version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        return 1
    fi

    return 0
}

# 更新版本号
update_version() {
    local new_version=$1

    info "正在更新版本号到 $new_version..."

    # 备份原始文件
    cp pubspec.yaml pubspec.yaml.bak

    # 为Flutter添加构建号，格式为 x.x.x+1
    local version_with_build="${new_version}+1"

    # 使用sed替换版本号
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/^version:.*/version: $version_with_build/" pubspec.yaml
    else
        # Linux
        sed -i "s/^version:.*/version: $version_with_build/" pubspec.yaml
    fi

    # 验证更新是否成功
    local updated_version=$(get_current_version)
    if [ "$updated_version" = "$version_with_build" ]; then
        success "版本号已成功更新到 $version_with_build"
        return 0
    else
        error "版本号更新失败"
        return 1
    fi
}

# 全局变量存储版本信息
NEW_VERSION=""
UPDATE_DESCRIPTION_ARRAY=""

# 处理版本号更新（仅在生产环境）
handle_version_update() {
    if [ "$APP_ENV" = "prd" ]; then
        echo ""
        echo "====================================================="
        echo "              生产环境版本号管理                      "
        echo "====================================================="

        # 获取当前版本号（去掉+build部分显示）
        local current_version=$(get_current_version)
        local display_version=$(echo "$current_version" | cut -d'+' -f1)
        info "当前版本号: ${GREEN}$display_version${NC}"

        echo ""
        echo "版本号格式说明："
        echo "  - 格式：主版本号.次版本号.修订号"
        echo "  - 示例：1.2.3"
        echo "  - 主版本号：重大功能变更或不兼容更新"
        echo "  - 次版本号：新功能添加，向后兼容"
        echo "  - 修订号：bug修复，向后兼容"
        echo ""

        # 直接要求输入新版本号
        while true; do
            echo ""
            read -p "请输入新的版本号 (格式: x.y.z): " new_version

            # 验证版本号格式
            if validate_version "$new_version"; then
                # 检查新版本号是否与当前版本号相同
                if [ "$new_version" = "$display_version" ]; then
                    warning "新版本号与当前版本号相同，请输入不同的版本号"
                    continue
                fi

                # 直接更新，不再二次确认
                info "更新版本号: ${RED}$display_version${NC} -> ${GREEN}$new_version${NC}"

                if update_version "$new_version"; then
                    # 保存新版本号到全局变量
                    NEW_VERSION="$new_version"
                    break
                else
                    error "版本号更新失败，请重试"
                fi
            else
                error "版本号格式不正确！请使用格式：x.y.z（例如：1.2.3）"
                echo "  - x, y, z 必须是数字"
                echo "  - 使用点号(.)分隔主版本、次版本、修订号"
            fi
        done

        # 输入更新信息
        echo ""
        echo "====================================================="
        echo "              版本更新信息                           "
        echo "====================================================="
        echo ""
        echo "更新信息格式说明："
        echo "  - 请用英文逗号（,）分隔不同的更新内容"
        echo "  - 示例：更新了微信兼容性,支持了其他APP,修复了已知问题"
        echo ""

        while true; do
            read -p "请输入本版本的更新信息: " update_info

            if [ -n "$update_info" ]; then
                info "您输入的更新信息: $update_info"

                # 将更新信息按英文逗号分割并转换为JSON数组格式
                UPDATE_DESCRIPTION_ARRAY=$(echo "$update_info" | sed 's/,/\n/g' | sed 's/^/"/; s/$/",/' | tr -d '\n' | sed 's/,$//')
                UPDATE_DESCRIPTION_ARRAY="[$UPDATE_DESCRIPTION_ARRAY]"

                info "解析后的更新信息数组: $UPDATE_DESCRIPTION_ARRAY"
                break
            else
                warning "更新信息不能为空，请重新输入"
            fi
        done

        echo "====================================================="
        echo ""
    fi
}

# 显示菜单并获取用户选择的环境
select_environment() {
    echo "请选择构建环境:"
    select env in "开发环境(develop)" "预发环境(pre)" "生产环境(prd)" "退出"; do
        case $env in
            "开发环境(develop)")
                export APP_ENV="develop"
                break
                ;;
            "预发环境(pre)")
                export APP_ENV="pre"
                break
                ;;
            "生产环境(prd)")
                export APP_ENV="prd"
                break
                ;;
            "退出")
                exit 0
                ;;
            *)
                error "无效选择，请重新选择"
                ;;
        esac
    done
}

# 检查必要的工具是否存在
check_requirements() {
    info "检查必要工具..."

    # 检查Flutter
    if ! command -v flutter &> /dev/null; then
        error "未找到Flutter命令，请确保Flutter已安装并添加到PATH中"
    fi

    # 检查ossutil（仅在生产环境需要）
    if [ "$APP_ENV" = "prd" ]; then
        if [ ! -f "/Users/<USER>/fang/ossutil/ossutil" ]; then
            error "未找到ossutil工具，无法上传到阿里云OSS"
        fi

        # 检查curl（用于调用版本存储API）
        if ! command -v curl &> /dev/null; then
            error "未找到curl工具，无法调用版本存储API"
        fi

        # 检查qrencode（用于生成二维码）
        if ! command -v qrencode &> /dev/null; then
            warning "未找到qrencode工具，将无法生成下载二维码"
            warning "可以使用以下命令安装: brew install qrencode"
            read -p "是否继续构建过程? (y/n): " continue_choice
            if [[ $continue_choice != "y" && $continue_choice != "Y" ]]; then
                exit 1
            fi
        fi
    fi

    success "所有必要工具检查通过"
}

# 配置混淆规则
configure_proguard() {
    info "配置最高级别混淆规则..."

    # 检查proguard-rules.pro文件是否存在
    PROGUARD_FILE="android/app/proguard-rules.pro"
    FULL_PROGUARD_FILE="android/app/proguard-rules-full.pro"

    if [ ! -f "$PROGUARD_FILE" ]; then
        error "未找到混淆规则文件: $PROGUARD_FILE"
    fi

    if [ ! -f "$FULL_PROGUARD_FILE" ]; then
        error "未找到完整混淆规则文件: $FULL_PROGUARD_FILE"
    fi

    # 备份原始文件
    cp "$PROGUARD_FILE" "${PROGUARD_FILE}.bak"

    # 使用完整的混淆规则文件
    cp "$FULL_PROGUARD_FILE" "$PROGUARD_FILE"

    success "混淆规则配置完成"
}

# 复制配置文件
copy_config_file() {
    info "准备配置文件..."

    # 源配置文件路径
    SOURCE_CONFIG="config/config.$APP_ENV.json"

    # 目标配置文件路径
    TARGET_CONFIG="android/app/src/main/assets/config.json"

    # 检查源文件是否存在
    if [ ! -f "$SOURCE_CONFIG" ]; then
        error "配置文件 $SOURCE_CONFIG 不存在!"
    fi

    # 确保目标目录存在
    mkdir -p "android/app/src/main/assets"

    # 复制配置文件
    cp "$SOURCE_CONFIG" "$TARGET_CONFIG"

    success "配置文件复制完成: $SOURCE_CONFIG -> $TARGET_CONFIG"

    # 如果是生产环境，显示提示
    if [ "$APP_ENV" = "prd" ]; then
        warning "当前使用生产环境配置，API地址为 https://api.xunhewenhua.com"
    fi
}

# 清理缓存
clean_cache() {
    info "清理缓存..."

    # 清理Flutter缓存
    flutter clean

    # 清理Gradle缓存
    cd android && ./gradlew clean && cd ..

    success "缓存清理完成"
}

# 构建APK
build_apk() {
    info "开始构建APK..."

    # 创建符号表目录
    mkdir -p build/app/outputs/symbols

    # 根据环境选择构建参数
    if [ "$APP_ENV" = "prd" ]; then
        info "生产环境：启用完整代码混淆和优化"
        # 生产环境使用完整混淆
        flutter build apk --release \
            --obfuscate \
            --split-debug-info=build/app/outputs/symbols \
            --dart-define=APP_ENV=$APP_ENV \
            --target-platform android-arm64
    elif [ "$APP_ENV" = "pre" ]; then
        info "预发布环境：启用标准混淆"
        # 预发布环境使用标准混淆
        flutter build apk --release \
            --dart-define=APP_ENV=$APP_ENV \
            --target-platform android-arm64
    else
        info "开发环境：不启用混淆"
        # 开发环境不混淆
        flutter build apk --release \
            --dart-define=APP_ENV=$APP_ENV
    fi

    # 检查构建结果
    if [ $? -ne 0 ]; then
        # 检查是否存在missing_rules.txt文件
        MISSING_RULES_FILE="build/app/outputs/mapping/release/missing_rules.txt"
        if [ -f "$MISSING_RULES_FILE" ]; then
            warning "检测到缺失的混淆规则，尝试自动修复..."

            # 将缺失的规则添加到混淆文件中
            cat "$MISSING_RULES_FILE" >> "android/app/proguard-rules-advanced.pro"

            # 重新尝试构建
            info "使用更新的混淆规则重新构建..."
            if [ "$APP_ENV" = "prd" ]; then
                flutter build apk --release \
                    --obfuscate \
                    --split-debug-info=build/app/outputs/symbols \
                    --dart-define=APP_ENV=$APP_ENV \
                    --target-platform android-arm64
            else
                flutter build apk --release \
                    --dart-define=APP_ENV=$APP_ENV \
                    --target-platform android-arm64
            fi

            # 再次检查构建结果
            if [ $? -ne 0 ]; then
                error "即使添加了缺失的规则，APK构建仍然失败"
            fi
        else
            error "APK构建失败，且未找到缺失规则文件"
        fi
    fi

    success "APK构建成功"

    # 显示混淆信息
    if [ "$APP_ENV" = "prd" ]; then
        info "生产环境构建完成，已启用完整代码混淆"
        info "符号表已保存到: build/app/outputs/symbols/"
        warning "请妥善保存符号表文件，用于后续调试和崩溃分析"
    fi
}

# 调用版本存储API
store_version_info() {
    if [ "$APP_ENV" = "prd" ] && [ -n "$NEW_VERSION" ] && [ -n "$UPDATE_DESCRIPTION_ARRAY" ]; then
        info "正在调用版本存储API..."

        # 从生产环境配置文件中获取API地址
        API_BASE_URL=$(grep '"api_base_url"' "config/config.prd.json" | sed 's/.*"api_base_url": *"\([^"]*\)".*/\1/')

        if [ -z "$API_BASE_URL" ]; then
            warning "无法从配置文件中获取API地址，使用默认地址"
            API_BASE_URL="https://api.xunhewenhua.com"
        fi

        API_URL="${API_BASE_URL}/app/version/store"

        # 构建JSON请求体
        JSON_DATA=$(cat <<EOF
{
    "version": "$NEW_VERSION",
    "update_description": $UPDATE_DESCRIPTION_ARRAY,
    "is_current": true
}
EOF
)

        info "API地址: $API_URL"
        info "请求数据: $JSON_DATA"

        # 调用API
        RESPONSE=$(curl -s -X POST "$API_URL" \
            -H "Content-Type: application/json" \
            -d "$JSON_DATA")

        if [ $? -eq 0 ]; then
            info "版本存储API调用成功"
            info "API响应: $RESPONSE"

            # 检查响应中的code字段
            CODE=$(echo "$RESPONSE" | grep -o '"code":[0-9]*' | sed 's/"code"://')
            if [ "$CODE" = "0" ]; then
                success "版本信息已成功存储到服务器"
            else
                MESSAGE=$(echo "$RESPONSE" | grep -o '"message":"[^"]*"' | sed 's/"message":"\([^"]*\)"/\1/')
                warning "版本存储失败: $MESSAGE"
            fi
        else
            warning "版本存储API调用失败"
        fi
    else
        info "跳过版本存储API调用（非生产环境或缺少版本信息）"
    fi
}

# 上传到阿里云OSS并生成二维码
upload_to_oss() {
    if [ "$APP_ENV" = "prd" ]; then
        info "准备上传APK到阿里云OSS..."

        APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
        DOWNLOAD_URL="https://assets-xunhe.oss-cn-qingdao.aliyuncs.com/apk/app-release.apk"
        QR_CODE_PATH="build/app/outputs/flutter-apk/download_qrcode.png"

        # 检查APK是否存在
        if [ ! -f "$APK_PATH" ]; then
            error "APK文件不存在: $APK_PATH"
        fi

        # 删除OSS上的旧文件
        info "删除OSS上的旧文件..."
        /Users/<USER>/fang/ossutil/ossutil rm oss://assets-xunhe/apk/app-release.apk --force

        # 上传新文件
        info "上传新文件到OSS..."
        /Users/<USER>/fang/ossutil/ossutil cp "$APK_PATH" oss://assets-xunhe/apk/app-release.apk --force

        if [ $? -ne 0 ]; then
            error "上传到OSS失败"
        fi

        success "APK已成功上传到阿里云OSS: oss://assets-xunhe/apk/app-release.apk"
        info "下载链接: $DOWNLOAD_URL"

        # 调用版本存储API
        store_version_info

        # 生成二维码
        if command -v qrencode &> /dev/null; then
            info "正在生成下载二维码..."

            # 确保输出目录存在
            mkdir -p "$(dirname "$QR_CODE_PATH")"

            # 生成二维码
            qrencode -o "$QR_CODE_PATH" -s 10 "$DOWNLOAD_URL"

            if [ $? -ne 0 ]; then
                warning "生成二维码失败"
            else
                success "二维码已生成: $QR_CODE_PATH"

                # 显示二维码（如果在终端中）
                if [ -t 1 ] && command -v open &> /dev/null; then
                    info "正在打开二维码图片..."
                    open "$QR_CODE_PATH"
                fi

                # 上传二维码到OSS
                info "上传二维码到OSS..."
                /Users/<USER>/fang/ossutil/ossutil cp "$QR_CODE_PATH" oss://assets-xunhe/apk/download_qrcode.png --force

                if [ $? -eq 0 ]; then
                    success "二维码已上传到OSS: oss://assets-xunhe/apk/download_qrcode.png"
                    info "二维码链接: https://assets-xunhe.oss-cn-qingdao.aliyuncs.com/apk/download_qrcode.png"
                else
                    warning "上传二维码到OSS失败"
                fi
            fi
        else
            warning "未找到qrencode工具，跳过生成二维码"
            warning "可以使用以下命令安装: brew install qrencode"
        fi
    else
        info "非生产环境，跳过上传到OSS"
    fi
}

# 主函数
main() {
    echo "====================================================="
    echo "              存点APP构建脚本                         "
    echo "====================================================="

    # 选择环境
    select_environment
    info "已选择环境: $APP_ENV"

    # 处理版本号更新（仅在生产环境）
    handle_version_update

    # 检查必要工具
    check_requirements

    # 配置混淆规则
    configure_proguard

    # 复制配置文件
    copy_config_file

    # 根据环境决定是否自动执行
    if [ "$APP_ENV" = "prd" ]; then
        # 生产环境自动执行所有步骤
        info "生产环境构建：自动执行清理和构建步骤"
        clean_cache
        build_apk
        upload_to_oss
    else
        # 非生产环境询问用户
        read -p "是否清理缓存? (y/n): " clean_cache_choice
        if [[ $clean_cache_choice == "y" || $clean_cache_choice == "Y" ]]; then
            clean_cache
        fi

        read -p "是否开始构建APK? (y/n): " build_choice
        if [[ $build_choice == "y" || $build_choice == "Y" ]]; then
            build_apk
            upload_to_oss
        else
            info "跳过构建过程"
        fi
    fi

    echo "====================================================="
    success "构建脚本执行完成"
    echo "====================================================="

    # 显示构建信息总结
    if [ "$APP_ENV" = "prd" ] && [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
        APK_SIZE=$(du -h "build/app/outputs/flutter-apk/app-release.apk" | cut -f1)
        APK_DATE=$(date "+%Y-%m-%d %H:%M:%S")
        FINAL_VERSION=$(get_current_version)
        DISPLAY_VERSION=$(echo "$FINAL_VERSION" | cut -d'+' -f1)

        echo ""
        echo "📱 构建信息摘要:"
        echo "---------------------------------------------------"
        echo "🔹 构建环境: ${GREEN}生产环境${NC}"
        echo "🔹 应用版本: ${GREEN}$DISPLAY_VERSION${NC}"
        echo "🔹 构建时间: $APK_DATE"
        echo "🔹 APK大小: $APK_SIZE"
        echo "🔹 下载链接: ${BLUE}https://assets-xunhe.oss-cn-qingdao.aliyuncs.com/apk/app-release.apk${NC}"

        if [ -f "build/app/outputs/flutter-apk/download_qrcode.png" ]; then
            echo "🔹 二维码链接: ${BLUE}https://assets-xunhe.oss-cn-qingdao.aliyuncs.com/apk/download_qrcode.png${NC}"
            echo ""
            echo "扫描二维码下载APK:"
            echo "---------------------------------------------------"
            echo "二维码已保存到: build/app/outputs/flutter-apk/download_qrcode.png"
        fi

        echo ""
    fi
}

# 执行主函数
main
