#!/bin/bash

# 获取环境变量，默认为develop
APP_ENV=${APP_ENV:-develop}
echo "当前环境: $APP_ENV"

# 源配置文件路径
SOURCE_CONFIG="config/config.$APP_ENV.json"

# 目标配置文件路径
TARGET_CONFIG="android/app/src/main/assets/config.json"

# 检查源文件是否存在
if [ ! -f "$SOURCE_CONFIG" ]; then
    echo "错误: 配置文件 $SOURCE_CONFIG 不存在!"
    exit 1
fi

# 复制配置文件
echo "正在复制配置文件: $SOURCE_CONFIG -> $TARGET_CONFIG"
cp "$SOURCE_CONFIG" "$TARGET_CONFIG"

echo "配置文件复制完成"
