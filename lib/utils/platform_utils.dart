/// 平台识别工具类
///
/// 用于根据schemeURL识别内容来源平台并提供相应的logo资源
class PlatformUtils {
  /// 根据schemeURL识别平台类型
  ///
  /// [schemeUrl] 原生跳转链接
  /// 返回平台标识字符串，如果无法识别则返回null
  static String? getPlatformFromSchemeUrl(String? schemeUrl) {
    if (schemeUrl == null || schemeUrl.isEmpty) {
      return null;
    }

    // 小红书
    if (schemeUrl.startsWith('xhsdiscover://')) {
      return 'xiaohongshu';
    }
    // B站
    else if (schemeUrl.startsWith('bilibili://')) {
      return 'bilibili';
    }
    // 抖音
    else if (schemeUrl.startsWith('snssdk1128://')) {
      return 'douyin';
    }
    // 微信 (支持原生协议和公众号链接)
    else if (schemeUrl.startsWith('weixin://') || _isWechatPublicAccountUrl(schemeUrl)) {
      return 'wechat';
    }
    // 豆瓣 (通过HTTP链接判断)
    else if (schemeUrl.contains('douban.com')) {
      return 'douban';
    }
    // 快手
    else if (schemeUrl.startsWith('kwai://') || schemeUrl.startsWith('kuaishou://')) {
      return 'kuaishou';
    }
    // 美团
    else if (schemeUrl.startsWith('imeituan://') || schemeUrl.contains('meituan.com')) {
      return 'meituan';
    }
    // 拼多多
    else if (schemeUrl.startsWith('pinduoduo://') || schemeUrl.contains('yangkeduo.com')) {
      return 'pinduoduo';
    }
    // 淘宝
    else if (schemeUrl.startsWith('taobao://') || schemeUrl.contains('taobao.com')) {
      return 'taobao';
    }
    // 京东
    else if (schemeUrl.startsWith('openapp.jdmobile://') || schemeUrl.contains('jd.com')) {
      return 'jingdong';
    }

    return null;
  }

  /// 判断是否为微信公众号链接
  ///
  /// [url] 待检查的URL
  /// 返回true如果是微信公众号链接，否则返回false
  static bool _isWechatPublicAccountUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host == 'mp.weixin.qq.com';
    } catch (e) {
      return false;
    }
  }

  /// 根据平台标识获取logo资源路径
  ///
  /// [platform] 平台标识
  /// 返回logo资源路径，如果平台不支持则返回null
  static String? getPlatformLogoAsset(String platform) {
    switch (platform) {
      case 'xiaohongshu':
        return 'assets/xiaohongshu.png';
      case 'bilibili':
        return 'assets/bilibili.png';
      case 'douyin':
        return 'assets/douyin.png';
      case 'wechat':
        return 'assets/wechat.png';
      case 'douban':
        return 'assets/douban.png';
      case 'kuaishou':
        return 'assets/kuaishou.png';
      case 'meituan':
        return 'assets/meituan.png';
      case 'pinduoduo':
        return 'assets/Pinduoduo.png';
      case 'taobao':
        return 'assets/taobao.png';
      case 'jingdong':
        return 'assets/jingdong.png';
      default:
        return null;
    }
  }

  /// 根据平台标识获取平台中文名称
  ///
  /// [platform] 平台标识
  /// 返回平台中文名称
  static String getPlatformDisplayName(String platform) {
    switch (platform) {
      case 'xiaohongshu':
        return '小红书';
      case 'bilibili':
        return 'B站';
      case 'douyin':
        return '抖音';
      case 'wechat':
        return '微信';
      case 'douban':
        return '豆瓣';
      case 'kuaishou':
        return '快手';
      case 'meituan':
        return '美团';
      case 'pinduoduo':
        return '拼多多';
      case 'taobao':
        return '淘宝';
      case 'jingdong':
        return '京东';
      default:
        return '未知平台';
    }
  }

  /// 根据schemeURL直接获取平台logo资源路径
  ///
  /// [schemeUrl] 原生跳转链接
  /// 返回logo资源路径，如果无法识别平台则返回null
  static String? getPlatformLogoFromSchemeUrl(String? schemeUrl) {
    final platform = getPlatformFromSchemeUrl(schemeUrl);
    if (platform == null) return null;
    return getPlatformLogoAsset(platform);
  }
}
