import 'package:flutter/foundation.dart';
import 'package:aishoucang/native_bridge/native_bridge.dart';
import 'package:aishoucang/api/api_provider.dart';

/// Flutter端的笔记WebView辅助类
/// 提供便捷的方法来打开笔记详情页面
class NoteWebviewHelper {
  static final NoteWebviewHelper _instance = NoteWebviewHelper._internal();
  factory NoteWebviewHelper() => _instance;
  NoteWebviewHelper._internal();

  final NativeBridge _nativeBridge = NativeBridge();

  /// 打开笔记详情WebView
  ///
  /// [noteId] 笔记ID
  ///
  /// 返回是否成功打开
  Future<bool> openNoteDetail(String noteId) async {
    try {
      if (noteId.isEmpty) {
        debugPrint('笔记ID为空，无法打开笔记详情');
        return false;
      }

      debugPrint('准备打开笔记详情: $noteId');

      final result = await _nativeBridge.openNoteWebview(noteId: noteId);

      if (result['success'] == true) {
        debugPrint('成功打开笔记详情WebView');
        return true;
      } else {
        final error = result['error'] ?? '未知错误';
        debugPrint('打开笔记详情WebView失败: $error');
        return false;
      }
    } catch (e) {
      debugPrint('打开笔记详情WebView异常: $e');
      return false;
    }
  }

  /// 获取笔记详情数据（不打开WebView）
  ///
  /// [noteId] 笔记ID
  ///
  /// 返回笔记详情数据，如果失败返回null
  Future<Map<String, dynamic>?> getNoteDetail(String noteId) async {
    try {
      if (noteId.isEmpty) {
        debugPrint('笔记ID为空，无法获取笔记详情');
        return null;
      }

      debugPrint('准备获取笔记详情数据: $noteId');

      final apiProvider = ApiProvider();
      final noteDetail = await apiProvider.noteApi.getNoteDetail(noteId);

      debugPrint('成功获取笔记详情数据: ${noteDetail.title}');

      return noteDetail.toJson();
    } catch (e) {
      debugPrint('获取笔记详情数据失败: $e');
      return null;
    }
  }

  /// 获取笔记HTML内容
  ///
  /// [noteId] 笔记ID
  ///
  /// 返回HTML内容字符串，如果失败返回null
  Future<String?> getNoteHtml(String noteId) async {
    try {
      if (noteId.isEmpty) {
        debugPrint('笔记ID为空，无法获取笔记HTML');
        return null;
      }

      debugPrint('准备获取笔记HTML内容: $noteId');

      final apiProvider = ApiProvider();
      final html = await apiProvider.noteApi.getNoteHtml(noteId);

      debugPrint('成功获取笔记HTML内容，长度: ${html.length}');

      return html;
    } catch (e) {
      debugPrint('获取笔记HTML内容失败: $e');
      return null;
    }
  }
}
