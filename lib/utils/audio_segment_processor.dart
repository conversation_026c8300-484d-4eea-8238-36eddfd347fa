import 'dart:io';
import 'dart:async';
import '../native_bridge/native_bridge.dart';
import '../native_bridge/spark_chain_bridge.dart';

/// 音频分段处理工具类
/// 用于将长音频切割成多个片段并进行语音识别
class AudioSegmentProcessor {
  static const int _segmentDurationSeconds = 60; // 每段60秒

  // 进度监听器映射
  static final Map<String, Function(double)> _progressListeners = {};
  
  /// 处理长音频文件，切割并识别
  ///
  /// [audioPath] 音频文件路径
  /// [videoDurationSeconds] 预先获取的视频时长（秒），如果提供则不再重新获取
  /// [onProgress] 进度回调，参数为 (当前段数, 总段数, 当前状态描述)
  /// [onOverallProgress] 整体进度回调，参数为进度百分比 (0.0-1.0)
  ///
  /// 返回识别结果，格式为：
  /// ```dart
  /// {
  ///   'success': true/false,
  ///   'text': '完整识别文本', // 成功时
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  static Future<Map<String, dynamic>> processLongAudio({
    required String audioPath,
    double? videoDurationSeconds,
    Function(int currentSegment, int totalSegments, String status)? onProgress,
    Function(double progress)? onOverallProgress,
  }) async {
    try {
      // 检查音频文件是否存在
      final audioFile = File(audioPath);
      if (!await audioFile.exists()) {
        return {
          'success': false,
          'error': '音频文件不存在: $audioPath',
        };
      }

      double totalDurationSeconds;

      // 如果已提供视频时长，则直接使用；否则报错
      if (videoDurationSeconds != null) {
        totalDurationSeconds = videoDurationSeconds;
        onProgress?.call(0, 0, '使用预获取的视频时长：${totalDurationSeconds.toStringAsFixed(1)}秒');
      } else {
        return {
          'success': false,
          'error': '未提供音频时长，无法进行分段处理',
        };
      }
      final totalSegments = (totalDurationSeconds / _segmentDurationSeconds).ceil();

      // 如果音频时长小于等于60秒，直接识别
      if (totalDurationSeconds <= _segmentDurationSeconds) {
        onProgress?.call(1, 1, '音频时长不超过1分钟，直接识别...');
        onOverallProgress?.call(0.0); // 开始识别
        final result = await _recognizeSingleAudio(audioPath, onOverallProgress);
        if (result['success'] == true) {
          onOverallProgress?.call(1.0); // 识别完成
        }
        return result;
      }

      print('=== 开始音频分段切割 ===');
      print('音频总时长: ${totalDurationSeconds.toStringAsFixed(1)} 秒');
      print('分段时长: $_segmentDurationSeconds 秒');
      print('总段数: $totalSegments 段');

      onProgress?.call(0, totalSegments, '开始切割音频，共需切割为 $totalSegments 段...');

      // 切割音频
      final segmentPaths = <String>[];
      final segmentInfos = <Map<String, dynamic>>[];  // 内存中保存片段信息
      for (int i = 0; i < totalSegments; i++) {
        final startTime = i * _segmentDurationSeconds;

        // 计算当前段的实际时长
        final remainingTime = totalDurationSeconds - startTime;
        final duration = remainingTime > _segmentDurationSeconds
            ? _segmentDurationSeconds
            : remainingTime.ceil();

        print('切割第 ${i + 1} 段: ${startTime}s - ${startTime + duration}s (时长: ${duration}s)');
        onProgress?.call(i + 1, totalSegments, '正在切割第 ${i + 1} 段音频（${duration}秒）...');

        final segmentResult = await NativeBridge().splitAudio(
          audioPath: audioPath,
          startTime: startTime,
          duration: duration,
          segmentIndex: i,
        );

        if (segmentResult['success'] != true) {
          print('切割第 ${i + 1} 段失败: ${segmentResult['error']}');
          // 清理已创建的片段
          await _cleanupSegments(segmentPaths);
          return {
            'success': false,
            'error': '切割第 ${i + 1} 段音频失败: ${segmentResult['error']}',
          };
        }

        final segmentPath = segmentResult['filePath'] as String;
        segmentPaths.add(segmentPath);

        // 在内存中保存片段信息
        final segmentInfo = {
          'index': i + 1,
          'startTime': startTime,
          'duration': duration,
          'endTime': startTime + duration,
          'filePath': segmentPath,
          'fileName': segmentPath.split('/').last,
        };
        segmentInfos.add(segmentInfo);

        print('切割第 ${i + 1} 段成功: $segmentPath');

        // 测试：将音频片段复制到相册目录
        await _copySegmentToGallery(segmentPath, i + 1);
      }

      print('=== 音频切割完成，共生成 ${segmentPaths.length} 个片段 ===');

      onProgress?.call(0, totalSegments, '音频切割完成，开始分段识别...');

      // 分段识别
      final recognitionResults = <String>[];
      for (int i = 0; i < segmentPaths.length; i++) {
        onProgress?.call(i + 1, totalSegments, '正在识别第 ${i + 1} 段音频...');

        final segmentPath = segmentPaths[i];
        print('开始识别第 ${i + 1} 段音频: $segmentPath');

        // 计算当前段的进度回调
        Function(double segmentProgress)? segmentProgressCallback;
        if (onOverallProgress != null) {
          segmentProgressCallback = (double segmentProgress) {
            // 整体进度 = (已完成段数 + 当前段进度) / 总段数
            final overallProgress = (i + segmentProgress) / totalSegments;
            print('🔄 段进度回调: 第${i+1}段进度=${(segmentProgress*100).toStringAsFixed(1)}%, 整体进度=${(overallProgress*100).toStringAsFixed(1)}%');
            onOverallProgress(overallProgress);
          };
        }

        final recognitionResult = await _recognizeSingleAudio(segmentPath, segmentProgressCallback);

        if (recognitionResult['success'] == true) {
          final text = recognitionResult['text'] as String? ?? '';
          if (text.isNotEmpty) {
            recognitionResults.add(text);
            print('第 ${i + 1} 段音频识别成功: "$text"');
          } else {
            print('第 ${i + 1} 段音频识别成功但结果为空');
          }
        } else {
          // 识别失败时记录错误但继续处理其他片段
          print('第 ${i + 1} 段音频识别失败: ${recognitionResult['error']}');
        }
      }

      // 清理临时音频片段
      await _cleanupSegments(segmentPaths);

      onProgress?.call(totalSegments, totalSegments, '识别完成，正在拼接结果...');

      // 打印识别统计信息
      print('=== 音频分段识别完成统计 ===');
      print('总段数: $totalSegments');
      print('成功识别段数: ${recognitionResults.length}');
      print('失败段数: ${totalSegments - recognitionResults.length}');

      // 拼接识别结果
      final finalText = recognitionResults.join(' ');
      print('最终拼接结果: "$finalText"');
      print('最终结果长度: ${finalText.length} 字符');

      return {
        'success': true,
        'text': finalText,
        'segmentCount': totalSegments,
        'recognizedSegments': recognitionResults.length,
        'segmentInfos': segmentInfos,  // 包含所有片段的详细信息
      };

    } catch (e) {
      return {
        'success': false,
        'error': '处理音频时发生异常: $e',
      };
    }
  }

  /// 识别单个音频文件
  static Future<Map<String, dynamic>> _recognizeSingleAudio(
    String audioPath,
    [Function(double progress)? onProgress]
  ) async {
    try {
      if (onProgress != null) {
        // 使用支持进度回调的方法
        final sessionId = 'segment_${DateTime.now().millisecondsSinceEpoch}';

        // 初始化进度事件监听器
        SparkChainBridge.initProgressListener();

        // 设置进度监听器
        _setupProgressListener(sessionId, onProgress);

        final recognitionResult = await SparkChainBridge.recognizeAudioFileWithProgress(
          audioFilePath: audioPath,
          language: 'zh_cn',  // 中文识别
          domain: 'slm',      // 大模型识别
          accent: 'mandarin', // 普通话
          sessionId: sessionId,
        );

        // 清理进度监听器
        _cleanupProgressListener(sessionId);

        return recognitionResult;
      } else {
        // 使用普通方法
        final recognitionResult = await SparkChainBridge.recognizeAudioFile(
          audioFilePath: audioPath,
          language: 'zh_cn',  // 中文识别
          domain: 'slm',      // 大模型识别
          accent: 'mandarin', // 普通话
        );

        return recognitionResult;
      }
    } catch (e) {
      return {
        'success': false,
        'error': '识别音频失败: $e',
      };
    }
  }

  /// 复制音频片段到外部存储目录（测试用）
  static Future<void> _copySegmentToGallery(String segmentPath, int segmentNumber) async {
    try {
      // 使用Android外部存储路径
      const externalStoragePath = '/storage/emulated/0/Download/AudioSegments';

      // 创建音频片段保存目录
      final galleryDir = Directory(externalStoragePath);
      if (!await galleryDir.exists()) {
        await galleryDir.create(recursive: true);
      }

      // 生成文件名
      final originalFile = File(segmentPath);
      final extension = originalFile.path.split('.').last;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final galleryPath = '$externalStoragePath/segment_${segmentNumber}_${timestamp}.${extension}';

      // 复制文件
      await originalFile.copy(galleryPath);
      print('音频片段已保存到下载目录: $galleryPath');

    } catch (e) {
      print('保存音频片段到下载目录失败: $e');
    }
  }

  /// 清理临时音频片段
  static Future<void> _cleanupSegments(List<String> segmentPaths) async {
    print('=== 开始清理临时音频片段 ===');
    for (final segmentPath in segmentPaths) {
      try {
        final file = File(segmentPath);
        if (await file.exists()) {
          await file.delete();
          print('已删除临时音频片段: $segmentPath');
        }
      } catch (e) {
        print('删除音频片段失败: $segmentPath, 错误: $e');
      }
    }
    print('=== 临时音频片段清理完成 ===');
  }

  /// 设置进度监听器
  static void _setupProgressListener(String sessionId, Function(double) onProgress) {
    _progressListeners[sessionId] = onProgress;
    print('设置进度监听器: $sessionId');
  }

  /// 清理进度监听器
  static void _cleanupProgressListener(String sessionId) {
    _progressListeners.remove(sessionId);
    print('清理进度监听器: $sessionId');
  }

  /// 处理进度事件（由EventChannel调用）
  static void handleProgressEvent(Map<String, dynamic> event) {
    print('📡 收到进度事件: $event');
    final sessionId = event['sessionId'] as String?;
    final progress = event['progress'] as double?;
    final type = event['type'] as String?;

    print('📊 解析事件 - sessionId: $sessionId, progress: $progress, type: $type');
    print('📋 当前监听器列表: ${_progressListeners.keys.toList()}');

    if (sessionId != null && progress != null && type == 'recognition_progress') {
      final listener = _progressListeners[sessionId];
      if (listener != null) {
        print('✅ 找到监听器，调用进度回调: ${(progress * 100).toStringAsFixed(1)}%');
        listener(progress);
        print('进度更新: $sessionId -> ${(progress * 100).toStringAsFixed(1)}%');
      } else {
        print('❌ 未找到对应的监听器: $sessionId');
      }
    } else {
      print('❌ 事件数据不完整或类型不匹配');
    }
  }
}
