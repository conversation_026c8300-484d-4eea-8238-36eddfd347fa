import 'dart:convert';
import 'package:flutter/services.dart';

/// 配置工具类
///
/// 用于读取应用配置文件
class ConfigUtil {
  // 单例实例
  static final ConfigUtil _instance = ConfigUtil._internal();
  
  // 工厂构造函数
  factory ConfigUtil() => _instance;
  
  // 内部构造函数
  ConfigUtil._internal();
  
  // 配置缓存
  Map<String, dynamic>? _config;
  
  /// 加载配置
  Future<Map<String, dynamic>> loadConfig() async {
    if (_config != null) {
      return _config!;
    }
    
    try {
      // 从assets中读取配置文件
      final String configString = await rootBundle.loadString('assets/config.json');
      _config = json.decode(configString) as Map<String, dynamic>;
      return _config!;
    } catch (e) {
      print('加载配置文件时出错: $e');
      // 返回空配置
      _config = {};
      return _config!;
    }
  }
  
  /// 获取字符串配置项
  Future<String?> getString(String key) async {
    final config = await loadConfig();
    return config[key] as String?;
  }
  
  /// 获取整数配置项
  Future<int?> getInt(String key) async {
    final config = await loadConfig();
    return config[key] as int?;
  }
  
  /// 获取布尔配置项
  Future<bool?> getBool(String key) async {
    final config = await loadConfig();
    return config[key] as bool?;
  }
  
  /// 获取微信AppID
  Future<String> getWechatAppId() async {
    final appId = await getString('wechat_app_id');
    return appId ?? 'wxb0872bb3945f31bc'; // 默认值
  }
}
