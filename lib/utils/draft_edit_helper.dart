import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../api/api_provider.dart';
import '../routes.dart';
import '../widgets/custom_toast.dart';

/// 草稿编辑辅助类
/// 提供便捷的方法来编辑草稿
class DraftEditHelper {
  static final DraftEditHelper _instance = DraftEditHelper._internal();
  factory DraftEditHelper() => _instance;
  DraftEditHelper._internal();

  final ApiProvider _apiProvider = ApiProvider();

  /// 跳转到编辑草稿页面
  ///
  /// [context] 上下文
  /// [noteId] 笔记ID
  ///
  /// 返回是否成功跳转
  Future<bool> openDraftEdit(BuildContext context, String noteId) async {
    try {
      if (noteId.isEmpty) {
        debugPrint('笔记ID为空，无法编辑草稿');
        CustomToast.show('笔记ID为空');
        return false;
      }

      debugPrint('准备编辑草稿: noteId=$noteId');

      // 确保API客户端已初始化
      await _apiProvider.initializeApiClient();

      // 获取草稿详情
      debugPrint('开始调用草稿详情API');
      final draftDetail = await _apiProvider.draftApi.getDraftDetailByNoteId(noteId);
      debugPrint('草稿详情API调用完成，结果: ${draftDetail != null ? '有草稿内容' : '无草稿内容'}');

      if (draftDetail != null) {
        // 有草稿内容，跳转到编辑页面
        debugPrint('找到草稿内容，长度: ${draftDetail.content.length}，跳转到编辑页面');
        Navigator.pushNamed(
          context,
          AppRoutes.noteEdit,
          arguments: {
            'noteId': noteId,
            'isDraft': true,
            'draftContent': draftDetail.content,
            'draftTitle': draftDetail.noteTitle,
            'draftDesc': draftDetail.noteDesc,
            'draftCover': draftDetail.noteCover,
          },
        );
        return true;
      } else {
        // 没有草稿内容，提示用户
        debugPrint('该笔记没有草稿内容');
        CustomToast.show('该笔记暂无草稿内容');
        return false;
      }
    } catch (e) {
      debugPrint('编辑草稿失败: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      CustomToast.show('获取草稿内容失败: ${e.toString()}');
      return false;
    }
  }

  /// 获取草稿内容（不跳转页面）
  ///
  /// [noteId] 笔记ID
  ///
  /// 返回草稿内容，如果没有草稿返回null
  Future<String?> getDraftContent(String noteId) async {
    try {
      if (noteId.isEmpty) {
        debugPrint('笔记ID为空，无法获取草稿内容');
        return null;
      }

      debugPrint('准备获取草稿内容: noteId=$noteId');

      // 确保API客户端已初始化
      await _apiProvider.initializeApiClient();

      // 获取草稿详情
      final draftDetail = await _apiProvider.draftApi.getDraftDetailByNoteId(noteId);

      if (draftDetail != null) {
        debugPrint('成功获取草稿内容，长度: ${draftDetail.content.length}');
        return draftDetail.content;
      } else {
        debugPrint('该笔记没有草稿内容');
        return null;
      }
    } catch (e) {
      debugPrint('获取草稿内容失败: $e');
      return null;
    }
  }
}
