/// 版本工具类
/// 提供版本号比较和处理相关的工具方法
class VersionUtils {
  /// 比较两个版本号
  /// 
  /// 返回值：
  /// - -1: version1 < version2
  /// - 0: version1 == version2  
  /// - 1: version1 > version2
  /// 
  /// 支持的版本格式：
  /// - x.y.z (如: 1.2.3)
  /// - x.y.z+build (如: 1.2.3+1)
  static int compareVersions(String version1, String version2) {
    // 移除构建号部分（+后面的内容）
    final cleanVersion1 = _cleanVersion(version1);
    final cleanVersion2 = _cleanVersion(version2);
    
    // 分割版本号
    final v1Parts = _parseVersionParts(cleanVersion1);
    final v2Parts = _parseVersionParts(cleanVersion2);
    
    // 比较每个部分
    final maxLength = v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;
    
    for (int i = 0; i < maxLength; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;
      
      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }
    
    return 0;
  }
  
  /// 检查是否有新版本
  /// 
  /// [currentVersion] 当前版本
  /// [latestVersion] 最新版本
  /// 
  /// 返回true表示有新版本可用
  static bool hasNewVersion(String currentVersion, String latestVersion) {
    return compareVersions(currentVersion, latestVersion) < 0;
  }
  
  /// 验证版本号格式是否正确
  /// 
  /// 支持的格式：
  /// - x.y.z
  /// - x.y.z+build
  static bool isValidVersion(String version) {
    if (version.isEmpty) return false;
    
    // 移除构建号部分
    final cleanVersion = _cleanVersion(version);
    
    // 检查格式：数字.数字.数字
    final regex = RegExp(r'^\d+\.\d+\.\d+$');
    return regex.hasMatch(cleanVersion);
  }
  
  /// 格式化版本号显示
  /// 
  /// 移除构建号部分，只显示主版本号
  static String formatVersionForDisplay(String version) {
    return _cleanVersion(version);
  }
  
  /// 获取版本号的主要部分（移除构建号）
  static String getMainVersion(String version) {
    return _cleanVersion(version);
  }
  
  /// 解析版本号的各个部分
  /// 
  /// 返回 [major, minor, patch] 数组
  static List<int> getVersionParts(String version) {
    final cleanVersion = _cleanVersion(version);
    return _parseVersionParts(cleanVersion);
  }
  
  /// 获取主版本号
  static int getMajorVersion(String version) {
    final parts = getVersionParts(version);
    return parts.isNotEmpty ? parts[0] : 0;
  }
  
  /// 获取次版本号
  static int getMinorVersion(String version) {
    final parts = getVersionParts(version);
    return parts.length > 1 ? parts[1] : 0;
  }
  
  /// 获取修订版本号
  static int getPatchVersion(String version) {
    final parts = getVersionParts(version);
    return parts.length > 2 ? parts[2] : 0;
  }
  
  /// 清理版本号，移除构建号部分
  static String _cleanVersion(String version) {
    if (version.contains('+')) {
      return version.split('+')[0];
    }
    return version;
  }
  
  /// 解析版本号各部分为整数数组
  static List<int> _parseVersionParts(String version) {
    try {
      return version.split('.').map((part) => int.parse(part.trim())).toList();
    } catch (e) {
      // 如果解析失败，返回默认版本 [0, 0, 0]
      return [0, 0, 0];
    }
  }
  
  /// 生成版本更新描述
  /// 
  /// 根据版本差异生成更新类型描述
  static String getUpdateTypeDescription(String currentVersion, String latestVersion) {
    if (!hasNewVersion(currentVersion, latestVersion)) {
      return '当前已是最新版本';
    }
    
    final currentParts = getVersionParts(currentVersion);
    final latestParts = getVersionParts(latestVersion);
    
    // 主版本号变化
    if (currentParts[0] < latestParts[0]) {
      return '重大版本更新';
    }
    
    // 次版本号变化
    if (currentParts[1] < latestParts[1]) {
      return '功能版本更新';
    }
    
    // 修订版本号变化
    if (currentParts[2] < latestParts[2]) {
      return '修复版本更新';
    }
    
    return '版本更新';
  }
}
