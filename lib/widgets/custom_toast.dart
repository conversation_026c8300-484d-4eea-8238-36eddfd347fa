import '../native_bridge/native_bridge.dart';

/// 自定义Toast工具类
///
/// 封装原生端的CustomToast功能，提供简洁的调用接口
class CustomToast {
  /// 显示短时间Toast
  ///
  /// [message] 要显示的消息
  static Future<void> show(String message) async {
    try {
      await NativeBridge().showCustomToast(message, isLong: false);
    } catch (e) {
      // 如果自定义Toast失败，回退到系统Toast
      await NativeBridge().showToast(message);
    }
  }

  /// 显示长时间Toast
  ///
  /// [message] 要显示的消息
  static Future<void> showLong(String message) async {
    try {
      await NativeBridge().showCustomToast(message, isLong: true);
    } catch (e) {
      // 如果自定义Toast失败，回退到系统Toast
      await NativeBridge().showToast(message, duration: NativeBridge.TOAST_LENGTH_LONG);
    }
  }

  /// 显示成功消息Toast
  ///
  /// [message] 要显示的成功消息
  static Future<void> showSuccess(String message) async {
    await show(message);
  }

  /// 显示错误消息Toast
  ///
  /// [message] 要显示的错误消息
  static Future<void> showError(String message) async {
    await showLong(message); // 错误消息显示时间稍长
  }
}
