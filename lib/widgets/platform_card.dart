import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../models/platform_model.dart';

/// 平台卡片组件
class PlatformCard extends StatefulWidget {
  final PlatformModel platform;

  const PlatformCard({
    Key? key,
    required this.platform,
  }) : super(key: key);

  @override
  State<PlatformCard> createState() => _PlatformCardState();
}

class _PlatformCardState extends State<PlatformCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 平台信息头部
          _buildPlatformHeader(),

          // 展开的场景列表
          if (_isExpanded) _buildScenesList(),
        ],
      ),
    );
  }

  /// 构建平台头部信息
  Widget _buildPlatformHeader() {
    return InkWell(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      borderRadius: BorderRadius.circular(12.r),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Row(
          children: [
            // 平台图标
            Image.asset(
              widget.platform.logoAsset,
              width: 40.r,
              height: 40.r,
            ),
            SizedBox(width: 16.w),

            // 平台信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.platform.name,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    widget.platform.description,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // 展开/收起图标
            Icon(
              _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              color: AppColors.textSecondary,
              size: 24.r,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建场景列表
  Widget _buildScenesList() {
    return Container(
      padding: EdgeInsets.only(left: 16.r, right: 16.r, bottom: 16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(height: 1, color: AppColors.divider),
          SizedBox(height: 16.h),

          // 场景标题
          Row(
            children: [
              Icon(
                Icons.category_outlined,
                size: 16.r,
                color: AppColors.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                '支持的场景',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),

          // 场景列表
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: widget.platform.supportedScenes.length,
            separatorBuilder: (context, index) => SizedBox(height: 12.h),
            itemBuilder: (context, index) {
              final scene = widget.platform.supportedScenes[index];
              return _buildSceneItem(scene);
            },
          ),
        ],
      ),
    );
  }

  /// 构建场景项
  Widget _buildSceneItem(SceneModel scene) {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: AppColors.buttonBackground,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Icon(
            scene.icon,
            size: 20.r,
            color: widget.platform.color,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  scene.name,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  scene.description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
