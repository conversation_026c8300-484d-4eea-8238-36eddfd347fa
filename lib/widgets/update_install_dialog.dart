import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../services/version_service.dart';
import 'custom_toast.dart';

/// 更新安装提示弹窗
///
/// 用于提示用户安装已下载的更新包
class UpdateInstallDialog extends StatefulWidget {
  final Map<String, String> updateInfo;

  const UpdateInstallDialog({
    super.key,
    required this.updateInfo,
  });

  @override
  State<UpdateInstallDialog> createState() => _UpdateInstallDialogState();

  /// 显示更新安装弹窗
  static Future<void> show(
    BuildContext context,
    Map<String, String> updateInfo,
  ) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // 不允许点击外部关闭
      builder: (BuildContext context) {
        return UpdateInstallDialog(updateInfo: updateInfo);
      },
    );
  }
}

class _UpdateInstallDialogState extends State<UpdateInstallDialog> {
  final _versionService = VersionService();
  bool _isInstalling = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      contentPadding: EdgeInsets.zero,
      content: Container(
        width: 300.w,
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 应用图标
            Container(
              width: 64.w,
              height: 64.w,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Icon(
                Icons.system_update,
                size: 32.w,
                color: AppColors.primary,
              ),
            ),

            SizedBox(height: 16.h),

            // 标题
            Text(
              '新版本下载完毕，安装即可启用',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
                height: 1.3,
              ),
            ),

            SizedBox(height: 8.h),

            // 版本号
            Text(
              'v${widget.updateInfo['version']}',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),

            SizedBox(height: 16.h),

            // 更新内容
            if (widget.updateInfo['updateContent']?.isNotEmpty == true) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '更新内容',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      widget.updateInfo['updateContent']!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.black54,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20.h),
            ],

            // 按钮区域
            Row(
              children: [
                // 稍后安装按钮
                Expanded(
                  child: TextButton(
                    onPressed: _isInstalling ? null : _handleLaterInstall,
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                        side: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    child: Text(
                      '稍后安装',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 12.w),

                // 立即安装按钮
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isInstalling ? null : _installUpdate,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      elevation: 0,
                    ),
                    child: _isInstalling
                        ? SizedBox(
                            width: 16.w,
                            height: 16.w,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            '立即安装',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 处理稍后安装
  void _handleLaterInstall() {
    // 显示toast提示
    CustomToast.show('点击头像-> 版本更新中可手动更新');
    // 关闭弹窗
    Navigator.of(context).pop();
  }

  /// 安装更新
  Future<void> _installUpdate() async {
    setState(() {
      _isInstalling = true;
    });

    try {
      final result = await _versionService.installPendingUpdate();

      if (result['success'] == true) {
        CustomToast.show('正在安装更新...');
        // 安装成功，关闭弹窗
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        if (result['needPermission'] == true) {
          // 需要权限，直接跳转到设置页面
          await _versionService.requestInstallPermission();
        } else {
          // 其他安装失败情况
          String errorMessage = result['message'] ?? '安装失败';
          CustomToast.show(errorMessage);
        }
      }
    } catch (e) {
      CustomToast.show('安装失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isInstalling = false;
        });
      }
    }
  }


}
