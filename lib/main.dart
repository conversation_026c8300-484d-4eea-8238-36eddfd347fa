import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/services.dart';
import 'constants/app_colors.dart';
import 'routes.dart';
import 'api/api_provider.dart';
import 'native_bridge/config_bridge.dart';
import 'native_bridge/native_bridge.dart';
import 'native_bridge/task_bridge.dart';
import 'services/version_service.dart';

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 从原生获取配置
  await ConfigBridge.init();
  print('应用配置已加载, API地址: ${ConfigBridge.getString("api_base_url")}');

  // 初始化API客户端，确保token被正确加载
  final apiProvider = ApiProvider();
  await apiProvider.initializeApiClient();
  print('应用启动时已完成API客户端初始化');

  // 初始化任务桥接
  await TaskBridge.init();
  print('任务桥接初始化完成');

  // APP启动时检查版本更新
  final versionService = VersionService();
  await versionService.checkVersionOnAppStart();

  // 检查悬浮窗权限并显示侧边栏
  await _checkAndShowSidebar();

  runApp(const App());
}

/// 检查悬浮窗权限并显示侧边栏
Future<void> _checkAndShowSidebar() async {
  try {
    final nativeBridge = NativeBridge();

    // 检查是否有悬浮窗权限
    final hasOverlayPermission = await nativeBridge.checkOverlayPermission();
    print('悬浮窗权限检查结果: $hasOverlayPermission');

    if (hasOverlayPermission) {
      // 有权限，显示侧边栏
      final success = await nativeBridge.showSidebar();
      print('侧边栏显示结果: $success');
    } else {
      print('没有悬浮窗权限，无法显示侧边栏');
    }
  } catch (e) {
    print('检查权限并显示侧边栏失败: $e');
  }
}

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // 设计稿尺寸（iPhone X）
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.primary,
              primary: AppColors.primary,
              secondary: AppColors.primaryLight,
              background: AppColors.background,
              surface: AppColors.surface,
              error: AppColors.error,
            ),
            useMaterial3: true,
            fontFamily: 'sans-serif',
            scaffoldBackgroundColor: AppColors.background,
            appBarTheme: AppBarTheme(
              backgroundColor: AppColors.surface,
              foregroundColor: AppColors.textPrimary,
              elevation: 0,
              systemOverlayStyle: SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: AppColors.background,
                systemNavigationBarIconBrightness: Brightness.dark,
              ),
            ),
            cardTheme: CardTheme(
              color: AppColors.surface,
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            inputDecorationTheme: InputDecorationTheme(
              filled: true,
              fillColor: AppColors.searchBarBackground,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide.none,
              ),
              contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              hintStyle: TextStyle(color: AppColors.textHint),
            ),
            textTheme: TextTheme(
              titleLarge: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              titleMedium: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              bodyLarge: TextStyle(
                fontSize: 16,
                color: AppColors.textPrimary,
              ),
              bodyMedium: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          initialRoute: AppRoutes.mainTab, // 初始路由
          routes: AppRoutes.getRoutes(), // 注册路由表
          onGenerateRoute: AppRoutes.generateRoute, // 自定义路由生成
          onUnknownRoute: AppRoutes.onUnknownRoute, // 处理未知路由
        );
      },
    );
  }
}
