import 'package:flutter/foundation.dart';
import 'api_client.dart';
import '../models/note_item.dart';

/// 笔记详情数据模型
class NoteDetail {
  final String id;
  final String parentId;
  final String userId;
  final String title;
  final String cover;
  final String desc;
  final String content;
  final String html;
  final String createTime;
  final String updateTime;

  NoteDetail({
    required this.id,
    required this.parentId,
    required this.userId,
    required this.title,
    required this.cover,
    required this.desc,
    required this.content,
    required this.html,
    required this.createTime,
    required this.updateTime,
  });

  factory NoteDetail.fromJson(Map<String, dynamic> json) {
    return NoteDetail(
      id: json['id'] ?? '',
      parentId: json['parent_id'] ?? '',
      userId: json['user_id'] ?? '',
      title: json['title'] ?? '',
      cover: json['cover'] ?? '',
      desc: json['desc'] ?? '',
      content: json['content'] ?? '',
      html: json['html'] ?? '',
      createTime: json['create_time'] ?? '',
      updateTime: json['update_time'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parent_id': parentId,
      'user_id': userId,
      'title': title,
      'cover': cover,
      'desc': desc,
      'content': content,
      'html': html,
      'create_time': createTime,
      'update_time': updateTime,
    };
  }
}

/// 笔记API服务类
class NoteApi {
  final ApiClient _apiClient;

  NoteApi(this._apiClient);

  /// 获取笔记详情
  ///
  /// [id] 笔记ID
  ///
  /// 返回笔记详情数据
  Future<NoteDetail> getNoteDetail(String id) async {
    try {
      debugPrint('开始获取笔记详情: id=$id');

      if (id.isEmpty) {
        throw Exception('笔记ID不能为空');
      }

      final response = await _apiClient.get('/note/detail?id=$id');

      debugPrint('笔记详情响应: $response');

      // 解析响应数据
      final data = response['data'] as Map<String, dynamic>? ?? {};

      if (data.isEmpty) {
        throw Exception('笔记详情数据为空');
      }

      final noteDetail = NoteDetail.fromJson(data);

      debugPrint('成功解析笔记详情: ${noteDetail.title}');

      return noteDetail;
    } catch (e, stackTrace) {
      debugPrint('获取笔记详情失败: $e');
      debugPrint('堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 获取笔记HTML内容（仅返回HTML字段）
  ///
  /// [id] 笔记ID
  ///
  /// 返回HTML内容字符串
  Future<String> getNoteHtml(String id) async {
    try {
      final noteDetail = await getNoteDetail(id);
      return noteDetail.html;
    } catch (e) {
      debugPrint('获取笔记HTML内容失败: $e');
      rethrow;
    }
  }

  /// 获取笔记列表
  ///
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为10
  ///
  /// 返回笔记列表响应数据
  Future<NoteListResponse> getNoteList({
    int page = 1,
    int pageSize = 10,
  }) async {
    try {
      debugPrint('开始获取笔记列表: page=$page, pageSize=$pageSize');

      // 参数验证
      if (page < 1) {
        throw Exception('页码必须大于0');
      }

      if (pageSize < 1 || pageSize > 100) {
        throw Exception('每页数量必须在1-100之间');
      }

      // 构建查询参数
      final queryParams = {
        'page': page.toString(),
        'page_size': pageSize.toString(),
      };

      // 构建带查询参数的URL
      final uri = Uri(
        path: '/note/list',
        queryParameters: queryParams,
      );

      final response = await _apiClient.get(uri.toString());

      debugPrint('笔记列表响应: $response');

      // 解析响应数据
      final data = response['data'] as Map<String, dynamic>? ?? {};

      if (data.isEmpty) {
        throw Exception('笔记列表数据为空');
      }

      final noteListResponse = NoteListResponse.fromJson(data);

      debugPrint('成功解析笔记列表: 共${noteListResponse.total}条记录，当前页${noteListResponse.notes.length}条');

      return noteListResponse;
    } catch (e, stackTrace) {
      debugPrint('获取笔记列表失败: $e');
      debugPrint('堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 更新笔记
  ///
  /// [id] 笔记ID，必填
  /// [title] 笔记标题，可选
  /// [html] HTML格式内容，可选
  ///
  /// 返回更新结果
  Future<Map<String, dynamic>> updateNote({
    required String id,
    String? title,
    String? html,
  }) async {
    try {
      debugPrint('开始更新笔记: id=$id');

      if (id.isEmpty) {
        throw Exception('笔记ID不能为空');
      }

      // 构建请求数据
      final requestData = <String, dynamic>{
        'id': id,
      };

      if (title != null && title.isNotEmpty) {
        requestData['title'] = title;
      }

      if (html != null && html.isNotEmpty) {
        requestData['html'] = html;
      }

      debugPrint('更新笔记请求数据: $requestData');

      final response = await _apiClient.post('/note/update', requestData);

      debugPrint('更新笔记响应: $response');

      return response;
    } catch (e, stackTrace) {
      debugPrint('更新笔记失败: $e');
      debugPrint('堆栈: $stackTrace');
      rethrow;
    }
  }
}
