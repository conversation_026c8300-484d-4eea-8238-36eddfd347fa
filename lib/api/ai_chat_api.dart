import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:eventflux/eventflux.dart';
import 'api_client.dart';

/// JSON流拼接器
class GenericJsonStreamAssembler {
  final Map<String, StringBuffer> _buffers = {};

  void handleChunk(String jsonStr) {
    try {
      final Map<String, dynamic> currentJson = jsonDecode(jsonStr);
      _processJson(currentJson, []);
    } catch (e) {
      print('JSON解析失败: $e');
    }
  }

  /// 获取拼接后的字段值，使用完整路径，例如：`choices.0.delta.content`
  String getValue(String keyPath) => _buffers[keyPath]?.toString() ?? '';

  void reset() {
    _buffers.clear();
  }

  /// 打印最终拼接结果（已禁用）
  void printFinalResult() {
    // 不再打印拼接结果
  }

  /// 递归处理JSON数据，简单拼接非null字段
  void _processJson(dynamic data, List<String> path) {
    // 如果是 Map，递归处理每个 key
    if (data is Map<String, dynamic>) {
      for (final key in data.keys) {
        final value = data[key];
        if (value != null) {
          _processJson(value, [...path, key]);
        }
      }
    }
    // 如果是 List，递归处理每个 index
    else if (data is List) {
      for (int i = 0; i < data.length; i++) {
        final value = data[i];
        if (value != null) {
          _processJson(value, [...path, '$i']);
        }
      }
    }
    // 如果是字符串或其他基础类型，智能拼接（避免重复）
    else {
      final joinedPath = path.join('.');
      final stringValue = data.toString();

      if (!_buffers.containsKey(joinedPath)) {
        _buffers[joinedPath] = StringBuffer(stringValue);
      } else {
        // 检查当前要拼接的字符串是否与已存在的内容重复
        final existingContent = _buffers[joinedPath]!.toString();

        // 如果新内容不是已存在内容的重复，才进行拼接
        if (!existingContent.endsWith(stringValue)) {
          _buffers[joinedPath]!.write(stringValue);
        }
      }
    }
  }

  /// 可用于查看所有字段及其拼接结果（调试用）
  Map<String, String> get allValues =>
      _buffers.map((k, v) => MapEntry(k, v.toString()));

  /// 重构完整的JSON对象
  Map<String, dynamic> get completeJson {
    final result = <String, dynamic>{};

    for (final entry in _buffers.entries) {
      if (entry.value.toString().isNotEmpty) {
        _setNestedValue(result, entry.key.split('.'), entry.value.toString());
      }
    }

    return result;
  }

  /// 根据路径设置嵌套值
  void _setNestedValue(Map<String, dynamic> obj, List<String> path, String value) {
    if (path.isEmpty) return;

    if (path.length == 1) {
      obj[path[0]] = value;
      return;
    }

    final key = path[0];
    final remainingPath = path.sublist(1);

    // 检查下一个路径是否是数字（数组索引）
    final nextKey = remainingPath[0];
    final isNextArray = int.tryParse(nextKey) != null;

    if (isNextArray) {
      // 当前key应该是数组
      obj[key] ??= <dynamic>[];
      final list = obj[key] as List<dynamic>;
      final index = int.parse(nextKey);

      // 确保数组有足够的元素
      while (list.length <= index) {
        list.add(<String, dynamic>{});
      }

      if (remainingPath.length == 1) {
        list[index] = value;
      } else {
        if (list[index] is! Map<String, dynamic>) {
          list[index] = <String, dynamic>{};
        }
        _setNestedValue(list[index] as Map<String, dynamic>, remainingPath.sublist(1), value);
      }
    } else {
      // 当前key是对象
      obj[key] ??= <String, dynamic>{};
      _setNestedValue(obj[key] as Map<String, dynamic>, remainingPath, value);
    }
  }
}

/// AI聊天API服务
class AiChatApi {
  final ApiClient _apiClient;

  /// 构造函数
  AiChatApi(this._apiClient);

  /// 发送聊天消息并接收流式响应
  ///
  /// [message] 用户消息内容
  /// [conversationHistory] 对话历史，可选
  /// [systemPrompt] 系统提示词，可选，默认为中文助手提示词
  /// [tools] 可用工具列表，可选
  Stream<Map<String, dynamic>> sendMessage({
    required String message,
    List<Map<String, dynamic>>? conversationHistory,
    String? systemPrompt,
    List<Map<String, dynamic>>? tools,
  }) async* {
    final completer = Completer<void>();
    final streamController = StreamController<Map<String, dynamic>>();

    // 用于拼接完整的OpenAI message对象
    Map<String, dynamic>? completeMessage;

    // 用于智能拼接JSON流数据（仅用于控制台打印）
    final assembler = GenericJsonStreamAssembler();

    try {
      // 构建请求URL - 使用OpenAI API
      final url = 'https://api.gptsapi.net/v1/chat/completions';

      // 准备请求头 - 使用OpenAI API Key
      final headers = <String, String>{
        'Authorization': 'Bearer sk-gY862e5b9a04ee26941ac843faead2bfbcc3e10c9eelGS92',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
      };

      // 准备消息列表
      final messages = <Map<String, dynamic>>[
        {'role': 'system', 'content': systemPrompt ?? '你是一个有用的AI助手，请用中文回答问题。'},
      ];

      // 添加对话历史
      if (conversationHistory != null) {
        messages.addAll(conversationHistory);
      }

      // 只有当消息不为空时才添加用户消息
      // 这样可以支持基于现有对话历史继续对话的场景
      if (message.trim().isNotEmpty) {
        messages.add({'role': 'user', 'content': message});
      }

      // 准备请求体 - 使用OpenAI格式
      final requestBody = <String, dynamic>{
        'model': 'gpt-4.1-mini',
        'messages': messages,
        'web_search_options': {'search_context_size': 'medium'},
        'stream': true
      };

      // 添加工具列表（如果提供）- 使用OpenAI格式
      if (tools != null && tools.isNotEmpty) {
        requestBody['tools'] = tools;
        requestBody['tool_choice'] = 'auto';
      }

      // 创建EventFlux实例
      final eventFlux = EventFlux.spawn();

      print('=== OpenAI聊天请求开始 ===');
      print('请求URL: $url');
      print('请求头: $headers');
      print('请求体: $requestBody');
      print('========================');

      // 连接到SSE流
      eventFlux.connect(
        EventFluxConnectionType.post,
        url,
        header: headers,
        body: requestBody,
        onSuccessCallback: (EventFluxResponse? response) {
          print('SSE连接成功');
          response?.stream?.listen(
            (data) {
              try {
                // 检查是否是结束标记
                if (data.data?.trim() == '[DONE]') {
                  // 发送完整的OpenAI message对象
                  if (completeMessage != null && !streamController.isClosed) {
                    streamController.add({
                      'complete_message': completeMessage,
                      'isComplete': true,
                    });
                  }

                  if (!streamController.isClosed) {
                    streamController.close();
                  }
                  if (!completer.isCompleted) {
                    completer.complete();
                  }
                  return;
                }

                // 解析JSON数据
                final dataContent = data.data;
                if (dataContent != null && dataContent.isNotEmpty) {
                  // 通过拼接器处理数据（仅用于控制台打印）
                  assembler.handleChunk(dataContent);

                  final jsonData = jsonDecode(dataContent);

                  // 检查是否有错误
                  if (jsonData['error'] != null) {
                    print('OpenAI API错误: ${jsonData['error']}');
                    if (!streamController.isClosed) {
                      streamController.addError(Exception('AI聊天错误: ${jsonData['error']}'));
                    }
                    return;
                  }

                  // 提取消息内容 - OpenAI格式
                  final choices = jsonData['choices'] as List?;
                  if (choices != null && choices.isNotEmpty) {
                    final choice = choices[0] as Map<String, dynamic>;
                    final delta = choice['delta'] as Map<String, dynamic>?;
                    final finishReason = choice['finish_reason'] as String?;

                    if (delta != null) {
                      final content = delta['content'] as String?;
                      final reasoningContent = delta['reasoning_content'] as String?;
                      final toolCalls = delta['tool_calls'] as List?;

                      // 初始化完整消息对象（如果还没有）
                      if (completeMessage == null) {
                        completeMessage = {
                          'role': 'assistant',
                          'content': '',
                        };
                      }

                      // 累积content
                      if (content != null && content.isNotEmpty) {
                        completeMessage!['content'] = (completeMessage!['content'] as String) + content;
                      }

                      // 累积reasoning_content
                      if (reasoningContent != null && reasoningContent.isNotEmpty) {
                        if (!completeMessage!.containsKey('reasoning_content')) {
                          completeMessage!['reasoning_content'] = '';
                        }
                        completeMessage!['reasoning_content'] = (completeMessage!['reasoning_content'] as String) + reasoningContent;
                      }

                      // 处理tool_calls（累积完整的工具调用）
                      if (toolCalls != null && toolCalls.isNotEmpty) {
                        if (!completeMessage!.containsKey('tool_calls')) {
                          completeMessage!['tool_calls'] = <Map<String, dynamic>>[];
                        }

                        final existingToolCalls = completeMessage!['tool_calls'] as List<Map<String, dynamic>>;

                        for (final toolCall in toolCalls) {
                          final toolCallMap = toolCall as Map<String, dynamic>;
                          final index = toolCallMap['index'] as int?;

                          if (index != null) {
                            // 确保有足够的空间
                            while (existingToolCalls.length <= index) {
                              existingToolCalls.add({});
                            }

                            // 初始化或更新工具调用
                            if (existingToolCalls[index].isEmpty) {
                              existingToolCalls[index] = {
                                'id': toolCallMap['id'],
                                'type': toolCallMap['type'],
                                'function': {
                                  'name': toolCallMap['function']?['name'],
                                  'arguments': '',
                                },
                              };
                            }

                            // 累积arguments
                            final arguments = toolCallMap['function']?['arguments'] as String?;
                            if (arguments != null) {
                              existingToolCalls[index]['function']['arguments'] =
                                  (existingToolCalls[index]['function']['arguments'] as String) + arguments;
                            }
                          }
                        }
                      }

                      // 发送流式内容（用于UI实时更新）
                      // 注意：不在这里发送tool_calls，只发送content和reasoning_content
                      if ((content != null && content.isNotEmpty) ||
                          (reasoningContent != null && reasoningContent.isNotEmpty)) {
                        if (!streamController.isClosed) {
                          streamController.add({
                            'content': content,
                            'reasoning_content': reasoningContent,
                            'isComplete': false,
                          });
                        }
                      }
                    }
                  }
                }

              } catch (e) {
                print('解析SSE数据错误: $e');
                // 继续处理下一个事件，不中断整个流
              }
            },
            onError: (error) {
              print('SSE流错误: $error');
              if (!streamController.isClosed) {
                streamController.addError(Exception('SSE流错误: $error'));
              }
              if (!completer.isCompleted) {
                completer.complete();
              }
            },
            onDone: () {
              print('SSE流完成');
              if (!streamController.isClosed) {
                streamController.close();
              }
              if (!completer.isCompleted) {
                completer.complete();
              }
            },
          );
        },
        onError: (EventFluxException? error) {
          print('SSE连接错误: $error');
          if (!streamController.isClosed) {
            streamController.addError(Exception('SSE连接错误: $error'));
          }
          if (!completer.isCompleted) {
            completer.complete();
          }
        },
        autoReconnect: false, // 不自动重连，聊天消息是一次性的
      );

      // 监听流数据并yield
      await for (final chunk in streamController.stream) {
        yield chunk;
      }

      // 等待连接完成
      if (!completer.isCompleted) {
        await completer.future;
      }

      // 断开连接
      await eventFlux.disconnect();

    } catch (e) {
      print('AI聊天请求失败: $e');
      throw Exception('AI聊天请求失败: $e');
    }
  }

  /// 发送简单的聊天消息（非流式）
  /// 用于兼容性或测试目的
  /// [systemPrompt] 系统提示词，可选
  Future<String> sendSimpleMessage(String message, {String? systemPrompt}) async {
    try {
      // 使用流式接口，但收集完整响应
      String fullResponse = '';
      await for (final chunk in sendMessage(
        message: message,
        systemPrompt: systemPrompt,
      )) {
        final content = chunk['content'] as String?;
        if (content != null && content.isNotEmpty) {
          fullResponse += content;
        }

        // 如果是完整响应，退出循环
        if (chunk['isComplete'] == true) {
          break;
        }
      }

      return fullResponse.isNotEmpty ? fullResponse : '抱歉，我没有理解您的问题。';
    } catch (e) {
      print('简单聊天请求失败: $e');
      throw Exception('AI聊天请求失败: $e');
    }
  }
}
