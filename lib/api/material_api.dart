import 'dart:developer' as developer;
import '../models/material.dart';
import 'api_client.dart';

/// 素材API接口
class MaterialApi {
  final ApiClient _apiClient;

  MaterialApi(this._apiClient);

  /// 获取素材列表
  ///
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为20，最大为100
  /// [type] 素材类型过滤（可选），1-图片，2-语音，3-视频
  Future<MaterialListResponse> getMaterialList({
    int page = 1,
    int pageSize = 20,
    int? type,
  }) async {
    try {
      developer.log('获取素材列表: page=$page, pageSize=$pageSize, type=$type');

      String endpoint = '/material/list?page=$page&page_size=$pageSize';
      if (type != null) {
        endpoint += '&type=$type';
      }

      final response = await _apiClient.get(endpoint);

      if (response['code'] == 0) {
        final data = response['data'] as Map<String, dynamic>;
        return MaterialListResponse.fromJson(data);
      } else {
        throw Exception(response['message'] ?? '获取素材列表失败');
      }
    } catch (e) {
      developer.log('获取素材列表失败: $e');
      rethrow;
    }
  }

  /// 添加素材
  ///
  /// [url] 素材的OSS地址
  /// [type] 素材类型，1-图片，2-语音，3-视频
  Future<Material> addMaterial({
    required String url,
    required int type,
  }) async {
    try {
      developer.log('添加素材: url=$url, type=$type');

      final response = await _apiClient.post(
        '/material/add',
        {
          'url': url,
          'type': type,
        },
      );

      if (response['code'] == 0) {
        final materialData = response['data']['material'] as Map<String, dynamic>;
        return Material.fromJson(materialData);
      } else {
        throw Exception(response['message'] ?? '添加素材失败');
      }
    } catch (e) {
      developer.log('添加素材失败: $e');
      rethrow;
    }
  }

  /// 删除素材
  ///
  /// [id] 素材ID
  Future<void> deleteMaterial(String id) async {
    try {
      developer.log('删除素材: id=$id');

      final response = await _apiClient.post(
        '/material/delete',
        {
          'id': id,
        },
      );

      if (response['code'] != 0) {
        throw Exception(response['message'] ?? '删除素材失败');
      }

      developer.log('删除素材成功: id=$id');
    } catch (e) {
      developer.log('删除素材失败: $e');
      rethrow;
    }
  }

  /// 根据类型获取素材列表
  ///
  /// [type] 素材类型，1-图片，2-语音，3-视频
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为20
  Future<MaterialListResponse> getMaterialsByType({
    required int type,
    int page = 1,
    int pageSize = 20,
  }) async {
    return getMaterialList(
      page: page,
      pageSize: pageSize,
      type: type,
    );
  }

  /// 获取图片素材列表
  ///
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为20
  Future<MaterialListResponse> getImageMaterials({
    int page = 1,
    int pageSize = 20,
  }) async {
    return getMaterialsByType(
      type: 1,
      page: page,
      pageSize: pageSize,
    );
  }

  /// 获取语音素材列表
  ///
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为20
  Future<MaterialListResponse> getAudioMaterials({
    int page = 1,
    int pageSize = 20,
  }) async {
    return getMaterialsByType(
      type: 2,
      page: page,
      pageSize: pageSize,
    );
  }

  /// 获取视频素材列表
  ///
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为20
  Future<MaterialListResponse> getVideoMaterials({
    int page = 1,
    int pageSize = 20,
  }) async {
    return getMaterialsByType(
      type: 3,
      page: page,
      pageSize: pageSize,
    );
  }
}
