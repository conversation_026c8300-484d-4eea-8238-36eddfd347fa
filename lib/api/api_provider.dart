import 'package:aishoucang/api/api_client.dart';
import 'package:aishoucang/api/auth_api.dart';
import 'package:aishoucang/api/favorites_api.dart';
import 'package:aishoucang/api/bookmark_api.dart';
import 'package:aishoucang/api/wechat_api.dart';
import 'package:aishoucang/api/version_api.dart';
import 'package:aishoucang/api/tag_api.dart';
import 'package:aishoucang/api/ai_chat_api.dart';
import 'package:aishoucang/api/note_api.dart';
import 'package:aishoucang/api/material_api.dart';
import 'package:aishoucang/api/task_api.dart';
import 'package:aishoucang/api/draft_api.dart';
import 'package:aishoucang/native_bridge/config_bridge.dart';
import 'package:aishoucang/services/storage_service.dart';

/// API服务提供者
///
/// 集中管理所有API接口，提供统一的访问点
class ApiProvider {
  static final ApiProvider _instance = ApiProvider._internal();

  /// 单例模式
  factory ApiProvider() => _instance;

  late final ApiClient _apiClient;
  late final AuthApi authApi;
  late final FavoritesApi favoritesApi;
  late final BookmarkApi bookmarkApi;
  late final WechatApi wechatApi;
  late final VersionApi versionApi;
  late final TagApi tagApi;
  late final AiChatApi aiChatApi;
  late final NoteApi noteApi;
  late final MaterialApi materialApi;
  late final TaskApi taskApi;
  late final DraftApi draftApi;
  final _storageService = StorageService();
  bool _isInitialized = false;

  /// 私有构造函数
  ApiProvider._internal() {
    // 构造函数中不进行初始化，等待 initializeApiClient 调用
  }

  /// 初始化API服务
  void _initializeApis() {
    if (_isInitialized) return; // 避免重复初始化

    // 创建API客户端，直接使用ConfigBridge获取基础URL
    _apiClient = ApiClient(baseUrl: ConfigBridge.getString("api_base_url", "http://*************:8080"));

    // 初始化各个API服务
    authApi = AuthApi(_apiClient);
    favoritesApi = FavoritesApi(_apiClient);
    bookmarkApi = BookmarkApi(_apiClient);
    wechatApi = WechatApi(_apiClient);
    versionApi = VersionApi(_apiClient);
    tagApi = TagApi(_apiClient);
    aiChatApi = AiChatApi(_apiClient);
    noteApi = NoteApi(_apiClient);
    materialApi = MaterialApi(_apiClient);
    taskApi = TaskApi(_apiClient);
    draftApi = DraftApi(_apiClient);

    _isInitialized = true;
  }

  /// 从本地存储加载token
  Future<void> _loadTokenFromStorage() async {
    final token = await _storageService.getToken();
    if (token != null && token.isNotEmpty) {
      _apiClient.setToken(token);
      print('成功从存储中加载 token: ${token.substring(0, 10)}...');
    } else {
      print('未找到存储的 token');
    }
  }

  /// 初始化API客户端，确保在使用前加载 token
  Future<void> initializeApiClient() async {
    // 初始化API服务，确保ConfigBridge已经初始化
    _initializeApis();
    await _loadTokenFromStorage();
    print('API客户端初始化完成');
  }

  /// 清除认证信息（用于退出登录）
  Future<void> clearAuthentication() async {
    // 清除API客户端中的token
    _apiClient.setToken(null);

    // 清除本地存储中的token
    await _storageService.clearToken();

    print('认证信息已清除');
  }

  /// 检查用户是否已登录
  Future<bool> isLoggedIn() async {
    final token = await _storageService.getToken();
    return token != null && token.isNotEmpty;
  }

  /// 获取API客户端实例
  ApiClient get apiClient => _apiClient;
}
