import 'package:aishoucang/api/api_client.dart';

/// 微信相关的API接口
class WechatApi {
  final ApiClient _apiClient;

  /// 构造函数
  /// [apiClient] API客户端实例
  WechatApi(this._apiClient);

  /// 微信登录
  ///
  /// [code] 微信授权码
  ///
  /// 返回登录结果，包含用户ID、手机号和认证令牌
  Future<WechatLoginResponse> login(String code) async {
    final response = await _apiClient.post('/wechat/login', {
      'code': code,
    });

    // 检查响应状态
    if (response['code'] == 0) {
      final loginResponse = WechatLoginResponse.fromJson(response['data']);

      // 设置认证令牌到API客户端
      _apiClient.setToken(loginResponse.token);

      return loginResponse;
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }
}

/// 微信登录响应
class WechatLoginResponse {
  final String userId;
  final String token;
  final String? nickname;
  final String? avatarUrl;
  final String? openid;
  final String? unionid;

  WechatLoginResponse({
    required this.userId,
    required this.token,
    this.nickname,
    this.avatarUrl,
    this.openid,
    this.unionid,
  });

  factory WechatLoginResponse.fromJson(Map<String, dynamic> json) {
    return WechatLoginResponse(
      userId: json['user_id'] ?? '',
      token: json['token'] ?? '',
      nickname: json['nickname'],
      avatarUrl: json['avatar'],
      openid: json['openid'],
      unionid: json['unionid'],
    );
  }
}

/// API异常
class ApiException implements Exception {
  final int code;
  final String message;
  final dynamic data;

  ApiException({
    required this.code,
    required this.message,
    this.data,
  });

  @override
  String toString() {
    return 'ApiException: $message (code: $code)';
  }
}
