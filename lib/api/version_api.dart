import 'package:aishoucang/api/api_client.dart';
import '../models/version_info.dart';

/// 版本检查相关的API接口
class VersionApi {
  final ApiClient _apiClient;

  /// 构造函数
  /// [apiClient] API客户端实例
  VersionApi(this._apiClient);

  /// 检查版本更新
  ///
  /// 调用服务端的 /app/version/check 接口
  /// 返回版本检查结果
  Future<VersionInfo> checkVersion() async {
    try {
      print('开始调用版本检查接口: /app/version/check');

      final response = await _apiClient.get('/app/version/check');

      print('版本检查接口响应: $response');

      // 检查响应状态
      if (response['code'] == 0) {
        final data = response['data'] as Map<String, dynamic>? ?? {};
        print('版本检查数据: $data');

        return VersionInfo.fromJson(data);
      } else {
        print('版本检查接口返回错误: code=${response['code']}, message=${response['message']}');
        throw ApiException(
          code: response['code'],
          message: response['message'] ?? '版本检查失败',
          data: response['data'],
        );
      }
    } catch (e) {
      print('版本检查接口调用失败: $e');
      rethrow;
    }
  }
}


