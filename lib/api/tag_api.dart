import 'package:aishoucang/api/api_client.dart';
import 'package:aishoucang/models/tag.dart';

/// 标签相关的API接口
class TagApi {
  final ApiClient _apiClient;

  /// 构造函数
  /// [apiClient] API客户端实例
  TagApi(this._apiClient);

  /// 获取标签列表
  ///
  /// 返回用户的所有标签列表
  Future<TagListResponse> getTagList() async {
    final response = await _apiClient.get('/tags/list');

    // 检查响应状态
    if (response['code'] == 0) {
      return TagListResponse.fromJson(response['data']);
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }

  /// 创建标签
  ///
  /// [name] 标签名称，必填，不能为空，最大长度50个字符
  /// [backgroundColor] 标签背景颜色，可选，十六进制颜色值，默认为 #1890ff
  /// [textColor] 标签文字颜色，可选，十六进制颜色值，默认为 #ffffff
  ///
  /// 返回创建的标签信息
  Future<CreateTagResponse> createTag(
    String name, {
    String? backgroundColor,
    String? textColor,
  }) async {
    final requestData = <String, dynamic>{
      'name': name,
    };

    // 如果提供了背景颜色，则添加到请求数据中
    if (backgroundColor != null && backgroundColor.isNotEmpty) {
      requestData['background_color'] = backgroundColor;
    }

    // 如果提供了文字颜色，则添加到请求数据中
    if (textColor != null && textColor.isNotEmpty) {
      requestData['text_color'] = textColor;
    }

    final response = await _apiClient.post('/tags/create', requestData);

    // 检查响应状态
    if (response['code'] == 0) {
      return CreateTagResponse.fromJson(response['data']);
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }

  /// 删除标签
  ///
  /// [tagId] 标签ID，必填，字符串类型
  ///
  /// 返回删除结果
  Future<bool> deleteTag(String tagId) async {
    final requestData = <String, dynamic>{
      'id': tagId,
    };

    final response = await _apiClient.post('/tags/delete', requestData);

    // 检查响应状态
    if (response['code'] == 0) {
      return true;
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }
}
