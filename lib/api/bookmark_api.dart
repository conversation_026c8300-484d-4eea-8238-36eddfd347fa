import 'package:flutter/foundation.dart';
import 'dart:math';
import 'api_client.dart';

/// 书签模型类
class BookmarkItem {
  /// 书签ID
  final String id;

  /// 博主名称
  final String influencerName;

  /// 博主头像
  final String? influencerAvatar;

  /// 封面
  final String? cover;

  /// 标题
  final String? title;

  /// 简介
  final String? desc;

  /// 原生跳转链接
  final String? schemeUrl;

  /// 收藏夹ID
  final String parentId;

  /// 创建时间
  final DateTime createTime;

  /// 最后更新时间
  final DateTime updateTime;

  /// 标签列表
  final List<String> tags;

  /// 构造函数
  BookmarkItem({
    required this.id,
    required this.influencerName,
    this.influencerAvatar,
    this.cover,
    this.title,
    this.desc,
    this.schemeUrl,
    required this.parentId,
    required this.createTime,
    required this.updateTime,
    this.tags = const [],
  });

  /// 从JSON创建BookmarkItem实例
  factory BookmarkItem.fromJson(Map<String, dynamic> json) {
    try {
      // 解析标签列表
      List<String> tagsList = [];
      if (json['tags'] != null) {
        debugPrint('原始标签数据: ${json['tags']}');
        if (json['tags'] is List) {
          tagsList = (json['tags'] as List).map((tag) {
            debugPrint('处理标签项: $tag (类型: ${tag.runtimeType})');
            if (tag is String) {
              // 如果标签是字符串，直接返回
              debugPrint('标签是字符串: $tag');
              return tag;
            } else if (tag is Map<String, dynamic>) {
              // 如果标签是对象，提取name字段
              final tagName = tag['name']?.toString() ?? '';
              debugPrint('标签是对象，提取name: $tagName');
              return tagName;
            } else {
              // 其他情况，转换为字符串
              final tagStr = tag.toString();
              debugPrint('标签转换为字符串: $tagStr');
              return tagStr;
            }
          }).where((tag) => tag.isNotEmpty).toList();
        }
        debugPrint('最终解析的标签列表: $tagsList');
      }

      return BookmarkItem(
        id: json['id'] as String,
        influencerName: json['influencer_name'] as String,
        influencerAvatar: json['influencer_avatar'] as String?,
        cover: json['cover'] as String?,
        title: json['title'] as String?,
        desc: json['desc'] as String?,
        schemeUrl: json['scheme_url'] as String?,
        parentId: json['parent_id'] as String,
        createTime: DateTime.parse(json['create_time'] as String),
        updateTime: DateTime.parse(json['update_time'] as String),
        tags: tagsList,
      );
    } catch (e) {
      debugPrint('解析BookmarkItem时出错: $e, JSON: $json');
      // 返回一个带有基本信息的对象，防止整个列表因为一个项目解析错误而失败
      return BookmarkItem(
        id: json['id']?.toString() ?? 'unknown',
        influencerName: json['influencer_name']?.toString() ?? '未知用户',
        parentId: json['parent_id']?.toString() ?? '',
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
        tags: [],
      );
    }
  }

  /// 将BookmarkItem转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'influencer_name': influencerName,
      'influencer_avatar': influencerAvatar,
      'cover': cover,
      'title': title,
      'desc': desc,
      'scheme_url': schemeUrl,
      'parent_id': parentId,
      'create_time': createTime.toIso8601String(),
      'update_time': updateTime.toIso8601String(),
      'tags': tags,
    };
  }
}

/// 书签API接口
class BookmarkApi {
  final ApiClient _apiClient;

  /// 构造函数
  BookmarkApi(this._apiClient);

  /// 获取特定收藏夹中的书签列表
  ///
  /// [favoriteId] 收藏夹ID（可选）
  /// [tagNames] 标签名称列表（可选）
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为20
  /// [platformType] 平台类型过滤（可选）
  ///
  /// 返回书签列表和分页信息
  Future<Map<String, dynamic>> getBookmarkList({
    String? favoriteId,
    List<String>? tagNames,
    int page = 1,
    int pageSize = 20,
    String? platformType,
  }) async {
    try {
      // 构建请求体参数
      Map<String, dynamic> requestBody = {
        'page': page,
        'page_size': pageSize,
      };

      if (favoriteId != null && favoriteId.isNotEmpty) {
        requestBody['parent_id'] = favoriteId;
      }

      if (tagNames != null && tagNames.isNotEmpty) {
        requestBody['tag_names'] = tagNames;
      }

      if (platformType != null && platformType.isNotEmpty) {
        requestBody['platform_type'] = platformType;
      }

      debugPrint('开始请求书签列表: favoriteId=$favoriteId, tagNames=$tagNames, platformType=$platformType, page=$page, pageSize=$pageSize');
      debugPrint('请求体: $requestBody');

      final response = await _apiClient.post('/bookmark/list', requestBody);

      debugPrint('书签列表响应: ${response.toString().substring(0, min(200, response.toString().length))}...');

      // 解析结果
      final data = response['data'] as Map<String, dynamic>? ?? {};

      debugPrint('数据结构键名: ${data.keys.join(', ')}');

      // 转换书签列表
      List<BookmarkItem> bookmarkItems = [];

      // 检查不同可能的键名
      final possibleListKeys = ['bookmarks', 'list', 'items'];
      String? usedKey;

      for (final key in possibleListKeys) {
        if (data.containsKey(key) && data[key] is List) {
          usedKey = key;
          final rawList = data[key] as List;
          debugPrint('找到列表键: $key, 包含 ${rawList.length} 个条目');

          try {
            bookmarkItems = rawList
                .map((item) {
                  if (item is Map<String, dynamic>) {
                    return BookmarkItem.fromJson(item);
                  }
                  debugPrint('列表项不是Map类型: $item');
                  return null;
                })
                .where((item) => item != null)
                .cast<BookmarkItem>()
                .toList();
          } catch (e) {
            debugPrint('转换列表时出错: $e');
          }
          break;
        }
      }

      if (usedKey == null) {
        debugPrint('未找到任何书签列表数据，完整响应: $data');
      }

      // 更新data中的列表为转换后的对象
      data['items'] = bookmarkItems;

      if (bookmarkItems.isNotEmpty) {
        debugPrint('成功解析 ${bookmarkItems.length} 个书签项');
        debugPrint('第一个项目: ${bookmarkItems.first.title ?? '无标题'} - ${bookmarkItems.first.influencerName}');
      } else {
        debugPrint('没有书签数据');
      }

      return data;
    } catch (e, stackTrace) {
      debugPrint('获取书签列表失败: $e');
      debugPrint('堆栈: $stackTrace');
      // 返回一个空数据结构，让UI层可以正常处理
      return {
        'items': <BookmarkItem>[],
        'total': 0,
        'error': e.toString()
      };
    }
  }

  /// 删除书签
  ///
  /// [bookmarkId] 书签ID
  ///
  /// 返回删除结果
  Future<Map<String, dynamic>> deleteBookmark(String bookmarkId) async {
    final data = <String, dynamic>{
      'id': bookmarkId,
    };

    final response = await _apiClient.post(
      '/bookmark/delete',
      data,
    );

    return response['data'];
  }



  /// 更新书签
  ///
  /// [id] 书签ID
  /// [title] 新标题
  /// [desc] 新描述
  ///
  /// 返回更新结果
  Future<Map<String, dynamic>> updateBookmark({
    required String id,
    String? title,
    String? desc,
  }) async {
    final data = <String, dynamic>{
      'id': id,
    };

    if (title != null) data['title'] = title;
    if (desc != null) data['desc'] = desc;

    final response = await _apiClient.post(
      '/bookmark/update',
      data,
    );

    return response['data'];
  }

  /// 给书签添加标签
  ///
  /// [bookmarkId] 书签ID
  /// [tagNames] 标签名称列表
  ///
  /// 返回添加结果
  Future<Map<String, dynamic>> addTagsToBookmark({
    required String bookmarkId,
    required List<String> tagNames,
  }) async {
    debugPrint('开始添加标签到书签: bookmarkId=$bookmarkId, tagNames=$tagNames');

    final data = <String, dynamic>{
      'bookmark_id': bookmarkId,
      'tag_names': tagNames,
    };

    debugPrint('请求数据: $data');

    try {
      final response = await _apiClient.post(
        '/bookmark/tags/add',
        data,
      );

      debugPrint('添加标签API响应: $response');
      return response['data'];
    } catch (e, stackTrace) {
      debugPrint('添加标签API调用失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 从书签移除标签
  ///
  /// [bookmarkId] 书签ID
  /// [tagIds] 要移除的标签ID列表
  ///
  /// 返回移除结果
  Future<Map<String, dynamic>> removeTagsFromBookmark({
    required String bookmarkId,
    required List<String> tagIds,
  }) async {
    debugPrint('开始从书签移除标签: bookmarkId=$bookmarkId, tagIds=$tagIds');

    final data = <String, dynamic>{
      'bookmark_id': bookmarkId,
      'tag_ids': tagIds,
    };

    debugPrint('请求数据: $data');

    try {
      final response = await _apiClient.post(
        '/bookmark/tags/remove',
        data,
      );

      debugPrint('移除标签API响应: $response');
      return response['data'];
    } catch (e, stackTrace) {
      debugPrint('移除标签API调用失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 搜索书签
  ///
  /// [keyword] 搜索关键词
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为10
  ///
  /// 返回搜索结果和分页信息
  Future<Map<String, dynamic>> searchBookmarks({
    required String keyword,
    int page = 1,
    int pageSize = 10,
  }) async {
    try {
      debugPrint('开始搜索书签: keyword=$keyword, page=$page, pageSize=$pageSize');

      // 构建查询参数
      final queryParams = <String, String>{
        'keyword': keyword,
        'page': page.toString(),
        'page_size': pageSize.toString(),
      };

      // 构建带查询参数的URL
      final uri = Uri.parse('/bookmark/search').replace(queryParameters: queryParams);
      final endpoint = uri.toString();

      final response = await _apiClient.get(endpoint);

      debugPrint('搜索结果响应: ${response.toString().substring(0, min(200, response.toString().length))}...');

      // 解析结果
      final data = response['data'] as Map<String, dynamic>? ?? {};

      debugPrint('数据结构键名: ${data.keys.join(', ')}');

      // 转换书签列表
      List<BookmarkItem> bookmarkItems = [];

      // 检查不同可能的键名
      final possibleListKeys = ['bookmarks', 'list', 'items'];
      String? usedKey;

      for (final key in possibleListKeys) {
        if (data.containsKey(key) && data[key] is List) {
          usedKey = key;
          final rawList = data[key] as List;
          debugPrint('找到列表键: $key, 包含 ${rawList.length} 个条目');

          try {
            bookmarkItems = rawList
                .map((item) {
                  if (item is Map<String, dynamic>) {
                    return BookmarkItem.fromJson(item);
                  }
                  debugPrint('列表项不是Map类型: $item');
                  return null;
                })
                .where((item) => item != null)
                .cast<BookmarkItem>()
                .toList();
          } catch (e) {
            debugPrint('转换列表时出错: $e');
          }
          break;
        }
      }

      if (usedKey == null) {
        debugPrint('未找到任何书签列表数据，完整响应: $data');
      }

      // 更新data中的列表为转换后的对象
      data['items'] = bookmarkItems;

      if (bookmarkItems.isNotEmpty) {
        debugPrint('成功解析 ${bookmarkItems.length} 个搜索结果');
        debugPrint('第一个结果: ${bookmarkItems.first.title ?? '无标题'} - ${bookmarkItems.first.influencerName}');
      } else {
        debugPrint('没有搜索结果');
      }

      return data;
    } catch (e, stackTrace) {
      debugPrint('搜索书签失败: $e');
      debugPrint('堆栈: $stackTrace');
      // 返回一个空数据结构，让UI层可以正常处理
      return {
        'items': <BookmarkItem>[],
        'total': 0,
        'page': page,
        'page_size': pageSize,
        'error': e.toString()
      };
    }
  }
}