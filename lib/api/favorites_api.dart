import 'api_client.dart';

/// 收藏夹API接口
class FavoritesApi {
  final ApiClient _apiClient;

  /// 构造函数
  FavoritesApi(this._apiClient);

  /// 创建收藏夹
  ///
  /// [name] 收藏夹名称
  /// [cover] 收藏夹封面图片URL（可选）
  ///
  /// 返回创建成功的收藏夹信息
  Future<Map<String, dynamic>> createFavorite({
    required String name,
    String? cover,
  }) async {
    final response = await _apiClient.post(
      '/favorites/create',
      {
        'name': name,
        if (cover != null && cover.isNotEmpty) 'cover': cover,
      },
    );

    return response['data'];
  }

  /// 获取用户收藏夹列表
  ///
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为15
  ///
  /// 返回收藏夹列表和分页信息
  Future<Map<String, dynamic>> getUserFavorites({
    int page = 1,
    int pageSize = 15,
  }) async {
    final response = await _apiClient.get('/favorites/list?page=$page&page_size=$pageSize');
    print('获取用户收藏夹列表接口返回: $response');
    return response['data'] as Map<String, dynamic>;
  }

  /// 删除收藏夹
  ///
  /// [favoriteId] 收藏夹ID
  /// 返回删除结果，包含删除的收藏夹ID和删除的书签数量
  Future<Map<String, dynamic>> deleteFavorite(String favoriteId) async {
    final data = <String, dynamic>{
      'id': favoriteId,
    };

    final response = await _apiClient.post(
      '/favorites/delete',
      data,
    );

    return response['data'];
  }

  /// 更新收藏夹
  ///
  /// [favoriteId] 收藏夹ID
  /// [name] 新的收藏夹名称（可选）
  /// [cover] 新的收藏夹封面图片URL（可选）
  Future<Map<String, dynamic>> updateFavorite({
    required String favoriteId,
    String? name,
    String? cover,
  }) async {
    final data = <String, dynamic>{
      'id': favoriteId,
    };

    if (name != null && name.isNotEmpty) data['name'] = name;
    if (cover != null && cover.isNotEmpty) data['cover'] = cover;

    final response = await _apiClient.post(
      '/favorites/update',
      data,
    );

    return response['data'];
  }

  /// 计算收藏夹排序值
  ///
  /// [prevId] 前一个收藏夹ID（可选）
  /// [nextId] 后一个收藏夹ID（可选）
  Future<String> calculateOrder({
    String? prevId,
    String? nextId,
  }) async {
    final data = <String, dynamic>{};

    if (prevId != null) data['prev_id'] = prevId;
    if (nextId != null) data['next_id'] = nextId;

    final response = await _apiClient.post(
      '/favorites/calculate_order',
      data,
    );

    return response['data']['order'] as String;
  }

  /// 更新收藏夹排序
  ///
  /// [favoriteId] 收藏夹ID
  /// [order] 新的排序值（字符串类型）
  Future<Map<String, dynamic>> updateFavoriteOrder({
    required String favoriteId,
    required String order,
  }) async {
    final data = <String, dynamic>{
      'id': favoriteId,
      'order': order,
    };

    final response = await _apiClient.post(
      '/favorites/update_order',
      data,
    );

    return response['data'];
  }

  /// 交换两个收藏夹的位置
  ///
  /// [sourceId] 源收藏夹ID
  /// [targetId] 目标收藏夹ID
  Future<Map<String, dynamic>> swapFavorites({
    required String sourceId,
    required String targetId,
  }) async {
    final data = <String, dynamic>{
      'source_id': sourceId,
      'target_id': targetId,
    };

    final response = await _apiClient.post(
      '/favorites/swap',
      data,
    );

    return response['data'];
  }
}
