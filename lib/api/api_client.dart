import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../native_bridge/native_bridge.dart';

/// API客户端基类，封装基本的HTTP请求方法
class ApiClient {
  final String baseUrl;
  final Map<String, String> _headers = {
    'Content-Type': 'application/json',
  };

  /// 构造函数
  /// [baseUrl] API服务器的基础URL
  ApiClient({required this.baseUrl});

  /// 设置认证令牌
  /// 如果 token 为 null，则清除认证头
  void setToken(String? token) {
    if (token != null && token.isNotEmpty) {
      _headers['Authorization'] = 'Bearer $token';
      debugPrint('设置认证令牌: Bearer ${token.substring(0, min(10, token.length))}...');
    } else {
      clearToken();
    }
  }

  /// 清除认证令牌
  void clearToken() {
    _headers.remove('Authorization');
  }

  /// 获取认证头
  String? getAuthHeader() {
    return _headers['Authorization'];
  }

  /// 发送GET请求
  Future<Map<String, dynamic>> get(String endpoint) async {
    final url = Uri.parse('$baseUrl$endpoint');
    debugPrint('GET 请求: $url');
    debugPrint('请求头: $_headers');

    final stopwatch = Stopwatch()..start();
    final response = await http.get(
      url,
      headers: _headers,
    );
    stopwatch.stop();

    debugPrint('响应状态码: ${response.statusCode}, 耗时: ${stopwatch.elapsedMilliseconds}ms');
    debugPrint('响应头: ${response.headers}');
    debugPrint('响应体: ${response.body}');

    return _handleResponse(response);
  }

  /// 发送POST请求
  Future<Map<String, dynamic>> post(String endpoint, Map<String, dynamic> data) async {
    final url = Uri.parse('$baseUrl$endpoint');
    final body = jsonEncode(data);

    debugPrint('POST 请求: $url');
    debugPrint('请求头: $_headers');
    debugPrint('请求体: $body');

    final stopwatch = Stopwatch()..start();
    http.Response? response;
    try {
      response = await http.post(
        url,
        headers: _headers,
        body: body,
      );
      stopwatch.stop();

      debugPrint('响应状态码: ${response.statusCode}, 耗时: ${stopwatch.elapsedMilliseconds}ms');
      debugPrint('响应头: ${response.headers}');
      debugPrint('响应体: ${response.body}');

      return _handleResponse(response);
    } catch (e) {
      stopwatch.stop();
      debugPrint('请求异常: $e, 耗时: ${stopwatch.elapsedMilliseconds}ms');
      if (response != null) {
        debugPrint('响应状态码: ${response.statusCode}');
        debugPrint('响应头: ${response.headers}');
        debugPrint('响应体: ${response.body}');
      }
      rethrow;
    }
  }

  /// 发送PUT请求
  Future<Map<String, dynamic>> put(String endpoint, Map<String, dynamic> data) async {
    final url = Uri.parse('$baseUrl$endpoint');
    final body = jsonEncode(data);

    debugPrint('PUT 请求: $url');
    debugPrint('请求头: $_headers');
    debugPrint('请求体: $body');

    final stopwatch = Stopwatch()..start();
    final response = await http.put(
      url,
      headers: _headers,
      body: body,
    );
    stopwatch.stop();

    debugPrint('响应状态码: ${response.statusCode}, 耗时: ${stopwatch.elapsedMilliseconds}ms');
    debugPrint('响应头: ${response.headers}');

    return _handleResponse(response);
  }

  /// 发送DELETE请求
  Future<Map<String, dynamic>> delete(String endpoint) async {
    final url = Uri.parse('$baseUrl$endpoint');

    debugPrint('DELETE 请求: $url');
    debugPrint('请求头: $_headers');

    final stopwatch = Stopwatch()..start();
    final response = await http.delete(
      url,
      headers: _headers,
    );
    stopwatch.stop();

    debugPrint('响应状态码: ${response.statusCode}, 耗时: ${stopwatch.elapsedMilliseconds}ms');
    debugPrint('响应头: ${response.headers}');

    return _handleResponse(response);
  }

  /// 处理HTTP响应
  Map<String, dynamic> _handleResponse(http.Response response) {
    try {
      // 使用UTF-8解码响应
      final decodedBody = utf8.decode(response.bodyBytes);
      debugPrint('解码后的响应体: $decodedBody');

      final Map<String, dynamic> responseData = jsonDecode(decodedBody);

      // 检查API自定义状态码
      if (responseData.containsKey('code')) {
        final apiCode = responseData['code'];
        if (apiCode == 0) { // API成功状态码
          debugPrint('请求成功: API状态码=$apiCode, HTTP状态码=${response.statusCode}');
          return responseData;
        } else {
          final message = responseData['message'] ?? 'API error';

          // 显示Toast提示
          _showErrorToast(message);

          final error = ApiException(
            code: apiCode,
            message: message,
            data: responseData['data'],
          );
          debugPrint('请求失败: $error');
          throw error;
        }
      }

      // 如果没有API状态码，则检查HTTP状态码
      if (response.statusCode >= 200 && response.statusCode < 300) {
        debugPrint('请求成功: HTTP状态码=${response.statusCode}');
        return responseData;
      } else {
        final message = responseData['message'] ?? 'HTTP error';

        // 显示Toast提示
        _showErrorToast(message);

        final error = ApiException(
          code: response.statusCode,
          message: message,
          data: responseData['data'],
        );
        debugPrint('请求失败: $error');
        throw error;
      }
    } catch (e) {
      if (e is ApiException) {
        // 已经是ApiException，直接抛出
        rethrow;
      } else if (e is FormatException) {
        // JSON解析错误
        debugPrint('响应解析错误: $e');
        debugPrint('原始响应体: ${response.body}');

        final message = 'Invalid response format: ${e.message}';
        _showErrorToast(message);

        throw ApiException(
          code: response.statusCode,
          message: message,
          data: response.body,
        );
      } else {
        // 其他错误
        debugPrint('响应处理错误: $e');

        final message = 'Error processing response: $e';
        _showErrorToast(message);

        throw ApiException(
          code: response.statusCode,
          message: message,
          data: response.body,
        );
      }
    }
  }
}

/// API异常类
class ApiException implements Exception {
  final int code;
  final String message;
  final dynamic data;

  ApiException({
    required this.code,
    required this.message,
    this.data,
  });

  @override
  String toString() => 'ApiException: [$code] $message';
}

/// 显示错误 Toast 提示
void _showErrorToast(String message) {
  try {
    // 使用 NativeBridge 显示 Toast
    NativeBridge().showToast(message, duration: NativeBridge.TOAST_LENGTH_LONG);
  } catch (e) {
    debugPrint('显示 Toast 错误: $e');
  }
}
