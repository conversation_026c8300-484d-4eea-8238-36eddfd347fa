import 'package:aishoucang/api/api_client.dart';

/// 用户认证相关的API接口
class AuthApi {
  final ApiClient _apiClient;
  
  /// 构造函数
  /// [apiClient] API客户端实例
  AuthApi(this._apiClient);
  
  /// 用户登录
  /// 
  /// [phone] 用户手机号
  /// [password] 用户密码
  /// 
  /// 返回登录结果，包含用户ID、手机号和认证令牌
  Future<LoginResponse> login(String phone, String password) async {
    final response = await _apiClient.post('/users/login', {
      'phone': phone,
      'password': password,
    });
    
    // 检查响应状态
    if (response['code'] == 0) {
      final loginResponse = LoginResponse.fromJson(response['data']);
      
      // 设置认证令牌到API客户端
      _apiClient.setToken(loginResponse.token);
      
      return loginResponse;
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }
  
  /// 用户注册
  /// 
  /// [phone] 用户手机号
  /// [password] 用户密码
  /// 
  /// 返回注册结果，包含用户ID
  Future<RegistResponse> register(String phone, String password) async {
    final response = await _apiClient.post('/users/regist', {
      'phone': phone,
      'password': password,
    });
    
    // 检查响应状态
    if (response['code'] == 0) {
      return RegistResponse.fromJson(response['data']);
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }
  
  /// 退出登录
  void logout() {
    _apiClient.clearToken();
  }
}

/// 登录响应数据模型
class LoginResponse {
  final String userId;
  final String phone;
  final String token;
  
  LoginResponse({
    required this.userId,
    required this.phone,
    required this.token,
  });
  
  /// 从JSON创建LoginResponse实例
  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      userId: json['user_id'],
      phone: json['phone'],
      token: json['token'],
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'phone': phone,
      'token': token,
    };
  }
}

/// 注册响应数据模型
class RegistResponse {
  final String userId;
  
  RegistResponse({
    required this.userId,
  });
  
  /// 从JSON创建RegistResponse实例
  factory RegistResponse.fromJson(Map<String, dynamic> json) {
    return RegistResponse(
      userId: json['user_id'],
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
    };
  }
}
