import 'package:flutter/material.dart';
import '../native_bridge/spark_chain_bridge.dart';

/// SparkChain 大模型识别使用示例
/// 
/// 展示如何使用 SparkChain SDK 进行语音识别
class SparkChainExample extends StatefulWidget {
  const SparkChainExample({Key? key}) : super(key: key);

  @override
  State<SparkChainExample> createState() => _SparkChainExampleState();
}

class _SparkChainExampleState extends State<SparkChainExample> {
  bool _isInitialized = false;
  bool _isRecognizing = false;
  String _recognitionResult = '';
  String _statusMessage = '未初始化';

  @override
  void initState() {
    super.initState();
    _initializeSparkChain();
  }

  /// 初始化 SparkChain SDK
  Future<void> _initializeSparkChain() async {
    setState(() {
      _statusMessage = '正在初始化...';
    });

    try {
      // 使用配置文件中的参数初始化
      final result = await SparkChainBridge.initSparkChainFromConfig(
        logLevel: 2, // INFO级别日志
        uid: 'flutter_user_001',
      );

      setState(() {
        _isInitialized = result['success'] ?? false;
        _statusMessage = result['message'] ?? '初始化状态未知';
      });

      if (_isInitialized) {
        print('SparkChain SDK 初始化成功');
      } else {
        print('SparkChain SDK 初始化失败: ${result['message']}');
      }
    } catch (e) {
      setState(() {
        _isInitialized = false;
        _statusMessage = '初始化异常: $e';
      });
      print('SparkChain SDK 初始化异常: $e');
    }
  }

  /// 开始语音识别
  Future<void> _startRecognition() async {
    if (!_isInitialized) {
      _showMessage('请先初始化SDK');
      return;
    }

    if (_isRecognizing) {
      _showMessage('识别正在进行中');
      return;
    }

    setState(() {
      _isRecognizing = true;
      _recognitionResult = '';
      _statusMessage = '正在启动识别...';
    });

    try {
      // 启动识别会话
      await SparkChainBridge.startRecognition(
        sessionId: 'demo_session_${DateTime.now().millisecondsSinceEpoch}',
        language: SparkChainLanguage.chinese,
        domain: SparkChainDomain.largeModel,
        accent: SparkChainAccent.mandarin,
        sampleRate: 16000,
        encoding: SparkChainEncoding.raw,
        // 启用高级功能
        ptt: true,     // 标点预测
        nunum: true,   // 数字规整
        vinfo: true,   // VAD信息
        vadEos: 60000,  // 尾静音截断时间
      );

      setState(() {
        _statusMessage = '识别已启动，请开始说话...';
      });

      // 模拟发送音频数据
      _simulateAudioData();

    } catch (e) {
      setState(() {
        _isRecognizing = false;
        _statusMessage = '启动识别失败: $e';
      });
      print('启动识别失败: $e');
    }
  }

  /// 模拟发送音频数据
  /// 在实际应用中，这里应该是从麦克风获取的真实音频数据
  Future<void> _simulateAudioData() async {
    // 模拟发送几帧音频数据
    for (int i = 0; i < 10; i++) {
      if (!_isRecognizing) break;

      // 创建模拟音频数据 (实际应用中应该是真实的PCM音频数据)
      final audioData = List.generate(1280, (index) => (index % 256));
      
      final result = await SparkChainBridge.writeAudioData(
        Uint8List.fromList(audioData)
      );

      if (!result['success']) {
        print('发送音频数据失败: ${result['message']}');
        break;
      }

      // 等待40ms (模拟实时音频流)
      await Future.delayed(const Duration(milliseconds: 40));
    }

    // 模拟完成后停止识别
    await Future.delayed(const Duration(seconds: 1));
    _stopRecognition();
  }

  /// 停止语音识别
  Future<void> _stopRecognition() async {
    if (!_isRecognizing) return;

    try {
      final result = await SparkChainBridge.stopRecognition(immediate: false);
      
      setState(() {
        _isRecognizing = false;
        _statusMessage = result['success'] 
            ? '识别已停止' 
            : '停止识别失败: ${result['message']}';
      });

      if (result['success']) {
        print('识别停止成功');
      }
    } catch (e) {
      setState(() {
        _isRecognizing = false;
        _statusMessage = '停止识别异常: $e';
      });
      print('停止识别异常: $e');
    }
  }

  /// 检查SDK状态
  Future<void> _checkStatus() async {
    try {
      final isInitialized = await SparkChainBridge.isSparkChainInitialized();
      final isRecognizing = await SparkChainBridge.isRecognitionActive();

      setState(() {
        _isInitialized = isInitialized;
        _statusMessage = 'SDK状态: ${isInitialized ? "已初始化" : "未初始化"}, '
                        '识别状态: ${isRecognizing ? "进行中" : "空闲"}';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '检查状态失败: $e';
      });
    }
  }

  /// 清理资源
  Future<void> _cleanup() async {
    try {
      final result = await SparkChainBridge.uninitSparkChain();
      
      setState(() {
        _isInitialized = false;
        _isRecognizing = false;
        _statusMessage = result['success'] 
            ? 'SDK已清理' 
            : '清理失败: ${result['message']}';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '清理异常: $e';
      });
    }
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SparkChain 语音识别示例'),
        backgroundColor: const Color(0xFF1EB9EF),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 状态显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'SDK状态',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('初始化: ${_isInitialized ? "✅" : "❌"}'),
                    Text('识别中: ${_isRecognizing ? "🎤" : "⏸️"}'),
                    const SizedBox(height: 8),
                    Text(
                      _statusMessage,
                      style: TextStyle(
                        color: _isInitialized ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 识别结果显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '识别结果',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      height: 100,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: SingleChildScrollView(
                        child: Text(
                          _recognitionResult.isEmpty 
                              ? '暂无识别结果' 
                              : _recognitionResult,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // 操作按钮
            ElevatedButton(
              onPressed: _isInitialized ? null : _initializeSparkChain,
              child: const Text('初始化SDK'),
            ),

            const SizedBox(height: 8),

            ElevatedButton(
              onPressed: (_isInitialized && !_isRecognizing) ? _startRecognition : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1EB9EF),
              ),
              child: const Text('开始识别'),
            ),

            const SizedBox(height: 8),

            ElevatedButton(
              onPressed: _isRecognizing ? _stopRecognition : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
              ),
              child: const Text('停止识别'),
            ),

            const SizedBox(height: 8),

            ElevatedButton(
              onPressed: _checkStatus,
              child: const Text('检查状态'),
            ),

            const SizedBox(height: 8),

            ElevatedButton(
              onPressed: _isInitialized ? _cleanup : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('清理资源'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // 页面销毁时清理资源
    if (_isInitialized) {
      _cleanup();
    }
    super.dispose();
  }
}
