import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../api/bookmark_api.dart';
import '../constants/app_colors.dart';
import '../utils/platform_utils.dart';
import '../routes.dart';
import '../native_bridge/native_bridge.dart';
import '../models/tag.dart';
import '../services/tag_service.dart';
import 'bookmark_context_menu.dart';
import 'bookmark_edit_dialog.dart';
import 'bookmark_tag_manager_dialog.dart';
import '../widgets/custom_toast.dart';
import '../api/api_provider.dart';

/// 收藏项大图网格展示组件
///
/// 参考小红书风格的大图展示模式
class BookmarkGridView extends StatefulWidget {
  final List<BookmarkItem> bookmarks;
  final ScrollController? scrollController;
  final bool hasMore;
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final Function(BookmarkItem, int)? onDelete;
  final Function(BookmarkItem, int)? onMove;
  final Function(BookmarkItem)? onEdit;
  final Function(BookmarkItem)? onTagManage;
  final bool isHomePage; // 新增参数，用于区分是否为首页

  const BookmarkGridView({
    super.key,
    required this.bookmarks,
    this.scrollController,
    this.hasMore = false,
    this.isLoading = false,
    this.onLoadMore,
    this.onDelete,
    this.onMove,
    this.onEdit,
    this.onTagManage,
    this.isHomePage = false, // 默认为false（详情页）
  });

  @override
  State<BookmarkGridView> createState() => _BookmarkGridViewState();
}

class _BookmarkGridViewState extends State<BookmarkGridView> {
  final TagService _tagService = TagService();
  Map<String, Tag> _tagMap = {}; // 标签名称到Tag对象的映射

  @override
  void initState() {
    super.initState();
    _loadTagData();
  }

  /// 加载标签数据
  Future<void> _loadTagData() async {
    try {
      final tagObjects = await _tagService.getAllTagObjects();
      if (mounted) {
        setState(() {
          _tagMap = {for (var tag in tagObjects) tag.name: tag};
        });
      }
    } catch (e) {
      print('加载标签数据失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.bookmarks.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      controller: widget.scrollController,
      padding: widget.isHomePage ? EdgeInsets.zero : EdgeInsets.all(16.r),
      physics: const AlwaysScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // 每行两个，类似小红书
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: 0.55, // 宽高比，让卡片更高一些
      ),
      itemCount: widget.bookmarks.length + (widget.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == widget.bookmarks.length) {
          return _buildLoadingItem();
        }

        final bookmark = widget.bookmarks[index];
        return _buildGridItem(context, bookmark, index);
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Text(
        '暂无收藏内容',
        style: TextStyle(
          fontSize: 16.sp,
          color: AppColors.textHint,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  /// 构建加载更多项
  Widget _buildLoadingItem() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: SizedBox(
          width: 24.r,
          height: 24.r,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            strokeWidth: 2.w,
          ),
        ),
      ),
    );
  }

  /// 构建网格项
  Widget _buildGridItem(BuildContext context, BookmarkItem bookmark, int index) {
    return _buildBookmarkCard(context, bookmark, index);
  }

  /// 构建收藏项卡片
  Widget _buildBookmarkCard(BuildContext context, BookmarkItem bookmark, int index) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 2,
      shadowColor: AppColors.cardShadow.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () => _handleBookmarkTap(context, bookmark),
        onLongPress: () => _showContextMenu(context, bookmark, index),
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 封面图片区域
            Expanded(
              flex: 5,
              child: _buildCoverImage(bookmark),
            ),
            // 内容信息区域
            Expanded(
              flex: 2,
              child: _buildContentInfo(bookmark),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建封面图片
  Widget _buildCoverImage(BookmarkItem bookmark) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.searchBarBackground,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: bookmark.cover != null && bookmark.cover!.isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // 封面图片
                  Image.network(
                    bookmark.cover!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildDefaultCover(bookmark);
                    },
                  ),
                  // 标签覆盖层
                  if (bookmark.tags.isNotEmpty)
                    _buildCoverTagsOverlay(bookmark.tags),
                ],
              ),
            )
          : _buildDefaultCover(bookmark),
    );
  }

  /// 构建默认封面
  Widget _buildDefaultCover(BookmarkItem bookmark) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.searchBarBackground,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _getPlatformIcon(bookmark.schemeUrl),
            SizedBox(height: 8.h),
            Text(
              _getPlatformName(bookmark.schemeUrl),
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建封面标签覆盖层
  Widget _buildCoverTagsOverlay(List<String> tags) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.6),
            ],
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        child: _buildCoverTags(tags),
      ),
    );
  }

  /// 构建封面标签
  Widget _buildCoverTags(List<String> tags) {
    // 最多显示3个标签
    final displayTags = tags.take(3).toList();
    final hasMore = tags.length > 3;

    return Wrap(
      spacing: 4.w,
      runSpacing: 4.h,
      children: [
        ...displayTags.asMap().entries.map((entry) {
          final index = entry.key;
          final tag = entry.value;
          return _buildCoverTag(tag, index);
        }),
        if (hasMore)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10.r),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 0.5,
              ),
            ),
            child: Text(
              '+${tags.length - 3}',
              style: TextStyle(
                fontSize: 9.sp,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建封面单个标签
  Widget _buildCoverTag(String tag, int colorIndex) {
    final displayText = tag.length > 5 ? '${tag.substring(0, 5)}...' : tag;

    // 尝试从标签映射中获取真实颜色
    final tagObject = _tagMap[tag];
    Color backgroundColor;
    Color textColor;

    if (tagObject != null) {
      // 使用服务端返回的颜色，但增加透明度以适应封面覆盖层
      backgroundColor = _parseColor(tagObject.backgroundColor).withOpacity(0.3);
      textColor = _parseColor(tagObject.textColor);
    } else {
      // 如果没有找到对应的标签对象，使用默认颜色配置
      final List<Color> tagColors = [
        const Color(0xFFFFE4E1), // 薄雾玫瑰
        const Color(0xFFFFF0F5), // 薰衣草腮红
        const Color(0xFFE0F2E9), // 薄荷绿
        const Color(0xFFE0F7FA), // 淡青色
        const Color(0xFFFFF5E1), // 香草色
        const Color(0xFFF5F0FF), // 淡紫色
        const Color(0xFFFFFDE7), // 柠檬雪纺
        const Color(0xFFFCE4EC), // 粉红色
        const Color(0xFFF3E5F5), // 兰花色
        const Color(0xFFE8F5E9), // 蜜瓜绿
        const Color(0xFFFBE9E7), // 桃花色
        const Color(0xFFE1F5FE), // 天空蓝
        const Color(0xFFFFF8E1), // 香槟色
        const Color(0xFFF9FBE7), // 青柠色
        const Color(0xFFF1F8E9), // 春绿色
        const Color(0xFFE8EAF6), // 淡靛蓝
        const Color(0xFFEDE7F6), // 淡紫罗兰
        const Color(0xFFF9FBE7), // 嫩绿色
        const Color(0xFFFFF3E0), // 杏仁色
        const Color(0xFFF0F4C3), // 淡黄绿
      ];

      final List<Color> textColors = [
        const Color(0xFFFF6B81), // 珊瑚粉
        const Color(0xFFE75480), // 深粉红
        const Color(0xFF6DBE9D), // 海绿色
        const Color(0xFF64B5F6), // 蓝色
        const Color(0xFFFFA07A), // 淡鲑鱼色
        const Color(0xFFA974FF), // 紫色
        const Color(0xFFFBC02D), // 金黄色
        const Color(0xFFEC407A), // 粉红色
        const Color(0xFFBA68C8), // 中兰花紫
        const Color(0xFF66BB6A), // 绿色
        const Color(0xFFFF7043), // 深橙色
        const Color(0xFF29B6F6), // 浅蓝色
        const Color(0xFFFFCA28), // 琥珀色
        const Color(0xFFD4E157), // 柠檬绿
        const Color(0xFF9CCC65), // 浅绿色
        const Color(0xFF5C6BC0), // 靛蓝色
        const Color(0xFF7E57C2), // 深紫色
        const Color(0xFFC0CA33), // 柠檬色
        const Color(0xFFFFA726), // 橙色
        const Color(0xFFAFB42B), // 橄榄绿
      ];

      backgroundColor = tagColors[colorIndex % tagColors.length].withOpacity(0.3);
      textColor = textColors[colorIndex % textColors.length];
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 3.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 0.5,
        ),
        // 添加阴影以增强可读性
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Text(
        displayText,
        style: TextStyle(
          fontSize: 9.sp,
          color: Colors.white, // 封面标签统一使用白色文字以确保可读性
          fontWeight: FontWeight.w600, // 增加字重以提高可读性
          shadows: [
            // 添加文字阴影以增强对比度
            Shadow(
              color: Colors.black.withOpacity(0.5),
              blurRadius: 1,
              offset: const Offset(0, 0.5),
            ),
          ],
        ),
      ),
    );
  }

  /// 解析十六进制颜色字符串为Color对象
  Color _parseColor(String colorString) {
    try {
      // 移除可能的 # 前缀
      String hexColor = colorString.replaceAll('#', '');

      // 如果是6位，添加FF作为alpha值
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }

      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // 如果解析失败，返回默认颜色
      return const Color(0xFF1890FF);
    }
  }

  /// 构建内容信息
  Widget _buildContentInfo(BookmarkItem bookmark) {
    return Padding(
      padding: EdgeInsets.all(12.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 标题区域
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              if (bookmark.title != null && bookmark.title!.isNotEmpty)
                Text(
                  bookmark.title!,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                    height: 1.2,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),

          SizedBox(height: 8.h),

          // 博主信息和时间
          Row(
            children: [
              // 博主头像或平台图标
              Container(
                width: 16.r,
                height: 16.r,
                decoration: BoxDecoration(
                  color: AppColors.searchBarBackground,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: bookmark.influencerAvatar != null && bookmark.influencerAvatar!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: Image.network(
                          bookmark.influencerAvatar!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _getPlatformIcon(bookmark.schemeUrl, size: 10.r);
                          },
                        ),
                      )
                    : _getPlatformIcon(bookmark.schemeUrl, size: 10.r),
              ),
              SizedBox(width: 6.w),
              // 博主名称
              Expanded(
                child: Text(
                  bookmark.influencerName,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // 平台图标和时间
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 平台图标
                  if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty)
                    Builder(
                      builder: (context) {
                        final logoAsset = PlatformUtils.getPlatformLogoFromSchemeUrl(bookmark.schemeUrl);
                        if (logoAsset != null) {
                          return Padding(
                            padding: EdgeInsets.only(right: 4.w),
                            child: Image.asset(
                              logoAsset,
                              width: 10.r,
                              height: 10.r,
                              errorBuilder: (context, error, stackTrace) {
                                return SizedBox.shrink();
                              },
                            ),
                          );
                        }
                        return SizedBox.shrink();
                      },
                    ),
                  // 时间
                  Text(
                    _formatDate(bookmark.createTime),
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: AppColors.textHint,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示上下文菜单
  void _showContextMenu(BuildContext context, BookmarkItem bookmark, int index) {
    BookmarkContextMenu.show(
      context,
      bookmark: bookmark,
      index: index,
      onEdit: widget.onEdit ?? (bookmark) => _showEditBookmarkDialog(context, bookmark),
      onMove: widget.onMove ?? (bookmark, index) => _showMoveDialog(context, bookmark, index),
      onDelete: widget.onDelete ?? (bookmark, index) => _showDeleteConfirmDialog(context, bookmark, index),
      onTagManage: widget.onTagManage ?? (bookmark) => _showTagManagerDialog(context, bookmark),
    );
  }

  /// 处理收藏项点击
  void _handleBookmarkTap(BuildContext context, BookmarkItem bookmark) {
    if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty) {
      // 检查是否是http/https开头的图片链接
      final schemeUrl = bookmark.schemeUrl!;
      final isImageUrl = schemeUrl.startsWith('http') &&
          (schemeUrl.endsWith('.jpg') ||
           schemeUrl.endsWith('.jpeg') ||
           schemeUrl.endsWith('.png') ||
           schemeUrl.endsWith('.gif') ||
           schemeUrl.endsWith('.webp') ||
           schemeUrl.contains('.jpg?') ||
           schemeUrl.contains('.jpeg?') ||
           schemeUrl.contains('.png?') ||
           schemeUrl.contains('.gif?') ||
           schemeUrl.contains('.webp?'));

      if (isImageUrl) {
        // 如果是图片链接，跳转到图片预览页面
        Navigator.pushNamed(
          context,
          AppRoutes.imagePreview,
          arguments: {'imageUrl': schemeUrl},
        );
      } else {
        // 如果不是图片链接，使用原来的逻辑打开scheme URL
        final nativeBridge = NativeBridge();

        // 先检查悬浮窗权限
        nativeBridge.checkOverlayPermission().then((hasPermission) {
          if (!hasPermission) {
            // 如果没有悬浮窗权限，提示用户并引导开启
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('需要悬浮窗权限才能显示返回按钮'),
                action: SnackBarAction(
                  label: '去开启',
                  onPressed: () {
                    nativeBridge.openOverlayPermissionSettings();
                  },
                ),
                duration: const Duration(seconds: 5),
              ),
            );
          }

          // 无论是否有权限，都尝试打开链接
          nativeBridge.openSchemeUrl(schemeUrl, showReturnButton: true).then((success) {
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('无法打开链接，可能没有安装对应的应用'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          });
        });
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('此内容没有可打开的链接'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// 获取平台图标
  Widget _getPlatformIcon(String? schemeUrl, {double? size}) {
    final iconSize = size ?? 18.r;

    if (schemeUrl == null || schemeUrl.isEmpty) {
      return Icon(Icons.public, size: iconSize, color: AppColors.textSecondary);
    }

    String platformType = _getPlatformType(schemeUrl);

    switch (platformType) {
      case 'xiaohongshu':
        return ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.asset('assets/xiaohongshu.png', width: iconSize, height: iconSize),
        );
      case 'bilibili':
        return ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.asset('assets/bilibili.png', width: iconSize, height: iconSize),
        );
      case 'douyin':
        return ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.asset('assets/douyin.png', width: iconSize, height: iconSize),
        );
      case 'wechat':
        return ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.asset('assets/wechat.png', width: iconSize, height: iconSize),
        );
      case 'pinduoduo':
        return ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.asset('assets/Pinduoduo.png', width: iconSize, height: iconSize),
        );
      case 'jingdong':
        return ClipRRect(
          borderRadius: BorderRadius.circular(iconSize / 2),
          child: Image.asset('assets/jingdong.png', width: iconSize, height: iconSize),
        );
      default:
        return Icon(Icons.public, size: iconSize, color: AppColors.textSecondary);
    }
  }

  /// 识别平台类型
  String _getPlatformType(String schemeUrl) {
    if (schemeUrl.startsWith('xhsdiscover://')) {
      return 'xiaohongshu';
    } else if (schemeUrl.startsWith('bilibili://')) {
      return 'bilibili';
    } else if (schemeUrl.startsWith('snssdk1128://')) {
      return 'douyin';
    } else if (schemeUrl.startsWith('weixin://') || _isWechatPublicAccountUrl(schemeUrl)) {
      return 'wechat';
    } else if (schemeUrl.startsWith('pinduoduo://') || schemeUrl.contains('yangkeduo.com')) {
      return 'pinduoduo';
    } else if (schemeUrl.startsWith('taobao://') || schemeUrl.contains('taobao.com')) {
      return 'taobao';
    } else if (schemeUrl.startsWith('openapp.jdmobile://') || schemeUrl.contains('jd.com')) {
      return 'jingdong';
    } else {
      return 'unknown';
    }
  }

  /// 判断是否为微信公众号链接
  bool _isWechatPublicAccountUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host == 'mp.weixin.qq.com';
    } catch (e) {
      return false;
    }
  }

  /// 获取平台名称
  String _getPlatformName(String? schemeUrl) {
    if (schemeUrl == null || schemeUrl.isEmpty) {
      return '未知平台';
    }

    String platformType = _getPlatformType(schemeUrl);

    switch (platformType) {
      case 'xiaohongshu':
        return '小红书';
      case 'bilibili':
        return 'B站';
      case 'douyin':
        return '抖音';
      case 'wechat':
        return '微信';
      case 'pinduoduo':
        return '拼多多';
      case 'taobao':
        return '淘宝';
      case 'jingdong':
        return '京东';
      default:
        return '其他';
    }
  }

  /// 格式化日期
  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    }
  }

  /// 显示编辑书签对话框
  void _showEditBookmarkDialog(BuildContext context, BookmarkItem bookmark) {
    showDialog(
      context: context,
      builder: (context) => BookmarkEditDialog(
        bookmark: bookmark,
        onUpdateSuccess: (updatedBookmark) {
          // 刷新标签数据
          _loadTagData();
        },
      ),
    );
  }

  /// 显示移动书签对话框
  void _showMoveDialog(BuildContext context, BookmarkItem bookmark, int index) async {
    try {
      final apiProvider = ApiProvider();
      // 获取收藏夹列表
      final response = await apiProvider.favoritesApi.getUserFavorites();

      if (!response.containsKey('favorites')) {
        CustomToast.show('获取收藏夹列表失败');
        return;
      }

      final favorites = response['favorites'] as List<dynamic>;
      // 过滤掉当前书签所在的收藏夹
      final otherFavorites = favorites.where((fav) => fav['id'] != bookmark.parentId).toList();

      if (otherFavorites.isEmpty) {
        CustomToast.show('没有其他收藏夹可以移动到');
        return;
      }

      // 显示选择对话框
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(
              '移动到收藏夹',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            content: Container(
              width: double.maxFinite,
              height: 300.h,
              child: ListView.builder(
                itemCount: otherFavorites.length,
                itemBuilder: (context, index) {
                  final favorite = otherFavorites[index];
                  return ListTile(
                    leading: Container(
                      width: 40.r,
                      height: 40.r,
                      decoration: BoxDecoration(
                        color: AppColors.searchBarBackground,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: favorite['cover'] != null && favorite['cover'].toString().isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: Image.network(
                              favorite['cover'],
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.collections_bookmark,
                                  size: 20.r,
                                  color: AppColors.primary.withOpacity(0.7),
                                );
                              },
                            ),
                          )
                        : Icon(
                            Icons.collections_bookmark,
                            size: 20.r,
                            color: AppColors.primary.withOpacity(0.7),
                          ),
                    ),
                    title: Text(
                      favorite['name'] ?? '未命名收藏夹',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                      _moveBookmarkToFavorite(context, bookmark, favorite['id'], favorite['name']);
                    },
                  );
                },
              ),
            ),
          );
        },
      );
    } catch (e) {
      CustomToast.show('获取收藏夹列表失败: ${e.toString()}');
    }
  }

  /// 移动书签到指定收藏夹
  void _moveBookmarkToFavorite(BuildContext context, BookmarkItem bookmark, String targetFavoriteId, String targetFavoriteName) async {
    try {
      // 第一步：先在目标收藏夹中新增收藏项
      await _addBookmarkToTargetFavorite(bookmark, targetFavoriteId);

      // 第二步：添加成功后，再从当前收藏夹删除这个收藏项
      final bookmarkApi = BookmarkApi(ApiProvider().apiClient);
      await bookmarkApi.deleteBookmark(bookmark.id);

      // 第三步：显示移动成功提示
      CustomToast.show('已移动到 "$targetFavoriteName"');

      print('移动书签成功: ${bookmark.title} 到收藏夹 $targetFavoriteName (ID: $targetFavoriteId)');
    } catch (e) {
      print('移动书签失败: $e');
      CustomToast.show('移动失败: ${e.toString()}');
    }
  }

  /// 添加书签到目标收藏夹
  Future<void> _addBookmarkToTargetFavorite(BookmarkItem bookmark, String targetFavoriteId) async {
    try {
      // 使用Flutter侧的API添加书签
      final data = <String, dynamic>{
        'parent_id': targetFavoriteId,
        'influencer_name': bookmark.influencerName,
        'title': bookmark.title,
        'scheme_url': bookmark.schemeUrl ?? '',
      };

      // 添加可选字段
      if (bookmark.influencerAvatar != null && bookmark.influencerAvatar!.isNotEmpty) {
        data['influencer_avatar'] = bookmark.influencerAvatar;
      }
      if (bookmark.cover != null && bookmark.cover!.isNotEmpty) {
        data['cover'] = bookmark.cover;
      }
      if (bookmark.desc != null && bookmark.desc!.isNotEmpty) {
        data['desc'] = bookmark.desc;
      }

      // 添加标签信息
      if (bookmark.tags.isNotEmpty) {
        data['tags'] = bookmark.tags;
      }

      final apiProvider = ApiProvider();
      final response = await apiProvider.apiClient.post('/bookmark/add', data);

      if (response['code'] != 0) {
        throw Exception(response['message'] ?? '添加书签失败');
      }

      print('成功添加书签到目标收藏夹: ${bookmark.title}，包含标签: ${bookmark.tags}');
    } catch (e) {
      print('添加书签到目标收藏夹失败: $e');
      throw Exception('添加书签到目标收藏夹失败: $e');
    }
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, BookmarkItem bookmark, int index) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          title: Text(
            '确认删除',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          content: Text(
            '确定要删除这个收藏吗？',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                '取消',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteBookmark(context, bookmark);
              },
              child: Text(
                '确定',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 删除书签
  void _deleteBookmark(BuildContext context, BookmarkItem bookmark) async {
    try {
      final bookmarkApi = BookmarkApi(ApiProvider().apiClient);
      await bookmarkApi.deleteBookmark(bookmark.id);

      CustomToast.show('删除成功');
    } catch (e) {
      CustomToast.show('删除失败: ${e.toString()}');
    }
  }

  /// 显示标签管理对话框
  void _showTagManagerDialog(BuildContext context, BookmarkItem bookmark) {
    showDialog<void>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) => BookmarkTagManagerDialog(
        bookmark: bookmark,
        onTagsUpdated: (updatedBookmark) {
          // 刷新标签数据
          _loadTagData();
        },
      ),
    );
  }
}
