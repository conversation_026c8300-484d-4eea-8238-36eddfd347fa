import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../constants/app_colors.dart';
import '../../services/storage_service.dart';
import '../../pages/search_page.dart';
import '../../routes.dart';
import 'user_avatar.dart';

class AppSearchBar extends StatelessWidget {
  final VoidCallback onPersonTap;
  final ValueChanged<String>? onSearchChanged;
  final String? hintText;
  final VoidCallback? onSearchTap;
  final VoidCallback? onFilterTap;
  final VoidCallback? onCreateTap;

  const AppSearchBar({
    super.key,
    required this.onPersonTap,
    this.onSearchChanged,
    this.hintText = '搜索收藏内容',
    this.onSearchTap,
    this.onFilterTap,
    this.onCreateTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      // decoration: BoxDecoration(
      //   color: Color(0xFFF5F5F5), // 背景颜色为浅灰色
      // ),
      child: Row(
        children: [
          // 左侧头像
          Container(
            margin: EdgeInsets.only(left: 16.w),
            child: UserAvatar(
              onTap: onPersonTap,
              radius: 18,
            ),
          ),
          // 搜索框
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: 12.w, right: 16.w, top: 8.h, bottom: 8.h),
              height: 40.h,
              decoration: BoxDecoration(
                color: Color(0xFFEEEEEE), // 搜索框颜色
                borderRadius: BorderRadius.circular(20.r),
              ),
              // 使用InkWell包装，使整个搜索框可点击
              child: InkWell(
                onTap: () {
                  if (onSearchTap != null) {
                    onSearchTap!();
                  } else {
                    // 默认行为：导航到搜索页面（无动画）
                    Navigator.push(
                      context,
                      NoAnimationPageRoute(builder: (context) => const SearchPage()),
                    );
                  }
                },
                borderRadius: BorderRadius.circular(20.r),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 12.w),
                      child: Icon(
                        Icons.search,
                        size: 18.r,
                        color: Color(0xFF999999),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        color: Color(0xFFEEEEEE), // 设置输入框背景颜色
                        child: TextField(
                          enabled: false, // 禁用输入，因为点击整个区域会跳转到搜索页面
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.textPrimary,
                          ),
                          cursorHeight: 16,
                          cursorColor: AppColors.primary,
                          onChanged: onSearchChanged,
                          decoration: InputDecoration(
                            hintText: hintText,
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 10.h,
                            ),
                            isDense: true,
                            hintStyle: TextStyle(
                              color: Color(0xFF999999),
                              fontSize: 14.sp,
                            ),
                            // 确保输入框背景是透明的，这样Container的颜色才能显示出来
                            fillColor: Colors.transparent,
                            filled: true,
                          ),
                        ),
                      ),
                    ),
                    // 右侧留出一些空间，使搜索图标和文本居中
                    SizedBox(width: 12.w),
                  ],
                ),
              ),
            ),
          ),
          // 右侧过滤图标
          if (onFilterTap != null)
            Container(
              margin: EdgeInsets.only(right: 8.w),
              child: InkWell(
                onTap: onFilterTap,
                borderRadius: BorderRadius.circular(20.r),
                child: Container(
                  width: 36.w,
                  height: 36.w,
                  decoration: BoxDecoration(
                    color: Color(0xFFEEEEEE),
                    borderRadius: BorderRadius.circular(18.r),
                  ),
                  child: Icon(
                    Icons.filter_list,
                    size: 18.r,
                    color: Color(0xFF666666),
                  ),
                ),
              ),
            ),
          // 右侧创建收藏夹按钮
          if (onCreateTap != null)
            Container(
              margin: EdgeInsets.only(right: 16.w),
              child: InkWell(
                onTap: onCreateTap,
                borderRadius: BorderRadius.circular(20.r),
                child: Container(
                  width: 36.w,
                  height: 36.w,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(18.r),
                  ),
                  child: Icon(
                    Icons.add,
                    size: 20.r,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
