import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../constants/app_colors.dart';
import '../../services/storage_service.dart';

/// 用户头像组件
///
/// 显示用户头像，如果用户未登录则显示默认头像
class UserAvatar extends StatefulWidget {
  final VoidCallback onTap;
  final double radius;

  const UserAvatar({
    Key? key,
    required this.onTap,
    this.radius = 18,
  }) : super(key: key);

  @override
  State<UserAvatar> createState() => _UserAvatarState();
}

class _UserAvatarState extends State<UserAvatar> {
  final StorageService _storageService = StorageService();
  bool _isLoggedIn = false;
  String? _phone;
  String? _avatarUrl;

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  /// 检查用户登录状态
  Future<void> _checkLoginStatus() async {
    final isLoggedIn = await _storageService.isLoggedIn();
    final phone = await _storageService.getPhone();
    final avatarUrl = await _storageService.getAvatarUrl();

    if (mounted) {
      setState(() {
        _isLoggedIn = isLoggedIn;
        _phone = phone;
        _avatarUrl = avatarUrl;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: _isLoggedIn ? AppColors.primaryLight.withOpacity(0.15) : Colors.grey[200],
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadow,
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: CircleAvatar(
          radius: widget.radius.r,
          backgroundColor: Colors.transparent,
          backgroundImage: _isLoggedIn && _avatarUrl != null && _avatarUrl!.isNotEmpty
              ? NetworkImage(_avatarUrl!)
              : null,
          child: _isLoggedIn && _avatarUrl != null && _avatarUrl!.isNotEmpty
              ? null
              : Icon(
                  _isLoggedIn ? Icons.person : Icons.person_outline,
                  size: (widget.radius * 1.2).r,
                  color: _isLoggedIn ? AppColors.primary : AppColors.textSecondary,
                ),
        ),
      ),
    );
  }
}
