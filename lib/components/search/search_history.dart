import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../constants/app_colors.dart';
import '../../services/search_history_service.dart';

/// 搜索历史组件
class SearchHistory extends StatefulWidget {
  /// 点击历史记录项的回调
  final Function(String) onHistoryItemTap;
  
  /// 清除历史记录的回调
  final VoidCallback onClearHistory;
  
  /// 历史记录列表
  final List<String> historyList;

  const SearchHistory({
    Key? key,
    required this.onHistoryItemTap,
    required this.onClearHistory,
    required this.historyList,
  }) : super(key: key);

  @override
  State<SearchHistory> createState() => _SearchHistoryState();
}

class _SearchHistoryState extends State<SearchHistory> {
  @override
  Widget build(BuildContext context) {
    // 如果没有历史记录，显示空状态
    if (widget.historyList.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.only(top: 40.h),
          child: Text(
            '暂无搜索记录',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textHint,
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和清除按钮
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '搜索历史',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
              // 清除按钮
              GestureDetector(
                onTap: widget.onClearHistory,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: AppColors.searchBarBackground,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.delete_outline,
                        size: 16.r,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '清除',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // 历史记录列表
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: widget.historyList.map((item) {
            return GestureDetector(
              onTap: () => widget.onHistoryItemTap(item),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: AppColors.searchBarBackground,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Text(
                  item,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
