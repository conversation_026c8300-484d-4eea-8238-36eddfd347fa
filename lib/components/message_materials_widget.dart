import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../models/material.dart' as material_model;

/// 消息中的素材展示组件
class MessageMaterialsWidget extends StatelessWidget {
  /// 素材列表
  final List<material_model.Material> materials;
  
  /// 最大显示数量
  final int maxDisplayCount;

  const MessageMaterialsWidget({
    super.key,
    required this.materials,
    this.maxDisplayCount = 4,
  });

  @override
  Widget build(BuildContext context) {
    if (materials.isEmpty) return const SizedBox.shrink();

    // 只显示图片类型的素材
    final imageMaterials = materials.where((m) => m.isImage).toList();
    if (imageMaterials.isEmpty) return const SizedBox.shrink();

    final displayMaterials = imageMaterials.take(maxDisplayCount).toList();
    final hasMore = imageMaterials.length > maxDisplayCount;

    return Container(
      margin: EdgeInsets.only(top: 8.h),
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children: [
          // 显示图片
          ...displayMaterials.map((material) => _buildMaterialItem(material)),
          // 如果有更多图片，显示数量提示
          if (hasMore) _buildMoreIndicator(imageMaterials.length - maxDisplayCount),
        ],
      ),
    );
  }

  /// 构建单个素材项
  Widget _buildMaterialItem(material_model.Material material) {
    return Container(
      width: 80.w,
      height: 80.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: Image.network(
          material.url,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: AppColors.divider,
              child: Icon(
                Icons.image_not_supported_outlined,
                color: AppColors.textHint,
                size: 24.r,
              ),
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              color: AppColors.divider,
              child: Center(
                child: SizedBox(
                  width: 20.r,
                  height: 20.r,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: AppColors.textHint,
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                        : null,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建更多图片指示器
  Widget _buildMoreIndicator(int moreCount) {
    return Container(
      width: 80.w,
      height: 80.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        color: AppColors.divider.withOpacity(0.5),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          '+$moreCount',
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
