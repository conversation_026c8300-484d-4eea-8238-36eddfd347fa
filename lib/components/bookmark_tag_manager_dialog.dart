import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../api/bookmark_api.dart';
import '../api/api_provider.dart';
import '../services/tag_service.dart';
import '../widgets/custom_toast.dart';
import '../pages/app/widgets/tag_chip.dart';
import '../models/tag.dart';

/// 书签标签管理对话框
class BookmarkTagManagerDialog extends StatefulWidget {
  final BookmarkItem bookmark;
  final Function(BookmarkItem)? onTagsUpdated;

  const BookmarkTagManagerDialog({
    super.key,
    required this.bookmark,
    this.onTagsUpdated,
  });

  @override
  State<BookmarkTagManagerDialog> createState() => _BookmarkTagManagerDialogState();
}

class _BookmarkTagManagerDialogState extends State<BookmarkTagManagerDialog> {
  final TagService _tagService = TagService();
  final TextEditingController _newTagController = TextEditingController();

  List<Tag> _allTagObjects = [];
  List<String> _bookmarkTags = [];
  bool _isLoading = true;
  bool _isAddingTag = false;

  @override
  void initState() {
    super.initState();
    _bookmarkTags = List.from(widget.bookmark.tags);
    _loadTags();
  }

  @override
  void dispose() {
    _newTagController.dispose();
    super.dispose();
  }

  /// 加载所有标签
  Future<void> _loadTags() async {
    try {
      final tagObjects = await _tagService.getAllTagObjects();
      if (mounted) {
        setState(() {
          _allTagObjects = tagObjects;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('加载标签失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 添加标签到书签
  Future<void> _addTagToBookmark(String tagName) async {
    print('点击添加标签: $tagName');
    print('书签ID: ${widget.bookmark.id}');
    print('当前标签列表: $_bookmarkTags');

    if (_bookmarkTags.contains(tagName)) {
      print('标签已存在，跳过添加');
      CustomToast.show('标签已存在');
      return;
    }

    print('开始调用API添加标签...');
    try {
      // 调用API给书签添加标签
      final result = await ApiProvider().bookmarkApi.addTagsToBookmark(
        bookmarkId: widget.bookmark.id,
        tagNames: [tagName],
      );

      print('API调用成功，返回结果: $result');

      // API调用成功后更新本地状态
      setState(() {
        _bookmarkTags.add(tagName);
      });

      print('本地状态已更新，新的标签列表: $_bookmarkTags');

      // 通知父组件标签已更新
      _notifyTagsUpdated();

      CustomToast.show('标签添加成功');
    } catch (e, stackTrace) {
      print('添加标签失败: $e');
      print('错误堆栈: $stackTrace');
      CustomToast.show('添加标签失败，请重试: ${e.toString()}');
    }
  }

  /// 从书签移除标签
  Future<void> _removeTagFromBookmark(String tagName) async {
    // 显示确认删除对话框
    final confirmed = await _showDeleteTagConfirmDialog(tagName);
    if (!confirmed) return;

    print('开始从书签移除标签: $tagName');
    print('书签ID: ${widget.bookmark.id}');
    print('当前标签列表: $_bookmarkTags');

    try {
      // 根据标签名称找到对应的标签ID
      final tagObject = _allTagObjects.firstWhere(
        (tag) => tag.name == tagName,
        orElse: () => throw Exception('标签不存在: $tagName'),
      );

      print('找到标签ID: ${tagObject.id} for 标签名称: $tagName');

      // 调用API从书签移除标签
      final result = await ApiProvider().bookmarkApi.removeTagsFromBookmark(
        bookmarkId: widget.bookmark.id,
        tagIds: [tagObject.id],
      );

      print('API调用成功，返回结果: $result');

      // API调用成功后更新本地状态
      setState(() {
        _bookmarkTags.remove(tagName);
      });

      print('本地状态已更新，新的标签列表: $_bookmarkTags');

      // 通知父组件标签已更新
      _notifyTagsUpdated();

      CustomToast.show('标签移除成功');
    } catch (e, stackTrace) {
      print('移除标签失败: $e');
      print('错误堆栈: $stackTrace');

      // 根据错误类型显示不同的提示
      String errorMessage = '移除标签失败，请重试';
      if (e.toString().contains('标签不存在')) {
        errorMessage = '标签不存在，可能已被删除';
        // 如果标签不存在，直接从本地移除
        setState(() {
          _bookmarkTags.remove(tagName);
        });
        _notifyTagsUpdated();
        CustomToast.show('标签已移除');
        return;
      }

      CustomToast.show(errorMessage);
    }
  }

  /// 删除标签（彻底删除标签本身）
  Future<void> _deleteTag(String tagName) async {
    // 显示确认删除对话框
    final confirmed = await _showDeleteTagPermanentlyConfirmDialog(tagName);
    if (!confirmed) return;

    print('开始删除标签: $tagName');

    try {
      // 调用TagService删除标签
      final success = await _tagService.removeTag(tagName);
      if (success && mounted) {
        // 删除成功后重新加载标签列表
        await _loadTags();

        // 如果删除的标签在当前书签的标签列表中，也要从本地状态中移除
        if (_bookmarkTags.contains(tagName)) {
          setState(() {
            _bookmarkTags.remove(tagName);
          });
          _notifyTagsUpdated();
        }

        CustomToast.show('标签"$tagName"已删除');
      }
      // 错误提示已在TagService中处理，这里不需要重复显示
    } catch (e) {
      print('删除标签失败: $e');
      // 错误提示已在TagService中处理，这里不需要重复显示
    }
  }

  /// 显示删除标签确认对话框（从书签移除）
  Future<bool> _showDeleteTagConfirmDialog(String tagName) async {
    return await showDialog<bool>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (dialogContext) => Center(
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: 280.w,
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: AppColors.cardShadow.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Padding(
                  padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 12.h),
                  child: Text(
                    '提示',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                // 内容
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Text(
                    '确定要移除标签"$tagName"吗？',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20.h),
                // 按钮区域
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: AppColors.divider,
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      // 取消按钮
                      Expanded(
                        child: InkWell(
                          onTap: () => Navigator.of(dialogContext).pop(false),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(12.r),
                          ),
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 14.h),
                            child: Text(
                              '取消',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.textSecondary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                      // 分割线
                      Container(
                        width: 0.5,
                        height: 44.h,
                        color: AppColors.divider,
                      ),
                      // 确认按钮
                      Expanded(
                        child: InkWell(
                          onTap: () => Navigator.of(dialogContext).pop(true),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(12.r),
                          ),
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 14.h),
                            child: Text(
                              '移除',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.red,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ) ?? false;
  }

  /// 显示彻底删除标签确认对话框
  Future<bool> _showDeleteTagPermanentlyConfirmDialog(String tagName) async {
    return await showDialog<bool>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (dialogContext) => Center(
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: 280.w,
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: AppColors.cardShadow.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Padding(
                  padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 12.h),
                  child: Text(
                    '提示',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                // 内容
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Text(
                    '确定要删除标签"$tagName"吗？',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20.h),
                // 按钮区域
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: AppColors.divider,
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      // 取消按钮
                      Expanded(
                        child: InkWell(
                          onTap: () => Navigator.of(dialogContext).pop(false),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(12.r),
                          ),
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 14.h),
                            child: Text(
                              '取消',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.textSecondary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                      // 分割线
                      Container(
                        width: 0.5,
                        height: 44.h,
                        color: AppColors.divider,
                      ),
                      // 确认按钮
                      Expanded(
                        child: InkWell(
                          onTap: () => Navigator.of(dialogContext).pop(true),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(12.r),
                          ),
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 14.h),
                            child: Text(
                              '删除',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.red,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ) ?? false;
  }

  /// 通知父组件标签已更新
  void _notifyTagsUpdated() {
    // 创建更新后的书签对象
    final updatedBookmark = BookmarkItem(
      id: widget.bookmark.id,
      influencerName: widget.bookmark.influencerName,
      influencerAvatar: widget.bookmark.influencerAvatar,
      cover: widget.bookmark.cover,
      title: widget.bookmark.title,
      desc: widget.bookmark.desc,
      schemeUrl: widget.bookmark.schemeUrl,
      parentId: widget.bookmark.parentId,
      createTime: widget.bookmark.createTime,
      updateTime: DateTime.now(),
      tags: _bookmarkTags,
    );

    // 通知父组件标签已更新
    widget.onTagsUpdated?.call(updatedBookmark);
  }

  /// 创建新标签并添加到书签
  Future<void> _createAndAddTag() async {
    final tagName = _newTagController.text.trim();
    if (tagName.isEmpty) {
      CustomToast.show('请输入标签名称');
      return;
    }

    if (_allTagObjects.any((tag) => tag.name == tagName)) {
      CustomToast.show('标签已存在');
      _newTagController.clear();
      return;
    }

    setState(() {
      _isAddingTag = true;
    });

    try {
      // 第一步：创建新标签
      final success = await _tagService.addTag(tagName);
      if (success && mounted) {
        // 第二步：重新加载标签列表以获取新创建的标签对象
        await _loadTags();

        // 第三步：将新创建的标签添加到书签中（调用API）
        await _addTagToBookmark(tagName);

        // 第四步：清空输入框
        setState(() {
          _newTagController.clear();
        });

        CustomToast.show('标签创建并添加成功');
      } else if (mounted) {
        CustomToast.show('创建标签失败');
      }
    } catch (e) {
      print('创建标签失败: $e');
      if (mounted) {
        CustomToast.show('创建标签失败');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAddingTag = false;
        });
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: 320.w,
          constraints: BoxConstraints(maxHeight: 500.h),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.divider,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '标签管理',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: 20.r,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 当前标签
                      _buildCurrentTags(),

                      SizedBox(height: 20.h),

                      // 可用标签
                      _buildAvailableTags(),

                      SizedBox(height: 20.h),

                      // 新建标签
                      _buildNewTagSection(),
                    ],
                  ),
                ),
              ),


            ],
          ),
        ),
      ),
    );
  }

  /// 构建当前标签区域
  Widget _buildCurrentTags() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '当前标签',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8.h),
        if (_bookmarkTags.isEmpty)
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.h),
            decoration: BoxDecoration(
              color: AppColors.searchBarBackground,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              '暂无标签',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          )
        else
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: _bookmarkTags.asMap().entries.map((entry) {
              final index = entry.key;
              final tagName = entry.value;
              // 根据标签名称找到对应的Tag对象
              final tagObject = _allTagObjects.firstWhere(
                (tag) => tag.name == tagName,
                orElse: () => Tag(
                  id: '',
                  name: tagName,
                  backgroundColor: '#1890ff',
                  textColor: '#ffffff',
                  createTime: 0,
                  updateTime: 0,
                  count: 0,
                ),
              );
              return TagChip(
                tag: tagName,
                colorIndex: index,
                tagObject: tagObject, // 传递Tag对象以显示真实颜色
                onDelete: (tagName) => _removeTagFromBookmark(tagName),
              );
            }).toList(),
          ),
      ],
    );
  }

  /// 构建可用标签区域
  Widget _buildAvailableTags() {
    if (_isLoading) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '可用标签',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          Center(
            child: SizedBox(
              width: 20.w,
              height: 20.w,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ),
        ],
      );
    }

    final availableTagObjects = _allTagObjects.where((tag) => !_bookmarkTags.contains(tag.name)).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '可用标签',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8.h),
        if (availableTagObjects.isEmpty)
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.h),
            decoration: BoxDecoration(
              color: AppColors.searchBarBackground,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              '暂无可用标签',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          )
        else
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: availableTagObjects.asMap().entries.map((entry) {
              final index = entry.key;
              final tagObject = entry.value;
              return TagChip(
                tag: tagObject.name,
                colorIndex: index,
                tagObject: tagObject, // 传递Tag对象以显示真实颜色
                onTap: (tagName) {
                  print('点击了可用标签: $tagName');
                  _addTagToBookmark(tagName);
                },
                onDelete: (tagName) {
                  print('长按删除可用标签: $tagName');
                  _deleteTag(tagName);
                },
              );
            }).toList(),
          ),
      ],
    );
  }
  /// 构建新建标签区域
  Widget _buildNewTagSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '新建标签',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _newTagController,
                decoration: InputDecoration(
                  hintText: '输入新标签名称',
                  hintStyle: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    borderSide: BorderSide(color: AppColors.divider),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    borderSide: BorderSide(color: AppColors.divider),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    borderSide: BorderSide(color: AppColors.primary),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                ),
                style: TextStyle(fontSize: 12.sp),
                maxLength: 50,
                buildCounter: (context, {required currentLength, required isFocused, maxLength}) => null,
              ),
            ),
            SizedBox(width: 8.w),
            SizedBox(
              width: 60.w,
              height: 36.h,
              child: ElevatedButton(
                onPressed: _isAddingTag ? null : _createAndAddTag,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: _isAddingTag
                    ? SizedBox(
                        width: 16.w,
                        height: 16.w,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        '添加',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 显示标签管理对话框
  static Future<void> show(
    BuildContext context, {
    required BookmarkItem bookmark,
    Function(BookmarkItem)? onTagsUpdated,
  }) async {
    await showDialog<void>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) => BookmarkTagManagerDialog(
        bookmark: bookmark,
        onTagsUpdated: onTagsUpdated,
      ),
    );
  }
}