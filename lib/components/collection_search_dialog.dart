import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../api/bookmark_api.dart';
import '../api/api_provider.dart';
import '../utils/platform_utils.dart';
import '../models/collection_item.dart';

/// 显示模式枚举
enum DisplayMode { collections, search, collectionContents }

/// 收藏搜索弹窗
class CollectionSearchDialog extends StatefulWidget {
  /// 选择收藏内容的回调（支持多选）
  final Function(List<BookmarkItem>) onCollectionSelected;

  const CollectionSearchDialog({
    super.key,
    required this.onCollectionSelected,
  });

  @override
  State<CollectionSearchDialog> createState() => _CollectionSearchDialogState();
}

class _CollectionSearchDialogState extends State<CollectionSearchDialog> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ApiProvider _apiProvider = ApiProvider();

  // 搜索相关状态
  List<BookmarkItem> _searchResults = [];
  bool _isSearching = false;
  bool _isLoadingMore = false;
  bool _hasMore = false;
  int _currentPage = 1;
  final int _pageSize = 10;

  // 收藏夹相关状态
  List<CollectionItem> _collections = [];
  bool _isLoadingCollections = false;
  bool _isLoadingMoreCollections = false;
  bool _hasMoreCollections = false;
  int _collectionsCurrentPage = 1;
  final int _collectionsPageSize = 15;

  // 收藏夹内容相关状态
  List<BookmarkItem> _collectionContents = [];
  bool _isLoadingContents = false;
  bool _isLoadingMoreContents = false;
  bool _hasMoreContents = false;
  int _contentsCurrentPage = 1;
  final int _contentsPageSize = 20;
  String? _selectedCollectionId;
  String? _selectedCollectionName;

  // 多选相关状态
  Set<String> _selectedBookmarkIds = {};

  // 当前显示模式
  DisplayMode _currentMode = DisplayMode.collections;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _loadCollections(); // 初始加载收藏夹列表
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  /// 滚动监听器
  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      switch (_currentMode) {
        case DisplayMode.collections:
          if (_hasMoreCollections && !_isLoadingMoreCollections) {
            _loadMoreCollections();
          }
          break;
        case DisplayMode.search:
          if (_hasMore && !_isLoadingMore) {
            _loadMoreResults();
          }
          break;
        case DisplayMode.collectionContents:
          if (_hasMoreContents && !_isLoadingMoreContents) {
            _loadMoreContents();
          }
          break;
      }
    }
  }

  /// 加载收藏夹列表
  Future<void> _loadCollections({bool loadMore = false}) async {
    if (loadMore) {
      setState(() {
        _isLoadingMoreCollections = true;
      });
    } else {
      setState(() {
        _isLoadingCollections = true;
        _collectionsCurrentPage = 1;
        _collections.clear();
      });
    }

    try {
      final response = await _apiProvider.favoritesApi.getUserFavorites(
        page: loadMore ? _collectionsCurrentPage + 1 : 1,
        pageSize: _collectionsPageSize,
      );

      final favorites = response['favorites'] as List<dynamic>? ?? [];
      final total = response['total'] as int? ?? 0;

      final collections = favorites.map((item) {
        return CollectionItem.fromJson(item as Map<String, dynamic>);
      }).toList();

      setState(() {
        if (loadMore) {
          _collectionsCurrentPage++;
          _collections.addAll(collections);
          _isLoadingMoreCollections = false;
        } else {
          _collections = collections;
          _isLoadingCollections = false;
        }
        _hasMoreCollections = _collections.length < total;
      });
    } catch (e) {
      print('加载收藏夹失败: $e');
      setState(() {
        if (loadMore) {
          _isLoadingMoreCollections = false;
        } else {
          _isLoadingCollections = false;
        }
      });
    }
  }

  /// 加载更多收藏夹
  Future<void> _loadMoreCollections() async {
    await _loadCollections(loadMore: true);
  }

  /// 加载收藏夹内容
  Future<void> _loadCollectionContents(String collectionId, String collectionName, {bool loadMore = false}) async {
    if (loadMore) {
      setState(() {
        _isLoadingMoreContents = true;
      });
    } else {
      setState(() {
        _isLoadingContents = true;
        _selectedCollectionId = collectionId;
        _selectedCollectionName = collectionName;
        _currentMode = DisplayMode.collectionContents;
        _selectedBookmarkIds.clear(); // 清空之前的选择
        _contentsCurrentPage = 1;
        _collectionContents.clear();
      });
    }

    try {
      final result = await _apiProvider.bookmarkApi.getBookmarkList(
        favoriteId: collectionId,
        page: loadMore ? _contentsCurrentPage + 1 : 1,
        pageSize: _contentsPageSize,
      );

      final bookmarks = result['items'] as List<BookmarkItem>? ?? [];
      final total = result['total'] as int? ?? 0;

      setState(() {
        if (loadMore) {
          _contentsCurrentPage++;
          _collectionContents.addAll(bookmarks);
          _isLoadingMoreContents = false;
        } else {
          _collectionContents = bookmarks;
          _isLoadingContents = false;
        }
        _hasMoreContents = _collectionContents.length < total;
      });
    } catch (e) {
      print('加载收藏夹内容失败: $e');
      setState(() {
        if (loadMore) {
          _isLoadingMoreContents = false;
        } else {
          _isLoadingContents = false;
        }
      });
    }
  }

  /// 加载更多收藏夹内容
  Future<void> _loadMoreContents() async {
    if (_selectedCollectionId != null) {
      await _loadCollectionContents(_selectedCollectionId!, _selectedCollectionName ?? '', loadMore: true);
    }
  }

  /// 执行搜索
  Future<void> _performSearch(String keyword) async {
    if (keyword.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _hasMore = false;
        _currentMode = DisplayMode.collections; // 回到收藏夹列表模式
        _selectedBookmarkIds.clear(); // 清空选择
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _currentPage = 1;
      _searchResults = [];
      _currentMode = DisplayMode.search; // 切换到搜索模式
      _selectedBookmarkIds.clear(); // 清空之前的选择
    });

    try {
      final result = await _apiProvider.bookmarkApi.searchBookmarks(
        keyword: keyword.trim(),
        page: _currentPage,
        pageSize: _pageSize,
      );

      final bookmarks = result['items'] as List<BookmarkItem>? ?? [];
      final total = result['total'] as int? ?? 0;

      setState(() {
        _searchResults = bookmarks;
        _isSearching = false;
        _hasMore = bookmarks.length < total;
      });
    } catch (e) {
      print('搜索失败: $e');
      setState(() {
        _isSearching = false;
        _searchResults = [];
        _hasMore = false;
      });
    }
  }

  /// 加载更多结果
  Future<void> _loadMoreResults() async {
    if (_isLoadingMore || !_hasMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final keyword = _searchController.text.trim();
      final result = await _apiProvider.bookmarkApi.searchBookmarks(
        keyword: keyword,
        page: _currentPage + 1,
        pageSize: _pageSize,
      );

      final bookmarks = result['items'] as List<BookmarkItem>? ?? [];
      final total = result['total'] as int? ?? 0;

      setState(() {
        _currentPage++;
        _searchResults.addAll(bookmarks);
        _isLoadingMore = false;
        _hasMore = _searchResults.length < total;
      });
    } catch (e) {
      print('加载更多失败: $e');
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  /// 切换书签选择状态
  void _toggleBookmarkSelection(String bookmarkId) {
    setState(() {
      if (_selectedBookmarkIds.contains(bookmarkId)) {
        _selectedBookmarkIds.remove(bookmarkId);
      } else {
        _selectedBookmarkIds.add(bookmarkId);
      }
    });
  }

  /// 返回到收藏夹列表
  void _backToCollections() {
    setState(() {
      _currentMode = DisplayMode.collections;
      _selectedCollectionId = null;
      _selectedCollectionName = null;
      _collectionContents.clear();
      _selectedBookmarkIds.clear();
    });
  }

  /// 确认选择
  void _confirmSelection() {
    List<BookmarkItem> selectedItems = [];

    if (_currentMode == DisplayMode.search) {
      selectedItems = _searchResults.where((item) =>
        _selectedBookmarkIds.contains(item.id)).toList();
    } else if (_currentMode == DisplayMode.collectionContents) {
      selectedItems = _collectionContents.where((item) =>
        _selectedBookmarkIds.contains(item.id)).toList();
    }

    if (selectedItems.isNotEmpty) {
      widget.onCollectionSelected(selectedItems);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          // 顶部拖拽指示器
          Container(
            margin: EdgeInsets.only(top: 8.h),
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: AppColors.divider,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // 标题栏
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Row(
              children: [
                // 返回按钮（仅在收藏夹内容模式显示）
                if (_currentMode == DisplayMode.collectionContents)
                  IconButton(
                    onPressed: _backToCollections,
                    icon: Icon(
                      Icons.arrow_back,
                      color: AppColors.textSecondary,
                      size: 24.r,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints(),
                  ),
                if (_currentMode == DisplayMode.collectionContents)
                  SizedBox(width: 8.w),

                // 标题
                Expanded(
                  child: Text(
                    _currentMode == DisplayMode.collectionContents
                        ? _selectedCollectionName ?? '收藏夹内容'
                        : '搜索收藏内容',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),

                // 确认按钮（有选择时显示）
                if (_selectedBookmarkIds.isNotEmpty)
                  Container(
                    margin: EdgeInsets.only(right: 8.w),
                    child: ElevatedButton(
                      onPressed: _confirmSelection,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        elevation: 2,
                        shadowColor: AppColors.primary.withOpacity(0.3),
                        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        minimumSize: Size(0, 32.h),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 16.r,
                            color: Colors.white,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            '确认 ${_selectedBookmarkIds.length}',
                            style: TextStyle(
                              fontSize: 13.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // 关闭按钮
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: AppColors.textSecondary,
                    size: 24.r,
                  ),
                ),
              ],
            ),
          ),

          // 搜索框（仅在非收藏夹内容模式显示）
          if (_currentMode != DisplayMode.collectionContents)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.searchBarBackground,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: AppColors.divider,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 12.w),
                      child: Icon(
                        Icons.search,
                        size: 18.r,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        autofocus: true,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textPrimary,
                        ),
                        decoration: InputDecoration(
                          hintText: '搜索收藏内容',
                          hintStyle: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.textHint,
                          ),
                          border: InputBorder.none,
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(vertical: 12.h),
                        ),
                        onChanged: (value) {
                          // 实时搜索
                          _performSearch(value);
                        },
                        onSubmitted: (value) {
                          _performSearch(value);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),

          SizedBox(height: 16.h),

          // 内容区域
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    switch (_currentMode) {
      case DisplayMode.collections:
        return _buildCollectionsGrid();
      case DisplayMode.search:
        return _buildSearchResults();
      case DisplayMode.collectionContents:
        return _buildCollectionContents();
    }
  }

  /// 构建收藏夹网格
  Widget _buildCollectionsGrid() {
    if (_isLoadingCollections) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    if (_collections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_outlined,
              size: 48.r,
              color: AppColors.textHint,
            ),
            SizedBox(height: 16.h),
            Text(
              '暂无收藏夹',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    // 计算网格项目数量，如果有更多数据则添加加载指示器
    final totalItems = _collections.length + (_hasMoreCollections ? 1 : 0);

    return GridView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3, // 每排三个
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: 0.8,
      ),
      itemCount: totalItems,
      itemBuilder: (context, index) {
        // 如果是最后一个项目且有更多数据，显示加载指示器
        if (index == _collections.length && _hasMoreCollections) {
          return Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: SizedBox(
                width: 24.r,
                height: 24.r,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  strokeWidth: 2.w,
                ),
              ),
            ),
          );
        }
        return _buildCollectionItem(_collections[index]);
      },
    );
  }

  /// 构建收藏夹项目
  Widget _buildCollectionItem(CollectionItem collection) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _loadCollectionContents(collection.id, collection.title),
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.searchBarBackground,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withOpacity(0.06),
                blurRadius: 6,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              // 封面区域
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.r),
                      topRight: Radius.circular(12.r),
                    ),
                  ),
                  child: collection.cover != null && collection.cover!.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12.r),
                            topRight: Radius.circular(12.r),
                          ),
                          child: Image.network(
                            collection.cover!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return _buildDefaultCover();
                            },
                          ),
                        )
                      : _buildDefaultCover(),
                ),
              ),
              // 标题区域
              Expanded(
                flex: 1,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  child: Center(
                    child: Text(
                      collection.title,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建默认封面
  Widget _buildDefaultCover() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Center(
        child: Icon(
          Icons.folder,
          size: 32.r,
          color: AppColors.primary,
        ),
      ),
    );
  }

  /// 构建搜索结果列表
  Widget _buildSearchResults() {
    if (_isSearching) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48.r,
              color: AppColors.textHint,
            ),
            SizedBox(height: 16.h),
            Text(
              _searchController.text.trim().isEmpty ? '输入关键词搜索收藏内容' : '没有找到相关内容',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      itemCount: _searchResults.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _searchResults.length) {
          return Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: SizedBox(
                width: 24.r,
                height: 24.r,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  strokeWidth: 2.w,
                ),
              ),
            ),
          );
        }

        return _buildBookmarkItem(_searchResults[index], true);
      },
    );
  }

  /// 构建收藏夹内容列表
  Widget _buildCollectionContents() {
    if (_isLoadingContents) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    if (_collectionContents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 48.r,
              color: AppColors.textHint,
            ),
            SizedBox(height: 16.h),
            Text(
              '收藏夹为空',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      itemCount: _collectionContents.length + (_hasMoreContents ? 1 : 0),
      itemBuilder: (context, index) {
        // 如果是最后一个项目且有更多数据，显示加载指示器
        if (index == _collectionContents.length && _hasMoreContents) {
          return Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: SizedBox(
                width: 24.r,
                height: 24.r,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  strokeWidth: 2.w,
                ),
              ),
            ),
          );
        }
        return _buildBookmarkItem(_collectionContents[index], true);
      },
    );
  }

  /// 构建书签项
  Widget _buildBookmarkItem(BookmarkItem bookmark, bool showCheckbox) {
    final isSelected = _selectedBookmarkIds.contains(bookmark.id);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isSelected ? AppColors.primary : Colors.transparent,
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? AppColors.primary.withOpacity(0.15)
                : AppColors.cardShadow.withOpacity(0.06),
            blurRadius: isSelected ? 12 : 6,
            offset: const Offset(0, 2),
            spreadRadius: isSelected ? 1 : 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (showCheckbox) {
              _toggleBookmarkSelection(bookmark.id);
            } else {
              widget.onCollectionSelected([bookmark]);
            }
          },
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(14.r),
            child: _buildBookmarkContent(bookmark),
          ),
        ),
      ),
    );
  }

  /// 构建书签内容
  Widget _buildBookmarkContent(BookmarkItem bookmark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 博主信息行
        Row(
          children: [
            // 博主头像或平台图标
            Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                color: AppColors.searchBarBackground,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: bookmark.influencerAvatar != null && bookmark.influencerAvatar!.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(10.r),
                      child: Image.network(
                        bookmark.influencerAvatar!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _getPlatformIcon(bookmark.schemeUrl);
                        },
                      ),
                    )
                  : _getPlatformIcon(bookmark.schemeUrl),
            ),
            SizedBox(width: 6.w),
            // 博主名称
            Expanded(
              child: Text(
                bookmark.influencerName,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // 平台logo和创建时间
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 平台logo
                if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty)
                  Builder(
                    builder: (context) {
                      final logoAsset = PlatformUtils.getPlatformLogoFromSchemeUrl(bookmark.schemeUrl);
                      if (logoAsset != null) {
                        return Padding(
                          padding: EdgeInsets.only(right: 4.w),
                          child: Image.asset(
                            logoAsset,
                            width: 12.r,
                            height: 12.r,
                            errorBuilder: (context, error, stackTrace) {
                              return SizedBox.shrink();
                            },
                          ),
                        );
                      }
                      return SizedBox.shrink();
                    },
                  ),
                // 创建时间
                Text(
                  _formatDate(bookmark.createTime),
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: AppColors.textHint,
                  ),
                ),
              ],
            ),
          ],
        ),

        SizedBox(height: 8.h),

        // 内容区域
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 左侧内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  if (bookmark.title != null)
                    Text(
                      bookmark.title!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                  SizedBox(height: 4.h),

                  // 描述
                  if (bookmark.desc != null)
                    Text(
                      bookmark.desc!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),

            SizedBox(width: 12.w),

            // 右侧封面图
            if (bookmark.cover != null && bookmark.cover!.isNotEmpty)
              ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: SizedBox(
                  width: 80.w,
                  height: 80.w,
                  child: Image.network(
                    bookmark.cover!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: AppColors.searchBarBackground,
                        child: Icon(
                          Icons.image_not_supported,
                          color: AppColors.textHint,
                          size: 24.r,
                        ),
                      );
                    },
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  /// 获取平台图标（圆形）
  Widget _getPlatformIcon(String? schemeUrl) {
    if (schemeUrl == null || schemeUrl.isEmpty) {
      return Icon(Icons.person, size: 12.r, color: AppColors.textSecondary);
    }

    final logoAsset = PlatformUtils.getPlatformLogoFromSchemeUrl(schemeUrl);
    if (logoAsset != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(10.r),
        child: Image.asset(
          logoAsset,
          width: 12.r,
          height: 12.r,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Icon(Icons.person, size: 12.r, color: AppColors.textSecondary);
          },
        ),
      );
    }

    return Icon(Icons.person, size: 12.r, color: AppColors.textSecondary);
  }

  /// 格式化日期
  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    }
  }
}
