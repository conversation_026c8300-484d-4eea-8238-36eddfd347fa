import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../constants/app_colors.dart';
import '../services/ai_chat_service.dart';
import '../services/ai_tool_helper.dart';
import '../widgets/custom_toast.dart';
import '../models/ui_chat_message.dart';
import '../components/material_management_dialog.dart';
import '../components/message_materials_widget.dart';
import '../models/material.dart' as material_model;

/// AI聊天组件
///
/// 可以嵌入到任何页面中的独立AI聊天组件
/// 使用AiChatService进行对话管理
class AiChatWidget extends StatefulWidget {
  /// 初始系统提示词
  final String? initialSystemPrompt;

  /// 初始工具列表
  final List<ToolDefinition>? initialTools;

  /// 工具调用回调
  final Future<void> Function(ToolCall, AiChatService)? onToolCall;

  /// 占位符文本
  final String? hintText;

  /// 是否显示头部工具栏
  final bool showHeader;

  /// 是否显示清空按钮
  final bool showClearButton;

  /// 是否显示历史记录数量
  final bool showHistoryCount;

  /// 是否显示头像
  final bool showAvatar;

  /// 最大高度（可选）
  final double? maxHeight;

  const AiChatWidget({
    super.key,
    this.initialSystemPrompt,
    this.initialTools,
    this.onToolCall,
    this.hintText,
    this.showHeader = true,
    this.showClearButton = true,
    this.showHistoryCount = false,
    this.showAvatar = true,
    this.maxHeight,
  });

  @override
  State<AiChatWidget> createState() => _AiChatWidgetState();
}

class _AiChatWidgetState extends State<AiChatWidget> {
  late final AiChatService _aiChatService;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // UI消息列表（用于界面展示，包含工具执行提示等）
  final List<UiChatMessage> _uiMessages = [];

  bool _isTyping = false;
  String _currentAiResponse = '';
  String _currentReasoningContent = '';
  bool _hasInputText = false;
  int? _currentAiMessageIndex;

  @override
  void initState() {
    super.initState();
    _aiChatService = AiChatService();

    // 监听输入框变化
    _messageController.addListener(() {
      final hasText = _messageController.text.trim().isNotEmpty;
      if (hasText != _hasInputText) {
        setState(() {
          _hasInputText = hasText;
        });
      }
    });

    // 设置初始系统提示词
    if (widget.initialSystemPrompt != null) {
      _aiChatService.setSystemPrompt(widget.initialSystemPrompt!);
    } else {
      // 默认的笔记编辑助手提示词
      _aiChatService.setSystemPrompt('''
你是一个专业的笔记编辑助手，具有以下特点：
1. 帮助用户优化笔记内容和结构
2. 提供写作建议和改进意见
3. 协助整理和总结信息
4. 回答与笔记编辑相关的问题
5. 用简洁明了的中文回答
请根据用户的需求提供专业的帮助。
''');
    }

    // 设置初始工具列表
    if (widget.initialTools != null) {
      _aiChatService.setTools(widget.initialTools);
    }
  }

  @override
  void dispose() {
    _aiChatService.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 发送消息
  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if ((text.isEmpty && _selectedMaterials.isEmpty) || _isTyping) return;

    // 构建发送给AI的消息内容
    String messageToAi = text;
    if (_selectedMaterials.isNotEmpty) {
      final materialUrls = _selectedMaterials.map((m) => m.url).join(',');
      if (text.isNotEmpty) {
        messageToAi = '$text，资源地址：$materialUrls';
      } else {
        messageToAi = '资源地址：$materialUrls';
      }
    }

    // 添加用户消息到UI（显示文案和图片）
    setState(() {
      _uiMessages.add(UiChatMessage.user(
        text.isNotEmpty ? text : '发送了${_selectedMaterials.length}个素材',
        materials: List.from(_selectedMaterials),
      ));
      _isTyping = true;
      _currentAiResponse = '';
      _currentReasoningContent = '';
      _currentAiMessageIndex = null;
      // 清空选中的素材
      _selectedMaterials.clear();
    });

    _messageController.clear();
    _scrollToBottom();

    try {
      // 用于拼接完整响应的变量（仅用于控制台打印）
      String completeAiResponse = '';
      String completeReasoningContent = '';

      // 使用流式响应（发送拼接后的消息给AI）
      await for (final chunk in _aiChatService.sendMessage(messageToAi)) {
        if (!mounted) break;

        // 拼接完整响应用于最终打印，但不在这里打印每个chunk
        final content = chunk['content'];
        final reasoningContent = chunk['reasoning_content'];
        final toolCalls = chunk['tool_calls'] as List<Map<String, dynamic>>?;

        if (content != null && content.isNotEmpty) {
          completeAiResponse += content;
        }
        if (reasoningContent != null && reasoningContent.isNotEmpty) {
          completeReasoningContent += reasoningContent;
        }

        // 检查是否有完整的工具调用需要处理
        final completeMessage = chunk['complete_message'] as Map<String, dynamic>?;
        final isComplete = chunk['isComplete'] == true;

        // 只在收到完整消息且包含工具调用时才处理
        if (isComplete && completeMessage != null && widget.onToolCall != null) {
          final completeToolCalls = completeMessage['tool_calls'] as List<Map<String, dynamic>>?;

          if (completeToolCalls != null && completeToolCalls.isNotEmpty) {
            print('AI聊天组件：收到完整的工具调用列表，数量: ${completeToolCalls.length}');

            // 处理所有工具调用
            for (final toolCallData in completeToolCalls) {
              try {
                final toolCall = ToolCall.fromApiResponse(toolCallData);
                print('AI聊天组件：处理工具调用 - ${toolCall.function.name}');

                // 添加工具执行开始提示到UI（不传递给OpenAI）
                _addToolExecutionMessage(
                  toolCall.function.name,
                  ToolExecutionStatus.starting,
                );

                // 执行工具调用
                await widget.onToolCall!(toolCall, _aiChatService);

                // 添加工具执行完成提示到UI（不传递给OpenAI）
                _addToolExecutionMessage(
                  toolCall.function.name,
                  ToolExecutionStatus.completed,
                );
              } catch (e) {
                print('处理工具调用失败: $e');
                // 添加工具执行失败提示到UI
                _addToolExecutionMessage(
                  '工具执行',
                  ToolExecutionStatus.failed,
                );
              }
            }

            // 所有工具执行完成后，自动继续对话获取AI基于工具结果的回复
            try {
              print('AI聊天组件：开始工具执行后的继续对话');

              // 重置当前AI响应状态
              _currentAiResponse = '';
              _currentReasoningContent = '';
              _currentAiMessageIndex = null;

              // 继续对话流
              await for (final continueChunk in _aiChatService.continueAfterToolExecution()) {
                if (!mounted) break;

                final content = continueChunk['content'];
                final reasoningContent = continueChunk['reasoning_content'];
                final continueCompleteMessage = continueChunk['complete_message'] as Map<String, dynamic>?;
                final continueIsComplete = continueChunk['isComplete'] == true;

                // 处理流式内容
                if (content != null && content.isNotEmpty) {
                  _currentAiResponse += content;
                }
                if (reasoningContent != null && reasoningContent.isNotEmpty) {
                  _currentReasoningContent += reasoningContent;
                }

                // 如果是第一个有效chunk，添加AI消息
                if (_currentAiMessageIndex == null && (_currentAiResponse.isNotEmpty || _currentReasoningContent.isNotEmpty)) {
                  setState(() {
                    _uiMessages.add(UiChatMessage.assistant(
                      content: _currentAiResponse,
                      reasoningContent: _currentReasoningContent.isNotEmpty ? _currentReasoningContent : null,
                    ));
                    _currentAiMessageIndex = _uiMessages.length - 1;
                  });
                } else if (_currentAiMessageIndex != null) {
                  // 更新现有AI消息内容
                  setState(() {
                    _uiMessages[_currentAiMessageIndex!] = UiChatMessage.assistant(
                      content: _currentAiResponse,
                      reasoningContent: _currentReasoningContent.isNotEmpty ? _currentReasoningContent : null,
                    );
                  });
                }

                _scrollToBottom();

                // 如果继续对话中又有新的完整工具调用，递归处理
                if (continueIsComplete && continueCompleteMessage != null) {
                  final newCompleteToolCalls = continueCompleteMessage['tool_calls'] as List<Map<String, dynamic>>?;

                  if (newCompleteToolCalls != null && newCompleteToolCalls.isNotEmpty) {
                    print('AI聊天组件：继续对话中收到新的工具调用，数量: ${newCompleteToolCalls.length}');

                    for (final newToolCallData in newCompleteToolCalls) {
                      try {
                        final newToolCall = ToolCall.fromApiResponse(newToolCallData);
                        print('AI聊天组件：处理继续对话中的工具调用 - ${newToolCall.function.name}');

                        _addToolExecutionMessage(
                          newToolCall.function.name,
                          ToolExecutionStatus.starting,
                        );

                        await widget.onToolCall!(newToolCall, _aiChatService);

                        _addToolExecutionMessage(
                          newToolCall.function.name,
                          ToolExecutionStatus.completed,
                        );
                      } catch (e) {
                        print('处理继续对话中的工具调用失败: $e');
                        _addToolExecutionMessage(
                          '工具执行',
                          ToolExecutionStatus.failed,
                        );
                      }
                    }
                  }
                }

                if (continueChunk['isComplete'] == true) {
                  break;
                }
              }
            } catch (e) {
              print('工具执行后继续对话失败: $e');
              // 添加错误提示
              if (mounted) {
                setState(() {
                  _uiMessages.add(UiChatMessage.assistant(
                    content: '工具执行完成，但获取后续回复时出现问题，请重新尝试。',
                  ));
                });
              }
            }
          }
        }

        if (chunk['isComplete'] == true) {
          // 流结束，不需要在这里打印，API层已经处理了完整打印

          setState(() {
            _isTyping = false;
          });
          _scrollToBottom();
          break;
        } else {
          // 处理流式内容，实时更新UI显示
          if (content != null && content.isNotEmpty) {
            _currentAiResponse += content;
          }
          if (reasoningContent != null && reasoningContent.isNotEmpty) {
            _currentReasoningContent += reasoningContent;
          }

          // 如果是第一个有效chunk，添加AI消息并停止typing指示器
          if (_currentAiMessageIndex == null && (_currentAiResponse.isNotEmpty || _currentReasoningContent.isNotEmpty)) {
            setState(() {
              _isTyping = false;
              _uiMessages.add(UiChatMessage.assistant(
                content: _currentAiResponse,
                reasoningContent: _currentReasoningContent.isNotEmpty ? _currentReasoningContent : null,
              ));
              _currentAiMessageIndex = _uiMessages.length - 1;
            });
          } else if (_currentAiMessageIndex != null) {
            // 更新现有AI消息内容
            setState(() {
              _uiMessages[_currentAiMessageIndex!] = UiChatMessage.assistant(
                content: _currentAiResponse,
                reasoningContent: _currentReasoningContent.isNotEmpty ? _currentReasoningContent : null,
              );
            });
          }

          _scrollToBottom();
        }
      }
    } catch (e) {
      print('AI聊天组件：发送消息失败 - $e');
      if (mounted) {
        setState(() {
          // 如果还没有添加AI消息，则添加错误消息
          if (_currentAiMessageIndex == null) {
            _uiMessages.add(UiChatMessage.assistant(
              content: '抱歉，AI服务暂时不可用，请稍后再试。',
            ));
          } else {
            // 如果已经有AI消息，则更新为错误消息
            _uiMessages[_currentAiMessageIndex!] = UiChatMessage.assistant(
              content: '抱歉，AI服务暂时不可用，请稍后再试。',
            );
          }
          _isTyping = false;
        });
        CustomToast.showError('发送消息失败');
      }
    }
  }

  /// 清空对话历史
  void _clearHistory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        title: Text(
          '清空对话',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        content: Text(
          '确定要清空所有对话记录吗？此操作不可撤销。',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              '取消',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14.sp,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _uiMessages.clear();
              });
              _aiChatService.clearHistory();
              CustomToast.show('对话已清空');
            },
            child: Text(
              '清空',
              style: TextStyle(
                color: AppColors.error,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 添加工具执行消息到UI（不传递给OpenAI）
  void _addToolExecutionMessage(String toolName, ToolExecutionStatus status) {
    if (!mounted) return;

    String content;
    switch (status) {
      case ToolExecutionStatus.starting:
        content = _getToolStartMessage(toolName);
        break;
      case ToolExecutionStatus.completed:
        content = _getToolCompleteMessage(toolName);
        break;
      case ToolExecutionStatus.failed:
        content = '工具执行失败，请稍后重试';
        break;
      default:
        content = '正在处理中...';
    }

    setState(() {
      _uiMessages.add(UiChatMessage.toolExecution(
        content: content,
        toolName: toolName,
        status: status,
      ));
    });

    _scrollToBottom();
  }

  /// 获取工具开始执行的友好提示
  String _getToolStartMessage(String toolName) {
    switch (toolName) {
      case 'optimize_note':
        return '开始优化笔记...';
      case 'search_web':
        return '正在搜索相关信息...';
      case 'generate_content':
        return '正在生成内容...';
      default:
        return '正在执行操作...';
    }
  }

  /// 获取工具完成执行的友好提示
  String _getToolCompleteMessage(String toolName) {
    switch (toolName) {
      case 'optimize_note':
        return '笔记优化完成！';
      case 'search_web':
        return '信息搜索完成！';
      case 'generate_content':
        return '内容生成完成！';
      default:
        return '操作执行完成！';
    }
  }

  /// 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 显示素材选择弹窗
  Future<void> _showMaterialDialog() async {
    final selectedMaterial = await MaterialManagementDialog.show(
      context,
      onMaterialSelected: _onMaterialSelected,
      onMaterialRemoved: _onMaterialRemoved,
      multiSelectMode: true,
    );
  }

  /// 选中的素材列表
  List<material_model.Material> _selectedMaterials = [];

  /// 素材选择回调
  void _onMaterialSelected(material_model.Material material) {
    setState(() {
      // 检查是否已经选择过该素材
      if (!_selectedMaterials.any((m) => m.id == material.id)) {
        _selectedMaterials.add(material);
      }
    });
  }

  /// 素材移除回调
  void _onMaterialRemoved(material_model.Material material) {
    setState(() {
      _selectedMaterials.removeWhere((m) => m.id == material.id);
    });
  }

  /// 删除选中的素材
  void _removeMaterial(material_model.Material material) {
    setState(() {
      _selectedMaterials.removeWhere((m) => m.id == material.id);
    });
  }

  /// 构建素材预览区域
  Widget _buildMaterialPreview() {
    return Container(
      height: 72.h, // 增加高度以容纳删除按钮
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 6.w), // 添加水平内边距
        itemCount: _selectedMaterials.length,
        itemBuilder: (context, index) {
          final material = _selectedMaterials[index];
          return _buildMaterialPreviewItem(material);
        },
      ),
    );
  }

  /// 构建单个素材预览项
  Widget _buildMaterialPreviewItem(material_model.Material material) {
    return Container(
      // 增加外边距以确保删除按钮不被截断
      margin: EdgeInsets.all(6.w),
      child: Stack(
        clipBehavior: Clip.none, // 允许子组件超出边界
        children: [
          // 素材图片
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: AppColors.divider,
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: material.isImage
                  ? Image.network(
                      material.url,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppColors.divider,
                          child: Icon(
                            Icons.broken_image,
                            color: AppColors.textHint,
                            size: 20.r,
                          ),
                        );
                      },
                    )
                  : Container(
                      color: AppColors.divider,
                      child: Icon(
                        material.isAudio ? Icons.audiotrack : Icons.videocam,
                        color: AppColors.textHint,
                        size: 20.r,
                      ),
                    ),
            ),
          ),

          // 删除按钮
          Positioned(
            top: -6.h,
            right: -6.w,
            child: GestureDetector(
              onTap: () => _removeMaterial(material),
              child: Container(
                width: 22.w,
                height: 22.h,
                decoration: BoxDecoration(
                  color: AppColors.error,
                  borderRadius: BorderRadius.circular(11.r),
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 14.r,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(UiChatMessage message) {
    final isUser = message.isUser;
    
    // 工具执行消息的特殊样式
    if (message.type == UiChatMessageType.toolExecution) {
      return _buildToolExecutionBubble(message);
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 12.w),
      child: Column(
        crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // AI思考过程（仅对AI消息显示）
          if (!isUser && message.reasoningContent != null && message.reasoningContent!.isNotEmpty)
            Container(
              margin: EdgeInsets.only(bottom: 8.h),
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppColors.warning.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.psychology_outlined,
                        size: 16.r,
                        color: AppColors.warning,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '思考过程',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 6.h),
                  Text(
                    message.reasoningContent!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          
          // 主要消息内容
          Row(
            mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // AI头像
              if (!isUser && widget.showAvatar) ...[
                Container(
                  width: 28.w,
                  height: 28.w,
                  decoration: BoxDecoration(
                    color: AppColors.success,
                    borderRadius: BorderRadius.circular(14.r),
                  ),
                  child: Icon(
                    Icons.smart_toy_outlined,
                    color: Colors.white,
                    size: 16.r,
                  ),
                ),
                SizedBox(width: 8.w),
              ],
              
              // 消息内容
              Flexible(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: isUser ? AppColors.primary : AppColors.searchBarBackground,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 文本内容
                      if (message.content.isNotEmpty)
                        isUser
                            ? Text(
                                message.content,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.white,
                                  height: 1.4,
                                ),
                              )
                            : MarkdownBody(
                                data: message.content,
                                shrinkWrap: true,
                                styleSheet: MarkdownStyleSheet(
                                  p: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.textPrimary,
                                    height: 1.4,
                                  ),
                                  code: TextStyle(
                                    fontSize: 13.sp,
                                    color: AppColors.primary,
                                    fontFamily: 'monospace',
                                    backgroundColor: AppColors.primary.withOpacity(0.1),
                                  ),
                                  codeblockDecoration: BoxDecoration(
                                    color: AppColors.searchBarBackground,
                                    borderRadius: BorderRadius.circular(8.r),
                                    border: Border.all(
                                      color: AppColors.divider.withOpacity(0.3),
                                      width: 1,
                                    ),
                                  ),
                                  codeblockPadding: EdgeInsets.all(12.r),
                                ),
                              ),

                      // 素材显示（仅用户消息）
                      if (isUser && message.materials != null && message.materials!.isNotEmpty)
                        MessageMaterialsWidget(materials: message.materials!),
                    ],
                  ),
                ),
              ),
              
              // 用户头像
              if (isUser && widget.showAvatar) ...[
                SizedBox(width: 8.w),
                Container(
                  width: 28.w,
                  height: 28.w,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(14.r),
                  ),
                  child: Icon(
                    Icons.person_outline,
                    color: Colors.white,
                    size: 16.r,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// 构建工具执行消息气泡
  Widget _buildToolExecutionBubble(UiChatMessage message) {
    Color statusColor;
    IconData statusIcon;

    switch (message.toolStatus) {
      case ToolExecutionStatus.starting:
        statusColor = AppColors.warning;
        statusIcon = Icons.play_circle_outline;
        break;
      case ToolExecutionStatus.executing:
        statusColor = AppColors.primary;
        statusIcon = Icons.refresh;
        break;
      case ToolExecutionStatus.completed:
        statusColor = AppColors.success;
        statusIcon = Icons.check_circle_outline;
        break;
      case ToolExecutionStatus.failed:
        statusColor = AppColors.error;
        statusIcon = Icons.error_outline;
        break;
      default:
        statusColor = AppColors.textSecondary;
        statusIcon = Icons.info_outline;
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 12.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AI头像（与普通AI消息保持一致）
          if (widget.showAvatar) ...[
            Container(
              width: 28.w,
              height: 28.w,
              decoration: BoxDecoration(
                color: AppColors.success,
                borderRadius: BorderRadius.circular(14.r),
              ),
              child: Icon(
                Icons.smart_toy_outlined,
                color: Colors.white,
                size: 16.r,
              ),
            ),
            SizedBox(width: 8.w),
          ],

          // 工具执行消息内容
          Flexible(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: statusColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    statusIcon,
                    size: 16.r,
                    color: statusColor,
                  ),
                  SizedBox(width: 6.w),
                  Flexible(
                    child: Text(
                      message.content,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: statusColor,
                        fontWeight: FontWeight.w500,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建正在输入指示器
  Widget _buildTypingIndicator() {
    if (!_isTyping) return const SizedBox.shrink();

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 12.w),
      child: Row(
        children: [
          if (widget.showAvatar)
            Container(
              width: 28.w,
              height: 28.w,
              decoration: BoxDecoration(
                color: AppColors.success,
                borderRadius: BorderRadius.circular(14.r),
              ),
              child: Icon(
                Icons.smart_toy_outlined,
                color: Colors.white,
                size: 16.r,
              ),
            ),
          if (widget.showAvatar) SizedBox(width: 8.w),
          Flexible(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.searchBarBackground,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 显示思考过程（如果有）
                  if (_currentReasoningContent.isNotEmpty) ...[
                    Container(
                      padding: EdgeInsets.all(8.w),
                      margin: EdgeInsets.only(bottom: 8.h),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color: AppColors.warning.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.psychology_outlined,
                                size: 16.r,
                                color: AppColors.warning,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                '思考过程',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.warning,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 6.h),
                          Text(
                            _currentReasoningContent,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColors.textSecondary,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  // 显示回复内容或加载指示器
                  if (_currentAiResponse.isNotEmpty)
                    MarkdownBody(
                      data: _currentAiResponse,
                      shrinkWrap: true,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textPrimary,
                          height: 1.4,
                        ),
                      ),
                    )
                  else
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 16.w,
                          height: 16.w,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          'AI正在思考...',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final content = Column(
      children: [
        // 标题栏（可选）
        if (widget.showHeader)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                bottom: BorderSide(
                  color: AppColors.divider,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.smart_toy_outlined,
                  color: AppColors.primary,
                  size: 20.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'AI助手',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                if (widget.showHistoryCount) ...[
                  SizedBox(width: 8.w),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      '${_aiChatService.historyCount}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
                const Spacer(),
                if (widget.showClearButton)
                  IconButton(
                    onPressed: _uiMessages.isNotEmpty ? _clearHistory : null,
                    icon: Icon(
                      Icons.clear_all,
                      color: _uiMessages.isNotEmpty ? AppColors.textSecondary : AppColors.divider,
                      size: 20.r,
                    ),
                  ),
              ],
            ),
          ),
        
        // 消息列表
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: EdgeInsets.symmetric(vertical: 8.h),
            itemCount: _uiMessages.length + (_isTyping ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _uiMessages.length && _isTyping) {
                return _buildTypingIndicator();
              }
              return _buildMessageBubble(_uiMessages[index]);
            },
          ),
        ),
        
        // 输入区域
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            border: Border(
              top: BorderSide(
                color: AppColors.divider,
                width: 1,
              ),
            ),
          ),
          child: Column(
            children: [
              // 素材预览区域
              if (_selectedMaterials.isNotEmpty)
                Container(
                  padding: EdgeInsets.fromLTRB(12.w, 12.w, 12.w, 8.w),
                  child: _buildMaterialPreview(),
                ),

              // 输入框区域
              Container(
                padding: EdgeInsets.all(12.w),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _messageController,
                        maxLines: null,
                        textInputAction: TextInputAction.send,
                        onSubmitted: (_) => _sendMessage(),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textPrimary,
                        ),
                        decoration: InputDecoration(
                          hintText: widget.hintText ?? '输入消息...',
                          hintStyle: TextStyle(
                            color: AppColors.textHint,
                            fontSize: 14.sp,
                          ),
                          filled: true,
                          fillColor: AppColors.searchBarBackground,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20.r),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 8.h,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // 素材图标按钮
                    Container(
                      width: 36.w,
                      height: 36.w,
                      decoration: BoxDecoration(
                        color: AppColors.iconColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _showMaterialDialog,
                          borderRadius: BorderRadius.circular(18.r),
                          child: Icon(
                            Icons.folder_outlined,
                            color: AppColors.iconColor,
                            size: 18.r,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // 发送按钮
                    Container(
                      width: 36.w,
                      height: 36.w,
                      decoration: BoxDecoration(
                        color: (_hasInputText || _selectedMaterials.isNotEmpty) && !_isTyping
                            ? AppColors.primary
                            : AppColors.divider,
                        borderRadius: BorderRadius.circular(18.r),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: (_hasInputText || _selectedMaterials.isNotEmpty) && !_isTyping
                              ? _sendMessage
                              : null,
                          borderRadius: BorderRadius.circular(18.r),
                          child: Icon(
                            Icons.send_rounded,
                            color: Colors.white,
                            size: 18.r,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );

    // 如果指定了最大高度，则包装在Container中
    if (widget.maxHeight != null) {
      return Container(
        height: widget.maxHeight,
        child: content,
      );
    }

    return content;
  }
}
