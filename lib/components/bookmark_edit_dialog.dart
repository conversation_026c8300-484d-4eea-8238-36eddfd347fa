import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:aishoucang/api/bookmark_api.dart';
import 'package:aishoucang/api/api_provider.dart';
import 'package:aishoucang/constants/app_colors.dart';
import 'package:aishoucang/widgets/custom_toast.dart';

/// 书签编辑对话框
class BookmarkEditDialog extends StatefulWidget {
  /// 书签项
  final BookmarkItem bookmark;

  /// 更新成功回调
  final Function(BookmarkItem updatedBookmark) onUpdateSuccess;

  /// 构造函数
  const BookmarkEditDialog({
    super.key,
    required this.bookmark,
    required this.onUpdateSuccess,
  });

  @override
  State<BookmarkEditDialog> createState() => _BookmarkEditDialogState();
}

class _BookmarkEditDialogState extends State<BookmarkEditDialog> {
  late TextEditingController _titleController;
  late TextEditingController _descController;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    _titleController = TextEditingController(text: widget.bookmark.title ?? '');
    _descController = TextEditingController(text: widget.bookmark.desc ?? '');
  }

  @override
  void dispose() {
    // 释放控制器
    _titleController.dispose();
    _descController.dispose();
    super.dispose();
  }

  /// 保存更新
  Future<void> _saveChanges() async {
    // 检查是否有变化
    final newTitle = _titleController.text.trim();
    final newDesc = _descController.text.trim();

    // 如果标题和描述都没有变化，直接关闭对话框
    if ((newTitle == (widget.bookmark.title ?? '')) &&
        (newDesc == (widget.bookmark.desc ?? ''))) {
      Navigator.of(context).pop();
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 调用API更新书签
      await ApiProvider().bookmarkApi.updateBookmark(
        id: widget.bookmark.id,
        title: newTitle.isEmpty ? null : newTitle,
        desc: newDesc.isEmpty ? null : newDesc,
      );

      // 创建更新的书签对象
      final updatedBookmark = BookmarkItem(
        id: widget.bookmark.id,
        influencerName: widget.bookmark.influencerName,
        influencerAvatar: widget.bookmark.influencerAvatar,
        cover: widget.bookmark.cover,
        title: newTitle.isEmpty ? null : newTitle,
        desc: newDesc.isEmpty ? null : newDesc,
        schemeUrl: widget.bookmark.schemeUrl,
        parentId: widget.bookmark.parentId,
        createTime: widget.bookmark.createTime,
        updateTime: DateTime.now(),
        tags: widget.bookmark.tags, // 保持原有标签
      );

      // 更新成功，调用回调
      widget.onUpdateSuccess(updatedBookmark);

      // 关闭对话框
      if (mounted) {
        Navigator.of(context).pop();

        // 显示成功提示
        CustomToast.show('书签更新成功');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '更新失败: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: 320.w,
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题区域
              Padding(
                padding: EdgeInsets.fromLTRB(20.w, 24.h, 20.w, 16.h),
                child: Column(
                  children: [
                    // 图标
                    Container(
                      width: 60.r,
                      height: 60.r,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.edit_outlined,
                        color: AppColors.primary,
                        size: 32.r,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      '编辑书签',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),

              // 内容区域
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // 标题输入框
                    TextField(
                      controller: _titleController,
                      decoration: InputDecoration(
                        labelText: '标题',
                        labelStyle: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 14.sp,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                          borderSide: BorderSide(
                            color: AppColors.divider,
                            width: 1,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                          borderSide: BorderSide(
                            color: AppColors.divider,
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                          borderSide: BorderSide(
                            color: AppColors.primary,
                            width: 2,
                          ),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 14.h,
                        ),
                        filled: true,
                        fillColor: AppColors.searchBarBackground,
                      ),
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 1,
                    ),

                    SizedBox(height: 16.h),

                    // 描述输入框
                    TextField(
                      controller: _descController,
                      decoration: InputDecoration(
                        labelText: '描述',
                        labelStyle: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 14.sp,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                          borderSide: BorderSide(
                            color: AppColors.divider,
                            width: 1,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                          borderSide: BorderSide(
                            color: AppColors.divider,
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                          borderSide: BorderSide(
                            color: AppColors.primary,
                            width: 2,
                          ),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 14.h,
                        ),
                        filled: true,
                        fillColor: AppColors.searchBarBackground,
                      ),
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 3,
                    ),

                    // 错误信息
                    if (_errorMessage != null) ...[
                      SizedBox(height: 12.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 8.h,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.error.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: AppColors.error,
                            fontSize: 14.sp,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],

                    SizedBox(height: 24.h),
                  ],
                ),
              ),

              // 按钮区域
              Container(
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: AppColors.divider,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    // 取消按钮
                    Expanded(
                      child: InkWell(
                        onTap: _isLoading ? null : () => Navigator.of(context).pop(),
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(16.r),
                        ),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          decoration: BoxDecoration(
                            border: Border(
                              right: BorderSide(
                                color: AppColors.divider,
                                width: 0.5,
                              ),
                            ),
                          ),
                          child: Text(
                            '取消',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: _isLoading
                                ? AppColors.textSecondary.withOpacity(0.5)
                                : AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    // 保存按钮
                    Expanded(
                      child: InkWell(
                        onTap: _isLoading ? null : _saveChanges,
                        borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(16.r),
                        ),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          child: _isLoading
                              ? SizedBox(
                                  width: 20.r,
                                  height: 20.r,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.primary,
                                    ),
                                  ),
                                )
                              : Text(
                                  '保存',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
