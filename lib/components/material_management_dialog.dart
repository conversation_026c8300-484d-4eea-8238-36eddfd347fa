import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_colors.dart';
import '../models/material.dart' as material_model;
import '../api/api_provider.dart';
import '../native_bridge/native_bridge.dart';
import '../widgets/custom_toast.dart';
import '../components/material_delete_confirmation_dialog.dart';

/// 素材管理弹窗
class MaterialManagementDialog extends StatefulWidget {
  /// 素材选择回调
  final Function(material_model.Material)? onMaterialSelected;
  /// 素材移除回调
  final Function(material_model.Material)? onMaterialRemoved;
  /// 是否支持多选模式
  final bool multiSelectMode;

  const MaterialManagementDialog({
    super.key,
    this.onMaterialSelected,
    this.onMaterialRemoved,
    this.multiSelectMode = false,
  });

  /// 显示素材管理弹窗
  static Future<material_model.Material?> show(
    BuildContext context, {
    Function(material_model.Material)? onMaterialSelected,
    Function(material_model.Material)? onMaterialRemoved,
    bool multiSelectMode = false,
  }) async {
    return await showDialog<material_model.Material>(
      context: context,
      barrierDismissible: true,
      builder: (context) => MaterialManagementDialog(
        onMaterialSelected: onMaterialSelected,
        onMaterialRemoved: onMaterialRemoved,
        multiSelectMode: multiSelectMode,
      ),
    );
  }

  @override
  State<MaterialManagementDialog> createState() => _MaterialManagementDialogState();
}

class _MaterialManagementDialogState extends State<MaterialManagementDialog>
    with SingleTickerProviderStateMixin {
  final _apiProvider = ApiProvider();
  final _imagePicker = ImagePicker();
  final _nativeBridge = NativeBridge();

  late TabController _tabController;
  List<material_model.Material> _materials = [];
  bool _isLoading = false;
  bool _isUploading = false;
  int _currentPage = 1;
  bool _hasMore = true;
  int _selectedType = 1; // 1-图片，2-语音，3-视频

  // 选中的素材ID集合
  Set<String> _selectedMaterialIds = <String>{};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_onTabChanged);
    _loadMaterials();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Tab切换监听
  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _selectedType = _tabController.index + 1;
        _materials.clear();
        _currentPage = 1;
        _hasMore = true;
      });
      _loadMaterials();
    }
  }

  /// 加载素材列表
  Future<void> _loadMaterials() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _apiProvider.materialApi.getMaterialsByType(
        type: _selectedType,
        page: _currentPage,
        pageSize: 20,
      );

      setState(() {
        if (_currentPage == 1) {
          _materials = response.materials;
        } else {
          _materials.addAll(response.materials);
        }
        _hasMore = response.hasMore;
        _currentPage++;
      });
    } catch (e) {
      CustomToast.show('加载素材失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 上传素材
  Future<void> _uploadMaterial() async {
    try {
      XFile? file;
      
      if (_selectedType == 1) {
        // 图片
        file = await _imagePicker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 85,
          maxWidth: 1200,
          maxHeight: 1200,
        );
      } else {
        // 暂时只支持图片上传
        CustomToast.show('暂时只支持图片上传');
        return;
      }

      if (file == null) return;

      setState(() {
        _isUploading = true;
      });

      // 上传到OSS
      final result = await _nativeBridge.uploadFileToOss(
        filePath: file.path,
      );

      if (result['success'] == true && result['ossUrl'] != null) {
        // 添加到素材库
        final material = await _apiProvider.materialApi.addMaterial(
          url: result['ossUrl'],
          type: _selectedType,
        );

        // 添加到列表顶部
        setState(() {
          _materials.insert(0, material);
        });

        CustomToast.show('素材上传成功');
      } else {
        CustomToast.show('上传失败: ${result['error'] ?? "未知错误"}');
      }
    } catch (e) {
      CustomToast.show('上传失败: $e');
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  /// 删除素材
  Future<void> _deleteMaterial(material_model.Material material) async {
    final confirmed = await MaterialDeleteConfirmationDialog.show(context);

    if (!confirmed) return;

    try {
      await _apiProvider.materialApi.deleteMaterial(material.id);
      setState(() {
        _materials.remove(material);
      });
      CustomToast.show('删除成功');
    } catch (e) {
      CustomToast.show('删除失败: $e');
    }
  }

  /// 选择素材
  void _selectMaterial(material_model.Material material) {
    if (widget.multiSelectMode) {
      setState(() {
        if (_selectedMaterialIds.contains(material.id)) {
          // 取消选中
          _selectedMaterialIds.remove(material.id);
        } else {
          // 选中
          _selectedMaterialIds.add(material.id);
          widget.onMaterialSelected?.call(material);
        }
      });
    } else {
      // 单选模式，直接选择并关闭弹窗
      widget.onMaterialSelected?.call(material);
      Navigator.of(context).pop(material);
    }
  }

  /// 判断素材是否已选中
  bool _isMaterialSelected(material_model.Material material) {
    return _selectedMaterialIds.contains(material.id);
  }

  /// 取消选择并关闭弹窗（清除所有选中状态）
  void _cancelAndClose() {
    // 通知AI聊天组件移除所有在此次选择中选中的素材
    for (String materialId in _selectedMaterialIds) {
      final material = _materials.firstWhere((m) => m.id == materialId);
      widget.onMaterialRemoved?.call(material);
    }

    setState(() {
      _selectedMaterialIds.clear();
    });
    Navigator.of(context).pop();
  }

  /// 确认选择并关闭弹窗（保持选中状态）
  void _confirmAndClose() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 40.h),
      child: Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadow.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          children: [
            // 头部
            Container(
              padding: EdgeInsets.fromLTRB(20.w, 20.h, 16.w, 16.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.divider.withOpacity(0.5),
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.folder_outlined,
                    color: AppColors.primary,
                    size: 24.r,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '素材管理',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Spacer(),
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: _cancelAndClose,
                      borderRadius: BorderRadius.circular(20.r),
                      child: Container(
                        width: 40.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: AppColors.iconColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Icon(
                          Icons.close,
                          color: AppColors.textSecondary,
                          size: 20.r,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Tab栏
            Container(
              margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.searchBarBackground,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: Colors.white,
                unselectedLabelColor: AppColors.textSecondary,
                indicator: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                indicatorPadding: EdgeInsets.all(2.w),
                labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
                unselectedLabelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
                dividerColor: Colors.transparent,
                tabs: [
                  Tab(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.image_outlined, size: 16.r),
                          SizedBox(width: 4.w),
                          Text('图片'),
                        ],
                      ),
                    ),
                  ),
                  Tab(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.audiotrack_outlined, size: 16.r),
                          SizedBox(width: 4.w),
                          Text('语音'),
                        ],
                      ),
                    ),
                  ),
                  Tab(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.videocam_outlined, size: 16.r),
                          SizedBox(width: 4.w),
                          Text('视频'),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 素材列表区域
            Expanded(
              child: _buildMaterialList(),
            ),

            // 底部按钮区域
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: AppColors.divider.withOpacity(0.5),
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // 上传按钮
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _isUploading ? null : _uploadMaterial,
                        borderRadius: BorderRadius.circular(12.r),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 14.h),
                          decoration: BoxDecoration(
                            color: _isUploading
                                ? AppColors.divider
                                : AppColors.searchBarBackground,
                            borderRadius: BorderRadius.circular(12.r),
                            border: Border.all(
                              color: _isUploading
                                  ? AppColors.divider
                                  : AppColors.primary.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (_isUploading)
                                SizedBox(
                                  width: 18.r,
                                  height: 18.r,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.textSecondary),
                                  ),
                                )
                              else
                                Icon(
                                  Icons.cloud_upload_outlined,
                                  color: AppColors.primary,
                                  size: 18.r,
                                ),
                              SizedBox(width: 6.w),
                              Text(
                                _isUploading ? '上传中...' : '上传素材',
                                style: TextStyle(
                                  color: _isUploading ? AppColors.textSecondary : AppColors.primary,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // 确认选择按钮
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _confirmAndClose,
                        borderRadius: BorderRadius.circular(12.r),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 14.h),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.primary,
                                AppColors.primary.withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12.r),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.check_circle_outline,
                                color: Colors.white,
                                size: 18.r,
                              ),
                              SizedBox(width: 6.w),
                              Text(
                                '确认选择',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建素材列表
  Widget _buildMaterialList() {
    if (_isLoading && _materials.isEmpty) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    if (_materials.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_outlined,
              size: 48.r,
              color: AppColors.textHint,
            ),
            SizedBox(height: 8.h),
            Text(
              '暂无素材',
              style: TextStyle(
                color: AppColors.textHint,
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: EdgeInsets.fromLTRB(20.w, 16.h, 20.w, 8.h),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.w,
        childAspectRatio: 1,
      ),
      itemCount: _materials.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= _materials.length) {
          // 加载更多
          _loadMaterials();
          return Container(
            decoration: BoxDecoration(
              color: AppColors.searchBarBackground,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          );
        }

        final material = _materials[index];
        return _buildMaterialItem(material);
      },
    );
  }

  /// 构建素材项
  Widget _buildMaterialItem(material_model.Material material) {
    final isSelected = _isMaterialSelected(material);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _selectMaterial(material),
        onLongPress: () => _deleteMaterial(material),
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: AppColors.divider.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Stack(
              children: [
                // 素材内容
                Positioned.fill(
                  child: material.isImage
                      ? Image.network(
                          material.url,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              color: AppColors.searchBarBackground,
                              child: Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                ),
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: AppColors.searchBarBackground,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.broken_image_outlined,
                                    color: AppColors.textHint,
                                    size: 32.r,
                                  ),
                                  SizedBox(height: 4.h),
                                  Text(
                                    '加载失败',
                                    style: TextStyle(
                                      color: AppColors.textHint,
                                      fontSize: 10.sp,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        )
                      : Container(
                          color: AppColors.searchBarBackground,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                material.isAudio
                                    ? Icons.audiotrack_outlined
                                    : Icons.videocam_outlined,
                                color: AppColors.primary,
                                size: 32.r,
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                material.typeName,
                                style: TextStyle(
                                  color: AppColors.textSecondary,
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                ),

                // 选中状态遮罩（多选模式且已选中时显示）
                if (widget.multiSelectMode && isSelected)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 40.r,
                          weight: 800,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
