import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../api/bookmark_api.dart';

/// 收藏项上下文菜单组件
///
/// 用于大图模式下的长按菜单
class BookmarkContextMenu extends StatelessWidget {
  final BookmarkItem bookmark;
  final int index;
  final Function(BookmarkItem)? onEdit;
  final Function(BookmarkItem, int)? onMove;
  final Function(BookmarkItem, int)? onDelete;
  final Function(BookmarkItem)? onTagManage;

  const BookmarkContextMenu({
    super.key,
    required this.bookmark,
    required this.index,
    this.onEdit,
    this.onMove,
    this.onDelete,
    this.onTagManage,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadow.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 编辑菜单项
            if (onEdit != null)
              _buildMenuItem(
                context,
                icon: Icons.edit_outlined,
                title: '编辑',
                onTap: () {
                  Navigator.of(context).pop();
                  onEdit!(bookmark);
                },
              ),

            // 移动菜单项
            if (onMove != null)
              _buildMenuItem(
                context,
                icon: Icons.drive_file_move_outline,
                title: '移动',
                color: Colors.orange,
                onTap: () {
                  Navigator.of(context).pop();
                  onMove!(bookmark, index);
                },
              ),

            // 标签管理菜单项
            if (onTagManage != null)
              _buildMenuItem(
                context,
                icon: Icons.local_offer_outlined,
                title: '标签',
                color: AppColors.primary,
                onTap: () {
                  Navigator.of(context).pop();
                  onTagManage!(bookmark);
                },
              ),

            // 删除菜单项
            if (onDelete != null)
              _buildMenuItem(
                context,
                icon: Icons.delete_outline,
                title: '删除',
                color: AppColors.error,
                onTap: () {
                  Navigator.of(context).pop();
                  onDelete!(bookmark, index);
                },
                isLast: true,
              ),
          ],
        ),
      ),
    );
  }

  /// 构建菜单项
  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
    bool isLast = false,
  }) {
    final itemColor = color ?? AppColors.textPrimary;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(isLast ? 0 : 12.r),
        bottom: Radius.circular(isLast ? 12.r : 0),
      ),
      child: Container(
        width: 300.w,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          border: isLast ? null : Border(
            bottom: BorderSide(
              color: AppColors.divider.withOpacity(0.3),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 22.r,
              color: itemColor,
            ),
            SizedBox(width: 16.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 15.sp,
                color: itemColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示上下文菜单
  static Future<void> show(
    BuildContext context, {
    required BookmarkItem bookmark,
    required int index,
    Function(BookmarkItem)? onEdit,
    Function(BookmarkItem, int)? onMove,
    Function(BookmarkItem, int)? onDelete,
    Function(BookmarkItem)? onTagManage,
  }) async {
    await showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) => Center(
        child: BookmarkContextMenu(
          bookmark: bookmark,
          index: index,
          onEdit: onEdit,
          onMove: onMove,
          onDelete: onDelete,
          onTagManage: onTagManage,
        ),
      ),
    );
  }
}
