import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:reorderable_grid_view/reorderable_grid_view.dart';
import '../../routes.dart';
import '../../models/collection_item.dart';
import '../../constants/app_colors.dart';

// 自定义通知，用于触发收藏夹列表刷新
class RefreshCollectionsNotification extends Notification {}



/// 收藏夹网格组件
class CollectionGrid extends StatelessWidget {
  final List<CollectionItem> items;
  final Function(int oldIndex, int newIndex) onReorder;
  final ScrollController? scrollController;

  const CollectionGrid({
    super.key,
    required this.items,
    required this.onReorder,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 8.h),
      child: ReorderableGridView.builder(
        controller: scrollController,
        physics: const AlwaysScrollableScrollPhysics(), // 确保始终可滚动，支持下拉刷新
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3, // 每行三个
          crossAxisSpacing: 12.w, // 增加水平间距
          mainAxisSpacing: 16.h, // 增加垂直间距
          childAspectRatio: 0.8, // 调整宽高比，使卡片更美观
        ),
        itemCount: items.length,
        cacheExtent: 500, // 增加缓存区域，提高性能
        onReorder: (oldIndex, newIndex) {
          // 直接调用回调，让父组件处理复杂的逻辑
          print('CollectionGrid onReorder: 从 $oldIndex 到 $newIndex');
          onReorder(oldIndex, newIndex);
        },
        itemBuilder: (context, index) {
          // 使用RepaintBoundary减少重绘开销，key使用hashCode保持拖拽稳定性
          return RepaintBoundary(
            key: ValueKey(items[index].hashCode),
            child: _buildCollectionItem(context, index),
          );
        },
        dragWidgetBuilder: (index, child) {
          // 自定义拖动时的样式
          return _buildDragWidget(index);
        },
      ),
    );
  }

  /// 构建收藏夹项目
  Widget _buildCollectionItem(BuildContext context, int index) {
    return Column(
      children: [
        // 收藏夹部分 - 正方形容器
        AspectRatio(
          aspectRatio: 1, // 确保是正方形
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(12.r),
            child: InkWell(
              borderRadius: BorderRadius.circular(12.r),
              onTap: () => _navigateToDetail(context, items[index]),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.searchBarBackground, // 使用更柔和的背景色
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.cardShadow,
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: items[index].cover != null && items[index].cover!.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12.r),
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          // 封面图片
                          Image.network(
                            items[index].cover!,
                            fit: BoxFit.cover,
                            alignment: Alignment.center,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: AppColors.searchBarBackground,
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                  size: 30.r,
                                ),
                              );
                            },
                          ),
                          // 渐变遮罩，使底部文字更清晰
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            height: 40.h,
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withOpacity(0.3),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : Center(
                      child: Icon(
                        Icons.collections_bookmark,
                        size: 36.r,
                        color: AppColors.primary.withOpacity(0.7),
                      ),
                    ),
              ),
            ),
          ),
        ),
        // 收藏夹名称文本
        SizedBox(height: 8.h),
        Text(
          items[index].title,
          style: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// 构建拖拽时的小部件
  Widget _buildDragWidget(int index) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.searchBarBackground,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 1,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: items[index].cover != null && items[index].cover!.isNotEmpty
        ? ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Stack(
              fit: StackFit.expand,
              children: [
                // 封面图片
                Image.network(
                  items[index].cover!,
                  fit: BoxFit.cover,
                  alignment: Alignment.center,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: AppColors.searchBarBackground,
                      child: Icon(
                        Icons.image_not_supported,
                        color: Colors.grey[400],
                        size: 30.r,
                      ),
                    );
                  },
                ),
                // 拖拽时的半透明遮罩
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: AppColors.primary.withOpacity(0.2),
                  ),
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.drag_indicator,
                    color: Colors.white,
                    size: 30.r,
                  ),
                ),
              ],
            ),
          )
        : Container(
            alignment: Alignment.center,
            child: Icon(
              Icons.collections_bookmark,
              size: 36.r,
              color: AppColors.primary.withOpacity(0.7),
            ),
          ),
    );
  }

  /// 导航到详情页
  Future<void> _navigateToDetail(BuildContext context, CollectionItem item) async {
    // 导航到详情页，并等待结果
    final needRefresh = await Navigator.pushNamed(
      context,
      AppRoutes.detail,
      arguments: {'id': item.id},
    );

    // 如果返回true，表示需要刷新收藏夹列表
    if (needRefresh == true) {
      // 使用自定义通知触发刷新
      RefreshCollectionsNotification().dispatch(context);
    }
  }
}