import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../api/bookmark_api.dart';
import '../api/api_provider.dart';
import '../models/tag.dart';
import '../constants/app_colors.dart';
import '../utils/platform_utils.dart';
import '../routes.dart';
import '../native_bridge/native_bridge.dart';
import '../components/bookmark_edit_dialog.dart';
import '../components/bookmark_tag_manager_dialog.dart';
import '../widgets/custom_toast.dart';

/// 书签列表项组件
///
/// 统一的书签列表项UI，基于收藏夹详情页面的设计
class BookmarkListItem extends StatelessWidget {
  final BookmarkItem bookmark;
  final Map<String, Tag> tagMap;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onEdit;
  final bool enableSlide;
  final VoidCallback? onRefresh; // 刷新回调
  final Function(BookmarkItem)? onTagManage; // 标签管理回调

  const BookmarkListItem({
    super.key,
    required this.bookmark,
    this.tagMap = const {},
    this.onTap,
    this.onLongPress,
    this.onEdit,
    this.enableSlide = false,
    this.onRefresh,
    this.onTagManage,
  });

  @override
  Widget build(BuildContext context) {
    final cardWidget = Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 1,
      shadowColor: AppColors.cardShadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap ?? () => _handleDefaultTap(context),
        onLongPress: onLongPress ?? () => _handleDefaultLongPress(context),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(14.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 博主信息行 - 移到顶部
              Row(
                children: [
                  // 博主头像或平台图标 - 使用固定尺寸的容器
                  Container(
                    width: 20.r,
                    height: 20.r,
                    decoration: BoxDecoration(
                      color: AppColors.searchBarBackground,
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    alignment: Alignment.center,
                    child: bookmark.influencerAvatar != null && bookmark.influencerAvatar!.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: Image.network(
                            bookmark.influencerAvatar!,
                            width: 20.r,
                            height: 20.r,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              // 如果头像加载失败，回退到平台图标
                              return bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty
                                ? _getPlatformIcon(bookmark.schemeUrl!)
                                : Icon(Icons.public, size: 12.r, color: AppColors.textSecondary);
                            },
                          ),
                        )
                      : bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty
                        ? _getPlatformIcon(bookmark.schemeUrl!)
                        : Icon(Icons.public, size: 12.r, color: AppColors.textSecondary),
                  ),
                  SizedBox(width: 8.w),

                  // 作者名称
                  Expanded(
                    child: Text(
                      bookmark.influencerName,
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // 平台logo和创建时间
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 平台logo
                      if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty)
                        Builder(
                          builder: (context) {
                            final logoAsset = PlatformUtils.getPlatformLogoFromSchemeUrl(bookmark.schemeUrl);
                            if (logoAsset != null) {
                              return Padding(
                                padding: EdgeInsets.only(right: 4.w),
                                child: Image.asset(
                                  logoAsset,
                                  width: 12.r,
                                  height: 12.r,
                                  errorBuilder: (context, error, stackTrace) {
                                    return SizedBox.shrink();
                                  },
                                ),
                              );
                            }
                            return SizedBox.shrink();
                          },
                        ),
                      // 创建时间
                      Text(
                        _formatDate(bookmark.createTime),
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: AppColors.textHint,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // 标题、描述和封面并排
              if ((bookmark.title != null && bookmark.title!.isNotEmpty) ||
                  (bookmark.desc != null && bookmark.desc!.isNotEmpty) ||
                  (bookmark.cover != null && bookmark.cover!.isNotEmpty))
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题和描述的容器
                    Expanded(
                      flex: 3,
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          // 计算图片的高度（正方形，宽高相等）
                          final imageHeight = constraints.maxWidth / 3 * 0.8; // 因为图片flex是1，文字flex是3

                          return Container(
                            constraints: BoxConstraints(
                              minHeight: imageHeight,
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 标题
                                if (bookmark.title != null && bookmark.title!.isNotEmpty)
                                  Text(
                                    bookmark.title!,
                                    style: TextStyle(
                                      fontSize: 15.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary,
                                      height: 1.3,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),

                                if (bookmark.title != null && bookmark.title!.isNotEmpty)
                                  SizedBox(height: 6.h),

                                // 描述
                                if (bookmark.desc != null && bookmark.desc!.isNotEmpty)
                                  Text(
                                    bookmark.desc!,
                                    style: TextStyle(
                                      fontSize: 13.sp,
                                      color: AppColors.textSecondary,
                                      height: 1.3,
                                    ),
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                  ),

                                // 标签显示
                                if (bookmark.tags.isNotEmpty) ...[
                                  SizedBox(height: 8.h),
                                  _buildBookmarkTags(bookmark.tags),
                                ],
                              ],
                            ),
                          );
                        }
                      ),
                    ),

                    // 添加间距
                    if (bookmark.cover != null && bookmark.cover!.isNotEmpty)
                      SizedBox(width: 12.w),

                    // 封面图片 - 保持宽度，高度设置为宽度相等
                    if (bookmark.cover != null && bookmark.cover!.isNotEmpty)
                      Expanded(
                        flex: 1,
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: Container(
                                width: constraints.maxWidth,
                                height: constraints.maxWidth * 0.8,
                                decoration: BoxDecoration(
                                  color: AppColors.searchBarBackground,
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Image.network(
                                  bookmark.cover!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: AppColors.searchBarBackground,
                                      alignment: Alignment.center,
                                      child: Icon(
                                        Icons.image_not_supported,
                                        size: 18.sp,
                                        color: AppColors.textHint
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          }
                        ),
                      ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );

    // 如果启用滑动功能，包装Slidable组件
    if (enableSlide) {
      return Slidable(
        key: Key(bookmark.id),
        endActionPane: ActionPane(
          motion: const DrawerMotion(),
          extentRatio: 0.6, // 增加滑动区域宽度以容纳三个按钮
          children: [
            Expanded(
              child: Container(
                margin: EdgeInsets.only(bottom: 12.h),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12.r),
                    bottomRight: Radius.circular(12.r),
                  ),
                ),
                child: Row(
                  children: [
                    // 移动按钮
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _showMoveDialog(context),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(0),
                              bottomRight: Radius.circular(0),
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.drive_file_move_outline,
                                color: Colors.white,
                                size: 20.r,
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                '移动',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // 标签管理按钮
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _showTagManagerDialog(context),
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.primary, // 标签管理按钮的蓝色背景
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(0), // 中间按钮不要右圆角
                              bottomRight: Radius.circular(0),
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
                            children: [
                              Icon(
                                Icons.local_offer_outlined,
                                color: Colors.white,
                                size: 20.r,
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                '标签',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // 删除按钮
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _showDeleteDialog(context),
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(12.r),
                              bottomRight: Radius.circular(12.r),
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.delete_outline,
                                color: Colors.white,
                                size: 20.r,
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                '删除',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        child: cardWidget,
      );
    }

    return cardWidget;
  }

  /// 默认点击处理
  void _handleDefaultTap(BuildContext context) {
    if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty) {
      // 检查是否是http/https开头的图片链接
      final schemeUrl = bookmark.schemeUrl!;
      final isImageUrl = schemeUrl.startsWith('http') &&
          (schemeUrl.endsWith('.jpg') ||
           schemeUrl.endsWith('.jpeg') ||
           schemeUrl.endsWith('.png') ||
           schemeUrl.endsWith('.gif') ||
           schemeUrl.endsWith('.webp') ||
           schemeUrl.contains('.jpg?') ||
           schemeUrl.contains('.jpeg?') ||
           schemeUrl.contains('.png?') ||
           schemeUrl.contains('.gif?') ||
           schemeUrl.contains('.webp?'));

      if (isImageUrl) {
        // 如果是图片链接，跳转到图片预览页面
        Navigator.pushNamed(
          context,
          AppRoutes.imagePreview,
          arguments: {'imageUrl': schemeUrl},
        );
      } else {
        // 如果不是图片链接，使用原来的逻辑打开scheme URL
        final nativeBridge = NativeBridge();

        // 先检查悬浮窗权限
        nativeBridge.checkOverlayPermission().then((hasPermission) {
          if (!hasPermission) {
            // 如果没有悬浮窗权限，提示用户并引导开启
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('需要悬浮窗权限才能显示返回按钮'),
                action: SnackBarAction(
                  label: '去开启',
                  onPressed: () {
                    nativeBridge.openOverlayPermissionSettings();
                  },
                ),
                duration: const Duration(seconds: 5),
              ),
            );
          }

          // 无论是否有权限，都尝试打开链接
          nativeBridge.openSchemeUrl(schemeUrl, showReturnButton: true).then((success) {
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('无法打开链接，可能没有安装对应的应用'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          });
        });
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('此内容没有可打开的链接'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// 默认长按处理
  void _handleDefaultLongPress(BuildContext context) {
    if (onEdit != null) {
      onEdit!();
    } else {
      // 显示编辑对话框
      showDialog(
        context: context,
        builder: (context) => BookmarkEditDialog(
          bookmark: bookmark,
          onUpdateSuccess: (updatedBookmark) {
            // 更新成功后的处理可以由父组件决定
          },
        ),
      );
    }
  }

  /// 格式化日期
  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    }
  }

  /// 构建书签标签显示
  Widget _buildBookmarkTags(List<String> tags) {
    // 最多显示4个标签，超出显示+数量
    final displayTags = tags.take(4).toList();
    final hasMore = tags.length > 4;

    return Wrap(
      spacing: 4.w,
      runSpacing: 4.h,
      children: [
        ...displayTags.asMap().entries.map((entry) {
          final index = entry.key;
          final tag = entry.value;
          return _buildSmallTag(tag, index);
        }),
        if (hasMore)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: AppColors.searchBarBackground,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              '+${tags.length - 4}',
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建小尺寸标签
  Widget _buildSmallTag(String tag, int colorIndex) {
    Color backgroundColor;
    Color textColor;

    // 尝试从标签映射中获取真实颜色
    final tagObject = tagMap[tag];
    if (tagObject != null) {
      // 使用服务端返回的颜色
      backgroundColor = _parseColor(tagObject.backgroundColor);
      textColor = _parseColor(tagObject.textColor);
    } else {
      // 如果没有找到对应的标签对象，使用默认颜色配置
      final List<Color> tagColors = [
        const Color(0xFFFFE4E1), // 薄雾玫瑰
        const Color(0xFFFFF0F5), // 薰衣草腮红
        const Color(0xFFE0F2E9), // 薄荷绿
        const Color(0xFFE0F7FA), // 淡青色
        const Color(0xFFFFF5E1), // 香草色
        const Color(0xFFF5F0FF), // 淡紫色
        const Color(0xFFFFFDE7), // 柠檬雪纺
        const Color(0xFFFCE4EC), // 粉红色
        const Color(0xFFF3E5F5), // 兰花色
        const Color(0xFFE8F5E9), // 蜜瓜绿
        const Color(0xFFFBE9E7), // 桃花色
        const Color(0xFFE1F5FE), // 天空蓝
        const Color(0xFFFFF8E1), // 香槟色
        const Color(0xFFF9FBE7), // 青柠色
        const Color(0xFFF1F8E9), // 春绿色
        const Color(0xFFE8EAF6), // 淡靛蓝
        const Color(0xFFFFF3E0), // 杏仁色
        const Color(0xFFE0F2F1), // 薄荷奶昔
        const Color(0xFFF8BBD9), // 樱花粉
        const Color(0xFFE1BEE7), // 紫罗兰
      ];

      final List<Color> textColors = [
        const Color(0xFF8B4513), // 深棕色
        const Color(0xFF8B008B), // 深洋红
        const Color(0xFF006400), // 深绿色
        const Color(0xFF008B8B), // 深青色
        const Color(0xFFFF8C00), // 深橙色
        const Color(0xFF4B0082), // 靛蓝色
        const Color(0xFFB8860B), // 深金黄
        const Color(0xFFDC143C), // 深红色
        const Color(0xFF9932CC), // 深兰花紫
        const Color(0xFF228B22), // 森林绿
        const Color(0xFFFF6347), // 番茄红
        const Color(0xFF4682B4), // 钢蓝色
        const Color(0xFFDAA520), // 金黄色
        const Color(0xFF32CD32), // 酸橙绿
        const Color(0xFF9ACD32), // 黄绿色
        const Color(0xFF6A5ACD), // 石板蓝
        const Color(0xFFFF7F50), // 珊瑚色
        const Color(0xFF20B2AA), // 浅海绿
        const Color(0xFFFF1493), // 深粉红
        const Color(0xFF9370DB), // 中紫色
      ];

      backgroundColor = tagColors[colorIndex % tagColors.length];
      textColor = textColors[colorIndex % textColors.length];
    }

    final displayText = tag.length > 5 ? '${tag.substring(0, 5)}...' : tag;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '🏷️',
            style: TextStyle(fontSize: 8.sp),
          ),
          SizedBox(width: 2.w),
          Text(
            displayText,
            style: TextStyle(
              fontSize: 10.sp,
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 解析十六进制颜色字符串为Color对象
  Color _parseColor(String colorString) {
    try {
      // 移除可能的 # 前缀
      String hexColor = colorString.replaceAll('#', '');

      // 如果是6位，添加FF作为alpha值
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }

      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // 如果解析失败，返回默认颜色
      return const Color(0xFF1890FF);
    }
  }

  /// 获取平台图标
  Widget _getPlatformIcon(String schemeUrl) {
    String platformType = _getPlatformType(schemeUrl);

    switch (platformType) {
      case 'xiaohongshu':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/xiaohongshu.png', width: 15.r, height: 15.r),
        );
      case 'bilibili':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/bilibili.png', width: 15.r, height: 15.r),
        );
      case 'douyin':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/douyin.png', width: 15.r, height: 15.r),
        );
      case 'wechat':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/wechat.png', width: 15.r, height: 15.r),
        );
      case 'pinduoduo':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/Pinduoduo.png', width: 15.r, height: 15.r),
        );
      case 'taobao':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/taobao.png', width: 15.r, height: 15.r),
        );
      case 'jingdong':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/jingdong.png', width: 15.r, height: 15.r),
        );
      default:
        return Icon(Icons.public, size: 12.r, color: AppColors.textSecondary);
    }
  }

  /// 识别平台类型
  String _getPlatformType(String schemeUrl) {
    if (schemeUrl.startsWith('xhsdiscover://')) {
      return 'xiaohongshu';
    } else if (schemeUrl.startsWith('bilibili://')) {
      return 'bilibili';
    } else if (schemeUrl.startsWith('snssdk1128://')) {
      return 'douyin';
    } else if (schemeUrl.startsWith('weixin://') || _isWechatPublicAccountUrl(schemeUrl)) {
      return 'wechat';
    } else if (schemeUrl.startsWith('pinduoduo://') || schemeUrl.contains('yangkeduo.com')) {
      return 'pinduoduo';
    } else if (schemeUrl.startsWith('taobao://') || schemeUrl.contains('taobao.com')) {
      return 'taobao';
    } else if (schemeUrl.startsWith('openapp.jdmobile://') || schemeUrl.contains('jd.com')) {
      return 'jingdong';
    } else {
      return 'unknown';
    }
  }

  /// 判断是否为微信公众号链接
  bool _isWechatPublicAccountUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host == 'mp.weixin.qq.com';
    } catch (e) {
      return false;
    }
  }

  /// 显示移动对话框
  void _showMoveDialog(BuildContext context) async {
    try {
      final apiProvider = ApiProvider();
      // 获取收藏夹列表
      final response = await apiProvider.favoritesApi.getUserFavorites();

      if (!response.containsKey('favorites')) {
        CustomToast.show('获取收藏夹列表失败');
        return;
      }

      final favorites = response['favorites'] as List<dynamic>;
      // 过滤掉当前书签所在的收藏夹
      final otherFavorites = favorites.where((fav) => fav['id'] != bookmark.parentId).toList();

      if (otherFavorites.isEmpty) {
        CustomToast.show('没有其他收藏夹可以移动到');
        return;
      }

      // 显示选择对话框
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(
              '移动到收藏夹',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            content: Container(
              width: double.maxFinite,
              height: 300.h,
              child: ListView.builder(
                itemCount: otherFavorites.length,
                itemBuilder: (context, index) {
                  final favorite = otherFavorites[index];
                  return ListTile(
                    leading: Container(
                      width: 40.r,
                      height: 40.r,
                      decoration: BoxDecoration(
                        color: AppColors.searchBarBackground,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: favorite['cover'] != null && favorite['cover'].toString().isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: Image.network(
                              favorite['cover'],
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.collections_bookmark,
                                  size: 20.r,
                                  color: AppColors.primary.withOpacity(0.7),
                                );
                              },
                            ),
                          )
                        : Icon(
                            Icons.collections_bookmark,
                            size: 20.r,
                            color: AppColors.primary.withOpacity(0.7),
                          ),
                    ),
                    title: Text(
                      favorite['name'] ?? '未命名收藏夹',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                      _moveBookmarkToFavorite(context, favorite['id'], favorite['name']);
                    },
                  );
                },
              ),
            ),
            backgroundColor: AppColors.surface,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          );
        },
      );
    } catch (e) {
      print('获取收藏夹列表失败: $e');
      CustomToast.show('获取收藏夹列表失败');
    }
  }

  /// 移动书签到指定收藏夹
  void _moveBookmarkToFavorite(BuildContext context, String targetFavoriteId, String targetFavoriteName) async {
    try {
      // 第一步：先在目标收藏夹中新增收藏项
      await _addBookmarkToTargetFavorite(targetFavoriteId);

      // 第二步：添加成功后，再从当前收藏夹删除这个收藏项
      final bookmarkApi = BookmarkApi(ApiProvider().apiClient);
      await bookmarkApi.deleteBookmark(bookmark.id);

      // 第三步：显示移动成功提示
      CustomToast.show('已移动到 "$targetFavoriteName"');

      // 触发刷新回调
      if (onRefresh != null) {
        onRefresh!();
      }

      print('移动书签成功: ${bookmark.title} 到收藏夹 $targetFavoriteName (ID: $targetFavoriteId)');
    } catch (e) {
      print('移动书签失败: $e');
      CustomToast.show('移动失败: ${e.toString()}');
    }
  }

  /// 添加书签到目标收藏夹
  Future<void> _addBookmarkToTargetFavorite(String targetFavoriteId) async {
    try {
      // 使用Flutter侧的API添加书签
      final data = <String, dynamic>{
        'parent_id': targetFavoriteId,
        'influencer_name': bookmark.influencerName,
        'title': bookmark.title,
        'scheme_url': bookmark.schemeUrl ?? '',
      };

      // 添加可选字段
      if (bookmark.influencerAvatar != null && bookmark.influencerAvatar!.isNotEmpty) {
        data['influencer_avatar'] = bookmark.influencerAvatar;
      }
      if (bookmark.cover != null && bookmark.cover!.isNotEmpty) {
        data['cover'] = bookmark.cover;
      }
      if (bookmark.desc != null && bookmark.desc!.isNotEmpty) {
        data['desc'] = bookmark.desc;
      }

      // 添加标签信息，确保移动时不丢失标签
      if (bookmark.tags.isNotEmpty) {
        data['tags'] = bookmark.tags;
      }

      final apiProvider = ApiProvider();
      final response = await apiProvider.apiClient.post('/bookmark/add', data);

      if (response['code'] != 0) {
        throw Exception(response['message'] ?? '添加书签失败');
      }

      print('成功添加书签到目标收藏夹: ${bookmark.title}，包含标签: ${bookmark.tags}');
    } catch (e) {
      print('添加书签到目标收藏夹失败: $e');
      throw Exception('添加书签到目标收藏夹失败: $e');
    }
  }

  /// 显示删除确认对话框
  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            '确认删除',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          content: Text(
            '确认要删除这个收藏吗？',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          backgroundColor: AppColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteBookmark(context);
              },
              child: Text(
                '确定',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 显示标签管理对话框
  void _showTagManagerDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) => BookmarkTagManagerDialog(
        bookmark: bookmark,
        onTagsUpdated: (updatedBookmark) {
          // 触发刷新回调
          if (onRefresh != null) {
            onRefresh!();
          }
        },
      ),
    );
  }

  /// 删除书签
  void _deleteBookmark(BuildContext context) async {
    try {
      final bookmarkApi = BookmarkApi(ApiProvider().apiClient);
      await bookmarkApi.deleteBookmark(bookmark.id);

      CustomToast.show('删除成功');

      // 触发刷新回调
      if (onRefresh != null) {
        onRefresh!();
      }
    } catch (e) {
      CustomToast.show('删除失败: ${e.toString()}');
    }
  }
}