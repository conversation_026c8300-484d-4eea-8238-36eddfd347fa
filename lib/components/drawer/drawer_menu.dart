import 'package:flutter/material.dart';
import 'menu_item.dart';
import '../../routes.dart';

/// 抽屉菜单列表组件
class DrawerMenu extends StatelessWidget {
  const DrawerMenu({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120, // 增加高度，因为现在有两个菜单项
      child: Column(
        children: [
          _buildMenuItem(
            context,
            icon: Icons.devices,
            title: '支持的平台',
            onTap: () => _handleSupportedPlatformsTap(context),
          ),
          _buildMenuItem(
            context,
            icon: Icons.system_update,
            title: '版本更新',
            onTap: () => _handleVersionUpdateTap(context),
          ),
        ],
      ),
    );
  }

  /// 构建菜单项
  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return OptimizedMenuItem(
      icon: icon,
      title: title,
      onTap: onTap,
    );
  }

  /// 处理支持的平台点击事件
  void _handleSupportedPlatformsTap(BuildContext context) {
    Navigator.pop(context);
    // 跳转到支持的平台页面
    Navigator.of(context).pushNamed(AppRoutes.supportedPlatforms);
  }

  /// 处理版本更新点击事件
  void _handleVersionUpdateTap(BuildContext context) {
    Navigator.pop(context);
    // 跳转到版本更新页面
    Navigator.of(context).pushNamed(AppRoutes.versionUpdate);
  }
}
