import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../routes.dart';
import '../../constants/app_colors.dart';

/// 抽屉头部组件 - 已登录状态
class LoggedInHeader extends StatelessWidget {
  final String? phone;
  final String? avatarUrl;
  final String? nickname;

  const LoggedInHeader({
    Key? key,
    required this.phone,
    this.avatarUrl,
    this.nickname,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 头像
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.primaryLight.withOpacity(0.15),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 30.r,
            backgroundColor: Colors.transparent,
            backgroundImage: avatarUrl != null && avatarUrl!.isNotEmpty
                ? NetworkImage(avatarUrl!)
                : null,
            child: avatarUrl != null && avatarUrl!.isNotEmpty
                ? null
                : Icon(
                    Icons.person,
                    size: 35.r,
                    color: AppColors.primary,
                  ),
          ),
        ),
        SizedBox(width: 15.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 优先显示昵称，其次是手机号
            Text(
              nickname ?? phone ?? '未知用户',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            // SizedBox(height: 4.h),
            // Text(
            //   '查看并编辑个人资料',
            //   style: TextStyle(
            //     fontSize: 14.sp,
            //     color: Colors.grey[600],
            //   ),
            // ),
          ],
        ),
      ],
    );
  }
}

/// 抽屉头部组件 - 未登录状态
class LoggedOutHeader extends StatelessWidget {
  const LoggedOutHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          // 关闭抽屉
          Navigator.of(context).pop();
          // 跳转到微信登录页面
          Navigator.of(context).pushNamed(AppRoutes.wechatLogin);
        },
        icon: const Icon(Icons.login),
        label: const Text('登录/注册'),
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor),
          foregroundColor: MaterialStateProperty.all(Colors.white),
          padding: MaterialStateProperty.all(EdgeInsets.symmetric(vertical: 12.h)),
          shape: MaterialStateProperty.all(RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          )),
        ),
      ),
    );
  }
}
