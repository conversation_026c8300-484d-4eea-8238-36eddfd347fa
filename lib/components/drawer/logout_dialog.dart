import 'package:flutter/material.dart';
import '../../routes.dart';

/// 退出登录确认对话框
class LogoutConfirmationDialog extends StatelessWidget {
  final VoidCallback onLogout;

  const LogoutConfirmationDialog({
    Key? key,
    required this.onLogout,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('确认退出'),
      content: const Text('您确定要退出登录吗？'),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(); // 关闭对话框
          },
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(); // 关闭对话框
            Navigator.of(context).pop(); // 关闭抽屉

            // 执行退出登录回调
            onLogout();

            // 退出登录，跳转到微信登录页面
            Navigator.of(context).pushNamedAndRemoveUntil(
              AppRoutes.wechatLogin,
              (route) => false, // 清除所有路由历史
            );

            // 显示退出成功提示
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('您已退出登录')),
            );
          },
          child: const Text(
            '退出',
            style: TextStyle(color: Colors.red),
          ),
        ),
      ],
    );
  }
}
