import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../pages/debug_page.dart';
import '../../native_bridge/config_bridge.dart';

/// 调试页面按钮组件
class HomeButton extends StatelessWidget {
  final VoidCallback? onPressed;

  const HomeButton({
    Key? key,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 只在开发环境显示调试按钮
    final String appEnv = ConfigBridge.getString('app_env');
    if (appEnv != 'develop') {
      return const SizedBox.shrink(); // 非开发环境不显示调试按钮
    }

    return RepaintBoundary(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: onPressed ?? () => _handleHomeButtonTap(context),
            icon: const Icon(Icons.bug_report_outlined),
            label: const Text('调试页面'),
            style: _buildButtonStyle(),
          ),
        ),
      ),
    );
  }

  /// 处理调试页面按钮点击
  void _handleHomeButtonTap(BuildContext context) {
    // 先关闭抽屉
    Navigator.pop(context);

    // 然后导航到调试页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DebugPage(title: '调试页面'),
      ),
    );
  }

  /// 构建按钮样式
  ButtonStyle _buildButtonStyle() {
    return ButtonStyle(
      backgroundColor: MaterialStateProperty.all(Colors.orange[50]),
      foregroundColor: MaterialStateProperty.all(Colors.orange[700]),
      padding: MaterialStateProperty.all(EdgeInsets.symmetric(vertical: 12.h)),
      shape: MaterialStateProperty.all(RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      )),
      // 添加按钮动画效果的优化
      animationDuration: const Duration(milliseconds: 100),
      overlayColor: MaterialStateProperty.resolveWith<Color?>(
        (Set<MaterialState> states) {
          if (states.contains(MaterialState.pressed)) {
            return Colors.orange.withOpacity(0.1);
          }
          return null;
        },
      ),
    );
  }
}
