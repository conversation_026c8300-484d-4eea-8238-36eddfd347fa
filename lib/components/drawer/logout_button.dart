import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'logout_dialog.dart';

/// 退出登录按钮组件
class LogoutButton extends StatelessWidget {
  final VoidCallback onLogout;
  
  const LogoutButton({
    Key? key,
    required this.onLogout,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _showLogoutConfirmationDialog(context),
            icon: const Icon(Icons.exit_to_app),
            label: const Text('退出登录'),
            style: _buildButtonStyle(),
          ),
        ),
      ),
    );
  }
  
  /// 显示退出确认对话框
  void _showLogoutConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => LogoutConfirmationDialog(
        onLogout: onLogout,
      ),
    );
  }
  
  /// 构建按钮样式
  ButtonStyle _buildButtonStyle() {
    return ButtonStyle(
      backgroundColor: MaterialStateProperty.all(Colors.red[50]),
      foregroundColor: MaterialStateProperty.all(Colors.red),
      padding: MaterialStateProperty.all(EdgeInsets.symmetric(vertical: 12.h)),
      shape: MaterialStateProperty.all(RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      )),
      // 添加按钮动画效果的优化
      animationDuration: const Duration(milliseconds: 100),
      overlayColor: MaterialStateProperty.resolveWith<Color?>(
        (Set<MaterialState> states) {
          if (states.contains(MaterialState.pressed)) {
            return Colors.red.withOpacity(0.1);
          }
          return null;
        },
      ),
    );
  }
}
