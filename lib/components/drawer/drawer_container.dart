import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 抽屉容器组件，封装抽屉的基本布局和样式
class DrawerContainer extends StatelessWidget {
  final double width;
  final List<Widget> children;
  
  const DrawerContainer({
    Key? key,
    required this.width,
    required this.children,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: AnimatedContainer(
        // 添加动画效果，使抽屉打开更加流畅
        duration: const Duration(milliseconds: 100),
        curve: Curves.fastLinearToSlowEaseIn,
        child: Material(
          elevation: 16.0,
          child: SizedBox(
            width: width,
            child: ColoredBox(
              color: Colors.white,
              child: Safe<PERSON>rea(
                child: Column(
                  children: children,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
