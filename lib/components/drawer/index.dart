import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../api/api_provider.dart';
import '../../services/storage_service.dart';
import '../../native_bridge/native_bridge.dart';
import 'drawer_header.dart';
import 'drawer_menu.dart';
import 'drawer_container.dart';
import 'logout_button.dart';
import 'home_button.dart';

/// 应用抽屉组件 - 单例模式实现
class AppDrawer extends StatefulWidget {
  final double? width;

  /// 私有构造函数，防止外部创建新实例
  const AppDrawer._internal({
    super.key,
    this.width,
  });

  /// 单例实例
  static final AppDrawer _instance = AppDrawer._internal();

  /// 工厂构造函数，始终返回同一个实例
  factory AppDrawer({Key? key, double? width}) {
    return _instance;
  }

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  final _storageService = StorageService();
  bool _isLoggedIn = false;
  String? _phone;
  String? _avatarUrl;
  String? _nickname;
  bool _initialized = false;

  @override
  Widget build(BuildContext context) {
    // 如果还没有初始化，则检查登录状态
    if (!_initialized) {
      _checkLoginStatus();
    }

    // 缓存MediaQuery值避免重复获取
    final mediaQuery = MediaQuery.of(context);
    final drawerWidth = widget.width ?? mediaQuery.size.width * 0.8;

    return DrawerContainer(
      width: drawerWidth,
      children: [
        // 抽屉头部 - 根据登录状态显示不同内容
        RepaintBoundary(
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: _isLoggedIn
              ? LoggedInHeader(
                  phone: _phone,
                  avatarUrl: _avatarUrl,
                  nickname: _nickname,
                )
              : const LoggedOutHeader(),
          ),
        ),

        const Divider(),

        // 菜单列表
        const DrawerMenu(),

        const Spacer(),

        // 底部按钮 - 返回首页
        const HomeButton(),

        // 退出登录按钮 - 仅在登录状态下显示
        if (_isLoggedIn)
          LogoutButton(
            onLogout: _handleLogout,
          ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    // 只在第一次创建时检查登录状态
    if (!_initialized) {
      _checkLoginStatus();
    }
  }

  /// 检查登录状态
  Future<void> _checkLoginStatus() async {
    final isLoggedIn = await _storageService.isLoggedIn();
    final phone = await _storageService.getPhone();
    final avatarUrl = await _storageService.getAvatarUrl();
    final nickname = await _storageService.getNickname();

    if (mounted) {
      setState(() {
        _isLoggedIn = isLoggedIn;
        _phone = phone;
        _avatarUrl = avatarUrl;
        _nickname = nickname;
        _initialized = true;
      });
    }
  }

  /// 处理退出登录事件
  void _handleLogout() async {
    try {
      // 1. 清除API客户端中的认证信息
      await ApiProvider().clearAuthentication();

      // 2. 清除本地存储中的用户信息
      await _storageService.clearUserInfo();

      // 3. 清除原生端的认证信息
      final nativeBridge = NativeBridge();
      await nativeBridge.putString('auth_token', '');
      await nativeBridge.putString('user_id', '');
      await nativeBridge.putString('user_phone', '');

      // 4. 更新抽屉状态
      if (mounted) {
        setState(() {
          _isLoggedIn = false;
          _phone = null;
          _avatarUrl = null;
          _nickname = null;
        });
      }

      print('退出登录成功，所有认证信息已清除');
    } catch (e) {
      print('退出登录时出错: $e');
    }
  }

  /// 强制刷新抽屉状态
  void refreshDrawerState() {
    _checkLoginStatus();
  }


}