/// 笔记数据模型
class NoteItem {
  /// 笔记ID
  final String id;

  /// 父级ID
  final String? parentId;

  /// 用户ID
  final String userId;

  /// 笔记标题
  final String title;

  /// 封面图片URL
  final String? cover;

  /// 描述
  final String? desc;

  /// 内容
  final String? content;

  /// HTML内容
  final String html;

  /// 创建时间
  final String createTime;

  /// 更新时间
  final String updateTime;

  NoteItem({
    required this.id,
    this.parentId,
    required this.userId,
    required this.title,
    this.cover,
    this.desc,
    this.content,
    required this.html,
    required this.createTime,
    required this.updateTime,
  });

  /// 从JSON创建NoteItem实例
  factory NoteItem.fromJson(Map<String, dynamic> json) {
    return NoteItem(
      id: json['id']?.toString() ?? '',
      parentId: json['parent_id']?.toString(),
      userId: json['user_id']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      cover: json['cover']?.toString(),
      desc: json['desc']?.toString(),
      content: json['content']?.toString(),
      html: json['html']?.toString() ?? '',
      createTime: json['create_time']?.toString() ?? '',
      updateTime: json['update_time']?.toString() ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parent_id': parentId,
      'user_id': userId,
      'title': title,
      'cover': cover,
      'desc': desc,
      'content': content,
      'html': html,
      'create_time': createTime,
      'update_time': updateTime,
    };
  }

  /// 获取格式化的创建时间
  DateTime? get createdAt {
    try {
      return DateTime.parse(createTime);
    } catch (e) {
      return null;
    }
  }

  /// 获取格式化的更新时间
  DateTime? get updatedAt {
    try {
      return DateTime.parse(updateTime);
    } catch (e) {
      return null;
    }
  }

  /// 获取显示用的内容（优先使用desc，其次content）
  String get displayContent {
    if (desc != null && desc!.isNotEmpty) {
      return desc!;
    }
    if (content != null && content!.isNotEmpty) {
      return content!;
    }
    return '';
  }

  /// 复制并修改部分属性
  NoteItem copyWith({
    String? id,
    String? parentId,
    String? userId,
    String? title,
    String? cover,
    String? desc,
    String? content,
    String? html,
    String? createTime,
    String? updateTime,
  }) {
    return NoteItem(
      id: id ?? this.id,
      parentId: parentId ?? this.parentId,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      cover: cover ?? this.cover,
      desc: desc ?? this.desc,
      content: content ?? this.content,
      html: html ?? this.html,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }
}

/// 笔记列表响应数据模型
class NoteListResponse {
  /// 笔记列表
  final List<NoteItem> notes;

  /// 总记录数
  final int total;

  /// 当前页码
  final int page;

  /// 每页数量
  final int pageSize;

  NoteListResponse({
    required this.notes,
    required this.total,
    required this.page,
    required this.pageSize,
  });

  /// 从JSON创建NoteListResponse实例
  factory NoteListResponse.fromJson(Map<String, dynamic> json) {
    final notesData = json['notes'] as List<dynamic>? ?? [];
    final notes = notesData.map((item) => NoteItem.fromJson(item as Map<String, dynamic>)).toList();

    return NoteListResponse(
      notes: notes,
      total: json['total']?.toInt() ?? 0,
      page: json['page']?.toInt() ?? 1,
      pageSize: json['page_size']?.toInt() ?? 10,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'notes': notes.map((note) => note.toJson()).toList(),
      'total': total,
      'page': page,
      'page_size': pageSize,
    };
  }
}

/// 草稿数据模型
class DraftItem {
  /// 草稿ID
  final String id;

  /// 笔记ID
  final String noteId;

  /// 草稿内容
  final String content;

  /// 创建时间
  final String createTime;

  /// 更新时间
  final String updateTime;

  /// 笔记标题
  final String noteTitle;

  /// 笔记封面
  final String? noteCover;

  /// 笔记描述
  final String? noteDesc;

  DraftItem({
    required this.id,
    required this.noteId,
    required this.content,
    required this.createTime,
    required this.updateTime,
    required this.noteTitle,
    this.noteCover,
    this.noteDesc,
  });

  /// 从JSON创建DraftItem实例
  factory DraftItem.fromJson(Map<String, dynamic> json) {
    return DraftItem(
      id: json['id']?.toString() ?? '',
      noteId: json['note_id']?.toString() ?? '',
      content: json['content']?.toString() ?? '',
      createTime: json['create_time']?.toString() ?? '',
      updateTime: json['update_time']?.toString() ?? '',
      noteTitle: json['note_title']?.toString() ?? '',
      noteCover: json['note_cover']?.toString(),
      noteDesc: json['note_desc']?.toString(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'note_id': noteId,
      'content': content,
      'create_time': createTime,
      'update_time': updateTime,
      'note_title': noteTitle,
      'note_cover': noteCover,
      'note_desc': noteDesc,
    };
  }

  /// 获取格式化的创建时间
  DateTime? get createdAt {
    try {
      return DateTime.parse(createTime);
    } catch (e) {
      return null;
    }
  }

  /// 获取格式化的更新时间
  DateTime? get updatedAt {
    try {
      return DateTime.parse(updateTime);
    } catch (e) {
      return null;
    }
  }

  /// 获取显示用的内容（优先使用noteDesc，其次content）
  String get displayContent {
    if (noteDesc != null && noteDesc!.isNotEmpty) {
      return noteDesc!;
    }
    if (content.isNotEmpty) {
      return content;
    }
    return '';
  }

  /// 复制并修改部分属性
  DraftItem copyWith({
    String? id,
    String? noteId,
    String? content,
    String? createTime,
    String? updateTime,
    String? noteTitle,
    String? noteCover,
    String? noteDesc,
  }) {
    return DraftItem(
      id: id ?? this.id,
      noteId: noteId ?? this.noteId,
      content: content ?? this.content,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      noteTitle: noteTitle ?? this.noteTitle,
      noteCover: noteCover ?? this.noteCover,
      noteDesc: noteDesc ?? this.noteDesc,
    );
  }
}

/// 草稿列表响应数据模型
class DraftListResponse {
  /// 草稿列表
  final List<DraftItem> drafts;

  /// 总记录数
  final int total;

  /// 当前页码
  final int page;

  /// 每页数量
  final int pageSize;

  DraftListResponse({
    required this.drafts,
    required this.total,
    required this.page,
    required this.pageSize,
  });

  /// 从JSON创建DraftListResponse实例
  factory DraftListResponse.fromJson(Map<String, dynamic> json) {
    final draftsData = json['drafts'] as List<dynamic>? ?? [];
    final drafts = draftsData.map((item) => DraftItem.fromJson(item as Map<String, dynamic>)).toList();

    return DraftListResponse(
      drafts: drafts,
      total: json['total']?.toInt() ?? 0,
      page: json['page']?.toInt() ?? 1,
      pageSize: json['page_size']?.toInt() ?? 10,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'drafts': drafts.map((draft) => draft.toJson()).toList(),
      'total': total,
      'page': page,
      'page_size': pageSize,
    };
  }
}
