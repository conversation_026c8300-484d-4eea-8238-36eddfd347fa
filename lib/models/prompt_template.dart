/// 提示词模板数据模型
class PromptTemplate {
  final String id;
  final String title;
  final String content;
  final DateTime createTime;
  final DateTime updateTime;

  PromptTemplate({
    required this.id,
    required this.title,
    required this.content,
    required this.createTime,
    required this.updateTime,
  });

  /// 从JSON创建PromptTemplate实例
  factory PromptTemplate.fromJson(Map<String, dynamic> json) {
    return PromptTemplate(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      createTime: DateTime.fromMillisecondsSinceEpoch(json['create_time'] as int),
      updateTime: DateTime.fromMillisecondsSinceEpoch(json['update_time'] as int),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'create_time': createTime.millisecondsSinceEpoch,
      'update_time': updateTime.millisecondsSinceEpoch,
    };
  }

  /// 创建副本并更新指定字段
  PromptTemplate copyWith({
    String? id,
    String? title,
    String? content,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return PromptTemplate(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  String toString() {
    return 'PromptTemplate{id: $id, title: $title, content: $content, createTime: $createTime, updateTime: $updateTime}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PromptTemplate && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
