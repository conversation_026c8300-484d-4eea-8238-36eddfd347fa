/// 版本信息模型
class VersionInfo {
  final String currentVersion;
  final String latestVersion;
  final bool hasUpdate;
  final String updateContent;
  final String downloadUrl;
  final bool forceUpdate;
  final String updateTime;

  const VersionInfo({
    required this.currentVersion,
    required this.latestVersion,
    required this.hasUpdate,
    required this.updateContent,
    required this.downloadUrl,
    required this.forceUpdate,
    required this.updateTime,
  });

  factory VersionInfo.fromJson(Map<String, dynamic> json) {
    // 处理服务端返回的格式：{version: "1.0.0", update_description: ["初始版本"]}
    final version = json['version'] ?? '';
    final updateDescriptionList = json['update_description'] as List<dynamic>? ?? [];
    final updateContent = updateDescriptionList.join('\n');

    return VersionInfo(
      currentVersion: '', // 当前版本需要从本地获取
      latestVersion: version,
      hasUpdate: false, // 需要通过比较版本号来确定
      updateContent: updateContent,
      downloadUrl: json['download_url'] ?? '',
      forceUpdate: json['force_update'] ?? false,
      updateTime: json['update_time'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_version': currentVersion,
      'latest_version': latestVersion,
      'has_update': hasUpdate,
      'update_content': updateContent,
      'download_url': downloadUrl,
      'force_update': forceUpdate,
      'update_time': updateTime,
    };
  }
}
