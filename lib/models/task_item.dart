/// 任务项数据模型
class TaskItem {
  /// 任务ID
  final String id;

  /// 任务类型：1=提取文案，2=下载视频
  final int taskType;

  /// 任务标题
  final String title;

  /// 平台标识
  final String platform;

  /// 任务URL
  final String url;

  /// 任务状态：1=新创建，2=已完成，3=失败
  final int status;

  /// 创建时间
  final DateTime createTime;

  /// 更新时间
  final DateTime updateTime;

  TaskItem({
    required this.id,
    required this.taskType,
    required this.title,
    required this.platform,
    required this.url,
    required this.status,
    required this.createTime,
    required this.updateTime,
  });

  /// 从JSON创建TaskItem实例
  factory TaskItem.fromJson(Map<String, dynamic> json) {
    return TaskItem(
      id: json['id']?.toString() ?? '',
      taskType: json['task_type']?.toInt() ?? 1,
      title: json['title']?.toString() ?? '',
      platform: json['platform']?.toString() ?? '',
      url: json['url']?.toString() ?? '',
      status: json['status']?.toInt() ?? 1,
      createTime: DateTime.tryParse(json['create_time']?.toString() ?? '') ?? DateTime.now(),
      updateTime: DateTime.tryParse(json['update_time']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'task_type': taskType,
      'title': title,
      'platform': platform,
      'url': url,
      'status': status,
      'create_time': createTime.toIso8601String(),
      'update_time': updateTime.toIso8601String(),
    };
  }

  /// 获取任务类型名称
  String get taskTypeName {
    switch (taskType) {
      case 1:
        return '提取文案';
      case 2:
        return '下载视频';
      case 3:
        return '创建笔记';
      default:
        return '未知';
    }
  }

  /// 获取任务状态名称
  String get statusName {
    switch (status) {
      case 1:
        return '新创建';
      case 2:
        return '已完成';
      case 3:
        return '失败';
      default:
        return '未知';
    }
  }

  /// 获取平台显示名称
  String get platformDisplayName {
    switch (platform.toLowerCase()) {
      case 'bilibili':
        return 'B站';
      case 'douyin':
        return '抖音';
      case 'xiaohongshu':
        return '小红书';
      case 'wechat':
        return '微信';
      case 'youtube':
        return 'YouTube';
      default:
        return platform;
    }
  }

  /// 获取状态颜色
  String get statusColor {
    switch (status) {
      case 1:
        return '#FFA500'; // 橙色 - 新创建
      case 2:
        return '#4CAF50'; // 绿色 - 已完成
      case 3:
        return '#F44336'; // 红色 - 失败
      default:
        return '#9E9E9E'; // 灰色 - 未知
    }
  }

  /// 复制并修改部分属性
  TaskItem copyWith({
    String? id,
    int? taskType,
    String? title,
    String? platform,
    String? url,
    int? status,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return TaskItem(
      id: id ?? this.id,
      taskType: taskType ?? this.taskType,
      title: title ?? this.title,
      platform: platform ?? this.platform,
      url: url ?? this.url,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  String toString() {
    return 'TaskItem{id: $id, taskType: $taskType, title: $title, platform: $platform, status: $status}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaskItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// 任务列表响应数据模型
class TaskListResponse {
  /// 任务列表
  final List<TaskItem> tasks;

  /// 总数量
  final int total;

  /// 当前页码
  final int page;

  /// 每页数量
  final int pageSize;

  TaskListResponse({
    required this.tasks,
    required this.total,
    required this.page,
    required this.pageSize,
  });

  /// 从JSON创建TaskListResponse实例
  factory TaskListResponse.fromJson(Map<String, dynamic> json) {
    final tasksJson = json['tasks'] as List<dynamic>? ?? [];
    final tasks = tasksJson.map((taskJson) => TaskItem.fromJson(taskJson as Map<String, dynamic>)).toList();

    return TaskListResponse(
      tasks: tasks,
      total: json['total']?.toInt() ?? 0,
      page: json['page']?.toInt() ?? 1,
      pageSize: json['page_size']?.toInt() ?? 10,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'tasks': tasks.map((task) => task.toJson()).toList(),
      'total': total,
      'page': page,
      'page_size': pageSize,
    };
  }

  /// 是否还有更多数据
  bool get hasMore {
    return tasks.length < total;
  }

  @override
  String toString() {
    return 'TaskListResponse{tasks: ${tasks.length}, total: $total, page: $page, pageSize: $pageSize}';
  }
}
