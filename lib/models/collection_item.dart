import 'package:flutter/material.dart';

/// 收藏夹项目模型
///
/// 用于表示收藏夹列表中的单个项目
class CollectionItem {
  /// 收藏夹ID
  final String id;

  /// 用户ID
  final String userId;

  /// 收藏夹名称
  final String title;

  /// 封面图片URL
  final String? cover;

  /// 创建时间
  final DateTime? createTime;

  /// 最后更新时间
  final DateTime? updateTime;

  /// 排序值（字符串类型，用于字母排序法）
  final String order;

  /// 收藏夹颜色 (前端属性，非服务端存储)
  final Color color;

  /// 构造函数
  CollectionItem({
    required this.id,
    required this.title,
    required this.color,
    this.userId = '',
    this.cover,
    this.createTime,
    this.updateTime,
    this.order = 'a',
  });

  /// 从JSON创建CollectionItem
  factory CollectionItem.fromJson(Map<String, dynamic> json, {Color? color}) {
    return CollectionItem(
      id: json['id'] as String,
      userId: json['user_id'] as String? ?? '',
      title: json['name'] as String,
      cover: json['cover'] as String?,
      createTime: json['create_time'] != null ? DateTime.parse(json['create_time'] as String) : null,
      updateTime: json['update_time'] != null ? DateTime.parse(json['update_time'] as String) : null,
      order: json['order'] as String? ?? 'a',
      color: color ?? Colors.blue,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': title,
      'cover': cover,
      'order': order,
      'create_time': createTime?.toIso8601String(),
      'update_time': updateTime?.toIso8601String(),
    };
  }

  /// 创建CollectionItem的副本
  CollectionItem copyWith({
    String? id,
    String? userId,
    String? title,
    String? cover,
    DateTime? createTime,
    DateTime? updateTime,
    String? order,
    Color? color,
  }) {
    return CollectionItem(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      cover: cover ?? this.cover,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      order: order ?? this.order,
      color: color ?? this.color,
    );
  }

  @override
  String toString() {
    return 'CollectionItem(id: $id, userId: $userId, title: $title, cover: $cover, order: $order, createTime: $createTime, updateTime: $updateTime)';
  }
}
