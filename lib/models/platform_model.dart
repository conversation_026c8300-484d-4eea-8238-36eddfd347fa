import 'package:flutter/material.dart';

/// 平台场景模型
class SceneModel {
  final String name;
  final String description;
  final IconData icon;

  SceneModel({
    required this.name,
    required this.description,
    required this.icon,
  });
}

/// 平台模型
class PlatformModel {
  final String name;
  final String logoAsset;
  final Color color;
  final String description;
  final List<SceneModel> supportedScenes;

  PlatformModel({
    required this.name,
    required this.logoAsset,
    required this.color,
    required this.description,
    required this.supportedScenes,
  });

  /// 获取所有支持的平台
  static List<PlatformModel> getSupportedPlatforms() {
    return [
      // 小红书
      PlatformModel(
        name: '小红书',
        logoAsset: 'assets/xiaohongshu.png',
        color: const Color(0xFFFF2442),
        description: '支持小红书的笔记、视频笔记和商品内容的收藏',
        supportedScenes: [
          SceneModel(
            name: '普通笔记',
            description: '支持收藏图文类型的笔记',
            icon: Icons.article_outlined,
          ),
          SceneModel(
            name: '视频笔记',
            description: '支持收藏视频类型的笔记',
            icon: Icons.videocam_outlined,
          ),
          SceneModel(
            name: '商品',
            description: '支持收藏商品页面',
            icon: Icons.shopping_bag_outlined,
          ),
        ],
      ),

      // 抖音
      PlatformModel(
        name: '抖音',
        logoAsset: 'assets/douyin.png',
        color: const Color(0xFF000000),
        description: '支持抖音的短视频和长视频内容的收藏',
        supportedScenes: [
          SceneModel(
            name: '短视频',
            description: '支持收藏短视频内容',
            icon: Icons.video_library_outlined,
          ),
          SceneModel(
            name: '长视频',
            description: '支持收藏长视频内容',
            icon: Icons.movie_outlined,
          ),
        ],
      ),

      // B站
      PlatformModel(
        name: 'B站',
        logoAsset: 'assets/bilibili.png',
        color: const Color(0xFF00A1D6),
        description: '支持B站的视频内容的收藏',
        supportedScenes: [
          SceneModel(
            name: '视频',
            description: '支持收藏视频内容',
            icon: Icons.ondemand_video_outlined,
          ),
        ],
      ),

      // 微信
      PlatformModel(
        name: '微信',
        logoAsset: 'assets/wechat.png',
        color: const Color(0xFF07C160),
        description: '支持微信公众号文章的收藏',
        supportedScenes: [
          SceneModel(
            name: '公众号文章',
            description: '支持收藏公众号文章',
            icon: Icons.article_outlined,
          ),
        ],
      ),

      // 豆瓣
      PlatformModel(
        name: '豆瓣',
        logoAsset: 'assets/douban.png',
        color: const Color(0xFF007722),
        description: '支持豆瓣的动态、电影、图书、音乐、播客等内容的收藏',
        supportedScenes: [
          SceneModel(
            name: '动态',
            description: '支持收藏用户动态和状态',
            icon: Icons.dynamic_feed_outlined,
          ),
          SceneModel(
            name: '电影',
            description: '支持收藏电影信息和评价',
            icon: Icons.movie_outlined,
          ),
          SceneModel(
            name: '图书',
            description: '支持收藏图书信息和书评',
            icon: Icons.book_outlined,
          ),
          SceneModel(
            name: '音乐',
            description: '支持收藏音乐专辑和歌曲',
            icon: Icons.music_note_outlined,
          ),
          SceneModel(
            name: '播客',
            description: '支持收藏播客节目',
            icon: Icons.podcasts_outlined,
          ),
        ],
      ),

      // 拼多多
      PlatformModel(
        name: '拼多多',
        logoAsset: 'assets/Pinduoduo.png',
        color: const Color(0xFFE02E24),
        description: '支持拼多多商品的收藏',
        supportedScenes: [
          SceneModel(
            name: '商品',
            description: '支持收藏拼多多商品页面',
            icon: Icons.shopping_bag_outlined,
          ),
        ],
      ),

      // 淘宝
      PlatformModel(
        name: '淘宝',
        logoAsset: 'assets/taobao.png',
        color: const Color(0xFFFF6600),
        description: '支持淘宝商品的收藏',
        supportedScenes: [
          SceneModel(
            name: '商品',
            description: '支持收藏淘宝商品页面',
            icon: Icons.shopping_bag_outlined,
          ),
        ],
      ),

      // 快手
      PlatformModel(
        name: '快手',
        logoAsset: 'assets/kuaishou.png',
        color: const Color(0xFFFF6600),
        description: '支持快手短视频的收藏',
        supportedScenes: [
          SceneModel(
            name: '短视频',
            description: '支持收藏快手短视频内容',
            icon: Icons.video_library_outlined,
          ),
        ],
      ),

      // 美团
      PlatformModel(
        name: '美团',
        logoAsset: 'assets/meituan.png',
        color: const Color(0xFFFFC300),
        description: '支持美团商品和服务的收藏',
        supportedScenes: [
          SceneModel(
            name: '商品',
            description: '支持收藏美团商品页面',
            icon: Icons.shopping_bag_outlined,
          ),
        ],
      ),

      // 京东
      PlatformModel(
        name: '京东',
        logoAsset: 'assets/jingdong.png',
        color: const Color(0xFFE1251B),
        description: '支持京东商品的收藏',
        supportedScenes: [
          SceneModel(
            name: '商品',
            description: '支持收藏京东商品页面',
            icon: Icons.shopping_bag_outlined,
          ),
        ],
      ),
    ];
  }
}
