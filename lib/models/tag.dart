/// 标签数据模型
class Tag {
  final String id;
  final String name;
  final String backgroundColor;
  final String textColor;
  final int createTime;
  final int updateTime;
  final int count;

  Tag({
    required this.id,
    required this.name,
    required this.backgroundColor,
    required this.textColor,
    required this.createTime,
    required this.updateTime,
    required this.count,
  });

  /// 从JSON创建Tag实例
  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      id: json['id'] as String,
      name: json['name'] as String,
      backgroundColor: json['background_color'] as String? ?? '#1890ff',
      textColor: json['text_color'] as String? ?? '#ffffff',
      createTime: json['create_time'] as int,
      updateTime: json['update_time'] as int,
      count: json['count'] as int? ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'background_color': backgroundColor,
      'text_color': textColor,
      'create_time': createTime,
      'update_time': updateTime,
      'count': count,
    };
  }

  @override
  String toString() {
    return 'Tag{id: $id, name: $name, backgroundColor: $backgroundColor, textColor: $textColor, createTime: $createTime, updateTime: $updateTime, count: $count}';
  }

  /// 为了向后兼容，提供 color 属性的 getter
  @Deprecated('Use backgroundColor instead')
  String get color => backgroundColor;
}

/// 标签列表响应数据模型
class TagListResponse {
  final List<Tag> tags;

  TagListResponse({
    required this.tags,
  });

  /// 从JSON创建TagListResponse实例
  factory TagListResponse.fromJson(Map<String, dynamic> json) {
    final tagsList = json['tags'] as List<dynamic>;
    final tags = tagsList.map((tagJson) => Tag.fromJson(tagJson as Map<String, dynamic>)).toList();

    return TagListResponse(
      tags: tags,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'tags': tags.map((tag) => tag.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'TagListResponse{tags: $tags}';
  }
}

/// 创建标签响应数据模型
class CreateTagResponse {
  final String id;
  final String name;
  final String backgroundColor;
  final String textColor;
  final int createTime;
  final int updateTime;

  CreateTagResponse({
    required this.id,
    required this.name,
    required this.backgroundColor,
    required this.textColor,
    required this.createTime,
    required this.updateTime,
  });

  /// 从JSON创建CreateTagResponse实例
  factory CreateTagResponse.fromJson(Map<String, dynamic> json) {
    return CreateTagResponse(
      id: json['id'] as String,
      name: json['name'] as String,
      backgroundColor: json['background_color'] as String? ?? '#1890ff',
      textColor: json['text_color'] as String? ?? '#ffffff',
      createTime: json['create_time'] as int,
      updateTime: json['update_time'] as int,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'background_color': backgroundColor,
      'text_color': textColor,
      'create_time': createTime,
      'update_time': updateTime,
    };
  }

  @override
  String toString() {
    return 'CreateTagResponse{id: $id, name: $name, backgroundColor: $backgroundColor, textColor: $textColor, createTime: $createTime, updateTime: $updateTime}';
  }

  /// 为了向后兼容，提供 color 属性的 getter
  @Deprecated('Use backgroundColor instead')
  String get color => backgroundColor;
}
