/// 素材数据模型
class Material {
  /// 素材ID
  final String id;

  /// 用户ID
  final String userId;

  /// 素材URL
  final String url;

  /// 创建时间（毫秒时间戳）
  final int createTime;

  /// 更新时间（毫秒时间戳）
  final int updateTime;

  /// 状态：1-正常，2-已删除
  final int status;

  /// 类型：1-图片，2-语音，3-视频
  final int type;

  const Material({
    required this.id,
    required this.userId,
    required this.url,
    required this.createTime,
    required this.updateTime,
    required this.status,
    required this.type,
  });

  /// 从JSON创建Material对象
  factory Material.fromJson(Map<String, dynamic> json) {
    return Material(
      id: json['id']?.toString() ?? '',
      userId: json['user_id']?.toString() ?? '',
      url: json['url']?.toString() ?? '',
      createTime: json['create_time']?.toInt() ?? 0,
      updateTime: json['update_time']?.toInt() ?? 0,
      status: json['status']?.toInt() ?? 1,
      type: json['type']?.toInt() ?? 1,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'url': url,
      'create_time': createTime,
      'update_time': updateTime,
      'status': status,
      'type': type,
    };
  }

  /// 获取素材类型名称
  String get typeName {
    switch (type) {
      case 1:
        return '图片';
      case 2:
        return '语音';
      case 3:
        return '视频';
      default:
        return '未知';
    }
  }

  /// 是否为图片类型
  bool get isImage => type == 1;

  /// 是否为语音类型
  bool get isAudio => type == 2;

  /// 是否为视频类型
  bool get isVideo => type == 3;

  /// 是否为正常状态
  bool get isNormal => status == 1;

  /// 获取创建时间的DateTime对象
  DateTime get createDateTime => DateTime.fromMillisecondsSinceEpoch(createTime);

  /// 获取更新时间的DateTime对象
  DateTime get updateDateTime => DateTime.fromMillisecondsSinceEpoch(updateTime);

  @override
  String toString() {
    return 'Material{id: $id, url: $url, type: $type, status: $status}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Material && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// 素材列表响应数据模型
class MaterialListResponse {
  /// 素材列表
  final List<Material> materials;

  /// 总数量
  final int total;

  /// 当前页码
  final int page;

  /// 每页数量
  final int pageSize;

  const MaterialListResponse({
    required this.materials,
    required this.total,
    required this.page,
    required this.pageSize,
  });

  /// 从JSON创建MaterialListResponse对象
  factory MaterialListResponse.fromJson(Map<String, dynamic> json) {
    final materialsJson = json['materials'] as List<dynamic>? ?? [];
    final materials = materialsJson
        .map((item) => Material.fromJson(item as Map<String, dynamic>))
        .toList();

    return MaterialListResponse(
      materials: materials,
      total: json['total']?.toInt() ?? 0,
      page: json['page']?.toInt() ?? 1,
      pageSize: json['page_size']?.toInt() ?? 20,
    );
  }

  /// 是否还有更多数据
  bool get hasMore => materials.length < total;

  @override
  String toString() {
    return 'MaterialListResponse{total: $total, page: $page, pageSize: $pageSize, materials: ${materials.length}}';
  }
}
