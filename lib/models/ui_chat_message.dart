import '../models/material.dart' as material_model;

/// UI聊天消息类型枚举
enum UiChatMessageType {
  user,           // 用户消息
  assistant,      // AI助手消息
  toolExecution,  // 工具执行提示消息（仅UI显示）
  system,         // 系统消息
}

/// UI聊天消息模型
///
/// 专门用于UI展示的消息模型，支持更丰富的消息类型
/// 与AiChatService中的AiChatMessage分离，允许UI显示更多信息
class UiChatMessage {
  /// 消息内容
  final String content;

  /// 消息类型
  final UiChatMessageType type;

  /// 时间戳
  final DateTime timestamp;

  /// AI思考过程内容（仅assistant类型消息）
  final String? reasoningContent;

  /// 是否为工具执行相关的消息
  final bool isToolRelated;

  /// 工具名称（当isToolRelated为true时使用）
  final String? toolName;

  /// 工具执行状态（当isToolRelated为true时使用）
  final ToolExecutionStatus? toolStatus;

  /// 附带的素材列表（用于UI显示）
  final List<material_model.Material>? materials;

  UiChatMessage({
    required this.content,
    required this.type,
    required this.timestamp,
    this.reasoningContent,
    this.isToolRelated = false,
    this.toolName,
    this.toolStatus,
    this.materials,
  });

  /// 创建用户消息
  factory UiChatMessage.user(String content, {List<material_model.Material>? materials}) {
    return UiChatMessage(
      content: content,
      type: UiChatMessageType.user,
      timestamp: DateTime.now(),
      materials: materials,
    );
  }

  /// 创建AI助手消息
  factory UiChatMessage.assistant({
    required String content,
    String? reasoningContent,
  }) {
    return UiChatMessage(
      content: content,
      type: UiChatMessageType.assistant,
      timestamp: DateTime.now(),
      reasoningContent: reasoningContent,
    );
  }

  /// 创建工具执行提示消息
  factory UiChatMessage.toolExecution({
    required String content,
    required String toolName,
    required ToolExecutionStatus status,
  }) {
    return UiChatMessage(
      content: content,
      type: UiChatMessageType.toolExecution,
      timestamp: DateTime.now(),
      isToolRelated: true,
      toolName: toolName,
      toolStatus: status,
    );
  }

  /// 创建系统消息
  factory UiChatMessage.system(String content) {
    return UiChatMessage(
      content: content,
      type: UiChatMessageType.system,
      timestamp: DateTime.now(),
    );
  }

  /// 判断是否为用户消息
  bool get isUser => type == UiChatMessageType.user;

  /// 判断是否为AI消息
  bool get isAssistant => type == UiChatMessageType.assistant;
}

/// 工具执行状态枚举
enum ToolExecutionStatus {
  starting,   // 开始执行
  executing,  // 执行中
  completed,  // 执行完成
  failed,     // 执行失败
}
