import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../routes.dart';
import '../constants/app_colors.dart';
import '../services/wechat_service.dart';
import '../api/api_provider.dart';
import '../api/wechat_api.dart';
import '../services/storage_service.dart';
import '../native_bridge/native_bridge.dart';
import '../helpers/exit_confirmation_dialog.dart';

/// 微信登录页面
///
/// 提供微信一键登录功能，样式参考小红书但使用存点的主题色
class WechatLoginPage extends StatefulWidget {
  const WechatLoginPage({super.key});

  @override
  State<WechatLoginPage> createState() => _WechatLoginPageState();
}

class _WechatLoginPageState extends State<WechatLoginPage> {
  // 是否同意用户协议和隐私政策
  bool _agreeToTerms = false;

  // 微信服务
  final WechatService _wechatService = WechatService();

  // API提供者
  final ApiProvider _apiProvider = ApiProvider();

  // 存储服务
  final StorageService _storageService = StorageService();

  // 是否正在登录
  bool _isLoading = false;

  // 错误信息
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // 初始化微信服务
    _initWechatService();
  }

  // 初始化微信服务
  Future<void> _initWechatService() async {
    try {
      await _wechatService.init();
      // 监听微信登录结果
      _wechatService.addLoginListener(_onWechatLoginResult);
    } catch (e) {
      setState(() {
        _errorMessage = '初始化微信服务失败: $e';
      });
    }
  }

  // 处理微信登录结果
  void _onWechatLoginResult(Map<String, dynamic> result) async {
    print('收到微信登录结果: $result');
    print('结果类型: ${result.runtimeType}');

    if (result['success'] == true) {
      // 登录成功，获取到授权码
      String code = result['code'];
      print('微信登录成功，授权码: $code');
      print('授权码长度: ${code.length}');
      print('授权码字符: ${code.split('').map((c) => '${c}(${c.codeUnitAt(0)})').join(', ')}');

      // 调用后端API使用code换取用户信息和token
      try {
        // 调用微信登录API
        final loginResponse = await _apiProvider.wechatApi.login(code);

        // 保存用户信息和token
        await _storageService.saveUserInfo(
          userId: loginResponse.userId,
          token: loginResponse.token,
          avatarUrl: loginResponse.avatarUrl,
          nickname: loginResponse.nickname,
        );

        // 同步到Android原生端
        final nativeBridge = NativeBridge();
        final tokenResult = await nativeBridge.putString('auth_token', loginResponse.token);
        final userIdResult = await nativeBridge.putString('user_id', loginResponse.userId);

        print('同步到Android原生端结果: token=$tokenResult, userId=$userIdResult');

        // 验证同步是否成功
        final savedToken = await nativeBridge.getString('auth_token');
        final savedUserId = await nativeBridge.getString('user_id');
        print('从Android原生端读取: token=$savedToken, userId=$savedUserId');

        print('保存用户信息成功: userId=${loginResponse.userId}, nickname=${loginResponse.nickname}, avatarUrl=${loginResponse.avatarUrl}');

        // 登录成功后跳转到首页
        if (mounted) {
          Navigator.pushReplacementNamed(context, AppRoutes.home);
        }
      } catch (e) {
        print('调用微信登录API失败: $e');

        // 显示错误信息
        String errorMessage = '登录失败';
        if (e is ApiException) {
          errorMessage = '${e.message} (${e.code})';
        } else {
          errorMessage = e.toString();
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = errorMessage;
          });
        }
      }
    } else {
      // 登录失败
      final error = result['error'] ?? '微信登录失败';
      print('微信登录失败: $error');

      setState(() {
        _isLoading = false;
        _errorMessage = error;
      });
    }
  }

  // 发起微信登录
  Future<void> _handleWechatLogin() async {
    // 检查是否同意用户协议
    if (!_agreeToTerms) {
      setState(() {
        _errorMessage = '请先阅读并同意用户协议和隐私政策';
      });
      return;
    }

    // 清除错误信息
    setState(() {
      _errorMessage = null;
      _isLoading = true;
    });

    // 发起微信登录
    bool success = await _wechatService.login();
    if (!success) {
      setState(() {
        _isLoading = false;
        _errorMessage = '发起微信登录失败';
      });
    }
  }

  @override
  void dispose() {
    // 移除微信登录结果监听
    _wechatService.removeLoginListener(_onWechatLoginResult);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      // 拦截返回手势，显示退出确认对话框
      onWillPop: () async {
        // 显示退出确认对话框
        return await ExitConfirmationDialog.show(context);
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Stack(
            children: [
              // 主要内容区域
              Container(
                margin: EdgeInsets.only(top: 30.w),
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                child: Column(
                  children: [
                    // 顶部空白区域
                    SizedBox(height: 0.2.sh),

                    // Logo区域 - "存点"文字 - 更居中显示
                    Center(
                      child: Text(
                        '存点',
                        style: TextStyle(
                          fontSize: 55.sp,
                          fontFamily: 'JiangHuGuFeng',
                          color: AppColors.primary,
                        ),
                      ),
                    ),

                    // 副标题 - 调整样式与小红书一致
                    Center(
                      child: Text(
                        '你的收藏专家',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.black87,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    ),

                    SizedBox(height: 20.h),

                    // 空白占位
                    SizedBox(height: 20.h),
                  ],
                ),
              ),

              // 底部微信登录按钮和协议区域
              Positioned(
                bottom: 180.h,
                left: 50.w,
                right: 50.w,
                child: Column(
                  children: [
                    ElevatedButton(
                      onPressed: _isLoading ? null : _handleWechatLogin,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF07C160), // 微信绿色
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24.r),
                        ),
                        // 禁用时的样式
                        disabledBackgroundColor: const Color(0xFF07C160).withOpacity(0.6),
                        disabledForegroundColor: Colors.white.withOpacity(0.8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (_isLoading)
                            SizedBox(
                              width: 20.w,
                              height: 20.w,
                              child: const CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2.0,
                              ),
                            )
                          else
                            Image.asset(
                              'assets/wechat.png',
                              width: 19.w,
                              color: Colors.white,
                            ),
                          SizedBox(width: 10.w),
                          Text(
                            _isLoading ? '登录中...' : '微信登录',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 错误信息
                    if (_errorMessage != null)
                      Padding(
                        padding: EdgeInsets.only(top: 10.h),
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12.sp,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                    // 协议区域
                    Padding(
                      padding: EdgeInsets.only(top: 15.h),
                      child: GestureDetector(
                        // 让整个协议区域都可以点击来切换同意状态
                        onTap: () {
                          setState(() {
                            _agreeToTerms = !_agreeToTerms;
                          });
                        },
                        child: Container(
                          // 扩大点击热区，但限制水平padding避免溢出
                          padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 8.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min, // 让Row适应内容大小
                            children: [
                              // 自定义圆形复选框 - 扩大点击区域
                              Container(
                                // 扩大复选框的点击热区
                                padding: EdgeInsets.all(4.w), // 减小padding避免溢出
                                child: Container(
                                  width: 14.w,
                                  height: 14.h,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: _agreeToTerms ? AppColors.primary : Colors.grey[400]!,
                                      width: 1.w,
                                    ),
                                    color: _agreeToTerms ? AppColors.primary : Colors.transparent,
                                  ),
                                  child: _agreeToTerms
                                      ? Icon(
                                          Icons.check,
                                          size: 10.sp,
                                          color: Colors.white,
                                        )
                                      : null,
                                ),
                              ),
                              SizedBox(width: 2.w), // 减小间距
                              Flexible( // 使用Flexible让文本能够自适应
                                child: Text(
                                  '我已阅读并同意',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ),
                              SizedBox(width: 2.w), // 减小间距
                              GestureDetector(
                                // 阻止事件冒泡，避免点击协议链接时触发同意状态切换
                                onTap: () {
                                  Navigator.pushNamed(context, AppRoutes.agreement);
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 1.w), // 减小padding
                                  child: Text(
                                    '《用户协议》',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: const Color(0xFF507FAF),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 2.w), // 减小间距
                              GestureDetector(
                                // 阻止事件冒泡，避免点击协议链接时触发同意状态切换
                                onTap: () {
                                  Navigator.pushNamed(context, AppRoutes.privacyPolicy);
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 1.w), // 减小padding
                                  child: Text(
                                    '《隐私政策》',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: const Color(0xFF507FAF),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 底部账号登录按钮 - 调整样式与小红书一致
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Center(
                  child: TextButton(
                    onPressed: () {
                      // 跳转到现有的登录页面，使用pushNamed而不是pushReplacementNamed
                      // 这样可以保留微信登录页面在导航栈中，便于返回
                      Navigator.pushNamed(context, AppRoutes.login);
                    },
                    child: Text(
                      '账号登录 >',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[500],
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
