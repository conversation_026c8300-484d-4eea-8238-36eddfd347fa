import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../native_bridge/native_bridge.dart';
import '../native_bridge/spark_chain_bridge.dart';
import '../native_bridge/tencent_asr_bridge.dart';
import '../services/storage_service.dart';
import '../routes.dart';
import '../constants/app_colors.dart';
import '../utils/note_webview_helper.dart';
import '../utils/audio_segment_processor.dart';
import '../utils/draft_edit_helper.dart';
import 'ml_kit_demo_page.dart';

class DebugPage extends StatefulWidget {
  const DebugPage({super.key, required this.title});

  final String title;

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  int _counter = 0;
  bool _isDownloading = false;
  bool _isDownloadingAudio = false;
  bool _isExtractingText = false;
  bool _isExtractingVideoUrl = false;
  bool _isOpeningWebPage = false;
  bool _isSharing = false;
  bool _isLoadingStorage = false;
  bool _isExtractingVideoUrlAndText = false;
  String _downloadResult = '';
  String _downloadAudioResult = '';
  String _extractedText = '';
  String _extractedVideoUrl = '';
  String _shareResult = '';
  String _storageContent = '';
  String _toastResult = '';
  String _ossLinkResult = '';
  String _extractedVideoUrlAndText = '';
  bool _isConvertingLink = false;
  bool _isOpeningSchemeUrl = false;
  bool _isUploadingToOss = false;
  bool _isExtractingHtml = false;
  String _ossUploadResult = '';
  String _extractedHtml = '';
  String _m4aRecognitionResult = '';  // 新增：M4A文件识别结果
  bool _isRecognizingM4a = false;  // 新增：M4A识别状态
  String _ocrSubtitlesResult = '';  // 新增：OCR字幕识别结果
  bool _isRecognizingOcrSubtitles = false;  // 新增：OCR字幕识别状态
  String _tencentAsrResult = '';  // 新增：腾讯云ASR识别结果
  bool _isRecognizingTencentAsr = false;  // 新增：腾讯云ASR识别状态
  String _businessJsResult = '';  // 新增：业务JavaScript执行结果
  bool _isExecutingBusinessJs = false;  // 新增：业务JavaScript执行状态

  // 笔记WebView测试相关状态
  bool _isOpeningNote = false;
  final TextEditingController _urlController = TextEditingController(text: 'https://www.baidu.com');
  final TextEditingController _resourceUrlController = TextEditingController(text: 'https://img.zcool.cn/community/01c42858268d39a84a0e282b9057e0.jpg');
  final TextEditingController _schemeUrlController = TextEditingController(text: 'taobao://go/order_detail');
  final TextEditingController _htmlUrlController = TextEditingController(text: 'https://www.xiaohongshu.com/explore');
  final TextEditingController _videoPageUrlController = TextEditingController(text: 'https://v.douyin.com/dsqnT7sh1pY/');
  final TextEditingController _videoUrlController = TextEditingController(text: 'https://v.douyin.com/dsqnT7sh1pY/');
  final TextEditingController _downloadVideoUrlController = TextEditingController(text: 'https://cn-hbwh-cm-01-12.bilivideo.com/upgcxcode/83/81/29347548183/29347548183-1-16.mp4?e=ig8euxZM2rNcNbRVhwdVhwdlhWdVhwdVhoNvNC8BqJIzNbfq9rVEuxTEnE8L5F6VnEsSTx0vkX8fqJeYTj_lta53NCM=&uipk=5&nbs=1&deadline=1744906872&gen=playurlv2&os=bcache&oi=1880676974&trid=00003cdd7c3e797243fe923758b86ca904d6h&mid=0&platform=html5&og=cos&upsig=6071d663ad534047651cf962dfe6e0e8&uparams=e,uipk,nbs,deadline,gen,os,oi,trid,mid,platform,og&cdnid=10208&bvc=vod&nettype=0&f=h_0_0&bw=44790&logo=80000000');
  final TextEditingController _downloadAudioUrlController = TextEditingController(text: 'https://example.com/video.mp4');
  final TextEditingController _progressTestUrlController = TextEditingController(text: 'https://example.com/video.mp4');
  final TextEditingController _ocrSubtitlesUrlController = TextEditingController(text: 'https://sns-video-ak.xhscdn.com/stream/79/110/114/01e857b9fb7e0b604f0370019796afa0ab_114.mp4');
  final TextEditingController _tencentAsrUrlController = TextEditingController(text: 'https://sns-video-ak.xhscdn.com/stream/79/110/114/01e857b9fb7e0b604f0370019796afa0ab_114.mp4');
  final TextEditingController _businessJsUrlController = TextEditingController(text: 'https://www.xiaohongshu.com/explore');
  final _storageService = StorageService();

  // 进度测试相关状态
  bool _isTestingProgress = false;
  String _progressTestResult = '';

  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }

  // 显示短时间 Toast
  Future<void> _showShortToast() async {
    try {
      await NativeBridge().showToast('这是一条短时间 Toast 消息');
      setState(() {
        _toastResult = '短 Toast 显示成功';
      });
    } catch (e) {
      setState(() {
        _toastResult = '显示 Toast 失败: $e';
      });
    }
  }

  // 显示长时间 Toast
  Future<void> _showLongToast() async {
    try {
      await NativeBridge().showToast('这是一条长时间 Toast 消息', duration: NativeBridge.TOAST_LENGTH_LONG);
      setState(() {
        _toastResult = '长 Toast 显示成功';
      });
    } catch (e) {
      setState(() {
        _toastResult = '显示 Toast 失败: $e';
      });
    }
  }

  // 显示自定义Toast
  Future<void> _showCustomToast({bool isLong = false}) async {
    try {
      final bool result = await NativeBridge().showCustomToast(
        isLong ? '这是一条长时间自定义Toast，模拟Android原生Toast效果' : '这是一条短时间自定义Toast',
        isLong: isLong,
      );
      setState(() {
        _toastResult = '自定义Toast显示${result ? "成功" : "失败，已回退到系统Toast"}';
      });
    } catch (e) {
      setState(() {
        _toastResult = '显示自定义Toast失败: $e';
      });
    }
  }

  // 显示自定义Toast Modal
  Future<void> _showCustomToastModal() async {
    try {
      final bool result = await NativeBridge().showCustomToastModal(
        title: '确认删除',
        content: '确认要删除这个收藏吗？',
        cancelText: '取消',
        confirmText: '确定',
      );
      setState(() {
        _toastResult = '自定义Toast Modal显示${result ? "成功" : "失败，需要悬浮窗权限"}';
      });
    } catch (e) {
      setState(() {
        _toastResult = '显示自定义Toast Modal失败: $e';
      });
    }
  }

  Future<void> _downloadDouyinVideo() async {
    setState(() {
      _isDownloading = true;
      _downloadResult = '正在下载视频...';
    });

    try {
      final videoUrl = _downloadVideoUrlController.text.trim();
      if (videoUrl.isEmpty) {
        setState(() {
          _downloadResult = '请输入视频下载链接';
        });
        return;
      }

      final result = await NativeBridge().downloadDouyinVideo(
        url: videoUrl,
      );

      if (result['success'] == true) {
        setState(() {
          _downloadResult = '视频下载成功：${result['filePath']}';
        });
      } else {
        setState(() {
          _downloadResult = '视频下载失败：${result['error']}';
        });
      }
    } catch (e) {
      setState(() {
        _downloadResult = '下载过程出错：$e';
      });
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }

  Future<void> _downloadVideoAsAudio() async {
    setState(() {
      _isDownloadingAudio = true;
      _downloadAudioResult = '正在下载视频...';
    });

    try {
      final videoUrl = _downloadAudioUrlController.text.trim();
      if (videoUrl.isEmpty) {
        setState(() {
          _downloadAudioResult = '请输入视频下载链接';
        });
        return;
      }

      // 第一步：下载视频（不保存到相册，保留在缓存目录）
      setState(() {
        _downloadAudioResult = '正在下载视频...';
      });

      final downloadResult = await NativeBridge().downloadVideo(
        url: videoUrl,
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        referer: 'https://www.douyin.com',
        outputPath: null, // 使用默认缓存路径
        saveToGallery: false, // 不保存到相册，保留在缓存目录
      );

      if (downloadResult['success'] != true) {
        setState(() {
          _downloadAudioResult = '视频下载失败：${downloadResult['error']}';
        });
        return;
      }

      final videoPath = downloadResult['filePath'] as String?;
      if (videoPath == null || videoPath.isEmpty) {
        setState(() {
          _downloadAudioResult = '视频下载失败：未获取到视频文件路径';
        });
        return;
      }

      // 第二步：直接转换为PCM格式（同时从转换日志中获取时长）
      setState(() {
        _downloadAudioResult = '视频下载成功，正在转换为PCM格式...';
      });

      final pcmResult = await NativeBridge().convertAudioToPCM(
        audioPath: videoPath, // 直接使用MP4文件路径
        sampleRate: 16000,
        channels: 1,
        saveToGallery: false,
      );

      if (pcmResult['success'] == true) {
        final pcmPath = pcmResult['filePath'] as String?;

        // 从转换结果中获取视频时长
        final videoDurationSeconds = pcmResult['duration'] as double?;

        String durationInfo = '';
        if (videoDurationSeconds != null) {
          final durationMinutes = (videoDurationSeconds / 60).toStringAsFixed(1);
          durationInfo = '视频时长：${durationMinutes}分钟\n';
        }

        setState(() {
          _downloadAudioResult = '${durationInfo}PCM转换成功：$pcmPath\n正在进行语音识别...';
        });

        // 第三步：使用科大讯飞进行语音识别
        await _recognizeAudioWithSparkChain(pcmPath, videoDurationSeconds);

        // 转换成功后删除临时视频文件
        try {
          final file = File(videoPath);
          if (await file.exists()) {
            await file.delete();
            print('已删除临时视频文件: $videoPath');
          }
        } catch (e) {
          print('删除临时视频文件失败: $e');
        }
      } else {
        setState(() {
          _downloadAudioResult = 'PCM转换失败：${pcmResult['error']}';
        });
      }
    } catch (e) {
      setState(() {
        _downloadAudioResult = '处理过程出错：$e';
      });
    } finally {
      setState(() {
        _isDownloadingAudio = false;
      });
    }
  }

  /// 使用科大讯飞识别PCM文件（支持长音频分段识别）
  Future<void> _recognizeAudioWithSparkChain(String? pcmPath, [double? videoDurationSeconds]) async {
    if (pcmPath == null || pcmPath.isEmpty) {
      setState(() {
        _downloadAudioResult += '\n语音识别失败：PCM文件路径为空';
      });
      return;
    }

    try {
      // 首先初始化SparkChain SDK
      setState(() {
        _downloadAudioResult += '\n正在初始化语音识别SDK...';
      });

      final initResult = await SparkChainBridge.initSparkChainFromConfig();
      if (initResult['success'] != true) {
        setState(() {
          _downloadAudioResult += '\n语音识别SDK初始化失败：${initResult['message']}';
        });
        return;
      }

      setState(() {
        _downloadAudioResult += '\nSDK初始化成功，开始处理音频...';
      });

      // 使用音频分段处理器进行识别（传递预获取的视频时长）
      final recognitionResult = await AudioSegmentProcessor.processLongAudio(
        audioPath: pcmPath,
        videoDurationSeconds: videoDurationSeconds,
        onProgress: (currentSegment, totalSegments, status) {
          setState(() {
            if (totalSegments > 1) {
              _downloadAudioResult = _downloadAudioResult.split('\n开始处理音频...')[0] +
                                   '\n开始处理音频...\n$status';
            } else {
              _downloadAudioResult = _downloadAudioResult.split('\n开始处理音频...')[0] +
                                   '\n开始处理音频...\n$status';
            }
          });
        },
        onOverallProgress: (double progress) {
          // 打印整体进度
          final progressPercent = (progress * 100).toStringAsFixed(1);
          print('整体识别进度: $progressPercent%');

          setState(() {
            final baseResult = _downloadAudioResult.split('\n开始处理音频...')[0];
            _downloadAudioResult = '$baseResult\n开始处理音频...\n整体进度: $progressPercent%';
          });
        },
      );

      if (recognitionResult['success'] == true) {
        final recognizedText = recognitionResult['text'] as String?;
        final segmentCount = recognitionResult['segmentCount'] as int? ?? 1;
        final recognizedSegments = recognitionResult['recognizedSegments'] as int? ?? 1;
        final segmentInfos = recognitionResult['segmentInfos'] as List<dynamic>? ?? [];

        setState(() {
          _downloadAudioResult += '\n\n🎯 语音识别完成！';
          if (segmentCount > 1) {
            _downloadAudioResult += '\n📊 处理统计：共切割 $segmentCount 段，成功识别 $recognizedSegments 段';

            // 显示片段详细信息
            _downloadAudioResult += '\n\n📁 音频片段信息：';
            for (final segmentInfo in segmentInfos) {
              final info = segmentInfo as Map<String, dynamic>;
              final index = info['index'];
              final startTime = info['startTime'];
              final duration = info['duration'];
              final fileName = info['fileName'];
              _downloadAudioResult += '\n  第${index}段: ${startTime}s-${startTime + duration}s (${duration}s) - $fileName';
            }
            _downloadAudioResult += '\n\n💾 音频片段已保存到: /storage/emulated/0/Download/AudioSegments/';
          }
          _downloadAudioResult += '\n\n📝 识别结果：\n${recognizedText ?? "识别结果为空"}';
        });
      } else {
        setState(() {
          _downloadAudioResult += '\n语音识别失败：${recognitionResult['error'] ?? "未知错误"}';
        });
      }

      // 识别完成后删除PCM文件
      try {
        final file = File(pcmPath);
        if (await file.exists()) {
          await file.delete();
          print('已删除PCM文件: $pcmPath');
        }
      } catch (e) {
        print('删除PCM文件失败: $e');
      }
    } catch (e) {
      setState(() {
        _downloadAudioResult += '\n语音识别过程出错：$e';
      });
    }
  }

  /// 选择M4A文件并转换为PCM进行识别
  Future<void> _selectAndRecognizeM4aFile() async {
    setState(() {
      _isRecognizingM4a = true;
      _m4aRecognitionResult = '正在选择M4A文件...';
    });

    try {
      // 使用FilePicker选择音频/视频文件
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['m4a', 'mp4', 'mov', 'mp3', 'wav', 'aac', 'flac'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        setState(() {
          _m4aRecognitionResult = '未选择文件';
        });
        return;
      }

      final file = result.files.first;
      final filePath = file.path;

      if (filePath == null) {
        setState(() {
          _m4aRecognitionResult = '无法获取文件路径';
        });
        return;
      }

      setState(() {
        _m4aRecognitionResult = '已选择文件: ${file.name}\n文件路径: $filePath\n正在检查文件格式...';
      });

      // 检查文件扩展名
      final fileName = file.name.toLowerCase();
      final supportedFormats = ['m4a', 'mp4', 'mov', 'mp3', 'wav', 'aac', 'flac'];
      final isSupported = supportedFormats.any((format) => fileName.endsWith('.$format'));

      if (!isSupported) {
        setState(() {
          _m4aRecognitionResult = '不支持的文件格式，请选择以下格式的文件：\n${supportedFormats.join(', ')}';
        });
        return;
      }

      setState(() {
        _m4aRecognitionResult = '文件格式检查通过\n正在转换为PCM格式...';
      });

      // 转换为PCM格式（同时从转换日志中获取时长）
      final pcmResult = await NativeBridge().convertAudioToPCM(
        audioPath: filePath,
        sampleRate: 16000,
        channels: 1,
        saveToGallery: false,
      );

      if (pcmResult['success'] != true) {
        setState(() {
          _m4aRecognitionResult = 'PCM转换失败：${pcmResult['error']}';
        });
        return;
      }

      final pcmPath = pcmResult['filePath'] as String?;
      if (pcmPath == null || pcmPath.isEmpty) {
        setState(() {
          _m4aRecognitionResult = 'PCM转换失败：未获取到PCM文件路径';
        });
        return;
      }

      // 从转换结果中获取音频时长
      final fileDurationSeconds = pcmResult['duration'] as double?;
      String durationInfo = '';
      if (fileDurationSeconds != null) {
        final durationMinutes = (fileDurationSeconds / 60).toStringAsFixed(1);
        durationInfo = '文件时长：${durationMinutes}分钟\n';
      }

      setState(() {
        _m4aRecognitionResult = '${durationInfo}PCM转换成功：$pcmPath\n正在初始化语音识别SDK...';
      });

      // 初始化SparkChain SDK
      final initResult = await SparkChainBridge.initSparkChainFromConfig();
      if (initResult['success'] != true) {
        setState(() {
          _m4aRecognitionResult += '\n语音识别SDK初始化失败：${initResult['message']}';
        });
        return;
      }

      setState(() {
        _m4aRecognitionResult += '\nSDK初始化成功，开始语音识别...';
      });

      // 使用音频分段处理器进行识别
      final recognitionResult = await AudioSegmentProcessor.processLongAudio(
        audioPath: pcmPath,
        videoDurationSeconds: fileDurationSeconds,
        onProgress: (currentSegment, totalSegments, status) {
          setState(() {
            if (totalSegments > 1) {
              _m4aRecognitionResult = _m4aRecognitionResult.split('\n开始语音识别...')[0] +
                                   '\n开始语音识别...\n$status';
            } else {
              _m4aRecognitionResult = _m4aRecognitionResult.split('\n开始语音识别...')[0] +
                                   '\n开始语音识别...\n$status';
            }
          });
        },
        onOverallProgress: (double progress) {
          // 打印整体进度
          final progressPercent = (progress * 100).toStringAsFixed(1);
          print('M4A文件整体识别进度: $progressPercent%');

          setState(() {
            final baseResult = _m4aRecognitionResult.split('\n开始语音识别...')[0];
            _m4aRecognitionResult = '$baseResult\n开始语音识别...\n整体进度: $progressPercent%';
          });
        },
      );

      if (recognitionResult['success'] == true) {
        final recognizedText = recognitionResult['text'] as String?;
        final segmentCount = recognitionResult['segmentCount'] as int? ?? 1;
        final recognizedSegments = recognitionResult['recognizedSegments'] as int? ?? 1;
        final segmentInfos = recognitionResult['segmentInfos'] as List<dynamic>? ?? [];

        setState(() {
          _m4aRecognitionResult += '\n\n🎯 语音识别完成！';
          _m4aRecognitionResult += '\n📊 处理统计：共切割 $segmentCount 段，成功识别 $recognizedSegments 段';

          if (segmentCount > 1) {
            // 显示片段详细信息
            _m4aRecognitionResult += '\n\n📁 音频片段信息：';
            for (final segmentInfo in segmentInfos) {
              final info = segmentInfo as Map<String, dynamic>;
              final index = info['index'];
              final startTime = info['startTime'];
              final duration = info['duration'];
              final fileName = info['fileName'];
              _m4aRecognitionResult += '\n  第${index}段: ${startTime}s-${startTime + duration}s (${duration}s) - $fileName';
            }
            _m4aRecognitionResult += '\n\n💾 音频片段已保存到: /storage/emulated/0/Download/AudioSegments/';
          }

          _m4aRecognitionResult += '\n\n📝 识别结果：\n${recognizedText ?? "识别结果为空"}';
        });
      } else {
        setState(() {
          _m4aRecognitionResult += '\n语音识别失败：${recognitionResult['error'] ?? "未知错误"}';
        });
      }

      // 清理PCM文件
      try {
        final file = File(pcmPath);
        if (await file.exists()) {
          await file.delete();
          print('已删除PCM文件: $pcmPath');
        }
      } catch (e) {
        print('删除PCM文件失败: $e');
      }

    } catch (e) {
      setState(() {
        _m4aRecognitionResult = 'M4A文件识别过程出错：$e';
      });
    } finally {
      setState(() {
        _isRecognizingM4a = false;
      });
    }
  }

  /// 测试进度显示的下载功能
  Future<void> _testProgressDownload() async {
    setState(() {
      _isTestingProgress = true;
      _progressTestResult = '正在下载视频...';
    });

    try {
      final videoUrl = _progressTestUrlController.text.trim();
      if (videoUrl.isEmpty) {
        setState(() {
          _progressTestResult = '请输入视频下载链接';
        });
        return;
      }

      // 第一步：下载视频（不保存到相册，保留在缓存目录）
      setState(() {
        _progressTestResult = '正在下载视频...';
      });

      final downloadResult = await NativeBridge().downloadVideo(
        url: videoUrl,
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        referer: 'https://www.douyin.com',
        outputPath: null, // 使用默认缓存路径
        saveToGallery: false, // 不保存到相册，保留在缓存目录
      );

      if (downloadResult['success'] != true) {
        setState(() {
          _progressTestResult = '视频下载失败：${downloadResult['error']}';
        });
        return;
      }

      final videoPath = downloadResult['filePath'] as String?;
      if (videoPath == null || videoPath.isEmpty) {
        setState(() {
          _progressTestResult = '视频下载失败：未获取到视频文件路径';
        });
        return;
      }

      // 第二步：直接转换为PCM格式（同时从转换日志中获取时长）
      setState(() {
        _progressTestResult = '视频下载成功，正在转换为PCM格式...';
      });

      final pcmResult = await NativeBridge().convertAudioToPCM(
        audioPath: videoPath, // 直接使用MP4文件路径
        sampleRate: 16000,
        channels: 1,
        saveToGallery: false,
      );

      if (pcmResult['success'] == true) {
        final pcmPath = pcmResult['filePath'] as String?;

        // 从转换结果中获取视频时长
        final videoDurationSeconds = pcmResult['duration'] as double?;

        String durationInfo = '';
        if (videoDurationSeconds != null) {
          final durationMinutes = (videoDurationSeconds / 60).toStringAsFixed(1);
          durationInfo = '视频时长：${durationMinutes}分钟\n';
        }

        setState(() {
          _progressTestResult = '${durationInfo}PCM转换成功：$pcmPath\n正在进行语音识别...';
        });

        // 第三步：使用科大讯飞进行语音识别（带进度显示）
        await _recognizeAudioWithProgressTest(pcmPath, videoDurationSeconds);

        // 转换成功后删除临时视频文件
        try {
          final file = File(videoPath);
          if (await file.exists()) {
            await file.delete();
            print('已删除临时视频文件: $videoPath');
          }
        } catch (e) {
          print('删除临时视频文件失败: $e');
        }
      } else {
        setState(() {
          _progressTestResult = 'PCM转换失败：${pcmResult['error']}';
        });
      }
    } catch (e) {
      setState(() {
        _progressTestResult = '处理过程出错：$e';
      });
    } finally {
      setState(() {
        _isTestingProgress = false;
      });
    }
  }

  /// 使用科大讯飞识别PCM文件（支持进度显示测试）
  Future<void> _recognizeAudioWithProgressTest(String? pcmPath, [double? videoDurationSeconds]) async {
    if (pcmPath == null || pcmPath.isEmpty) {
      setState(() {
        _progressTestResult += '\n语音识别失败：PCM文件路径为空';
      });
      return;
    }

    try {
      // 初始化SparkChain SDK
      final initResult = await SparkChainBridge.initSparkChainFromConfig();
      if (initResult['success'] != true) {
        setState(() {
          _progressTestResult += '\n语音识别SDK初始化失败：${initResult['message']}';
        });
        return;
      }

      setState(() {
        _progressTestResult += '\nSDK初始化成功，开始处理音频...';
      });

      // 使用音频分段处理器进行识别（传递预获取的视频时长）
      final recognitionResult = await AudioSegmentProcessor.processLongAudio(
        audioPath: pcmPath,
        videoDurationSeconds: videoDurationSeconds,
        onProgress: (currentSegment, totalSegments, status) {
          setState(() {
            if (totalSegments > 1) {
              _progressTestResult = _progressTestResult.split('\n开始处理音频...')[0] +
                                   '\n开始处理音频...\n$status';
            } else {
              _progressTestResult = _progressTestResult.split('\n开始处理音频...')[0] +
                                   '\n开始处理音频...\n$status';
            }
          });
        },
        onOverallProgress: (double progress) {
          // 显示整体进度
          final progressPercent = (progress * 100).toStringAsFixed(1);
          print('🎯 进度测试 - 整体识别进度: $progressPercent%');

          setState(() {
            final baseResult = _progressTestResult.split('\n开始处理音频...')[0];
            _progressTestResult = '$baseResult\n开始处理音频...\n🎯 整体进度: $progressPercent%';
          });
        },
      );

      if (recognitionResult['success'] == true) {
        final recognizedText = recognitionResult['text'] as String?;
        final segmentCount = recognitionResult['segmentCount'] as int? ?? 1;
        final recognizedSegments = recognitionResult['recognizedSegments'] as int? ?? 1;

        setState(() {
          _progressTestResult += '\n\n🎯 进度测试完成！';
          _progressTestResult += '\n📊 处理统计：共切割 $segmentCount 段，成功识别 $recognizedSegments 段';
          _progressTestResult += '\n📝 识别结果：\n${recognizedText ?? "识别结果为空"}';
        });
      } else {
        setState(() {
          _progressTestResult += '\n语音识别失败：${recognitionResult['error'] ?? "未知错误"}';
        });
      }

      // 清理PCM文件
      try {
        final file = File(pcmPath);
        if (await file.exists()) {
          await file.delete();
          print('已删除PCM文件: $pcmPath');
        }
      } catch (e) {
        print('删除PCM文件失败: $e');
      }
    } catch (e) {
      setState(() {
        _progressTestResult += '\n语音识别过程出错：$e';
      });
    }
  }

  Future<void> _extractVideoUrlFromWebpage() async {
    setState(() {
      _isExtractingVideoUrl = true;
      _extractedVideoUrl = '正在提取视频链接...';
    });

    try {
      final testUrl = _videoUrlController.text.trim();
      if (testUrl.isEmpty) {
        setState(() {
          _extractedVideoUrl = '请输入视频页面URL';
        });
        return;
      }

      final result = await NativeBridge().extractVideoUrlFromWebpage(
        url: testUrl,
      );

      if (result['success'] == true && result['videoUrl'] != null) {
        setState(() {
          _extractedVideoUrl = '提取成功：${result['videoUrl']}';
        });
      } else {
        setState(() {
          _extractedVideoUrl = '提取失败：${result['error'] ?? "未找到视频"}';
        });
      }
    } catch (e) {
      setState(() {
        _extractedVideoUrl = '提取过程出错：$e';
      });
    } finally {
      setState(() {
        _isExtractingVideoUrl = false;
      });
    }
  }

  Future<void> _downloadAndExtractText() async {
    setState(() {
      _isExtractingText = true;
      _extractedText = '正在下载视频并提取文字...';
    });

    try {
      final result = await NativeBridge().downloadDouyinVideoAndExtractText(
        url: "https://sns-video-ak.xhscdn.com/stream/79/110/114/01e83535ae49db544f037001970fdb6c84_114.mp4",
        useChinese: true,
      );

      if (result['success'] == true && result['texts'] != null) {
        final List<dynamic> texts = result['texts'] as List<dynamic>;
        setState(() {
          _extractedText = '提取到的文字（${texts.length}条）:\n\n${texts.join('\n\n')}';
        });
      } else {
        setState(() {
          _extractedText = '提取文字失败：${result['error'] ?? "未知错误"}';
        });
      }
    } catch (e) {
      setState(() {
        _extractedText = '提取文字过程出错：$e';
      });
    } finally {
      setState(() {
        _isExtractingText = false;
      });
    }
  }

  Future<void> _extractVideoUrlAndExtractText() async {
    setState(() {
      _isExtractingVideoUrlAndText = true;
      _extractedVideoUrlAndText = '正在从网页提取视频链接并提取文字...';
    });

    try {
      final testUrl = _videoPageUrlController.text.trim();
      if (testUrl.isEmpty) {
        setState(() {
          _extractedVideoUrlAndText = '请输入视频页面URL';
        });
        return;
      }

      final result = await NativeBridge().extractVideoUrlAndExtractText(
        webpageUrl: testUrl,
        timeout: 30000,
        useChinese: true,
      );

      if (result['success'] == true && result['texts'] != null) {
        final List<dynamic> texts = result['texts'] as List<dynamic>;
        setState(() {
          _extractedVideoUrlAndText = '提取到的文字（${texts.length}条）:\n\n${texts.join('\n\n')}';
        });
      } else {
        setState(() {
          _extractedVideoUrlAndText = '提取失败：${result['error'] ?? "未知错误"}';
        });
      }
    } catch (e) {
      setState(() {
        _extractedVideoUrlAndText = '提取过程出错：$e';
      });
    } finally {
      setState(() {
        _isExtractingVideoUrlAndText = false;
      });
    }
  }

  Future<void> _recognizeVideoSubtitlesWithOCR() async {
    setState(() {
      _isRecognizingOcrSubtitles = true;
      _ocrSubtitlesResult = '正在下载视频并OCR识别字幕...';
    });

    try {
      final videoUrl = _ocrSubtitlesUrlController.text.trim();
      if (videoUrl.isEmpty) {
        setState(() {
          _ocrSubtitlesResult = '请输入视频URL';
        });
        return;
      }

      final result = await NativeBridge().downloadVideoAndExtractSubtitles(
        url: videoUrl,
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        referer: 'https://www.douyin.com',
        useChinese: true,
      );

      if (result['success'] == true && result['subtitles'] != null) {
        final List<dynamic> subtitles = result['subtitles'] as List<dynamic>;
        final int subtitleCount = result['subtitleCount'] as int? ?? subtitles.length;

        setState(() {
          _ocrSubtitlesResult = 'OCR识别完成，共识别到${subtitleCount}条字幕:\n\n';
          for (int i = 0; i < subtitles.length; i++) {
            _ocrSubtitlesResult += '第${i + 1}条: ${subtitles[i]}\n\n';
          }
        });
      } else {
        setState(() {
          _ocrSubtitlesResult = 'OCR识别失败：${result['error'] ?? "未知错误"}';
        });
      }
    } catch (e) {
      setState(() {
        _ocrSubtitlesResult = 'OCR识别过程出错：$e';
      });
    } finally {
      setState(() {
        _isRecognizingOcrSubtitles = false;
      });
    }
  }

  /// 腾讯云ASR极速识别
  Future<void> _recognizeWithTencentAsr() async {
    setState(() {
      _isRecognizingTencentAsr = true;
      _tencentAsrResult = '正在初始化腾讯云ASR SDK...';
    });

    try {
      final videoUrl = _tencentAsrUrlController.text.trim();
      if (videoUrl.isEmpty) {
        setState(() {
          _tencentAsrResult = '请输入视频URL';
        });
        return;
      }

      // 第一步：初始化腾讯云ASR SDK
      setState(() {
        _tencentAsrResult = '正在初始化腾讯云ASR SDK...';
      });

      final initResult = await TencentAsrBridge.initTencentAsrSdk();
      if (initResult['success'] != true) {
        setState(() {
          _tencentAsrResult = '腾讯云ASR SDK初始化失败：${initResult['error']}';
        });
        return;
      }

      // 第二步：下载视频到本地内存
      setState(() {
        _tencentAsrResult = 'SDK初始化成功，正在下载视频...';
      });

      final downloadResult = await NativeBridge().downloadVideo(
        url: videoUrl,
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        referer: 'https://www.douyin.com',
        outputPath: null, // 使用默认缓存路径
        saveToGallery: false, // 不保存到相册，保留在缓存目录
      );

      if (downloadResult['success'] != true) {
        setState(() {
          _tencentAsrResult = '视频下载失败：${downloadResult['error']}';
        });
        return;
      }

      final videoPath = downloadResult['filePath'] as String?;
      if (videoPath == null || videoPath.isEmpty) {
        setState(() {
          _tencentAsrResult = '视频下载失败：未获取到视频文件路径';
        });
        return;
      }

      // 第三步：调用腾讯云ASR极速识别（自动转换MP4为PCM）
      setState(() {
        _tencentAsrResult = '视频下载成功，正在转换格式并进行语音识别...';
      });

      final recognitionResult = await TencentAsrBridge.recognizeFlashByFile(
        filePath: videoPath,
        voiceFormat: 'mp4', // 这里传入mp4，后端会自动检测并转换为PCM
        engineModelType: '16k_zh_video',
      );

      // 第四步：删除临时视频文件
      try {
        final file = File(videoPath);
        if (await file.exists()) {
          await file.delete();
          print('已删除临时视频文件: $videoPath');
        }
      } catch (e) {
        print('删除临时视频文件失败: $e');
      }

      // 第五步：显示识别结果
      if (recognitionResult['success'] == true) {
        final recognizedText = recognitionResult['result'] as String?;
        setState(() {
          _tencentAsrResult = '🎯 腾讯云ASR极速识别完成！\n\n📝 识别结果：\n${recognizedText ?? "识别结果为空"}';
        });
      } else {
        setState(() {
          _tencentAsrResult = '腾讯云ASR识别失败：${recognitionResult['error'] ?? "未知错误"}';
        });
      }
    } catch (e) {
      setState(() {
        _tencentAsrResult = '腾讯云ASR识别过程出错：$e';
      });
    } finally {
      setState(() {
        _isRecognizingTencentAsr = false;
      });
    }
  }

  Future<void> _openWebPage(bool useSystemBrowser) async {
    setState(() {
      _isOpeningWebPage = true;
    });

    try {
      final url = _urlController.text.trim();
      if (url.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请输入网页地址')),
        );
        return;
      }

      final success = await NativeBridge().openWebPage(
        url: url,
        useSystemBrowser: useSystemBrowser,
      );

      if (!success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('打开网页失败')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('打开网页出错: $e')),
      );
    } finally {
      setState(() {
        _isOpeningWebPage = false;
      });
    }
  }

  Future<void> _viewLocalStorage() async {
    setState(() {
      _isLoadingStorage = true;
      _storageContent = '正在加载本地存储...';
    });

    try {
      // 获取SharedPreferences实例来访问所有键值对
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();

      // 格式化显示
      final buffer = StringBuffer();
      buffer.writeln('===== 本地存储内容 =====');
      buffer.writeln('共${allKeys.length}个键值对\n');

      // 按字母顺序排序键
      final sortedKeys = allKeys.toList()..sort();

      // 显示所有键值对
      for (final key in sortedKeys) {
        buffer.writeln('\n----- $key -----');

        // 获取值
        final value = _getValueByKey(prefs, key);

        // 如果值是字符串且太长，只显示部分
        if (value is String && value.length > 100) {
          buffer.writeln('${value.substring(0, 50)}...${value.substring(value.length - 50)}');
          buffer.writeln('(长度: ${value.length}字符)');
        } else {
          buffer.writeln(value);
        }
      }

      setState(() {
        _storageContent = buffer.toString();
      });
    } catch (e) {
      setState(() {
        _storageContent = '加载存储出错: $e';
      });
    } finally {
      setState(() {
        _isLoadingStorage = false;
      });
    }
  }

  /// 根据键获取值，处理不同类型
  dynamic _getValueByKey(SharedPreferences prefs, String key) {
    // 尝试以不同类型获取值
    if (prefs.containsKey(key)) {
      if (prefs.getString(key) != null) return prefs.getString(key);
      if (prefs.getBool(key) != null) return prefs.getBool(key);
      if (prefs.getInt(key) != null) return prefs.getInt(key);
      if (prefs.getDouble(key) != null) return prefs.getDouble(key);
      if (prefs.getStringList(key) != null) return prefs.getStringList(key)?.join(", ");
    }
    return "null";
  }

  Future<void> _clearLocalStorage() async {
    try {
      await _storageService.clearUserInfo();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('本地存储已清除')),
      );
      // 如果正在显示存储内容，则刷新显示
      if (_storageContent.isNotEmpty) {
        _viewLocalStorage();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('清除本地存储出错: $e')),
      );
    }
  }

  // 将资源链接转换为OSS链接
  Future<void> _convertResourceToOssLink() async {
    final url = _resourceUrlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入资源链接')),
      );
      return;
    }

    setState(() {
      _isConvertingLink = true;
      _ossLinkResult = '正在转换资源链接...';
    });

    try {
      // 使用自定义方法，需要在NativeBridge中添加此方法
      final result = await NativeBridge().convertToOssLink(url: url);

      if (result['success'] == true) {
        setState(() {
          _ossLinkResult = '转换成功：${result['ossUrl']}';
        });
      } else {
        setState(() {
          _ossLinkResult = '转换失败：${result['error'] ?? "未知错误"}';
        });
      }
    } catch (e) {
      setState(() {
        _ossLinkResult = '转换过程出错：$e';
      });
    } finally {
      setState(() {
        _isConvertingLink = false;
      });
    }
  }

  // 上传文件到OSS
  Future<void> _uploadFileToOss() async {
    // 使用图片选择器选择图片
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('未选择图片')),
      );
      return;
    }

    setState(() {
      _isUploadingToOss = true;
      _ossUploadResult = '正在上传文件到OSS...';
    });

    try {
      // 上传文件到OSS
      final result = await NativeBridge().uploadFileToOss(
        filePath: image.path,
      );

      if (result['success'] == true) {
        setState(() {
          _ossUploadResult = '上传成功：${result['ossUrl']}';
        });
      } else {
        setState(() {
          _ossUploadResult = '上传失败：${result['error'] ?? "未知错误"}';
        });
      }
    } catch (e) {
      setState(() {
        _ossUploadResult = '上传过程出错：$e';
      });
    } finally {
      setState(() {
        _isUploadingToOss = false;
      });
    }
  }

  // 打开URL Scheme
  Future<void> _openSchemeUrl() async {
    final schemeUrl = _schemeUrlController.text.trim();
    if (schemeUrl.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入URL Scheme')),
      );
      return;
    }

    setState(() {
      _isOpeningSchemeUrl = true;
    });

    try {
      final result = await NativeBridge().openSchemeUrl(schemeUrl);

      if (result) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('URL Scheme跳转成功')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('URL Scheme跳转失败，可能没有安装对应的应用')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('URL Scheme跳转出错: $e')),
      );
    } finally {
      setState(() {
        _isOpeningSchemeUrl = false;
      });
    }
  }

  // 提取HTML内容
  Future<void> _extractHtmlFromUrl() async {
    final url = _htmlUrlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入网页URL')),
      );
      return;
    }

    setState(() {
      _isExtractingHtml = true;
      _extractedHtml = '正在提取HTML内容...';
    });

    try {
      final result = await NativeBridge().extractHtmlFromUrl(url: url);

      if (result['success'] == true) {
        final html = result['html'] as String;
        // 只显示HTML的前1000个字符，避免UI过载
        final displayHtml = html.length > 1000
            ? '${html.substring(0, 1000)}...\n\n(HTML内容过长，仅显示前1000个字符，总长度: ${html.length}字符)'
            : html;

        setState(() {
          _extractedHtml = displayHtml;
        });
      } else {
        setState(() {
          _extractedHtml = '提取失败：${result['error'] ?? "未知错误"}';
        });
      }
    } catch (e) {
      setState(() {
        _extractedHtml = '提取过程出错：$e';
      });
    } finally {
      setState(() {
        _isExtractingHtml = false;
      });
    }
  }

  // 执行业务JavaScript
  Future<void> _executeBusinessJavaScript() async {
    final url = _businessJsUrlController.text.trim();
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入网页URL')),
      );
      return;
    }

    setState(() {
      _isExecutingBusinessJs = true;
      _businessJsResult = '正在执行业务JavaScript...';
    });

    try {
      final result = await NativeBridge().executeBusinessJavaScript(
        url: url,
        businessName: 'extractVideo',
      );

      if (result['success'] == true) {
        final jsResult = result['result'] as String? ?? '无返回结果';
        setState(() {
          _businessJsResult = '执行成功：\n$jsResult';
        });
        print('业务JavaScript执行结果: $jsResult');
      } else {
        setState(() {
          _businessJsResult = '执行失败：${result['error'] ?? "未知错误"}';
        });
      }
    } catch (e) {
      setState(() {
        _businessJsResult = '执行过程出错：$e';
      });
    } finally {
      setState(() {
        _isExecutingBusinessJs = false;
      });
    }
  }

  // 显示自定义通知面板
  Future<void> _showCustomNotificationPanel() async {
    try {
      // 直接调用显示方法，权限检查在原生代码中处理
      await NativeBridge().showCustomNotificationPanel();
      setState(() {
        _toastResult = '自定义通知面板已显示，请查看通知栏';
      });
    } catch (e) {
      setState(() {
        _toastResult = '显示自定义通知面板失败：$e';
      });
    }
  }

  // 展示应用内通知
  Future<void> _showInAppNotification() async {
    try {
      // 显示一个简单的应用内通知
      await NativeBridge().showToast('这是一个应用内通知测试！', duration: NativeBridge.TOAST_LENGTH_LONG);
      setState(() {
        _toastResult = '应用内通知已显示';
      });
    } catch (e) {
      setState(() {
        _toastResult = '显示应用内通知失败：$e';
      });
    }
  }

  // 打开笔记WebView测试
  Future<void> _openNoteWebview() async {
    setState(() {
      _isOpeningNote = true;
    });

    try {
      // 测试打开ID为2的笔记
      final helper = NoteWebviewHelper();
      final success = await helper.openNoteDetail('2');

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('成功打开笔记详情')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('打开笔记详情失败')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('打开笔记详情出错: $e')),
      );
    } finally {
      setState(() {
        _isOpeningNote = false;
      });
    }
  }

  // 编辑草稿测试
  Future<void> _openDraftEdit() async {
    try {
      // 测试编辑ID为2的笔记的草稿
      final helper = DraftEditHelper();
      final success = await helper.openDraftEdit(context, '2');

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('成功打开草稿编辑')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('该笔记暂无草稿内容')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('编辑草稿出错: $e')),
      );
    }
  }

  // 构建调试页面的每一个部分
  Widget _buildSection(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8.0),
        content,
      ],
    );
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _urlController.dispose();
    _resourceUrlController.dispose();
    _schemeUrlController.dispose();
    _htmlUrlController.dispose();
    _videoPageUrlController.dispose();
    _videoUrlController.dispose();
    _downloadVideoUrlController.dispose();
    _tencentAsrUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.background,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        title: Text(
          widget.title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              Color(0xFFF0F4F9),
            ],
            stops: [0.0, 1.0],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // 页面标题和计数器
                _buildHeaderSection(),
                SizedBox(height: 16.h),

                // 核心功能区域
                _buildCoreFeatureSection(),
                SizedBox(height: 16.h),

                // 视频处理功能
                _buildVideoProcessingSection(),
                SizedBox(height: 16.h),

                // 一体化测试功能
                _buildIntegratedTestSection(),
                SizedBox(height: 16.h),

                // 系统功能
                _buildSystemFeaturesSection(),
                SizedBox(height: 16.h),

                // 网络和存储功能
                _buildNetworkStorageSection(),
                SizedBox(height: 16.h),

                // 其他工具
                _buildOtherToolsSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建页面头部区域
  Widget _buildHeaderSection() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.r),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.bug_report_outlined,
              color: AppColors.primary,
              size: 24.r,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '调试工具',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '当前计数: $_counter',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          _buildModernButton(
            onPressed: _incrementCounter,
            icon: Icons.add,
            label: '增加',
            color: AppColors.primary,
          ),
        ],
      ),
    );
  }

  // 构建核心功能区域
  Widget _buildCoreFeatureSection() {
    return _buildSectionCard(
      title: '核心功能',
      icon: Icons.star_outline,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: () async {
                  final hasPermission = await NativeBridge().checkOverlayPermission();
                  if (!hasPermission) {
                    await NativeBridge().openOverlayPermissionSettings();
                    return;
                  }
                  await NativeBridge().toggleFloatingWindow();
                },
                icon: Icons.picture_in_picture_outlined,
                label: '悬浮窗',
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: () async {
                  final hasPermission = await NativeBridge().checkAccessibilityPermission();
                  if (!hasPermission) {
                    await NativeBridge().openAccessibilitySettings();
                  }
                },
                icon: Icons.accessibility_outlined,
                label: '无障碍权限',
                color: AppColors.info,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: () async {
                  await NativeBridge().showPanel();
                },
                icon: Icons.share_outlined,
                label: '分享面板',
                color: AppColors.success,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: () async {
                  final hasPermission = await NativeBridge().checkOverlayPermission();
                  if (!hasPermission) {
                    await NativeBridge().openOverlayPermissionSettings();
                    return;
                  }

                  final isShowing = await NativeBridge().isSidebarShowing();
                  if (isShowing) {
                    await NativeBridge().hideSidebar();
                  } else {
                    await NativeBridge().showSidebar();
                  }
                },
                icon: Icons.border_left_outlined,
                label: '侧边栏',
                color: AppColors.warning,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: () async {
                  final hasPermission = await NativeBridge().checkOverlayPermission();
                  if (!hasPermission) {
                    await NativeBridge().openOverlayPermissionSettings();
                    return;
                  }

                  final isShowing = await NativeBridge().isSidebarFloatingMenuShowing();
                  if (isShowing) {
                    await NativeBridge().hideSidebarFloatingMenu();
                  } else {
                    await NativeBridge().showSidebarFloatingMenu();
                  }
                },
                icon: Icons.menu_outlined,
                label: '侧边栏菜单',
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Container(), // 占位符，保持布局对称
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: () {
                  Navigator.pushNamed(context, AppRoutes.wechatLogin);
                },
                icon: Icons.chat_outlined,
                label: '微信登录',
                color: Color(0xFF07C160),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Container(), // 占位符，保持布局对称
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: _isOpeningNote ? null : _openNoteWebview,
                icon: Icons.article_outlined,
                label: _isOpeningNote ? '打开中...' : '笔记详情(ID:2)',
                color: AppColors.warning,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: _openDraftEdit,
                icon: Icons.edit_outlined,
                label: '编辑草稿',
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 构建视频处理功能区域
  Widget _buildVideoProcessingSection() {
    return _buildSectionCard(
      title: '视频处理',
      icon: Icons.video_library_outlined,
      children: [
        _buildModernTextField(
          controller: _downloadVideoUrlController,
          label: '视频下载链接',
          hint: '例如: https://example.com/video.mp4',
          icon: Icons.video_file,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isDownloading ? null : _downloadDouyinVideo,
          icon: Icons.download_outlined,
          label: _isDownloading ? '下载中...' : '下载抖音视频',
          isLoading: _isDownloading,
          color: AppColors.primary,
        ),
        if (_downloadResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_downloadResult, _downloadResult.contains('成功')),
        ],
        SizedBox(height: 16.h),
        _buildModernTextField(
          controller: _progressTestUrlController,
          label: '进度测试视频链接',
          hint: '例如: https://example.com/video.mp4',
          icon: Icons.speed,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isTestingProgress ? null : _testProgressDownload,
          icon: Icons.analytics_outlined,
          label: _isTestingProgress ? '测试中...' : '测试进度显示下载',
          isLoading: _isTestingProgress,
          color: AppColors.success,
        ),
        if (_progressTestResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_progressTestResult, _progressTestResult.contains('成功')),
        ],
        SizedBox(height: 16.h),
        _buildModernTextField(
          controller: _downloadAudioUrlController,
          label: '视频转音频链接',
          hint: '例如: https://example.com/video.mp4',
          icon: Icons.audiotrack,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isDownloadingAudio ? null : _downloadVideoAsAudio,
          icon: Icons.audiotrack_outlined,
          label: _isDownloadingAudio ? '转换中...' : '下载视频为音频 (MP3)',
          isLoading: _isDownloadingAudio,
          color: AppColors.warning,
        ),
        if (_downloadAudioResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_downloadAudioResult, _downloadAudioResult.contains('成功')),
        ],
        SizedBox(height: 16.h),
        // M4A文件识别功能
        _buildActionButton(
          onPressed: _isRecognizingM4a ? null : _selectAndRecognizeM4aFile,
          icon: Icons.audio_file_outlined,
          label: _isRecognizingM4a ? '识别中...' : '选择M4A文件进行识别',
          isLoading: _isRecognizingM4a,
          color: AppColors.info,
        ),
        if (_m4aRecognitionResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_m4aRecognitionResult, _m4aRecognitionResult.contains('成功')),
        ],
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isExtractingText ? null : _downloadAndExtractText,
          icon: Icons.text_fields_outlined,
          label: _isExtractingText ? '处理中...' : '下载视频并提取文字',
          isLoading: _isExtractingText,
          color: AppColors.success,
        ),
        if (_extractedText.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_extractedText, _extractedText.contains('提取到的文字')),
        ],
        SizedBox(height: 16.h),
        // OCR识别视频字幕功能
        _buildModernTextField(
          controller: _ocrSubtitlesUrlController,
          label: 'OCR字幕识别视频链接',
          hint: '例如: https://sns-video-ak.xhscdn.com/stream/.../video.mp4',
          icon: Icons.subtitles,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isRecognizingOcrSubtitles ? null : _recognizeVideoSubtitlesWithOCR,
          icon: Icons.subtitles_outlined,
          label: _isRecognizingOcrSubtitles ? '识别中...' : 'OCR识别视频字幕',
          isLoading: _isRecognizingOcrSubtitles,
          color: AppColors.info,
        ),
        if (_ocrSubtitlesResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_ocrSubtitlesResult, _ocrSubtitlesResult.contains('OCR识别完成')),
        ],
        SizedBox(height: 16.h),
        // 腾讯云ASR极速识别功能
        _buildModernTextField(
          controller: _tencentAsrUrlController,
          label: '腾讯云ASR极速识别视频链接',
          hint: '例如: https://sns-video-ak.xhscdn.com/stream/.../video.mp4',
          icon: Icons.record_voice_over,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isRecognizingTencentAsr ? null : _recognizeWithTencentAsr,
          icon: Icons.record_voice_over_outlined,
          label: _isRecognizingTencentAsr ? '识别中...' : '腾讯云ASR极速识别',
          isLoading: _isRecognizingTencentAsr,
          color: Color(0xFF00D4AA),
        ),
        if (_tencentAsrResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_tencentAsrResult, _tencentAsrResult.contains('识别完成')),
        ],
        SizedBox(height: 12.h),
        _buildModernTextField(
          controller: _videoUrlController,
          label: '视频页面URL',
          hint: '例如: https://v.douyin.com/dsqnT7sh1pY/',
          icon: Icons.link,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isExtractingVideoUrl ? null : _extractVideoUrlFromWebpage,
          icon: Icons.link_outlined,
          label: _isExtractingVideoUrl ? '提取中...' : '提取网页视频链接',
          isLoading: _isExtractingVideoUrl,
          color: AppColors.info,
        ),
        if (_extractedVideoUrl.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_extractedVideoUrl, _extractedVideoUrl.contains('提取成功')),
        ],
      ],
    );
  }

  // 构建一体化测试功能区域
  Widget _buildIntegratedTestSection() {
    return _buildSectionCard(
      title: '一体化测试',
      icon: Icons.auto_awesome_outlined,
      children: [
        Text(
          '输入视频页面URL，自动提取视频链接并下载视频提取文字',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: 12.h),
        _buildModernTextField(
          controller: _videoPageUrlController,
          label: '视频页面URL',
          hint: '例如: https://v.douyin.com/dsqnT7sh1pY/',
          icon: Icons.link,
        ),
        SizedBox(height: 16.h),
        _buildActionButton(
          onPressed: _isExtractingVideoUrlAndText ? null : _extractVideoUrlAndExtractText,
          icon: Icons.auto_fix_high_outlined,
          label: _isExtractingVideoUrlAndText ? '处理中...' : '一键提取文字',
          isLoading: _isExtractingVideoUrlAndText,
          color: AppColors.primary,
          isPrimary: true,
        ),
        if (_extractedVideoUrlAndText.isNotEmpty) ...[
          SizedBox(height: 16.h),
          _buildResultCard(_extractedVideoUrlAndText, _extractedVideoUrlAndText.contains('提取到的文字')),
        ],
      ],
    );
  }

  // 构建系统功能区域
  Widget _buildSystemFeaturesSection() {
    return _buildSectionCard(
      title: '系统功能',
      icon: Icons.settings_outlined,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: _isLoadingStorage ? null : _viewLocalStorage,
                icon: Icons.storage_outlined,
                label: _isLoadingStorage ? '加载中...' : '查看存储',
                color: AppColors.info,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: _clearLocalStorage,
                icon: Icons.clear_all_outlined,
                label: '清除存储',
                color: AppColors.warning,
              ),
            ),
          ],
        ),
        if (_storageContent.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_storageContent, true),
        ],
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: _showShortToast,
                icon: Icons.message_outlined,
                label: '短Toast',
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: _showLongToast,
                icon: Icons.message_outlined,
                label: '长Toast',
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: () => _showCustomToast(isLong: false),
                icon: Icons.notifications_outlined,
                label: '自定义Toast',
                color: AppColors.success,
              ),
            ),
          ],
        ),
        if (_toastResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_toastResult, true),
        ],
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: _showCustomToastModal,
                icon: Icons.chat_bubble_outline,
                label: 'Toast Modal',
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const MLKitDemoPage()),
                  );
                },
                icon: Icons.psychology_outlined,
                label: 'ML Kit 演示',
                color: AppColors.info,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 构建网络和存储功能区域
  Widget _buildNetworkStorageSection() {
    return _buildSectionCard(
      title: '网络与存储',
      icon: Icons.cloud_outlined,
      children: [
        _buildModernTextField(
          controller: _urlController,
          label: '网页地址',
          hint: '请输入要打开的网页地址',
          icon: Icons.web,
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: _isOpeningWebPage ? null : () => _openWebPage(false),
                icon: Icons.web_outlined,
                label: _isOpeningWebPage ? '打开中...' : '应用内打开',
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: _isOpeningWebPage ? null : () => _openWebPage(true),
                icon: Icons.open_in_browser_outlined,
                label: _isOpeningWebPage ? '打开中...' : '浏览器打开',
                color: AppColors.info,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        _buildModernTextField(
          controller: _resourceUrlController,
          label: '资源链接',
          hint: '例如: https://example.com/image.jpg',
          icon: Icons.link,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isConvertingLink ? null : _convertResourceToOssLink,
          icon: Icons.transform_outlined,
          label: _isConvertingLink ? '转换中...' : '转换为OSS链接',
          isLoading: _isConvertingLink,
          color: AppColors.success,
        ),
        if (_ossLinkResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_ossLinkResult, !_ossLinkResult.contains('失败')),
        ],
        SizedBox(height: 16.h),
        _buildActionButton(
          onPressed: _isUploadingToOss ? null : _uploadFileToOss,
          icon: Icons.upload_outlined,
          label: _isUploadingToOss ? '上传中...' : '选择图片并上传到OSS',
          isLoading: _isUploadingToOss,
          color: AppColors.primary,
        ),
        if (_ossUploadResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_ossUploadResult, !_ossUploadResult.contains('失败')),
        ],
      ],
    );
  }

  // 构建其他工具区域
  Widget _buildOtherToolsSection() {
    return _buildSectionCard(
      title: '其他工具',
      icon: Icons.build_outlined,
      children: [
        _buildModernTextField(
          controller: _schemeUrlController,
          label: 'URL Scheme',
          hint: '例如: taobao://go/order_detail',
          icon: Icons.launch,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isOpeningSchemeUrl ? null : _openSchemeUrl,
          icon: Icons.launch_outlined,
          label: _isOpeningSchemeUrl ? '跳转中...' : '跳转到应用',
          isLoading: _isOpeningSchemeUrl,
          color: AppColors.warning,
        ),
        SizedBox(height: 16.h),
        _buildModernTextField(
          controller: _htmlUrlController,
          label: '网页URL',
          hint: '例如: https://www.xiaohongshu.com/explore',
          icon: Icons.html,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isExtractingHtml ? null : _extractHtmlFromUrl,
          icon: Icons.code_outlined,
          label: _isExtractingHtml ? '提取中...' : '提取HTML内容',
          isLoading: _isExtractingHtml,
          color: AppColors.info,
        ),
        if (_extractedHtml.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_extractedHtml, !_extractedHtml.contains('失败')),
        ],
        SizedBox(height: 16.h),
        _buildModernTextField(
          controller: _businessJsUrlController,
          label: '业务JavaScript测试URL',
          hint: '例如: https://www.xiaohongshu.com/explore',
          icon: Icons.javascript,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          onPressed: _isExecutingBusinessJs ? null : _executeBusinessJavaScript,
          icon: Icons.play_arrow_outlined,
          label: _isExecutingBusinessJs ? '执行中...' : '执行extractVideo.js',
          isLoading: _isExecutingBusinessJs,
          color: AppColors.success,
        ),
        if (_businessJsResult.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildResultCard(_businessJsResult, !_businessJsResult.contains('失败')),
        ],
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: () {
                  Navigator.pushNamed(context, AppRoutes.aiChat);
                },
                icon: Icons.smart_toy_outlined,
                label: 'AI聊天助手',
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildFeatureButton(
                onPressed: _showCustomNotificationPanel,
                icon: Icons.notifications_outlined,
                label: '自定义通知面板',
                color: Color(0xFF1EB9EF),
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildFeatureButton(
                onPressed: _showInAppNotification,
                icon: Icons.notification_important_outlined,
                label: '展示应用内通知',
                color: AppColors.success,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Container(), // 占位符，保持布局对称
            ),
          ],
        ),
      ],
    );
  }

  // 构建现代化的卡片容器
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 20.r,
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          ...children,
        ],
      ),
    );
  }

  // 构建现代化的按钮
  Widget _buildModernButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    required Color color,
    bool isSmall = true,
  }) {
    return Container(
      height: isSmall ? 36.h : 48.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color, color.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(isSmall ? 8.r : 12.r),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(isSmall ? 8.r : 12.r),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: isSmall ? 16.r : 20.r,
                ),
                SizedBox(width: 6.w),
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isSmall ? 12.sp : 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建功能按钮
  Widget _buildFeatureButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      height: 48.h,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20.r,
                ),
                SizedBox(height: 4.h),
                Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建操作按钮
  Widget _buildActionButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    required Color color,
    bool isLoading = false,
    bool isPrimary = false,
  }) {
    return Container(
      width: double.infinity,
      height: 48.h,
      decoration: BoxDecoration(
        gradient: isPrimary
            ? LinearGradient(
                colors: [color, color.withOpacity(0.8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: isPrimary ? null : color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: isPrimary
            ? null
            : Border.all(
                color: color.withOpacity(0.3),
                width: 1,
              ),
        boxShadow: isPrimary
            ? [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading)
                  SizedBox(
                    width: 20.r,
                    height: 20.r,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isPrimary ? Colors.white : color,
                      ),
                    ),
                  )
                else
                  Icon(
                    icon,
                    color: isPrimary ? Colors.white : color,
                    size: 20.r,
                  ),
                SizedBox(width: 8.w),
                Text(
                  label,
                  style: TextStyle(
                    color: isPrimary ? Colors.white : color,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建现代化的输入框
  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.divider,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withOpacity(0.5),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        maxLines: maxLines,
        style: TextStyle(
          fontSize: 14.sp,
          color: AppColors.textPrimary,
        ),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(
            icon,
            color: AppColors.primary,
            size: 20.r,
          ),
          labelStyle: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14.sp,
          ),
          hintStyle: TextStyle(
            color: AppColors.textSecondary.withOpacity(0.6),
            fontSize: 14.sp,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
      ),
    );
  }

  // 构建结果显示卡片
  Widget _buildResultCard(String content, bool isSuccess) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxHeight: 200.h,
      ),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: isSuccess
            ? AppColors.success.withOpacity(0.05)
            : AppColors.error.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isSuccess
              ? AppColors.success.withOpacity(0.2)
              : AppColors.error.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle_outline : Icons.error_outline,
                color: isSuccess ? AppColors.success : AppColors.error,
                size: 20.r,
              ),
              SizedBox(width: 8.w),
              Text(
                isSuccess ? '执行成功' : '执行失败',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: isSuccess ? AppColors.success : AppColors.error,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Expanded(
            child: SingleChildScrollView(
              child: Text(
                content,
                style: TextStyle(
                  fontSize: 13.sp,
                  color: AppColors.textSecondary,
                  height: 1.4,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}