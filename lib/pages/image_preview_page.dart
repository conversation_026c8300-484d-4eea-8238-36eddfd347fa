import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 极简版图片预览页面 - 在全屏Container中显示图片
class ImagePreviewPage extends StatelessWidget {
  /// 图片URL
  final String imageUrl;

  /// 构造函数
  const ImagePreviewPage({super.key, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    // 设置全屏模式
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

    return Container(
      // 设置黑色背景
      color: Colors.black,
      // 使用Stack布局
      child: Stack(
        children: [
          // 使用SingleChildScrollView允许长图滚动查看
          // 设置最小高度为屏幕高度（相当于100vh）
          SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height,
              ),
              child: Image.network(
              imageUrl,
              width: MediaQuery.of(context).size.width, // 使用屏幕宽度
              fit: BoxFit.fitWidth, // 适应宽度，高度自动调整
              alignment: Alignment.center, // 确保图片从顶部开始显示
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) {
                  return child;
                }
                return SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height / 2,
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height / 2,
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 50, color: Colors.white70),
                        SizedBox(height: 16),
                        Text(
                          '图片加载失败',
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            ),
          ),

          // 添加返回按钮
          Positioned(
            top: 40,
            left: 16,
            child: GestureDetector(
              onTap: () {
                // 恢复系统UI
                SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
                Navigator.of(context).pop();
              },
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.arrow_back,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}