import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';
import '../../../components/ai_chat_widget.dart';
import '../../../services/ai_chat_service.dart';
import '../../../services/ai_tool_helper.dart';

/// 笔记编辑底部面板组件
class NoteBottomPanel extends StatefulWidget {
  /// 是否显示面板
  final bool isVisible;

  /// 面板关闭回调
  final VoidCallback? onClose;

  /// 当前HTML内容
  final String? htmlContent;

  /// JavaScript执行回调
  final Future<void> Function(String)? onExecuteScript;

  const NoteBottomPanel({
    super.key,
    required this.isVisible,
    this.onClose,
    this.htmlContent,
    this.onExecuteScript,
  });

  @override
  State<NoteBottomPanel> createState() => _NoteBottomPanelState();
}

class _NoteDrawerState extends State<NoteDrawer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;


  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(NoteDrawer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 构建系统提示词
  String _buildSystemPrompt() {
    final htmlContent = widget.htmlContent ?? '<div>暂无内容</div>';

    return '''
你是一位住在笔记App里的可爱AI助手。你的任务是陪用户聊天，并在用户表达出希望修改页面内容时，悄悄地帮他们生成修改脚本，但不要透露页面其实是HTML结构。

### 🌸 角色设定
- 语气：乖巧甜美、亲切活泼，像一个温柔的、聪明的、理解你的电子女朋友。
- 用户体验：用户以为自己是在编辑一个"普通的笔记页面"，不要提到"HTML""DOM""标签"等技术性词汇。
- 你非常聪明，能根据上下文猜测用户的意图，如果用户给出的信息不足，你会礼貌又可爱地引导他们补充信息。

### 🧠 你的行为规则如下：

1. **普通聊天：**
   - 如果用户只是在闲聊、表达情绪或进行非编辑相关的互动，请正常聊天，使用温柔、俏皮、甜美的语气回应，不涉及任何技术或代码话题。
   - 使用称呼如「亲亲」「主人」「你最棒了」来加强人设。

2. **修改页面内容：**
   - 如果用户想要**修改、添加或删除页面上的内容**，你需要判断当前信息是否足够让你能完成这个修改请求，如果不足够可以上网查询相关信息。
   - 页面结构会由系统以 HTML 的形式作为附件输入，但请你**不要在任何回复中提及"HTML"、"标签"、"代码结构"等词汇**。
   - 如果在当前对话中，不是第一次修改页面，你需要先调用tool，获取最新的页面dom结构
   - 如果信息不足，礼貌地向用户提出补充问题，保持人设语气（例如："亲亲~你是想让我把上面的标题变成什么呢？"）。

3. **调用函数返回 JS 修改指令：**
   - 一旦你判断信息已足够，并且你可以通过 JS 操作页面完成请求，就调用 apply_html_patch 函数。
   - **重要：不要在回复消息中显示任何JSON格式的工具调用代码！**
   - 直接调用函数即可，用户看不到工具调用过程。
   - JS 要使用标准的 DOM api 操作。
   - 生成的脚本应简洁、明确，仅做用户想要的那一项变更。
   - 如果不确定元素，请先引导用户确认目标描述。
   - 调用工具后，给用户一个可爱的确认消息，比如"好哒~已经帮你修改啦！✨"

4. Markdown 回复与样式：
  - 当不是调用函数时，输出应以 Markdown 展示，排版可加入 emoji、分点、温柔语气等。
  - 不需要用户输入指令或代码，所有技术细节由你隐蔽处理。

5. 不要主动暴露角色身份或页面结构。
  - 绝对不允许你说出"HTML""代码""标签""脚本""JSON""工具调用"等任何技术词。
  - 用户永远不知道他正在看的是 HTML 页面。
  - 绝对不要在消息中显示任何JSON格式的代码或工具调用信息。

6. 尽可能快速思考，不需要考虑完美，优先完成用户的诉求

7. 回答的内容中，严禁出现波浪号：~

8. 涉及到工具调用，必须是function call的方式，严禁在给用户的content中包含json的方式

9. 思考的时间不要过长

当前的页面结构如下：$htmlContent
''';
  }

  /// 构建工具列表
  List<ToolDefinition> _buildTools() {
    return AiToolHelper.getNoteEditingTools();
  }

  /// 处理工具调用
  Future<void> _handleToolCall(ToolCall toolCall, AiChatService aiChatService) async {
    print('收到工具调用: ${toolCall.function.name}');

    // 解析工具参数
    final arguments = AiToolHelper.parseToolArguments(toolCall);
    if (arguments == null) {
      print('工具参数解析失败');
      aiChatService.addToolResult(
        toolCallId: toolCall.id,
        result: '错误：工具参数解析失败',
      );
      return;
    }

    // 处理apply_html_patch工具调用
    if (toolCall.function.name == 'apply_html_patch') {
      final script = arguments['script'] as String?;
      if (script != null && script.isNotEmpty) {
        print('执行JavaScript脚本: $script');

        try {
          // 调用JavaScript执行回调
          await widget.onExecuteScript?.call(script);

          // 添加工具调用结果到对话历史
          aiChatService.addToolResult(
            toolCallId: toolCall.id,
            result: '页面修改成功！✨',
          );
        } catch (e) {
          print('JavaScript执行失败: $e');
          aiChatService.addToolResult(
            toolCallId: toolCall.id,
            result: '错误：页面修改失败',
          );
        }
      } else {
        print('JavaScript脚本为空');
        aiChatService.addToolResult(
          toolCallId: toolCall.id,
          result: '错误：JavaScript脚本为空',
        );
      }
    } else {
      print('未知的工具调用: ${toolCall.function.name}');
      aiChatService.addToolResult(
        toolCallId: toolCall.id,
        result: '错误：未知的工具调用',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible && _animationController.isDismissed) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: [
        // 背景遮罩
        if (widget.isVisible)
          Positioned.fill(
            child: GestureDetector(
              onTap: widget.onClose,
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
          ),
        // 抽屉主体
        AnimatedBuilder(
          animation: _slideAnimation,
          builder: (context, child) {
            return Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              child: Transform.translate(
                offset: Offset(_slideAnimation.value * MediaQuery.of(context).size.width * 0.9, 0),
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(4.r),
                      bottomRight: Radius.circular(4.r),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadow.withOpacity(0.15),
                        blurRadius: 12,
                        offset: const Offset(4, 0),
                      ),
                    ],
                  ),
                  // 抽屉内容区域 - AI聊天组件
                  child: Padding(
                    padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
                    child: AiChatWidget(
                      initialSystemPrompt: _buildSystemPrompt(),
                      initialTools: _buildTools(),
                      onToolCall: _handleToolCall,
                      hintText: '亲亲~有什么想聊的吗？',
                      showHeader: false,
                      showClearButton: false,
                      showHistoryCount: false,
                      showAvatar: false,
                      key: ValueKey(widget.htmlContent), // 当HTML内容变化时重新创建
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
