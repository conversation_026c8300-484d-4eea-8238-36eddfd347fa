import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';

/// 笔记错误显示组件
class NoteError extends StatelessWidget {
  /// 错误信息
  final String message;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 返回回调
  final VoidCallback? onBack;

  const NoteError({
    super.key,
    required this.message,
    this.onRetry,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.background,
      child: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 32.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 错误图标
              Icon(
                Icons.error_outline,
                size: 64.r,
                color: AppColors.error.withOpacity(0.6),
              ),
              
              SizedBox(height: 24.h),
              
              // 错误标题
              Text(
                '加载失败',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              
              SizedBox(height: 12.h),
              
              // 错误信息
              Text(
                message,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              SizedBox(height: 32.h),
              
              // 操作按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 返回按钮
                  if (onBack != null) ...[
                    OutlinedButton(
                      onPressed: onBack,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.textSecondary,
                        side: BorderSide(color: AppColors.divider),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: 24.w,
                          vertical: 12.h,
                        ),
                      ),
                      child: Text(
                        '返回',
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    ),
                    SizedBox(width: 16.w),
                  ],
                  
                  // 重试按钮
                  if (onRetry != null)
                    ElevatedButton(
                      onPressed: onRetry,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: 24.w,
                          vertical: 12.h,
                        ),
                      ),
                      child: Text(
                        '重试',
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
