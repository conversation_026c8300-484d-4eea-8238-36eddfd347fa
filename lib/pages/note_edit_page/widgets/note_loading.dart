import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';

/// 笔记加载指示器组件
class NoteLoading extends StatelessWidget {
  /// 加载提示文本
  final String? message;
  
  /// 是否显示背景
  final bool showBackground;

  const NoteLoading({
    super.key,
    this.message,
    this.showBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    final content = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 加载指示器
        SizedBox(
          width: 40.r,
          height: 40.r,
          child: CircularProgressIndicator(
            strokeWidth: 3.0,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
        
        // 加载文本
        if (message != null) ...[
          SizedBox(height: 16.h),
          Text(
            message!,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    if (showBackground) {
      return Container(
        color: AppColors.background.withOpacity(0.8),
        child: Center(child: content),
      );
    } else {
      return Center(child: content);
    }
  }
}
