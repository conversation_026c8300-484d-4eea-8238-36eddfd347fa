import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';

/// 笔记工具栏组件
class NoteToolbar extends StatelessWidget {
  /// 保存回调
  final VoidCallback? onSave;
  
  /// 编辑回调
  final VoidCallback? onEdit;
  
  /// 分享回调
  final VoidCallback? onShare;
  
  /// 更多操作回调
  final VoidCallback? onMore;
  
  /// 是否显示编辑按钮
  final bool showEdit;
  
  /// 是否显示保存按钮
  final bool showSave;
  
  /// 是否显示分享按钮
  final bool showShare;
  
  /// 是否显示更多按钮
  final bool showMore;

  const NoteToolbar({
    super.key,
    this.onSave,
    this.onEdit,
    this.onShare,
    this.onMore,
    this.showEdit = true,
    this.showSave = false,
    this.showShare = true,
    this.showMore = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: AppColors.divider.withOpacity(0.3),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 编辑按钮
          if (showEdit)
            _buildToolbarButton(
              icon: Icons.edit_outlined,
              label: '编辑',
              onTap: onEdit,
            ),
          
          // 保存按钮
          if (showSave)
            _buildToolbarButton(
              icon: Icons.save_outlined,
              label: '保存',
              onTap: onSave,
              color: AppColors.primary,
            ),
          
          // 分享按钮
          if (showShare)
            _buildToolbarButton(
              icon: Icons.share_outlined,
              label: '分享',
              onTap: onShare,
            ),
          
          // 更多按钮
          if (showMore)
            _buildToolbarButton(
              icon: Icons.more_horiz_outlined,
              label: '更多',
              onTap: onMore,
            ),
        ],
      ),
    );
  }

  /// 构建工具栏按钮
  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    VoidCallback? onTap,
    Color? color,
  }) {
    final buttonColor = color ?? AppColors.textSecondary;
    
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 8.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 20.r,
                  color: buttonColor,
                ),
                SizedBox(height: 4.h),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: buttonColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
