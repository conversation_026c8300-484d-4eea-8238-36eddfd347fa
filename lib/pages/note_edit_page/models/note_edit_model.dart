/// 笔记编辑页面状态枚举
enum NoteEditState {
  /// 初始状态
  initial,
  /// 加载中
  loading,
  /// 加载成功
  success,
  /// 加载失败
  error,
}

/// 笔记编辑数据模型
class NoteEditModel {
  /// 笔记ID
  final String noteId;
  
  /// 笔记标题
  final String title;
  
  /// 笔记内容（HTML格式）
  final String htmlContent;
  
  /// 笔记封面图片URL
  final String cover;
  
  /// 笔记描述
  final String description;
  
  /// 笔记原始内容（文本格式）
  final String content;
  
  /// 创建时间
  final String createTime;
  
  /// 更新时间
  final String updateTime;
  
  /// 页面状态
  final NoteEditState state;
  
  /// 错误信息
  final String? errorMessage;

  const NoteEditModel({
    required this.noteId,
    this.title = '',
    this.htmlContent = '',
    this.cover = '',
    this.description = '',
    this.content = '',
    this.createTime = '',
    this.updateTime = '',
    this.state = NoteEditState.initial,
    this.errorMessage,
  });

  /// 从JSON创建模型
  factory NoteEditModel.fromJson(Map<String, dynamic> json) {
    return NoteEditModel(
      noteId: json['id'] ?? '',
      title: json['title'] ?? '',
      htmlContent: json['html'] ?? '',
      cover: json['cover'] ?? '',
      description: json['desc'] ?? '',
      content: json['content'] ?? '',
      createTime: json['create_time'] ?? '',
      updateTime: json['update_time'] ?? '',
      state: NoteEditState.success,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': noteId,
      'title': title,
      'html': htmlContent,
      'cover': cover,
      'desc': description,
      'content': content,
      'create_time': createTime,
      'update_time': updateTime,
    };
  }

  /// 复制并更新模型
  NoteEditModel copyWith({
    String? noteId,
    String? title,
    String? htmlContent,
    String? cover,
    String? description,
    String? content,
    String? createTime,
    String? updateTime,
    NoteEditState? state,
    String? errorMessage,
  }) {
    return NoteEditModel(
      noteId: noteId ?? this.noteId,
      title: title ?? this.title,
      htmlContent: htmlContent ?? this.htmlContent,
      cover: cover ?? this.cover,
      description: description ?? this.description,
      content: content ?? this.content,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      state: state ?? this.state,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// 是否为加载状态
  bool get isLoading => state == NoteEditState.loading;

  /// 是否为成功状态
  bool get isSuccess => state == NoteEditState.success;

  /// 是否为错误状态
  bool get isError => state == NoteEditState.error;

  /// 是否有HTML内容
  bool get hasHtmlContent => htmlContent.isNotEmpty;

  /// 是否有封面图片
  bool get hasCover => cover.isNotEmpty;

  /// 是否有描述
  bool get hasDescription => description.isNotEmpty;

  @override
  String toString() {
    return 'NoteEditModel(noteId: $noteId, title: $title, state: $state, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NoteEditModel &&
        other.noteId == noteId &&
        other.title == title &&
        other.htmlContent == htmlContent &&
        other.cover == cover &&
        other.description == description &&
        other.content == content &&
        other.createTime == createTime &&
        other.updateTime == updateTime &&
        other.state == state &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(
      noteId,
      title,
      htmlContent,
      cover,
      description,
      content,
      createTime,
      updateTime,
      state,
      errorMessage,
    );
  }
}
