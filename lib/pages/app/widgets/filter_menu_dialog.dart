import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';
import '../../../services/tag_service.dart';
import '../../../widgets/custom_toast.dart';
import '../../../models/tag.dart';
import '../../../models/platform_model.dart';
import 'tag_chip.dart';
import 'platform_chip.dart';

class FilterMenuDialog extends StatefulWidget {
  final VoidCallback? onDefaultMode;
  final Function(String)? onTagSelected;
  final VoidCallback? onAddTag;
  final Function(String)? onPlatformSelected;

  const FilterMenuDialog({
    super.key,
    this.onDefaultMode,
    this.onTagSelected,
    this.onAddTag,
    this.onPlatformSelected,
  });

  @override
  State<FilterMenuDialog> createState() => _FilterMenuDialogState();

  // 显示过滤菜单的静态方法
  static void show(
    BuildContext context, {
    VoidCallback? onDefaultMode,
    Function(String)? onTagSelected,
    VoidCallback? onAddTag,
    Function(String)? onPlatformSelected,
  }) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (dialogContext) => FilterMenuDialog(
        onDefaultMode: onDefaultMode,
        onTagSelected: (tag) {
          Navigator.of(dialogContext).pop();
          onTagSelected?.call(tag);
        },
        onAddTag: onAddTag,
        onPlatformSelected: (platform) {
          Navigator.of(dialogContext).pop();
          onPlatformSelected?.call(platform);
        },
      ),
    );
  }

  // 刷新当前显示的过滤菜单标签列表
  static void refreshTags() {
    _FilterMenuDialogState._currentInstance?._loadTags();
  }
}

class _FilterMenuDialogState extends State<FilterMenuDialog> {
  final TagService _tagService = TagService();
  List<Tag> _tagObjects = [];
  bool _isLoading = true;
  static _FilterMenuDialogState? _currentInstance;

  @override
  void initState() {
    super.initState();
    _currentInstance = this;
    _loadTags();
  }

  @override
  void dispose() {
    if (_currentInstance == this) {
      _currentInstance = null;
    }
    super.dispose();
  }

  /// 加载标签列表
  Future<void> _loadTags() async {
    try {
      final tagObjects = await _tagService.getAllTagObjects();
      if (mounted) {
        setState(() {
          _tagObjects = tagObjects;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('加载标签失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 删除标签
  Future<void> _deleteTag(String tag) async {
    // 显示确认删除对话框
    final confirmed = await _showDeleteConfirmDialog(tag);
    if (!confirmed) return;

    try {
      final success = await _tagService.removeTag(tag);
      if (success && mounted) {
        // 删除成功后刷新标签列表
        await _loadTags();
        // 使用CustomToast显示成功提示
        CustomToast.show('标签"$tag"已删除');
      }
      // 错误提示已在TagService中处理，这里不需要重复显示
    } catch (e) {
      print('删除标签失败: $e');
      // 错误提示已在TagService中处理，这里不需要重复显示
    }
  }

  /// 显示删除确认对话框
  Future<bool> _showDeleteConfirmDialog(String tag) async {
    return await showDialog<bool>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (dialogContext) => Center(
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: 280.w,
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: AppColors.cardShadow.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Padding(
                  padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 12.h),
                  child: Text(
                    '确认删除',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                // 内容
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Text(
                    '确定要删除标签"$tag"吗？',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20.h),
                // 按钮区域
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: AppColors.divider,
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      // 取消按钮
                      Expanded(
                        child: InkWell(
                          onTap: () => Navigator.of(dialogContext).pop(false),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(12.r),
                          ),
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 14.h),
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  color: AppColors.divider,
                                  width: 0.5,
                                ),
                              ),
                            ),
                            child: Text(
                              '取消',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.textSecondary,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                      // 确认按钮
                      Expanded(
                        child: InkWell(
                          onTap: () => Navigator.of(dialogContext).pop(true),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(12.r),
                          ),
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 14.h),
                            child: Text(
                              '删除',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.red,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部间距
              SizedBox(height: 8.h),
              // 默认排序菜单项
              _buildFilterMenuItem(
                context,
                icon: Icons.access_time,
                title: '默认排序',
                onTap: () {
                  Navigator.of(context).pop();
                  widget.onDefaultMode?.call();
                },
              ),
              // 平台过滤标题
              Container(
                width: 315.w,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                child: Text(
                  '按平台过滤',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
              // 平台芯片列表
              _buildPlatformChips(),
              // 标签过滤分割线
              Container(
                width: 315.w,
                height: 1,
                margin: EdgeInsets.symmetric(vertical: 12.h),
                color: AppColors.divider.withOpacity(0.3),
              ),
              // 标签过滤标题和新增按钮
              Container(
                width: 315.w,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '标签过滤',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    InkWell(
                      onTap: widget.onAddTag,
                      borderRadius: BorderRadius.circular(12.r),
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12.r),
                          border: Border.all(
                            color: AppColors.primary.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.add,
                              size: 14.r,
                              color: AppColors.primary,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              '新增标签',
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: AppColors.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 标签列表
              _buildTagsSection(),
              // 底部间距
              SizedBox(height: 12.h),
            ],
          ),
        ),
      ),
    );
  }

  // 构建过滤菜单项
  Widget _buildFilterMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isLast = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(isLast ? 0 : 12.r),
        bottom: Radius.circular(isLast ? 12.r : 0),
      ),
      child: Container(
        width: 315.w,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20.r,
              color: AppColors.textSecondary,
            ),
            SizedBox(width: 12.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 15.sp,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建标签区域
  Widget _buildTagsSection() {
    if (_isLoading) {
      return Container(
        width: 315.w,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Center(
          child: SizedBox(
            width: 20.w,
            height: 20.w,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),
        ),
      );
    }

    if (_tagObjects.isEmpty) {
      return Container(
        width: 315.w,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Text(
          '暂无标签',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Container(
      width: 315.w,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Wrap(
        spacing: 8.w,
        runSpacing: 10.h,
        children: _tagObjects.asMap().entries.map((entry) =>
          TagChip(
            tag: entry.value.name,
            colorIndex: entry.key,
            tagObject: entry.value, // 传递Tag对象以显示真实颜色
            onTap: (tag) {
              widget.onTagSelected?.call(tag);
            },
            onDelete: (tag) {
              _deleteTag(tag);
            },
          )).toList(),
      ),
    );
  }

  // 构建平台芯片列表
  Widget _buildPlatformChips() {
    final platforms = PlatformModel.getSupportedPlatforms()
        .where((platform) => platform.name != '快手' && platform.name != '美团')
        .toList();

    return Container(
      width: 315.w,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Wrap(
        spacing: 8.w,
        runSpacing: 10.h,
        children: platforms.map((platform) =>
          PlatformChip(
            platform: platform,
            onTap: (platformName) {
              // 将平台名称转换为对应的类型标识
              String platformType = _getPlatformType(platformName);
              widget.onPlatformSelected?.call(platformType);
            },
          )).toList(),
      ),
    );
  }

  // 将平台名称转换为对应的类型标识
  String _getPlatformType(String platformName) {
    switch (platformName) {
      case '小红书':
        return 'xiaohongshu';
      case '抖音':
        return 'douyin';
      case 'B站':
        return 'bilibili';
      case '微信':
        return 'wechat';
      case '豆瓣':
        return 'douban';
      case '拼多多':
        return 'pinduoduo';
      case '淘宝':
        return 'taobao';
      case '京东':
        return 'jingdong';
      default:
        return platformName.toLowerCase();
    }
  }
}
