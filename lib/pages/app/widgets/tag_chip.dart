import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../models/tag.dart';

class TagChip extends StatefulWidget {
  final String tag;
  final int colorIndex;
  final Function(String)? onTap;
  final Function(String)? onDelete;
  final Tag? tagObject; // 新增：支持Tag对象

  const TagChip({
    super.key,
    required this.tag,
    required this.colorIndex,
    this.onTap,
    this.onDelete,
    this.tagObject,
  });

  @override
  State<TagChip> createState() => _TagChipState();
}

class _TagChipState extends State<TagChip> {
  bool _isEditMode = false;

  void _onLongPress() {
    if (widget.onDelete != null) {
      setState(() {
        _isEditMode = true;
      });
    }
  }

  void _onTapOutside() {
    if (_isEditMode) {
      setState(() {
        _isEditMode = false;
      });
    }
  }

  void _onDelete() {
    setState(() {
      _isEditMode = false;
    });
    widget.onDelete?.call(widget.tag);
  }

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color textColor;

    // 如果提供了Tag对象，使用真实的颜色
    if (widget.tagObject != null) {
      backgroundColor = _parseColor(widget.tagObject!.backgroundColor);
      textColor = _parseColor(widget.tagObject!.textColor);
    } else {
      // 否则使用默认的颜色数组 - 淡雅女孩子喜欢的色系
      final List<Color> tagColors = [
        const Color(0xFFFFE4E1), // 薄雾玫瑰
        const Color(0xFFFFF0F5), // 薰衣草腮红
        const Color(0xFFE0F2E9), // 薄荷绿
        const Color(0xFFE0F7FA), // 淡青色
        const Color(0xFFFFF5E1), // 香草色
        const Color(0xFFF5F0FF), // 淡紫色
        const Color(0xFFFFFDE7), // 柠檬雪纺
        const Color(0xFFFCE4EC), // 粉红色
        const Color(0xFFF3E5F5), // 兰花色
        const Color(0xFFE8F5E9), // 蜜瓜绿
        const Color(0xFFFBE9E7), // 桃花色
        const Color(0xFFE1F5FE), // 天空蓝
        const Color(0xFFFFF8E1), // 香槟色
        const Color(0xFFF9FBE7), // 青柠色
        const Color(0xFFF1F8E9), // 春绿色
        const Color(0xFFE8EAF6), // 淡靛蓝
        const Color(0xFFEDE7F6), // 淡紫罗兰
        const Color(0xFFF9FBE7), // 嫩绿色
        const Color(0xFFFFF3E0), // 杏仁色
        const Color(0xFFF0F4C3), // 淡黄绿
      ];

      final List<Color> textColors = [
        const Color(0xFFFF6B81), // 珊瑚粉
        const Color(0xFFE75480), // 深粉红
        const Color(0xFF6DBE9D), // 海绿色
        const Color(0xFF64B5F6), // 蓝色
        const Color(0xFFFFA07A), // 淡鲑鱼色
        const Color(0xFFA974FF), // 紫色
        const Color(0xFFFBC02D), // 金黄色
        const Color(0xFFEC407A), // 粉红色
        const Color(0xFFBA68C8), // 中兰花紫
        const Color(0xFF66BB6A), // 绿色
        const Color(0xFFFF7043), // 深橙色
        const Color(0xFF29B6F6), // 浅蓝色
        const Color(0xFFFFCA28), // 琥珀色
        const Color(0xFFD4E157), // 柠檬绿
        const Color(0xFF9CCC65), // 浅绿色
        const Color(0xFF5C6BC0), // 靛蓝色
        const Color(0xFF7E57C2), // 深紫色
        const Color(0xFFC0CA33), // 柠檬色
        const Color(0xFFFFA726), // 橙色
        const Color(0xFFAFB42B), // 橄榄绿
      ];

      backgroundColor = tagColors[widget.colorIndex % tagColors.length];
      textColor = textColors[widget.colorIndex % textColors.length];
    }

    return GestureDetector(
      onTap: () {
        if (!_isEditMode) {
          widget.onTap?.call(widget.tag);
        } else {
          _onTapOutside();
        }
      },
      onLongPress: _onLongPress,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: BorderRadius.circular(18.r),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '🏷️',
                        style: TextStyle(fontSize: 12.sp),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        widget.tag.length > 7 ? '${widget.tag.substring(0, 7)}...' : widget.tag,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: textColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (_isEditMode && widget.onDelete != null)
                  Positioned(
                    top: -6.h,
                    right: -6.w,
                    child: GestureDetector(
                      onTap: _onDelete,
                      child: Container(
                        width: 18.w,
                        height: 18.h,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 12.sp,
                        ),
                      ),
                    ),
                  ),
        ],
      ),
    );
  }

  /// 解析十六进制颜色字符串为Color对象
  Color _parseColor(String colorString) {
    try {
      // 移除可能的 # 前缀
      String hexColor = colorString.replaceAll('#', '');

      // 如果是6位，添加FF作为alpha值
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }

      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // 如果解析失败，返回默认颜色
      return const Color(0xFF1890FF);
    }
  }
}
