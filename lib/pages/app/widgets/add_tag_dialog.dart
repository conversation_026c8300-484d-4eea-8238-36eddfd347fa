import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';

class AddTagDialog extends StatefulWidget {
  final Function(String)? onConfirm;

  const AddTagDialog({
    super.key,
    this.onConfirm,
  });

  @override
  State<AddTagDialog> createState() => _AddTagDialogState();

  // 显示新增标签对话框的静态方法
  static void show(
    BuildContext context, {
    Function(String)? onConfirm,
  }) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (dialogContext) => AddTagDialog(
        onConfirm: (tagName) {
          Navigator.of(dialogContext).pop();
          onConfirm?.call(tagName);
        },
      ),
    );
  }
}

class _AddTagDialogState extends State<AddTagDialog> {
  final TextEditingController _tagController = TextEditingController();

  @override
  void dispose() {
    _tagController.dispose();
    super.dispose();
  }

  void _handleSubmit() {
    final tagName = _tagController.text.trim();
    if (tagName.isNotEmpty) {
      widget.onConfirm?.call(tagName);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: 315.w,
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部间距
              SizedBox(height: 20.h),
              // 标题
              Container(
                width: 315.w,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Text(
                  '新增标签',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              // 间距
              SizedBox(height: 16.h),
              // 输入框
              Container(
                width: 315.w,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: TextField(
                  controller: _tagController,
                  autofocus: true,
                  decoration: InputDecoration(
                    hintText: '请输入标签名称',
                    hintStyle: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: AppColors.divider,
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: AppColors.divider,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: AppColors.primary,
                        width: 1.5,
                      ),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                    filled: true,
                    fillColor: AppColors.background,
                  ),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                  ),
                  onSubmitted: (value) {
                    if (value.trim().isNotEmpty) {
                      _handleSubmit();
                    }
                  },
                ),
              ),
              // 间距
              SizedBox(height: 20.h),
              // 按钮区域
              Container(
                width: 315.w,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  children: [
                    // 取消按钮
                    Expanded(
                      child: InkWell(
                        onTap: () => Navigator.of(context).pop(),
                        borderRadius: BorderRadius.circular(8.r),
                        child: Container(
                          height: 44.h,
                          decoration: BoxDecoration(
                            color: AppColors.background,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: AppColors.divider,
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              '取消',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.textSecondary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // 间距
                    SizedBox(width: 12.w),
                    // 确定按钮
                    Expanded(
                      child: InkWell(
                        onTap: _handleSubmit,
                        borderRadius: BorderRadius.circular(8.r),
                        child: Container(
                          height: 44.h,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Center(
                            child: Text(
                              '确定',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 底部间距
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }
}
