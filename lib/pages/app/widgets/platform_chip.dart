import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../models/platform_model.dart';

class PlatformChip extends StatelessWidget {
  final PlatformModel platform;
  final Function(String)? onTap;

  const PlatformChip({
    super.key,
    required this.platform,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap?.call(platform.name);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: platform.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(18.r),
          border: Border.all(
            color: platform.color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 平台图标
            Container(
              width: 16.w,
              height: 16.w,
              decoration: BoxDecoration(
                color: platform.color,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.apps,
                size: 10.sp,
                color: Colors.white,
              ),
            ),
            SizedBox(width: 4.w),
            Text(
              platform.name.length > 7 ? '${platform.name.substring(0, 7)}...' : platform.name,
              style: TextStyle(
                fontSize: 12.sp,
                color: platform.color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
