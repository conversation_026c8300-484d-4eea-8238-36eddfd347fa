import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../native_bridge/native_bridge.dart';
import '../../../constants/app_colors.dart';

/// 权限检查控制面板
/// 用于显示和管理无障碍服务和悬浮窗权限
class PermissionPanel extends StatefulWidget {
  final bool hideWhenAllEnabled;

  const PermissionPanel({
    super.key,
    this.hideWhenAllEnabled = true,
  });

  @override
  State<PermissionPanel> createState() => PermissionPanelState();
}

class PermissionPanelState extends State<PermissionPanel> {
  // 权限状态
  bool _accessibilityPermissionEnabled = false;
  bool _overlayPermissionEnabled = false;
  // 悬浮窗开关状态
  bool _floatingWindowEnabled = false;
  final _nativeBridge = NativeBridge();

  @override
  void initState() {
    super.initState();
    _checkPermissions();

    // 注册悬浮窗关闭事件监听器
    _nativeBridge.registerFloatingWindowClosedListener(() {
      if (mounted) {
        setState(() {
          _floatingWindowEnabled = false;
        });
        print('收到悬浮窗关闭事件，更新权限面板状态');
      }
    });
  }

  @override
  void dispose() {
    // 取消注册悬浮窗关闭事件监听器
    _nativeBridge.unregisterFloatingWindowClosedListener();
    super.dispose();
  }

  // 检查权限状态
  Future<bool> _checkPermissions() async {
    final accessibilityEnabled = await _nativeBridge.checkAccessibilityPermission();
    final overlayEnabled = await _nativeBridge.checkOverlayPermission();
    // 检查悬浮窗是否正在显示
    final floatingShowing = await _nativeBridge.isFloatingWindowShowing();

    if (mounted) {
      setState(() {
        _accessibilityPermissionEnabled = accessibilityEnabled;
        _overlayPermissionEnabled = overlayEnabled;
        // 开关状态基于悬浮窗实际显示状态
        _floatingWindowEnabled = floatingShowing;
      });

      final allEnabled = accessibilityEnabled && overlayEnabled;

      // 如果所有权限都已启用，但悬浮窗未显示，只更新开关状态为可用，不主动启用悬浮窗
      if (allEnabled && !floatingShowing) {
        print('权限已全部开启，悬浮窗未显示，开关状态已更新为可用');
      }

      return allEnabled;
    }

    return false;
  }

  // 打开无障碍服务设置页面
  Future<void> _openAccessibilitySettings() async {
    await _nativeBridge.openAccessibilitySettings();
    // 打开设置页面后，需要用户手动开启权限，所以这里不立即更新状态
    // 在应用恢复前台时会自动重新检查权限状态
  }

  // 打开悬浮窗权限设置页面
  Future<void> _openOverlaySettings() async {
    await _nativeBridge.openOverlayPermissionSettings();
    // 打开设置页面后，需要用户手动开启权限，所以这里不立即更新状态
    // 在应用恢复前台时会自动重新检查权限状态
  }

  // 切换悬浮窗开关状态
  Future<void> _toggleFloatingWindow() async {
    if (_floatingWindowEnabled) {
      // 如果当前是启用状态，则禁用悬浮窗
      await _nativeBridge.disableFloatingWindow();
      setState(() {
        _floatingWindowEnabled = false;
      });
      print('悬浮窗已禁用');
    } else {
      // 如果当前是禁用状态，则启用悬浮窗
      final success = await _nativeBridge.enableFloatingWindow();
      setState(() {
        _floatingWindowEnabled = success;
      });
      print('悬浮窗已启用: $success');
    }
  }

  // 构建权限状态项
  Widget _buildPermissionItem({
    required String title,
    required String subtitle,
    required bool isEnabled,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(12.r),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 4.w),
          child: Row(
            children: [
              // 添加图标
              Container(
                width: 40.r,
                height: 40.r,
                decoration: BoxDecoration(
                  color: isEnabled ? AppColors.primary.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(
                  icon,
                  color: isEnabled ? AppColors.primary : Colors.grey,
                  size: 20.r,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              // 使用自定义开关样式
              Container(
                width: 50.w,
                height: 28.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(14.r),
                  color: isEnabled ? AppColors.primary : Colors.grey.withOpacity(0.3),
                ),
                child: Stack(
                  children: [
                    AnimatedPositioned(
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeInOut,
                      left: isEnabled ? 22.w : 2.w,
                      top: 2.h,
                      child: Container(
                        width: 24.r,
                        height: 24.r,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 检查是否所有权限都已启用
  bool get areAllPermissionsEnabled =>
      _accessibilityPermissionEnabled && _overlayPermissionEnabled;

  @override
  Widget build(BuildContext context) {
    // 如果所有权限都已启用且悬浮窗已开启，则隐藏权限面板
    if (areAllPermissionsEnabled && _floatingWindowEnabled) {
      return SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 10,
            spreadRadius: 1,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.security,
                  color: AppColors.primary,
                  size: 20.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  '权限设置',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                Spacer(),
                Text(
                  areAllPermissionsEnabled
                    ? (_floatingWindowEnabled ? '' : '悬浮窗已关闭')
                    : '请开启以下权限',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          // 权限项
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              children: [
                // 如果所有权限都已启用且悬浮窗已关闭，才显示悬浮窗开关
                if (areAllPermissionsEnabled && !_floatingWindowEnabled)
                  _buildPermissionItem(
                    title: '悬浮窗开关',
                    subtitle: '控制悬浮窗显示/隐藏',
                    isEnabled: _floatingWindowEnabled,
                    onTap: _toggleFloatingWindow,
                    icon: Icons.visibility,
                  )
                else ...[
                  // 如果权限未全部启用，显示权限项
                  _buildPermissionItem(
                    title: '无障碍服务',
                    subtitle: '用于自动提取内容',
                    isEnabled: _accessibilityPermissionEnabled,
                    onTap: _openAccessibilitySettings,
                    icon: Icons.accessibility_new,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    child: Divider(
                      color: AppColors.divider,
                      height: 1.h,
                    ),
                  ),
                  _buildPermissionItem(
                    title: '悬浮窗权限',
                    subtitle: '用于显示悬浮按钮',
                    isEnabled: _overlayPermissionEnabled,
                    onTap: _openOverlaySettings,
                    icon: Icons.picture_in_picture,
                  ),
                ],
              ],
            ),
          ),
          // 底部提示 - 根据权限状态和悬浮窗状态显示不同的提示
          if (!areAllPermissionsEnabled || !_floatingWindowEnabled) // 只有当权限未全部启用或悬浮窗已关闭时才显示提示
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
              decoration: BoxDecoration(
                color: AppColors.info.withOpacity(0.1),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
              ),
              child: Text(
                !areAllPermissionsEnabled
                    ? '点击相应权限项进入系统设置页面开启权限'
                    : '点击悬浮窗开关可重新显示悬浮窗',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.info,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 刷新权限状态
  /// 可以在应用从后台恢复时调用
  /// 返回是否所有权限都已启用
  Future<bool> refreshPermissions() async {
    final allEnabled = await _checkPermissions();
    // 检查悬浮窗是否正在显示
    final floatingShowing = await _nativeBridge.isFloatingWindowShowing();

    if (mounted) {
      setState(() {
        _floatingWindowEnabled = floatingShowing;
      });
    }

    print('刷新权限状态，所有权限已启用: $allEnabled, 悬浮窗显示状态: $floatingShowing');
    return allEnabled;
  }
}
