import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../api/api_provider.dart';
import '../../../models/collection_item.dart';

/// 收藏夹加载工具类
///
/// 用于处理收藏夹列表的加载、分页和数据处理
class LoadFavoritesUtil {
  /// 获取默认颜色
  static Color getDefaultColor() {
    // 使用固定的灰色作为默认颜色
    return Colors.grey[300]!;
  }

  /// 加载收藏夹列表
  ///
  /// [apiProvider] API提供者
  /// [currentPage] 当前页码
  /// [isLoading] 是否正在加载
  /// [isLoadingMore] 是否正在加载更多
  /// [hasMoreData] 是否还有更多数据
  /// [items] 当前收藏夹列表
  /// [mounted] 组件是否已挂载
  /// [loadMore] 是否是加载更多操作
  /// [setState] 状态更新回调
  static Future<LoadFavoritesResult> loadFavorites({
    required ApiProvider apiProvider,
    required int currentPage,
    required bool isLoading,
    required bool isLoadingMore,
    required bool hasMoreData,
    required List<CollectionItem> items,
    required bool mounted,
    bool loadMore = false,
  }) async {
    if (!mounted) return LoadFavoritesResult(
      success: false,
      items: items,
      currentPage: currentPage,
      hasMoreData: hasMoreData,
      isLoading: isLoading,
      isLoadingMore: isLoadingMore,
    );

    // 如果正在加载或者没有更多数据且是加载更多操作，则返回
    if ((isLoading || isLoadingMore) || (loadMore && !hasMoreData)) {
      return LoadFavoritesResult(
        success: false,
        items: items,
        currentPage: currentPage,
        hasMoreData: hasMoreData,
        isLoading: isLoading,
        isLoadingMore: isLoadingMore,
      );
    }

    // 重置页码，除非是加载更多
    int newPage = currentPage;
    if (!loadMore) {
      newPage = 1;
    }

    // 更新加载状态
    bool newIsLoading = isLoading;
    bool newIsLoadingMore = isLoadingMore;

    if (loadMore) {
      newIsLoadingMore = true;
    } else {
      newIsLoading = true;
    }

    try {
      // 调用API获取收藏夹列表
      final apiResponse = await apiProvider.favoritesApi.getUserFavorites(
        page: newPage,
      );

      // 打印响应数据，帮助调试
      print('收藏夹数据: $apiResponse');

      // 解析收藏夹数据 - 根据日志中的响应格式调整
      List<dynamic> favorites = [];

      // 直接使用apiResponse中的favorites字段
      if (apiResponse.containsKey('favorites')) {
        favorites = apiResponse['favorites'] as List<dynamic>;
        print('从响应中直接获取到收藏夹列表，数量: ${favorites.length}');
      } else {
        print('未找到favorites字段，尝试其他可能的结构');
      }

      // 检查是否还有更多数据
      bool newHasMoreData = false;

      print('检查是否还有更多数据: apiResponse=$apiResponse');

      // 根据提供的API响应结构判断
      if (apiResponse.containsKey('total') && apiResponse.containsKey('page') && apiResponse.containsKey('page_size')) {
        final total = apiResponse['total'] as int;
        final page = apiResponse['page'] as int;
        final pageSize = apiResponse['page_size'] as int;

        // 计算总页数
        final totalPages = (total / pageSize).ceil();

        // 如果当前页小于总页数，则还有更多数据
        newHasMoreData = page < totalPages;

        print('判断是否还有更多数据: 总数=$total, 当前页=$page, 每页数量=$pageSize, 总页数=$totalPages, 还有更多数据=$newHasMoreData');
      } else {
        // 如果没有分页信息，则根据返回的数据量判断
        final pageSize = 15; // 与favoritesApi中的默认值保持一致
        newHasMoreData = favorites.length >= pageSize;
        print('没有找到分页信息，根据返回数据量判断: 数据量=${favorites.length}, 每页大小=$pageSize, 还有更多数据=$newHasMoreData');
      }

      // 更新数据
      List<CollectionItem> newItems = List.from(items);

      if (loadMore) {
        // 添加新数据到列表尾部
        newItems.addAll(favorites.map((favorite) => CollectionItem(
          color: getDefaultColor(),
          title: favorite['name'] as String,
          id: favorite['id'] as String,
          cover: favorite['cover'] as String?,
          order: favorite['order'] as String? ?? 'a',
        )));
        newIsLoadingMore = false;
      } else {
        // 替换整个列表
        newItems = favorites.map((favorite) => CollectionItem(
          color: getDefaultColor(),
          title: favorite['name'] as String,
          id: favorite['id'] as String,
          cover: favorite['cover'] as String?,
          order: favorite['order'] as String? ?? 'a',
        )).toList();
        newIsLoading = false;
      }

      // 更新页码
      if (newHasMoreData) {
        newPage++;
      }

      return LoadFavoritesResult(
        success: true,
        items: newItems,
        currentPage: newPage,
        hasMoreData: newHasMoreData,
        isLoading: newIsLoading,
        isLoadingMore: newIsLoadingMore,
      );
    } catch (e) {
      // 错误已由 ApiClient 中的 Toast 提示处理
      print('加载收藏夹失败: $e');

      // 只将加载状态设置为完成，保持当前列表不变
      if (loadMore) {
        newIsLoadingMore = false;
      } else {
        newIsLoading = false;
      }

      return LoadFavoritesResult(
        success: false,
        items: items,
        currentPage: currentPage,
        hasMoreData: hasMoreData,
        isLoading: newIsLoading,
        isLoadingMore: newIsLoadingMore,
        error: e.toString(),
      );
    }
  }
}

/// 加载收藏夹结果类
class LoadFavoritesResult {
  final bool success;
  final List<CollectionItem> items;
  final int currentPage;
  final bool hasMoreData;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;

  LoadFavoritesResult({
    required this.success,
    required this.items,
    required this.currentPage,
    required this.hasMoreData,
    required this.isLoading,
    required this.isLoadingMore,
    this.error,
  });
}
