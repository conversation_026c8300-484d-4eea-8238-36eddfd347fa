import 'package:flutter/material.dart';
import '../../../api/api_provider.dart';

/// 创建收藏夹工具类
/// 
/// 用于处理创建收藏夹的业务逻辑
class CreateCollectionUtil {
  /// 创建收藏夹
  /// 
  /// [context] 上下文
  /// [name] 收藏夹名称
  /// [apiProvider] API提供者
  /// [showLoadingIndicator] 显示加载指示器的回调
  /// [hideLoadingIndicator] 隐藏加载指示器的回调
  /// [onSuccess] 创建成功后的回调
  static Future<void> createCollection({
    required BuildContext context,
    required String name,
    required ApiProvider apiProvider,
    required Function(String) showLoadingIndicator,
    required Function() hideLoadingIndicator,
    required Function() onSuccess,
  }) async {
    if (name.isEmpty) {
      return;
    }
    
    try {
      // 显示加载指示器
      showLoadingIndicator('正在创建收藏夹...');
      
      // 调用API创建收藏夹
      await apiProvider.favoritesApi.createFavorite(
        name: name,
      );
      
      // 隐藏加载指示器
      hideLoadingIndicator();
      
      // 显示创建成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('已创建收藏夹"$name"')),
      );
      
      // 调用成功回调
      onSuccess();
    } catch (e) {
      // 隐藏加载指示器
      hideLoadingIndicator();
      
      // 错误已由 ApiClient 中的 Toast 提示处理
    }
  }
}
