import 'package:flutter/material.dart';

/// 滚动监听器工具类
/// 
/// 用于处理列表滚动加载更多功能，包含截流机制
class ScrollListenerUtil {
  /// 创建滚动监听器函数
  /// 
  /// [scrollController] 滚动控制器
  /// [isLoading] 是否正在加载数据
  /// [isLoadingMore] 是否正在加载更多数据
  /// [hasMoreData] 是否还有更多数据
  /// [lastLoadTime] 上次加载时间
  /// [onLoadMore] 加载更多回调函数
  /// [throttleTime] 截流时间（毫秒），默认500毫秒
  /// [triggerDistance] 触发距离（像素），默认200像素
  static ScrollListener create({
    required ScrollController scrollController,
    required bool isLoading,
    required bool isLoadingMore,
    required bool hasMoreData,
    required DateTime lastLoadTime,
    required Function(DateTime) onLoadMore,
    int throttleTime = 500,
    double triggerDistance = 200,
  }) {
    return () {
      // 打印日志，帮助调试
      print('滚动位置: ${scrollController.position.pixels}, 最大滚动位置: ${scrollController.position.maxScrollExtent}');
      print('加载状态: isLoading=$isLoading, isLoadingMore=$isLoadingMore, hasMoreData=$hasMoreData');
      
      // 当滚动到距离底部指定像素时触发加载更多
      if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - triggerDistance && 
          !isLoading && 
          !isLoadingMore && 
          hasMoreData) {
        
        // 添加截流机制，指定时间内只能触发一次
        final now = DateTime.now();
        final difference = now.difference(lastLoadTime).inMilliseconds;
        
        if (difference < throttleTime) {
          print('截流生效: 上次加载时间距现在 $difference 毫秒，小于 $throttleTime 毫秒');
          return;
        }
        
        print('触发加载更多操作');
        onLoadMore(now);
      }
    };
  }
}

/// 滚动监听器函数类型
typedef ScrollListener = void Function();
