import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../components/collection_grid/index.dart';
import '../../../models/collection_item.dart';
import '../../../constants/app_colors.dart';

/// 收藏夹内容构建工具类
///
/// 用于构建收藏夹列表的不同状态视图
class CollectionContentBuilder {
  /// 构建收藏夹内容
  ///
  /// [context] 上下文
  /// [isLoading] 是否正在加载数据
  /// [isLoadingMore] 是否正在加载更多数据
  /// [hasMoreData] 是否还有更多数据
  /// [items] 收藏夹列表
  /// [scrollController] 滚动控制器
  /// [onReorder] 重新排序回调
  /// [onCreateCollection] 创建收藏夹回调
  /// [onLoadMore] 加载更多回调
  static Widget build({
    required BuildContext context,
    required bool isLoading,
    required bool isLoadingMore,
    required bool hasMoreData,
    required List<CollectionItem> items,
    required ScrollController scrollController,
    required Function(int, int) onReorder,
    required Function() onCreateCollection,
    required Function() onLoadMore,
    required Future<void> Function() onRefresh,
  }) {
    // 加载中状态
    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 40.r,
              height: 40.r,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                strokeWidth: 3.w,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              '正在加载收藏夹...',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    // 空列表状态
    if (items.isEmpty) {
      return Center(
        child: Text(
          '暂无收藏夹',
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.textHint,
            fontWeight: FontWeight.w400,
          ),
        ),
      );
    }

    // 打印当前收藏夹数量
    print('当前收藏夹数量: ${items.length}, 加载更多状态: $isLoadingMore, 还有更多数据: $hasMoreData');

    // 正常列表状态
    return RefreshIndicator(
      onRefresh: onRefresh,
      color: AppColors.primary,
      backgroundColor: Colors.white,
      displacement: 50.0,
      strokeWidth: 3.0,
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification scrollInfo) {
          // 监听滚动事件，增强滚动检测
          if (scrollInfo is ScrollEndNotification) {
            // 当滚动结束时检查是否需要加载更多
            if (scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 200 &&
                !isLoading &&
                !isLoadingMore &&
                hasMoreData) {
              print('滚动结束触发加载更多');
              onLoadMore();
            }
          }
          return false;
        },
        child: Stack(
          children: [
            // 收藏夹网格
            CollectionGrid(
              items: items,
              onReorder: onReorder,
              scrollController: scrollController,
            ),
            // 加载更多指示器
            if (isLoadingMore)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 70.h,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withOpacity(0),
                        Colors.white.withOpacity(0.9),
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 22.r,
                        height: 22.r,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                        )
                      ),
                      SizedBox(width: 12.w),
                      Text(
                        '加载更多收藏夹...',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}
