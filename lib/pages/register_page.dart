import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../api/index.dart';
import '../routes.dart';
import '../constants/app_colors.dart';
import '../services/storage_service.dart';
import '../native_bridge/native_bridge.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final ApiProvider _apiProvider = ApiProvider();
  final StorageService _storageService = StorageService();

  bool _isRegistering = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false; // 是否同意用户协议和隐私政策

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // 验证表单
  bool _validateForm() {
    final phone = _phoneController.text.trim();
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;

    if (phone.isEmpty) {
      _showErrorSnackBar('请输入手机号');
      return false;
    }

    if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(phone)) {
      _showErrorSnackBar('请输入有效的手机号');
      return false;
    }

    if (password.isEmpty) {
      _showErrorSnackBar('请输入密码');
      return false;
    }

    if (password.length < 6) {
      _showErrorSnackBar('密码长度不能少于6位');
      return false;
    }

    if (confirmPassword.isEmpty) {
      _showErrorSnackBar('请确认密码');
      return false;
    }

    if (password != confirmPassword) {
      _showErrorSnackBar('两次输入的密码不一致');
      return false;
    }

    if (!_agreeToTerms) {
      _showErrorSnackBar('请阅读并同意用户协议和隐私政策');
      return false;
    }

    return true;
  }

  // 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // 处理注册
  Future<void> _handleRegister() async {
    if (!_validateForm()) return;

    setState(() {
      _isRegistering = true;
    });

    try {
      final phone = _phoneController.text.trim();
      final password = _passwordController.text;

      // 调用注册API
      final response = await _apiProvider.authApi.register(phone, password);

      // 注册成功
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('注册成功'),
          backgroundColor: const Color(0xFF4CAF50),
        ),
      );

      // 自动登录
      await _autoLogin(phone, password);
    } catch (e) {
      // 处理注册错误
      String errorMessage = '注册失败';
      if (e is ApiException) {
        errorMessage = '${e.message} (${e.code})';
      } else {
        errorMessage = e.toString();
      }

      if (!mounted) return;

      _showErrorSnackBar(errorMessage);

      setState(() {
        _isRegistering = false;
      });
    }
  }

  // 自动登录
  Future<void> _autoLogin(String phone, String password) async {
    try {
      // 调用登录API
      final loginResponse = await _apiProvider.authApi.login(phone, password);
      final token = loginResponse.token;
      final userId = loginResponse.userId;

      // 1. 保存到Flutter本地存储
      await _storageService.saveUserInfo(
        userId: userId,
        phone: loginResponse.phone,
        token: token,
      );

      // 2. 同步到Android原生端
      final nativeBridge = NativeBridge();
      await nativeBridge.putString('auth_token', token);
      await nativeBridge.putString('user_id', userId);
      await nativeBridge.putString('user_phone', loginResponse.phone);

      // 导航到首页
      if (!mounted) return;
      Navigator.pushReplacementNamed(context, AppRoutes.home);
    } catch (e) {
      // 登录失败，但注册已成功，返回登录页面
      if (!mounted) return;

      String errorMessage = '自动登录失败，请手动登录';
      if (e is ApiException) {
        errorMessage = '自动登录失败: ${e.message}';
      }

      _showErrorSnackBar(errorMessage);

      // 延迟一下，让用户看到错误消息
      await Future.delayed(const Duration(seconds: 1));

      if (!mounted) return;
      Navigator.pop(context); // 返回登录页面
    } finally {
      if (mounted) {
        setState(() {
          _isRegistering = false;
        });
      }
    }
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    required IconData icon,
    bool obscureText = false,
    Widget? suffixIcon,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      style: TextStyle(fontSize: 16.sp, color: AppColors.textPrimary),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(color: AppColors.textHint.withOpacity(0.7)),
        prefixIcon: Padding(
          padding: EdgeInsetsDirectional.only(start: 16.w, end: 12.w),
          child: Icon(icon, color: AppColors.textHint.withOpacity(0.7), size: 22.sp),
        ),
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: AppColors.searchBarBackground.withOpacity(0.8), // Light background for text fields
        contentPadding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30.r),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30.r),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30.r),
          borderSide: BorderSide(color: AppColors.primaryLight, width: 1.5),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 28.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: 60.h), // Adjust spacing as needed

                    Text(
                      "注册账户",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 30.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      "创建您的新账户",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    SizedBox(height: 40.h),

                    _buildTextField(
                      controller: _phoneController,
                      hintText: '手机号', // 'Full Name' in reference
                      icon: Icons.phone_android, // Icons.person_outline in reference
                      keyboardType: TextInputType.phone,
                    ),
                    SizedBox(height: 20.h),
                    // Email field from reference image - not currently in logic
                    // _buildTextField(
                    //   controller: _emailController, // Needs new controller
                    //   hintText: '<EMAIL>',
                    //   icon: Icons.email_outlined,
                    //   keyboardType: TextInputType.emailAddress,
                    // ),
                    // SizedBox(height: 20.h),
                    _buildTextField(
                      controller: _passwordController,
                      hintText: '密码',
                      icon: Icons.lock_outline,
                      obscureText: _obscurePassword,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword ? Icons.visibility_off_outlined : Icons.visibility_outlined,
                          color: AppColors.textHint.withOpacity(0.7),
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                    ),
                    SizedBox(height: 20.h),
                    _buildTextField(
                      controller: _confirmPasswordController,
                      hintText: '确认密码',
                      icon: Icons.lock_outline,
                      obscureText: _obscureConfirmPassword,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscureConfirmPassword ? Icons.visibility_off_outlined : Icons.visibility_outlined,
                          color: AppColors.textHint.withOpacity(0.7),
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureConfirmPassword = !_obscureConfirmPassword;
                          });
                        },
                      ),
                    ),
                    SizedBox(height: 24.h),
                    // 用户协议和隐私政策勾选
                    Row(
                      children: [
                        SizedBox(
                          width: 24.w,
                          height: 24.h,
                          child: Checkbox(
                            value: _agreeToTerms,
                            onChanged: (bool? value) {
                              setState(() {
                                _agreeToTerms = value ?? false;
                              });
                            },
                            activeColor: AppColors.primary,
                            checkColor: Colors.white,
                            side: BorderSide(color: AppColors.textHint, width: 1.5),
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Wrap(
                            children: [
                              Text(
                                "我已阅读并同意",
                                style: TextStyle(fontSize: 12.sp, color: AppColors.textSecondary),
                              ),
                              GestureDetector(
                                onTap: () {
                                  Navigator.of(context).pushNamed(AppRoutes.agreement);
                                },
                                child: Text(
                                  "《用户协议》",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              Text(
                                "和",
                                style: TextStyle(fontSize: 12.sp, color: AppColors.textSecondary),
                              ),
                              GestureDetector(
                                onTap: () {
                                  Navigator.of(context).pushNamed(AppRoutes.privacyPolicy);
                                },
                                child: Text(
                                  "《隐私政策》",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 24.h),
                    ElevatedButton(
                      onPressed: _isRegistering ? null : _handleRegister,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary, // Dark green
                        padding: EdgeInsets.symmetric(vertical: 18.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.r),
                        ),
                      ),
                      child: _isRegistering
                          ? SizedBox(
                              width: 24.r,
                              height: 24.r,
                              child: const CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2.0,
                              ),
                            )
                          : Text(
                              '注册',
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                    ),
                    SizedBox(height: 20.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "已有账户？ ",
                          style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context); // Go back to login
                          },
                          child: Text(
                            '登录',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryLight, // Accent green
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 40.h),
                  ],
                ),
              ),
            ),
          ),
          Positioned( // Back button
            top: 40.h, // Adjust for status bar
            left: 16.w,
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: AppColors.textPrimary), // Changed color to be visible on white
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
        ],
      ),
    );
  }
}
