import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:aishoucang/api/api_provider.dart';
import 'package:aishoucang/models/collection_item.dart';
import 'package:aishoucang/constants/app_colors.dart';
import 'package:aishoucang/native_bridge/native_bridge.dart';

class CollectionSettingsPage extends StatefulWidget {
  final String favoriteId;
  final String initialName;
  final String? initialCover;
  final Function(String newName, String? newCover) onSettingsChanged;

  const CollectionSettingsPage({
    super.key,
    required this.favoriteId,
    required this.initialName,
    this.initialCover,
    required this.onSettingsChanged,
  });

  @override
  State<CollectionSettingsPage> createState() => _CollectionSettingsPageState();
}

class _CollectionSettingsPageState extends State<CollectionSettingsPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _apiProvider = ApiProvider();
  final _imagePicker = ImagePicker();
  final _nativeBridge = NativeBridge();

  String? _coverUrl;
  bool _isLoading = false;
  bool _isUploadingImage = false;
  String? _errorMessage;
  File? _selectedImageFile;

  // 书签数量，用于删除确认
  int _bookmarkCount = 0;

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.initialName;
    _coverUrl = widget.initialCover;

    // 获取收藏夹内的书签数量
    _getBookmarkCount();
  }

  // 获取收藏夹内的书签数量
  Future<void> _getBookmarkCount() async {
    try {
      final result = await _apiProvider.bookmarkApi.getBookmarkList(
        favoriteId: widget.favoriteId,
        page: 1,
        pageSize: 1, // 只需要获取总数，不需要所有数据
      );

      // 更新书签数量
      setState(() {
        _bookmarkCount = result['total'] as int? ?? 0;
      });

      print('收藏夹 ${widget.favoriteId} 内有 $_bookmarkCount 个书签');
    } catch (e) {
      print('获取书签数量失败: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  // 保存设置
  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final newName = _nameController.text.trim();

      // 调用API更新收藏夹
      await _apiProvider.favoritesApi.updateFavorite(
        favoriteId: widget.favoriteId,
        name: newName,
        cover: _coverUrl,
      );

      // 通知父组件设置已更改
      widget.onSettingsChanged(newName, _coverUrl);

      // 显示成功消息并返回
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: Colors.white,
                  size: 20.r,
                ),
                SizedBox(width: 12.w),
                Text('收藏夹设置已保存'),
              ],
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            margin: EdgeInsets.all(16.r),
            duration: const Duration(seconds: 2),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _errorMessage = '保存失败: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 选择封面图片
  Future<void> _selectCoverImage() async {
    try {
      // 使用image_picker从相册选择图片
      final XFile? pickedImage = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85, // 选择时进行初步压缩
        maxWidth: 1200,
        maxHeight: 1200,
      );

      if (pickedImage == null) {
        // 用户取消了选择
        return;
      }

      // 更新UI显示选择的图片
      setState(() {
        _selectedImageFile = File(pickedImage.path);
        _isUploadingImage = true;
        _errorMessage = null;
      });

      // 上传图片到OSS
      try {
        final result = await _nativeBridge.uploadFileToOss(
          filePath: pickedImage.path,
        );

        if (result['success'] == true && result['ossUrl'] != null) {
          // 上传成功，更新封面URL
          setState(() {
            _coverUrl = result['ossUrl'];
            _isUploadingImage = false;
          });

          // 显示成功提示
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('封面图片上传成功'),
                backgroundColor: AppColors.success,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                margin: EdgeInsets.all(16.r),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        } else {
          // 上传失败
          setState(() {
            _isUploadingImage = false;
            _errorMessage = '图片上传失败: ${result['error'] ?? "未知错误"}';
          });
        }
      } catch (e) {
        // 处理上传过程中的异常
        setState(() {
          _isUploadingImage = false;
          _errorMessage = '图片上传过程出错: $e';
        });
      }
    } catch (e) {
      // 处理选择图片过程中的异常
      setState(() {
        _errorMessage = '选择图片失败: $e';
      });
    }
  }

  // 删除收藏夹
  Future<void> _deleteFavorite() async {
    // 显示确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Text(
          '确认删除',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 60.r,
              height: 60.r,
              decoration: BoxDecoration(
                color: AppColors.error.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.delete_outline,
                color: AppColors.error,
                size: 32.r,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              _bookmarkCount > 0
                ? '该收藏夹内有 $_bookmarkCount 个内容'
                : '确定要删除该收藏夹吗？',
              style: TextStyle(
                fontSize: 15.sp,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            if (_bookmarkCount > 0)
              Padding(
                padding: EdgeInsets.only(top: 8.h),
                child: Text(
                  '删除后将无法恢复',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              elevation: 0,
            ),
            child: Text(
              '删除',
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
        actionsPadding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
        actionsAlignment: MainAxisAlignment.center,
      ),
    );

    if (confirmed != true) {
      return;
    }

    // 显示加载状态
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 调用API删除收藏夹
      final result = await _apiProvider.favoritesApi.deleteFavorite(widget.favoriteId);

      // 显示成功消息
      final deletedBookmarksCount = result['deleted_bookmarks_count'] as int? ?? 0;
      final message = deletedBookmarksCount > 0
          ? '已删除收藏夹及其中的 $deletedBookmarksCount 个内容'
          : '已删除收藏夹';

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            margin: EdgeInsets.all(16.r),
          ),
        );

        // 返回上一页
        Navigator.of(context).pop(true); // 传递true表示已删除
      }
    } catch (e) {
      setState(() {
        _errorMessage = '删除失败: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('收藏夹设置'),
        elevation: 0,
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 40.r,
                    height: 40.r,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      strokeWidth: 3.w,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    '正在处理...',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(20.r),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 错误消息
                    if (_errorMessage != null)
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16.r),
                        margin: EdgeInsets.only(bottom: 20.r),
                        decoration: BoxDecoration(
                          color: AppColors.error.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: AppColors.error,
                              size: 20.r,
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(
                                  color: AppColors.error,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                    // 收藏夹信息卡片
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(16.r),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.cardShadow,
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.all(20.r),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 名称输入框
                          Text(
                            '收藏夹名称',
                            style: TextStyle(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          TextFormField(
                            controller: _nameController,
                            decoration: InputDecoration(
                              hintText: '请输入收藏夹名称',
                              filled: true,
                              fillColor: AppColors.searchBarBackground,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.r),
                                borderSide: BorderSide.none,
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.r),
                                borderSide: BorderSide.none,
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.r),
                                borderSide: BorderSide(
                                  color: AppColors.primary.withOpacity(0.5),
                                  width: 1.5,
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 14.h,
                              ),
                              hintStyle: TextStyle(
                                color: AppColors.textHint,
                                fontSize: 15.sp,
                              ),
                            ),
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: AppColors.textPrimary,
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return '请输入收藏夹名称';
                              }
                              return null;
                            },
                          ),

                          SizedBox(height: 24.h),

                          // 封面图片
                          Text(
                            '封面图片',
                            style: TextStyle(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          GestureDetector(
                            onTap: _selectCoverImage,
                            child: Container(
                              width: double.infinity,
                              height: 180.h,
                              decoration: BoxDecoration(
                                color: AppColors.searchBarBackground,
                                borderRadius: BorderRadius.circular(12.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.cardShadow,
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: _isUploadingImage
                                  ? Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            width: 40.r,
                                            height: 40.r,
                                            child: CircularProgressIndicator(
                                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                              strokeWidth: 3.w,
                                            ),
                                          ),
                                          SizedBox(height: 16.h),
                                          Text(
                                            '正在上传图片...',
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              color: AppColors.textSecondary,
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : _selectedImageFile != null
                                      ? ClipRRect(
                                          borderRadius: BorderRadius.circular(12.r),
                                          child: Image.file(
                                            _selectedImageFile!,
                                            fit: BoxFit.cover,
                                          ),
                                        )
                                      : _coverUrl != null && _coverUrl!.isNotEmpty
                                          ? ClipRRect(
                                              borderRadius: BorderRadius.circular(12.r),
                                              child: Image.network(
                                                _coverUrl!,
                                                fit: BoxFit.cover,
                                                errorBuilder: (context, error, stackTrace) {
                                                  return Center(
                                                    child: Column(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Icon(
                                                          Icons.broken_image_outlined,
                                                          size: 40.r,
                                                          color: AppColors.textHint,
                                                        ),
                                                        SizedBox(height: 8.h),
                                                        Text(
                                                          '图片加载失败',
                                                          style: TextStyle(
                                                            fontSize: 14.sp,
                                                            color: AppColors.textSecondary,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            )
                                          : Center(
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.add_photo_alternate_outlined,
                                                    size: 48.r,
                                                    color: AppColors.primary.withOpacity(0.7),
                                                  ),
                                                  SizedBox(height: 12.h),
                                                  Text(
                                                    '点击选择封面图片',
                                                    style: TextStyle(
                                                      fontSize: 15.sp,
                                                      color: AppColors.textSecondary,
                                                    ),
                                                  ),
                                                  SizedBox(height: 4.h),
                                                  Text(
                                                    '推荐使用方形图片',
                                                    style: TextStyle(
                                                      fontSize: 12.sp,
                                                      color: AppColors.textHint,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 24.h),

                    // 操作按钮区域
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(16.r),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.cardShadow,
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.all(20.r),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 保存按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isUploadingImage ? null : _saveSettings,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.symmetric(vertical: 14.h),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                elevation: 0,
                                disabledBackgroundColor: AppColors.primary.withOpacity(0.5),
                                disabledForegroundColor: Colors.white.withOpacity(0.7),
                              ),
                              child: Text(
                                _isUploadingImage ? '正在上传图片...' : '保存设置',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),

                          SizedBox(height: 16.h),

                          // 删除收藏夹按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isUploadingImage ? null : _deleteFavorite,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: AppColors.error,
                                padding: EdgeInsets.symmetric(vertical: 14.h),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12.r),
                                  side: BorderSide(
                                    color: _isUploadingImage
                                        ? AppColors.error.withOpacity(0.2)
                                        : AppColors.error.withOpacity(0.5),
                                    width: 1.5,
                                  ),
                                ),
                                elevation: 0,
                                disabledBackgroundColor: Colors.white.withOpacity(0.9),
                                disabledForegroundColor: AppColors.error.withOpacity(0.4),
                              ),
                              child: Text(
                                '删除收藏夹',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),

                          if (_bookmarkCount > 0)
                            Padding(
                              padding: EdgeInsets.only(top: 8.h),
                              child: Text(
                                '该收藏夹包含 $_bookmarkCount 个内容，删除后将无法恢复',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.textHint,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
