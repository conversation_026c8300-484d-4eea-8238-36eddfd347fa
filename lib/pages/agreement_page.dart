import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AgreementPage extends StatelessWidget {
  const AgreementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用户协议'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '存点产品和服务许可协议',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              '最后更新日期：2024年5月1日',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 24.h),
            _buildAgreementContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildAgreementContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildParagraph(
          '《存点产品和服务许可协议》（以下简称"本协议"）是由您与上海讯河文化传播有限公司（以下简称"讯河文化"）就使用"存点"产品和服务所达成的协议。在您开始使用"存点"产品和服务之前，请您务必应认真阅读（未成年人应当在监护人陪同下阅读）并充分理解各条款内容，特别涉及是免除或者限制讯河文化责任的条款。 除非您完全接受本协议的全部内容，否则您无权使用"存点"产品和服务。若您使用"存点"产品，则视为您已充分理解本协议并同意签署本协议，承诺作为协议的一方当事人接受协议的约束。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('1. 协议范围'),
        _buildParagraph(
          '1.1 本协议约定了上海讯河文化传播有限公司和用户之间就使用"存点"产品和服务事宜发生的权利义务关系。'
        ),
        _buildParagraph(
          '1.2 讯河文化：是指上海讯河文化传播有限公司，但就本协议涉及、及"存点"产品将来可能涉及的某些服务项目，讯河文化的关联企业也可能向您提供产品和服务，与您发生权利义务关系。'
        ),
        _buildParagraph(
          '1.3 您：又称"用户"，是指任何以合法的方式获取和使用"存点"产品的人，不限于自然人或机构。'
        ),
        _buildParagraph(
          '1.4 存点产品：又称"存点"或"存点软件"，是指由讯河文化合法拥有并运营的、标注名称为"存点"的移动客户端应用程序。客户端应用程序以软件形式提供，包括但不限于iOS、Android等多个版本，用户必须选择与所安装终端设备相匹配的软件版本。'
        ),
        _buildParagraph(
          '1.5 存点服务：指讯河文化向您提供的与"存点"产品相关的各项运营服务。'
        ),
        _buildParagraph(
          '1.6 本协议内容同时包括讯河文化及其关联企业可能不断发布的关于"存点"产品和服务的业务规则、相关协议及其修订版本等内容。上述内容一经正式发布，即为本协议不可分割的组成部分，用户同样应当遵守。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('2. 产品与服务'),
        _buildParagraph(
          '2.1 用户可以从合法的渠道下载"存点"产品到其合法拥有的终端设备中。但除非得到特别的授权，否则，用户不得以任何形式改编、复制或交易"存点"产品。如果你从未经讯河文化授权的渠道获取"存点"软件或与"存点"名称相同的安装程序，讯河文化无法保证该软件能够正常使用，并对因此给用户造成的损失不予负责。'
        ),
        _buildParagraph(
          '2.2 一旦用户在其终端设备中打开"存点"产品，即视为使用"存点"产品和服务。为充分实现"存点"产品及服务的全部功能，用户可能需要将其终端设备联网。'
        ),
        _buildParagraph(
          '2.3 讯河文化授权用户拥有中华人民共和国境内永久地、不可转让地、非独占地和非商业性地使用"存点"产品和服务的权利，但该权利不可转让，讯河文化也保留在任何必要情形下收回该授权的权利。'
        ),
        _buildParagraph(
          '2.4 为了保证"存点"产品和服务的安全性和功能的一致性，讯河文化有权不经向用户特别通知而对软件进行更新，或者对软件的部分功能效果进行改变或限制。'
        ),
        _buildParagraph(
          '2.5 "存点"软件新版本发布后，旧版本的软件可能无法使用。讯河文化不保证旧版本软件继续可用，请用户随时核对并下载最新版本。'
        ),
        _buildParagraph(
          '2.6 用户理解并同意：为了提供有效的服务，"存点"会利用用户终端设备的处理器和带宽等资源。"存点"使用过程中可能会产生数据流量费用，用户需自行向运营商了解相关资费信息，并自行承担相关费用。'
        ),
        _buildParagraph(
          '2.7 讯河文化有权根据实际情况自行决定单个用户在"存点"产品和服务中数据的最长储存期限，并在服务器上为其分配的存储空间。用户可根据自己的需要自行备份本软件及服务中的相关数据。'
        ),
        _buildParagraph(
          '2.8 如果用户停止使用本软件及服务或服务被终止或取消，讯河文化可以从服务器上永久地删除用户的数据。服务停止、终止或取消后，讯河文化没有义务向用户返还任何数据。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('3. 帐号和用户行为'),
        _buildParagraph(
          '3.1 "存点"为用户提供了注册通道，用户在有权选择合法并符合规定的字符组合作为自己的帐号，并自行设置符合安全要求的密码。用户设置的帐号、密码是用户用以登录"存点"产品，并以注册用户身份使用产品或接受服务的凭证。"存点"帐号的所有权归讯河文化所有，用户完成申请注册手续后，仅获得帐号的使用权，且该使用权仅属于初始申请注册人。'
        ),
        _buildParagraph(
          '3.2 您在"存点"中的注册帐号仅限于您本人使用，禁止赠与、借用、出租或售卖。如果讯河文化发现或者有理由怀疑使用者并非帐号初始注册人，则有权在未经通知的情况下，暂停或终止向该注册帐号提供服务，并有权注销该帐号，而无需向注册该帐号的用户承担法律责任。由此带来的包括并不限于用户通讯中断、用户资料和信息等清空等损失由用户自行承担。'
        ),
        _buildParagraph(
          '3.3 用户有责任维护个人帐号、密码的安全性与保密性，用户就以其注册帐号名义所从事的一切活动负全部责任，包括用户数据的修改、发表的评论以及其他在"存点"产品上的操作行为。因此，用户应高度重视对帐号与密码的保密。若发现他人未经许可使用其帐号或发生其他任何安全漏洞问题时，用户应立即通知讯河文化。但无论因何种原因发生的密码泄露，均应由用户自行承担责任。'
        ),
        _buildParagraph(
          '3.4 用户知晓帐号及密码保管责任在于用户，讯河文化不承诺帐号丢失或遗忘密码后用户能通过有效途径找回帐号。'
        ),
        _buildParagraph(
          '3.5 除自行注册"存点"帐号外，用户也可选择通过"存点"的帐号关联系统，授权使用其合法拥有的Apple ID、腾讯微信、腾讯QQ等帐号注册并登录"存点"产品。当用户以上述已有帐号注册的，同样适用本协议中对帐号的相关约定。'
        ),
        _buildParagraph(
          '3.6 用户在使用"存点"产品和服务时，应当遵守中华人民共和国的法律。用户不得利用讯河文化的产品和服务从事上述法律法规、政策以及侵犯他人合法权利的行为。用户不得通过头像、用户名、昵称、网址、自述等个人资料冒充他人身份，无论是否发布信息。如果遇到上述情形，讯河文化保留收回帐号的权利。'
        ),
        _buildParagraph(
          '3.7 用户不得使用未经讯河文化授权或许可的任何插件、外挂或第三方工具对"存点"产品和服务的正常运行进行干扰、破坏、修改或施加其他影响。'
        ),
        _buildParagraph(
          '3.8 用户不得利用或针对"存点"产品进行任何危害计算机网络安全的行为，包括但不限于：使用未经许可的数据或进入未经许可的服务器/帐户；未经允许进入公众计算机网络或者他人计算机系统并删除、修改、增加存储信息；未经许可，企图探查、扫描、测试"存点"产品系统或网络的弱点或其它实施破坏网络安全的行为；企图干涉、破坏"存点"产品系统或网站的正常运行，故意传播恶意程序或病毒以及其他破坏干扰正常网络信息服务的行为；伪造TCP/IP数据包名称或部分名称。'
        ),
        _buildParagraph(
          '3.9 未经讯河文化许可，您不得在"存点"产品中进行任何诸如发布广告、销售商品的商业行为。'
        ),
        _buildParagraph(
          '3.10 在任何情况下，如果讯河文化有理由认为用户的任何行为违反或可能违反上述约定的，讯河文化可在任何时候不经任何事先通知终止向用户提供服务。用户注册"存点"帐号后如果长期不登录该帐号，讯河文化有权回收该帐号，以免造成资源浪费，由此带来的任何损失均由用户自行承担。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('4. 知识产权'),
        _buildParagraph(
          '4.1 未经相关权利人同意，用户不得对"存点"产品和服务涉及的相关网页、应用、软件等产品进行反向工程、反向汇编、反向编译等。'
        ),
        _buildParagraph(
          '4.2 用户在使用"存点"产品和服务时发表上传的文字、图片、视频、软件以及表演等用户原创的信息，此知识产权归属用户，但用户的发表、上传行为表明该信息对讯河文化非独占性、永久性和可转让的授权。讯河文化可将上述信息在"存点"中使用，可在讯河文化的其他产品中使用，也可以由讯河文化授权给合作方使用。'
        ),
        _buildParagraph(
          '4.3 用户应保证，在使用"存点"产品和服务时上传的文字、图片、视频、软件以及表演等信息不侵犯任何第三方知识产权。否则，讯河文化有权移除该侵权信息，并对此不负任何责任。如前述第三方提出权利主张，用户应自行承担责任，并保证讯河文化不会因此而遭受任何损失。'
        ),
        _buildParagraph(
          '4.4 讯河文化是"存点"产品和服务的知识产权权利人。"存点"享有的或可能享有的一切商标权、著作权、专利权和商业秘密等知识产权，以及由讯河文化构思创作并提供的与"存点"产品和服务相关的信息内容（包括但不限于网页、文字、图片、作品汇编、改编、版面设计、有关数据等）的知识产权，均归属于讯河文化所有。'
        ),
        _buildParagraph(
          '4.5 "存点"产品和服务中所包含的内容的知识产权均受到法律保护，未经讯河文化、用户或相关权利人书面许可，任何人不得以任何形式进行使用或创造相关衍生作品。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('5. 个人信息和隐私'),
        _buildParagraph(
          '5.1 用户在注册帐号或使用"存点"产品和服务的过程中，可能需要填写一些必要的信息。若国家法律法规有特殊规定的，用户需要填写真实的身份信息。若用户填写的信息不完整，则无法使用相关产品或服务或在使用过程中受到限制。'
        ),
        _buildParagraph(
          '5.2 出于安全性和身份识别（如号码申诉服务）的考虑，用户可能无法修改注册时提供的初始注册信息及其他验证信息。'
        ),
        _buildParagraph(
          '5.3 讯河文化将运用各种安全技术和程序建立完善的管理制度来保护用户的个人信息，以免遭受未经授权的访问、使用或披露。但用户同时理解，由于互联网的开放性以及技术更新非常快，非讯河文化可控制的因素导致用户信息泄漏的，讯河文化不承担责任。'
        ),
        _buildParagraph(
          '5.4 讯河文化不会将您的个人信息转移或披露给任何非关联的第三方，除非：'
        ),
        _buildParagraph(
          '（1）相关法律法规或法院、政府机关要求；或'
        ),
        _buildParagraph(
          '（2）为完成合并、分立、收购或资产转让而转移；或'
        ),
        _buildParagraph(
          '（3）为提供您要求的服务所必需。'
        ),
        _buildParagraph(
          '5.5 请用户注意勿在使用"存点"产品和服务时随意透露您或他人的各类财产帐户、银行卡、信用卡、第三方支付账户及对应密码等重要资料，否则由此带来的任何损失由用户自行承担。用户不应将自认为隐私的信息通过"存点"产品发表、上传或扩散。讯河文化不对因此造成的损失承担任何责任。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('6. 服务的变更、中断和终止'),
        _buildParagraph(
          '6.1 用户理解并同意，讯河文化提供的"存点"产品和服务是按照现有技术和条件所能达到的现状提供的。讯河文化会尽最大努力向您提供服务，确保服务的连贯性和安全性；但讯河文化不能随时预见和防范法律、技术以及其他风险，包括但不限于不可抗力、病毒、木马、黑客攻击、系统不稳定、网络环境、第三方服务瑕疵、政府行为等原因可能导致的服务中断、数据丢失以及其他的损失和风险。对于上述原因造成的用户数据损失，讯河文化无须向用户或第三方负责或承担任何赔偿责任。'
        ),
        _buildParagraph(
          '6.2 用户理解并同意，讯河文化为了服务整体运营的需要，有权在不事先通知用户的情况下修改、中断、中止或终止"存点"产品内的各项服务，而无须向用户或第三方负责或承担任何赔偿责任。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('7. 法律责任声明'),
        _buildParagraph(
          '7.1 讯河文化对于任何自"存点"产品和服务中获得的他人的信息、内容或者广告宣传等任何资讯（以下统称"讯息"），不负保证真实、准确和完整性的责任。如果任何单位或者个人通过上述"讯息"而进行任何行为，须自行甄别真伪和谨慎预防风险，否则，无论何种原因，讯河文化不对任何非与本应用程序直接发生的交易和（或）行为承担任何直接、间接、附带或衍生的损失和责任。'
        ),
        _buildParagraph(
          '7.2 讯河文化不保证产品和服务完全适合用户的使用要求；不保证产品或服务不受干扰，及时、安全、可靠，或不出现错误用户；不保证经由讯河文化取得的任何产品、服务或其他材料符合用户的期望；不保证产品或服务中任何错误都将能得到更正。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('8. 违约责任'),
        _buildParagraph(
          '8.1 用户因违反国家法律法规或本协议的约定，或用户侵害他人任何权利因而衍生或导致任何第三人向讯河文化提出任何索赔或请求，包括但不限于诉讼费用、律师费用、差旅费用、和解金额、罚款或生效法律文书中规定的损害赔偿金额、软件使用费等而给讯河文化造成损失的，用户应赔偿讯河文化因此而遭受的一切损失，并消除影响。'
        ),
        _buildParagraph(
          '8.2 如果讯河文化发现或收到他人举报或投诉用户违反本协议约定的，讯河文化有权不经通知随时对相关内容进行删除、屏蔽，并视行为情节对违规帐号处以包括但不限于警告、限制或禁止使用部分或全部功能、帐号封禁直至注销的处罚，并有权公告处理结果。'
        ),
        SizedBox(height: 16.h),
        _buildSectionTitle('9. 其他条款'),
        _buildParagraph(
          '9.1 您使用"存点"产品和服务即视为您已阅读并同意受本协议的约束。'
        ),
        _buildParagraph(
          '9.2 本协议可能因国家政策、产品以及履行环境发生变化而进行修改，讯河文化有权在必要时变更本协议条款，并在相关网站、页面或应用程序内进行通知。您也可以在讯河文化及"存点"产品的相关页面查阅最新版本的协议条款。本协议条款变更后，如果您继续使用"存点"产品和服务，即视为您已接受变更后的协议。如果您不接受变更后的协议，应当停止登录或使用"存点"产品和服务。'
        ),
        _buildParagraph(
          '9.3 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。倘本协议之任何规定因与中国法律抵触而无效，则这些条款将尽可能接近本协议原条文意旨重新解析，且本协议其它规定仍应具有完整的效力及效果。本协议的签署地点为讯河文化公司所在地（上海市），若用户与讯河文化发生争议的，双方同意将争议提交上海市有管辖权的人民法院诉讼解决。'
        ),
        _buildParagraph(
          '9.4 本协议所有条款的标题仅为阅读方便，本身并无实际涵义，不能作为本协议涵义解释的依据。'
        ),
        _buildParagraph(
          '9.5 本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。'
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildParagraph(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16.sp,
          height: 1.5,
          color: Colors.black87,
        ),
      ),
    );
  }
}