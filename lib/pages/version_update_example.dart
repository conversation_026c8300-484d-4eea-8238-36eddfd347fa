import 'package:flutter/material.dart';
import '../services/version_service.dart';
import '../widgets/custom_toast.dart';

/// 版本更新示例页面
///
/// 展示如何使用安装功能
class VersionUpdateExamplePage extends StatefulWidget {
  const VersionUpdateExamplePage({Key? key}) : super(key: key);

  @override
  State<VersionUpdateExamplePage> createState() => _VersionUpdateExamplePageState();
}

class _VersionUpdateExamplePageState extends State<VersionUpdateExamplePage> {
  final _versionService = VersionService();

  String _downloadStatus = '';
  bool _hasDownloadedFile = false;
  bool _hasInstallPermission = false;

  @override
  void initState() {
    super.initState();
    _checkStatus();
  }

  /// 检查当前状态
  Future<void> _checkStatus() async {
    setState(() {
      _hasDownloadedFile = _versionService.downloadedFilePath != null;
    });

    // 检查安装权限
    final hasPermission = await _versionService.hasInstallPermission();
    setState(() {
      _hasInstallPermission = hasPermission;
    });
  }

  /// 安装应用
  Future<void> _installApp() async {
    try {
      setState(() {
        _downloadStatus = '正在安装...';
      });

      final result = await _versionService.installPendingUpdate();

      if (result['success'] == true) {
        CustomToast.show('安装流程已启动');
        setState(() {
          _downloadStatus = '安装流程已启动';
        });
      } else if (result['needPermission'] == true) {
        CustomToast.show('需要安装权限，请在设置中授权');
        setState(() {
          _downloadStatus = '需要安装权限';
          _hasInstallPermission = false;
        });
      } else {
        CustomToast.show('安装失败: ${result['message']}');
        setState(() {
          _downloadStatus = '安装失败: ${result['message']}';
        });
      }
    } catch (e) {
      CustomToast.show('安装异常: $e');
      setState(() {
        _downloadStatus = '安装异常: $e';
      });
    }
  }

  /// 请求安装权限
  Future<void> _requestInstallPermission() async {
    try {
      await _versionService.requestInstallPermission();
      CustomToast.show('请在设置中允许安装未知来源应用');

      // 延迟检查权限状态
      Future.delayed(const Duration(seconds: 2), () {
        _checkStatus();
      });
    } catch (e) {
      CustomToast.show('请求权限失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('版本更新示例'),
        backgroundColor: const Color(0xFF1EB9EF),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 状态信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前状态',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('下载状态: $_downloadStatus'),
                    Text('是否有已下载文件: ${_hasDownloadedFile ? "是" : "否"}'),
                    Text('是否有安装权限: ${_hasInstallPermission ? "是" : "否"}'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 操作按钮
            ElevatedButton(
              onPressed: _hasDownloadedFile ? _installApp : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('安装应用'),
            ),

            const SizedBox(height: 8),

            ElevatedButton(
              onPressed: !_hasInstallPermission ? _requestInstallPermission : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('请求安装权限'),
            ),

            const SizedBox(height: 8),

            ElevatedButton(
              onPressed: _checkStatus,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
              ),
              child: const Text('刷新状态'),
            ),

            const SizedBox(height: 16),

            // 说明文字
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '使用说明',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '1. 如果有已下载的文件，点击"安装应用"\n'
                      '2. 如果提示需要权限，点击"请求安装权限"\n'
                      '3. 在设置中允许安装未知来源应用\n'
                      '4. 返回应用，重新点击"安装应用"',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
