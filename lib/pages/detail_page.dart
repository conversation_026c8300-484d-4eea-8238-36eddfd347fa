import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:aishoucang/api/api_provider.dart';
import 'package:aishoucang/api/bookmark_api.dart';
import 'package:aishoucang/native_bridge/native_bridge.dart';

import 'package:aishoucang/pages/collection_settings_page.dart';
import 'package:aishoucang/components/bookmark_edit_dialog.dart';
import 'package:aishoucang/components/bookmark_grid_view.dart';
import 'package:aishoucang/components/bookmark_tag_manager_dialog.dart';
import 'package:aishoucang/routes.dart';
import 'package:aishoucang/constants/app_colors.dart';
import 'package:aishoucang/utils/platform_utils.dart';
import 'package:aishoucang/services/storage_service.dart';
import 'package:aishoucang/pages/app/widgets/filter_menu_dialog.dart';
import 'package:aishoucang/pages/app/widgets/add_tag_dialog.dart';
import 'package:aishoucang/services/tag_service.dart';
import 'package:aishoucang/models/tag.dart';
import 'package:aishoucang/widgets/custom_toast.dart';

class DetailPage extends StatefulWidget {
  final String id;

  const DetailPage({super.key, required this.id});

  @override
  State<DetailPage> createState() => _DetailPageState();
}

class _DetailPageState extends State<DetailPage> {
  final _apiProvider = ApiProvider();
  final _bookmarkApi = ApiProvider().bookmarkApi;
  final _scrollController = ScrollController();
  final _storageService = StorageService();
  final _tagService = TagService();

  List<BookmarkItem> _bookmarks = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _hasMore = true;
  int _currentPage = 1;
  final int _pageSize = 20;

  // 收藏夹信息
  String _collectionName = '收藏详情';
  String? _collectionCover;

  // 视图模式状态
  bool _isGridView = false; // false: 列表模式, true: 大图模式

  // 过滤状态
  String? _selectedTag; // 当前选择的标签过滤
  String? _selectedPlatform; // 当前选中的平台

  // 标签对象缓存
  Map<String, Tag> _tagMap = {};

  // 平台相关
  // 识别平台类型
  String _getPlatformType(String schemeUrl) {
    if (schemeUrl.startsWith('xhsdiscover://')) {
      return 'xiaohongshu';
    } else if (schemeUrl.startsWith('bilibili://')) {
      return 'bilibili';
    } else if (schemeUrl.startsWith('snssdk1128://')) {
      return 'douyin';
    } else if (schemeUrl.startsWith('weixin://') || _isWechatPublicAccountUrl(schemeUrl)) {
      return 'wechat';
    } else if (schemeUrl.startsWith('pinduoduo://') || schemeUrl.contains('yangkeduo.com')) {
      return 'pinduoduo';
    } else if (schemeUrl.startsWith('taobao://') || schemeUrl.contains('taobao.com')) {
      return 'taobao';
    } else if (schemeUrl.startsWith('openapp.jdmobile://') || schemeUrl.contains('jd.com')) {
      return 'jingdong';
    } else {
      return 'unknown';
    }
  }

  /// 判断是否为微信公众号链接
  bool _isWechatPublicAccountUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host == 'mp.weixin.qq.com';
    } catch (e) {
      return false;
    }
  }

  // 获取平台图标
  Widget _getPlatformIcon(String schemeUrl) {
    String platformType = _getPlatformType(schemeUrl);

    switch (platformType) {
      case 'xiaohongshu':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/xiaohongshu.png', width: 15.r, height: 15.r),
        );
      case 'bilibili':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/bilibili.png', width: 15.r, height: 15.r),
        );
      case 'douyin':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/douyin.png', width: 15.r, height: 15.r),
        );
      case 'wechat':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/wechat.png', width: 15.r, height: 15.r),
        );
      case 'pinduoduo':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/Pinduoduo.png', width: 15.r, height: 15.r),
        );
      case 'jingdong':
        return ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.asset('assets/jingdong.png', width: 15.r, height: 15.r),
        );
      default:
        return Icon(Icons.public, size: 18.r, color: Colors.grey[600]);
    }
  }

  @override
  void initState() {
    super.initState();
    // 加载用户偏好的视图模式
    _loadViewModePreference();
    // 加载收藏夹信息
    _loadCollectionInfo();
    // 加载标签对象
    _loadTagObjects();
    // 加载书签列表
    _loadBookmarks();

    // 添加滚动监听器，实现上拉加载更多
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200 &&
          !_isLoading && _hasMore) {
        _loadMoreBookmarks();
      }
    });
  }

  // 加载用户偏好的视图模式
  Future<void> _loadViewModePreference() async {
    try {
      final isGridView = await _storageService.getGridViewMode();
      setState(() {
        _isGridView = isGridView;
      });
    } catch (e) {
      print('加载视图模式偏好失败: $e');
      // 失败时使用默认值（列表模式）
    }
  }

  // 保存视图模式偏好
  Future<void> _saveViewModePreference(bool isGridView) async {
    try {
      await _storageService.saveGridViewMode(isGridView);
    } catch (e) {
      print('保存视图模式偏好失败: $e');
    }
  }

  // 加载收藏夹信息
  Future<void> _loadCollectionInfo() async {
    try {
      // 获取收藏夹列表
      final response = await _apiProvider.favoritesApi.getUserFavorites();

      if (response.containsKey('favorites')) {
        final favorites = response['favorites'] as List<dynamic>;

        // 查找当前收藏夹
        for (final favorite in favorites) {
          if (favorite['id'] == widget.id) {
            setState(() {
              _collectionName = favorite['name'] as String? ?? '收藏详情';
              _collectionCover = favorite['cover'] as String?;
            });
            break;
          }
        }
      }
    } catch (e) {
      print('加载收藏夹信息失败: $e');
      // 失败时使用默认名称
    }
  }

  // 加载标签对象
  Future<void> _loadTagObjects() async {
    try {
      final tagObjects = await _tagService.getAllTagObjects();
      setState(() {
        _tagMap = {for (var tag in tagObjects) tag.name: tag};
      });
    } catch (e) {
      print('加载标签对象失败: $e');
      // 失败时使用空的标签映射
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 加载书签列表
  Future<void> _loadBookmarks() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _currentPage = 1;
    });

    try {
      print('开始加载收藏夹ID: ${widget.id} 的书签列表，页码: $_currentPage');

      // 构建标签过滤参数
      List<String>? tagNames;
      if (_selectedTag != null && _selectedTag!.isNotEmpty) {
        tagNames = [_selectedTag!];
      }

      final result = await _bookmarkApi.getBookmarkList(
        favoriteId: widget.id,
        tagNames: tagNames,
        page: _currentPage,
        pageSize: _pageSize,
        platformType: _selectedPlatform,
      );

      print('获取书签API响应: $result');

      final bookmarks = result['items'] as List<BookmarkItem>? ?? [];
      final total = result['total'] as int? ?? bookmarks.length;

      print('解析到 ${bookmarks.length} 个书签项');
      if (bookmarks.isNotEmpty) {
        print('第一个书签示例: ${bookmarks.first.title} - ${bookmarks.first.influencerName}');
      }

      setState(() {
        _bookmarks = bookmarks;
        _hasMore = _bookmarks.length < total;
        _isLoading = false;
      });
    } catch (e) {
      print('加载书签时出错: $e');
      setState(() {
        _hasError = true;
        _errorMessage = '加载失败: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  // 加载更多书签
  Future<void> _loadMoreBookmarks() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      _currentPage++;

      // 构建标签过滤参数
      List<String>? tagNames;
      if (_selectedTag != null && _selectedTag!.isNotEmpty) {
        tagNames = [_selectedTag!];
      }

      final result = await _bookmarkApi.getBookmarkList(
        favoriteId: widget.id,
        tagNames: tagNames,
        page: _currentPage,
        pageSize: _pageSize,
        platformType: _selectedPlatform,
      );

      final newBookmarks = result['items'] as List<BookmarkItem>? ?? [];
      final total = result['total'] as int? ?? 0;

      setState(() {
        _bookmarks.addAll(newBookmarks);
        _hasMore = _bookmarks.length < total;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _currentPage--;
        _hasError = true;
        _errorMessage = '加载更多失败: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          _collectionName,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        elevation: 0,
        backgroundColor: AppColors.surface,
        titleSpacing: 0, // 减少标题间距
        actions: [
          // 过滤按钮
          Container(
            width: 24,
            height: 24,
            margin: EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: _selectedTag != null || _selectedPlatform != null
                ? AppColors.primary
                : AppColors.searchBarBackground,
              borderRadius: BorderRadius.circular(2),
            ),
            child: InkWell(
              onTap: _showFilterMenu,
              borderRadius: BorderRadius.circular(2),
              child: SizedBox(
                width: 24,
                height: 24,
                child: Icon(
                  Icons.filter_list,
                  size: 14,
                  color: _selectedTag != null || _selectedPlatform != null
                    ? Colors.white
                    : AppColors.textSecondary,
                ),
              ),
            ),
          ),
          // 视图模式切换按钮 - 极简设计避免溢出
          Container(
            height: 24,
            margin: EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: AppColors.searchBarBackground,
              borderRadius: BorderRadius.circular(2),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 列表模式按钮
                InkWell(
                  onTap: () {
                    setState(() {
                      _isGridView = false;
                    });
                    _saveViewModePreference(false);
                  },
                  borderRadius: BorderRadius.circular(2),
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: !_isGridView ? AppColors.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Icon(
                      Icons.view_list,
                      size: 14,
                      color: !_isGridView ? Colors.white : AppColors.textSecondary,
                    ),
                  ),
                ),
                // 大图模式按钮
                InkWell(
                  onTap: () {
                    setState(() {
                      _isGridView = true;
                    });
                    _saveViewModePreference(true);
                  },
                  borderRadius: BorderRadius.circular(2),
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: _isGridView ? AppColors.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Icon(
                      Icons.grid_view,
                      size: 14,
                      color: _isGridView ? Colors.white : AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 设置按钮 - 紧凑设计
          IconButton(
            icon: const Icon(Icons.settings, size: 18),
            onPressed: _openSettings,
            padding: EdgeInsets.all(8),
            constraints: BoxConstraints(
              minWidth: 36,
              minHeight: 36,
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // 滚动到顶部
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          }
        },
        mini: true,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.arrow_upward, color: Colors.white),
      ),
      // 直接使用书签列表作为主体内容
      body: _buildBody(),
    );
  }

  // 构建收藏夹头部信息
  Widget _buildCollectionHeader() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 收藏夹信息
          Row(
            children: [
              // 收藏夹封面
              Container(
                width: 48.r,
                height: 48.r,
                decoration: BoxDecoration(
                  color: AppColors.searchBarBackground,
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.cardShadow,
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: _collectionCover != null && _collectionCover!.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: Image.network(
                        _collectionCover!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                            child: Icon(
                              Icons.collections_bookmark,
                              size: 24.r,
                              color: AppColors.primary.withOpacity(0.7),
                            ),
                          );
                        },
                      ),
                    )
                  : Center(
                      child: Icon(
                        Icons.collections_bookmark,
                        size: 24.r,
                        color: AppColors.primary.withOpacity(0.7),
                      ),
                    ),
              ),
              SizedBox(width: 12.w),
              // 收藏夹信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _collectionName,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),


        ],
      ),
    );
  }


  // 显示过滤菜单
  void _showFilterMenu() {
    FilterMenuDialog.show(
      context,
      onDefaultMode: () {
        setState(() {
          _selectedTag = null;
          _selectedPlatform = null;
        });
        _loadBookmarks(); // 重新加载数据
        CustomToast.show('已切换到默认排序');
      },
      onTagSelected: (tag) {
        setState(() {
          _selectedTag = tag;
          _selectedPlatform = null; // 清除平台过滤状态
        });
        _loadBookmarks(); // 重新加载数据
      },
      onAddTag: () {
        _showAddTagDialog();
      },
      onPlatformSelected: (platform) {
        setState(() {
          _selectedPlatform = platform;
          _selectedTag = null; // 清除标签过滤状态
        });
        _loadBookmarks(); // 重新加载数据
      },
    );
  }

  // 显示新增标签对话框
  void _showAddTagDialog() {
    AddTagDialog.show(
      context,
      onConfirm: _addNewTag,
    );
  }

  // 添加新标签
  Future<void> _addNewTag(String tagName) async {
    try {
      final success = await _tagService.addTag(tagName);
      if (success) {
        CustomToast.show('标签"$tagName"添加成功');
        // 刷新过滤菜单中的标签列表
        FilterMenuDialog.refreshTags();
      }
    } catch (e) {
      print('添加标签失败: $e');
      // 错误提示已在TagService中处理
    }
  }



  // 打开设置页面
  Future<void> _openSettings() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => CollectionSettingsPage(
          favoriteId: widget.id,
          initialName: _collectionName,
          initialCover: _collectionCover,
          onSettingsChanged: (newName, newCover) {
            setState(() {
              _collectionName = newName;
              _collectionCover = newCover;
            });
            // 刷新书签列表
            _loadBookmarks();
          },
        ),
      ),
    );

    // 如果收藏夹被删除（result为true），返回上一页并传递需要刷新的信息
    if (result == true) {
      if (mounted) {
        // 返回true表示需要刷新收藏夹列表
        Navigator.of(context).pop(true);
      }
    }
  }

  Widget _buildBody() {
    if (_hasError) {
      return Center(
        child: Container(
          width: 300.w,
          padding: EdgeInsets.all(24.r),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow,
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 60.r,
                color: AppColors.error.withOpacity(0.7),
              ),
              SizedBox(height: 16.h),
              Text(
                '加载失败',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                _errorMessage,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 20.h),
              ElevatedButton(
                onPressed: _loadBookmarks,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: Text(
                  '重新加载',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_bookmarks.isEmpty) {
      if (_isLoading) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 40.r,
                height: 40.r,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  strokeWidth: 3.w,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                '正在加载收藏内容...',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        );
      }

      return Center(
        child: Text(
          '暂无收藏内容',
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.textHint,
            fontWeight: FontWeight.w400,
          ),
        ),
      );
    }

    // 根据视图模式显示不同的组件
    if (_isGridView) {
      // 大图网格模式
      return RefreshIndicator(
        onRefresh: _loadBookmarks,
        color: AppColors.primary,
        backgroundColor: Colors.white,
        displacement: 50.0,
        strokeWidth: 3.0,
        child: BookmarkGridView(
          bookmarks: _bookmarks,
          scrollController: _scrollController,
          hasMore: _hasMore,
          isLoading: _isLoading,
          onLoadMore: _loadMoreBookmarks,
          onDelete: _showDeleteConfirmDialog,
          onMove: _showMoveDialog,
          onEdit: _showEditBookmarkDialog,
          onTagManage: _showTagManagerDialog,
        ),
      );
    } else {
      // 列表模式
      return RefreshIndicator(
        onRefresh: _loadBookmarks,
        color: AppColors.primary,
        backgroundColor: Colors.white,
        displacement: 50.0,
        strokeWidth: 3.0,
        child: SlidableAutoCloseBehavior(
          child: ListView.builder(
            controller: _scrollController,
            padding: EdgeInsets.all(16.r),
            // 始终允许滚动
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: _bookmarks.length + (_hasMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _bookmarks.length) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: SizedBox(
                      width: 24.r,
                      height: 24.r,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                        strokeWidth: 2.w,
                      ),
                    ),
                  ),
                );
              }

              final bookmark = _bookmarks[index];
              return _buildDismissibleBookmarkItem(bookmark, index);
            },
          ),
        ),
      );
    }
  }

  Widget _buildBookmarkItem(BookmarkItem bookmark) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 1,
      shadowColor: AppColors.cardShadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () {
          // 处理点击事件
          if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty) {
            // 检查是否是http/https开头的图片链接
            final schemeUrl = bookmark.schemeUrl!;
            final isImageUrl = schemeUrl.startsWith('http') &&
                (schemeUrl.endsWith('.jpg') ||
                 schemeUrl.endsWith('.jpeg') ||
                 schemeUrl.endsWith('.png') ||
                 schemeUrl.endsWith('.gif') ||
                 schemeUrl.endsWith('.webp') ||
                 schemeUrl.contains('.jpg?') ||
                 schemeUrl.contains('.jpeg?') ||
                 schemeUrl.contains('.png?') ||
                 schemeUrl.contains('.gif?') ||
                 schemeUrl.contains('.webp?'));

            if (isImageUrl) {
              // 如果是图片链接，跳转到图片预览页面
              Navigator.pushNamed(
                context,
                AppRoutes.imagePreview,
                arguments: {'imageUrl': schemeUrl},
              );
            } else {
              // 如果不是图片链接，使用原来的逻辑打开scheme URL
              final nativeBridge = NativeBridge();

              // 先检查悬浮窗权限
              nativeBridge.checkOverlayPermission().then((hasPermission) {
                if (!hasPermission) {
                  // 如果没有悬浮窗权限，提示用户并引导开启
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('需要悬浮窗权限才能显示返回按钮'),
                      action: SnackBarAction(
                        label: '去开启',
                        onPressed: () {
                          nativeBridge.openOverlayPermissionSettings();
                        },
                      ),
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }

                // 无论是否有权限，都尝试打开链接
                nativeBridge.openSchemeUrl(schemeUrl, showReturnButton: true).then((success) {
                  if (!success) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('无法打开链接，可能没有安装对应的应用'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                });
              });
            }
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('此内容没有可打开的链接'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        },
        // 添加长按事件处理
        onLongPress: () {
          // 显示编辑对话框
          _showEditBookmarkDialog(bookmark);
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(14.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 博主信息行 - 移到顶部
              Row(
                children: [
                  // 博主头像或平台图标 - 使用固定尺寸的容器
                  Container(
                    width: 20.r,
                    height: 20.r,
                    decoration: BoxDecoration(
                      color: AppColors.searchBarBackground,
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    alignment: Alignment.center,
                    child: bookmark.influencerAvatar != null && bookmark.influencerAvatar!.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: Image.network(
                            bookmark.influencerAvatar!,
                            width: 20.r,
                            height: 20.r,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              // 如果头像加载失败，回退到平台图标
                              return bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty
                                ? _getPlatformIcon(bookmark.schemeUrl!)
                                : Icon(Icons.public, size: 12.r, color: AppColors.textSecondary);
                            },
                          ),
                        )
                      : bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty
                        ? _getPlatformIcon(bookmark.schemeUrl!)
                        : Icon(Icons.public, size: 12.r, color: AppColors.textSecondary),
                  ),
                  SizedBox(width: 8.w),

                  // 作者名称
                  Expanded(
                    child: Text(
                      bookmark.influencerName,
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // 平台logo和创建时间
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 平台logo
                      if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty)
                        Builder(
                          builder: (context) {
                            final logoAsset = PlatformUtils.getPlatformLogoFromSchemeUrl(bookmark.schemeUrl);
                            if (logoAsset != null) {
                              return Padding(
                                padding: EdgeInsets.only(right: 4.w),
                                child: Image.asset(
                                  logoAsset,
                                  width: 12.r,
                                  height: 12.r,
                                  errorBuilder: (context, error, stackTrace) {
                                    return SizedBox.shrink();
                                  },
                                ),
                              );
                            }
                            return SizedBox.shrink();
                          },
                        ),
                      // 创建时间
                      Text(
                        _formatDate(bookmark.createTime),
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: AppColors.textHint,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // 标题、描述和封面并排
              if ((bookmark.title != null && bookmark.title!.isNotEmpty) ||
                  (bookmark.desc != null && bookmark.desc!.isNotEmpty) ||
                  (bookmark.cover != null && bookmark.cover!.isNotEmpty))
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题和描述的容器
                    Expanded(
                      flex: 3,
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          // 计算图片的高度（正方形，宽高相等）
                          final imageHeight = constraints.maxWidth / 3 * 0.8; // 因为图片flex是1，文字flex是3

                          return Container(
                            constraints: BoxConstraints(
                              minHeight: imageHeight,
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 标题
                                if (bookmark.title != null && bookmark.title!.isNotEmpty)
                                  Text(
                                    bookmark.title!,
                                    style: TextStyle(
                                      fontSize: 15.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary,
                                      height: 1.3,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),

                                if (bookmark.title != null && bookmark.title!.isNotEmpty)
                                  SizedBox(height: 6.h),

                                // 描述
                                if (bookmark.desc != null && bookmark.desc!.isNotEmpty)
                                  Text(
                                    bookmark.desc!,
                                    style: TextStyle(
                                      fontSize: 13.sp,
                                      color: AppColors.textSecondary,
                                      height: 1.3,
                                    ),
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                  ),

                                // 标签显示
                                if (bookmark.tags.isNotEmpty) ...[
                                  SizedBox(height: 8.h),
                                  _buildBookmarkTags(bookmark.tags),
                                ],
                              ],
                            ),
                          );
                        }
                      ),
                    ),

                    // 添加间距
                    if (bookmark.cover != null && bookmark.cover!.isNotEmpty)
                      SizedBox(width: 12.w),

                    // 封面图片 - 保持宽度，高度设置为宽度相等
                    if (bookmark.cover != null && bookmark.cover!.isNotEmpty)
                      Expanded(
                        flex: 1,
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: Container(
                                width: constraints.maxWidth,
                                height: constraints.maxWidth * 0.8,
                                decoration: BoxDecoration(
                                  color: AppColors.searchBarBackground,
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Image.network(
                                  bookmark.cover!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: AppColors.searchBarBackground,
                                      alignment: Alignment.center,
                                      child: Icon(
                                        Icons.image_not_supported,
                                        size: 18.sp,
                                        color: AppColors.textHint
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          }
                        ),
                      ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  // 格式化日期
  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    }
  }

  // 显示编辑书签对话框
  void _showEditBookmarkDialog(BookmarkItem bookmark) {
    showDialog(
      context: context,
      builder: (context) => BookmarkEditDialog(
        bookmark: bookmark,
        onUpdateSuccess: (updatedBookmark) {
          // 更新成功后，刷新书签列表
          _loadBookmarks();
        },
      ),
    );
  }

  // 构建可滑动删除的书签项
  Widget _buildDismissibleBookmarkItem(BookmarkItem bookmark, int index) {
    return Slidable(
      key: Key(bookmark.id), // 使用书签ID作为唯一标识
      // 从右向左滑动显示删除按钮（iOS风格）
      endActionPane: ActionPane(
        motion: const DrawerMotion(), // 使用抽屉式动画，更像卡片背后的按钮
        extentRatio: 0.6, // 滑动区域宽度，增加以容纳三个按钮
        children: [
          // 第六步：添加移动按钮，支持多个按钮
          Expanded(
            child: Container(
              margin: EdgeInsets.only(bottom: 12.h), // 与卡片的margin保持一致
              decoration: BoxDecoration(
                color: Colors.transparent, // 透明背景
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(12.r), // 右上角圆角
                  bottomRight: Radius.circular(12.r), // 右下角圆角
                ),
              ),
              child: Row(
                children: [
                  // 移动按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showMoveDialog(bookmark, index),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.orange, // 移动按钮的橙色背景
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(0), // 中间按钮不要右圆角
                            bottomRight: Radius.circular(0),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
                          children: [
                            Icon(
                              Icons.drive_file_move_outline,
                              color: Colors.white,
                              size: 20.r,
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              '移动',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // 标签管理按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showTagManagerDialog(bookmark),
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.primary, // 标签管理按钮的蓝色背景
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(0), // 中间按钮不要右圆角
                            bottomRight: Radius.circular(0),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
                          children: [
                            Icon(
                              Icons.local_offer_outlined,
                              color: Colors.white,
                              size: 20.r,
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              '标签',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // 删除按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showDeleteConfirmDialog(bookmark, index),
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.error, // 删除按钮的红色背景
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(12.r), // 最右边按钮保持右圆角
                            bottomRight: Radius.circular(12.r),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
                          children: [
                            Icon(
                              Icons.delete_outline,
                              color: Colors.white,
                              size: 20.r,
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              '删除',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      child: _buildBookmarkItem(bookmark),
    );
  }

  // 显示移动对话框
  void _showMoveDialog(BookmarkItem bookmark, int index) async {
    try {
      // 获取收藏夹列表
      final response = await _apiProvider.favoritesApi.getUserFavorites();

      if (!response.containsKey('favorites')) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('获取收藏夹列表失败'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final favorites = response['favorites'] as List<dynamic>;
      // 过滤掉当前收藏夹
      final otherFavorites = favorites.where((fav) => fav['id'] != widget.id).toList();

      if (otherFavorites.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('没有其他收藏夹可以移动到'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // 显示选择对话框
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(
              '移动到收藏夹',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            content: Container(
              width: double.maxFinite,
              height: 300.h, // 设置固定高度
              child: ListView.builder(
                itemCount: otherFavorites.length,
                itemBuilder: (context, index) {
                  final favorite = otherFavorites[index];
                  return ListTile(
                    leading: Container(
                      width: 40.r,
                      height: 40.r,
                      decoration: BoxDecoration(
                        color: AppColors.searchBarBackground,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: favorite['cover'] != null && favorite['cover'].toString().isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: Image.network(
                              favorite['cover'],
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.collections_bookmark,
                                  size: 20.r,
                                  color: AppColors.primary.withOpacity(0.7),
                                );
                              },
                            ),
                          )
                        : Icon(
                            Icons.collections_bookmark,
                            size: 20.r,
                            color: AppColors.primary.withOpacity(0.7),
                          ),
                    ),
                    title: Text(
                      favorite['name'] ?? '未命名收藏夹',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                      _moveBookmarkToFavorite(bookmark, index, favorite['id'], favorite['name']);
                    },
                  );
                },
              ),
            ),
            backgroundColor: AppColors.surface,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          );
        },
      );
    } catch (e) {
      print('获取收藏夹列表失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('获取收藏夹列表失败'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 移动书签到指定收藏夹
  void _moveBookmarkToFavorite(BookmarkItem bookmark, int index, String targetFavoriteId, String targetFavoriteName) async {
    try {
      // 第一步：先在目标收藏夹中新增收藏项，使用Flutter侧的API
      await _addBookmarkToTargetFavorite(bookmark, targetFavoriteId);

      // 第二步：添加成功后，再从当前收藏夹删除这个收藏项（不显示删除成功提示）
      await _bookmarkApi.deleteBookmark(bookmark.id);

      // 从UI列表中移除 - 使用书签ID查找正确的索引，而不是使用传入的index
      setState(() {
        final actualIndex = _bookmarks.indexWhere((item) => item.id == bookmark.id);
        if (actualIndex != -1 && actualIndex < _bookmarks.length) {
          _bookmarks.removeAt(actualIndex);
        } else {
          print('警告：未找到要移除的书签，ID: ${bookmark.id}，当前列表长度: ${_bookmarks.length}');
        }
      });

      // 第三步：显示移动成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已移动到 "$targetFavoriteName"'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );

      print('移动书签成功: ${bookmark.title} 到收藏夹 $targetFavoriteName (ID: $targetFavoriteId)');
    } catch (e) {
      print('移动书签失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('移动失败: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // 添加书签到目标收藏夹
  Future<void> _addBookmarkToTargetFavorite(BookmarkItem bookmark, String targetFavoriteId) async {
    try {
      // 使用Flutter侧的API添加书签
      final data = <String, dynamic>{
        'parent_id': targetFavoriteId,
        'influencer_name': bookmark.influencerName,
        'title': bookmark.title,
        'scheme_url': bookmark.schemeUrl ?? '',
      };

      // 添加可选字段
      if (bookmark.influencerAvatar != null && bookmark.influencerAvatar!.isNotEmpty) {
        data['influencer_avatar'] = bookmark.influencerAvatar;
      }
      if (bookmark.cover != null && bookmark.cover!.isNotEmpty) {
        data['cover'] = bookmark.cover;
      }
      if (bookmark.desc != null && bookmark.desc!.isNotEmpty) {
        data['desc'] = bookmark.desc;
      }

      // 添加标签信息
      if (bookmark.tags.isNotEmpty) {
        data['tags'] = bookmark.tags;
      }

      final response = await _apiProvider.apiClient.post('/bookmark/add', data);

      if (response['code'] != 0) {
        throw Exception(response['message'] ?? '添加书签失败');
      }

      print('成功添加书签到目标收藏夹: ${bookmark.title}，包含标签: ${bookmark.tags}');
    } catch (e) {
      print('添加书签到目标收藏夹失败: $e');
      throw Exception('添加书签到目标收藏夹失败: $e');
    }
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog(BookmarkItem bookmark, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            '确认删除',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          content: Text(
            '确认要删除这个收藏吗？',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          backgroundColor: AppColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
              },
              child: Text(
                '取消',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
                _deleteBookmark(bookmark, index); // 调用删除函数
              },
              child: Text(
                '确定',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // 删除书签的方法
  void _deleteBookmark(BookmarkItem bookmark, int index) {
    // 先从列表中移除
    setState(() {
      _bookmarks.removeAt(index);
    });

    // 创建一个取消标记
    final cancelToken = {
      'cancel': false
    };

    // 显示撤销操作的Snackbar
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final snackBar = SnackBar(
      content: const Text('书签已删除'),
      action: SnackBarAction(
        label: '撤销',
        onPressed: () {
          // 撤销删除操作，将书签重新插入到原位置
          setState(() {
            _bookmarks.insert(index, bookmark);
          });
          // 取消API删除请求（实际上无法取消已发出的请求，但可以忽略其结果）
          cancelToken['cancel'] = true;
        },
      ),
      duration: const Duration(seconds: 3),
    );

    scaffoldMessenger.showSnackBar(snackBar);

    // 异步调用API删除书签
    Future.delayed(const Duration(seconds: 3), () async {
      // 如果用户点击了撤销，则不执行删除操作
      if (cancelToken['cancel'] == true) return;

      try {
        await _bookmarkApi.deleteBookmark(bookmark.id);
        // 删除成功，不需要做任何事情，因为UI已经更新
      } catch (e) {
        print('删除书签失败: $e');
        // 删除失败，但不显示错误消息，因为用户体验上已经删除了
        // 如果需要，可以在后台静默重试
      }
    });
  }

  // 显示标签管理对话框
  void _showTagManagerDialog(BookmarkItem bookmark) {
    showDialog<void>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) => BookmarkTagManagerDialog(
        bookmark: bookmark,
        onTagsUpdated: (updatedBookmark) {
          // 更新本地书签列表中的标签
          final index = _bookmarks.indexWhere((b) => b.id == updatedBookmark.id);
          if (index != -1) {
            setState(() {
              _bookmarks[index] = updatedBookmark;
            });
          }
        },
      ),
    );
  }

  // 构建书签标签显示
  Widget _buildBookmarkTags(List<String> tags) {
    // 最多显示4个标签，超出显示+数量
    final displayTags = tags.take(4).toList();
    final hasMore = tags.length > 4;

    return Wrap(
      spacing: 4.w,
      runSpacing: 4.h,
      children: [
        ...displayTags.asMap().entries.map((entry) {
          final index = entry.key;
          final tag = entry.value;
          return _buildSmallTag(tag, index);
        }),
        if (hasMore)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: AppColors.searchBarBackground,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              '+${tags.length - 4}',
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  // 构建小尺寸标签
  Widget _buildSmallTag(String tag, int colorIndex) {
    Color backgroundColor;
    Color textColor;

    // 尝试从标签映射中获取真实颜色
    final tagObject = _tagMap[tag];
    if (tagObject != null) {
      // 使用服务端返回的颜色
      backgroundColor = _parseColor(tagObject.backgroundColor);
      textColor = _parseColor(tagObject.textColor);
    } else {
      // 如果没有找到对应的标签对象，使用默认颜色配置
      final List<Color> tagColors = [
        const Color(0xFFFFE4E1), // 薄雾玫瑰
        const Color(0xFFFFF0F5), // 薰衣草腮红
        const Color(0xFFE0F2E9), // 薄荷绿
        const Color(0xFFE0F7FA), // 淡青色
        const Color(0xFFFFF5E1), // 香草色
        const Color(0xFFF5F0FF), // 淡紫色
        const Color(0xFFFFFDE7), // 柠檬雪纺
        const Color(0xFFFCE4EC), // 粉红色
        const Color(0xFFF3E5F5), // 兰花色
        const Color(0xFFE8F5E9), // 蜜瓜绿
        const Color(0xFFFBE9E7), // 桃花色
        const Color(0xFFE1F5FE), // 天空蓝
        const Color(0xFFFFF8E1), // 香槟色
        const Color(0xFFF9FBE7), // 青柠色
        const Color(0xFFF1F8E9), // 春绿色
        const Color(0xFFE8EAF6), // 淡靛蓝
        const Color(0xFFFFF3E0), // 杏仁色
        const Color(0xFFE0F2F1), // 薄荷奶昔
        const Color(0xFFF8BBD9), // 樱花粉
        const Color(0xFFE1BEE7), // 紫罗兰
      ];

      final List<Color> textColors = [
        const Color(0xFF8B4513), // 深棕色
        const Color(0xFF8B008B), // 深洋红
        const Color(0xFF006400), // 深绿色
        const Color(0xFF008B8B), // 深青色
        const Color(0xFFFF8C00), // 深橙色
        const Color(0xFF4B0082), // 靛蓝色
        const Color(0xFFB8860B), // 深金黄
        const Color(0xFFDC143C), // 深红色
        const Color(0xFF9932CC), // 深兰花紫
        const Color(0xFF228B22), // 森林绿
        const Color(0xFFFF6347), // 番茄红
        const Color(0xFF4682B4), // 钢蓝色
        const Color(0xFFDAA520), // 金黄色
        const Color(0xFF32CD32), // 酸橙绿
        const Color(0xFF9ACD32), // 黄绿色
        const Color(0xFF6A5ACD), // 石板蓝
        const Color(0xFFFF7F50), // 珊瑚色
        const Color(0xFF20B2AA), // 浅海绿
        const Color(0xFFFF1493), // 深粉红
        const Color(0xFF9370DB), // 中紫色
      ];

      backgroundColor = tagColors[colorIndex % tagColors.length];
      textColor = textColors[colorIndex % textColors.length];
    }

    final displayText = tag.length > 5 ? '${tag.substring(0, 5)}...' : tag;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '🏷️',
            style: TextStyle(fontSize: 8.sp),
          ),
          SizedBox(width: 2.w),
          Text(
            displayText,
            style: TextStyle(
              fontSize: 10.sp,
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 解析十六进制颜色字符串为Color对象
  Color _parseColor(String colorString) {
    try {
      // 移除可能的 # 前缀
      String hexColor = colorString.replaceAll('#', '');

      // 如果是6位，添加FF作为alpha值
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }

      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // 如果解析失败，返回默认颜色
      return const Color(0xFF1890FF);
    }
  }


}