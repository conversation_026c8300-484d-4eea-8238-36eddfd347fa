import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../constants/app_colors.dart';
import '../components/search/search_history.dart';
import '../components/bookmark_list_item.dart';
import '../components/bookmark_grid_view.dart';
import '../services/search_history_service.dart';
import '../services/storage_service.dart';
import '../services/tag_service.dart';
import '../api/bookmark_api.dart';
import '../models/tag.dart';
import '../api/api_provider.dart';
import '../routes.dart';
import '../native_bridge/native_bridge.dart';

/// 搜索页面
class SearchPage extends StatefulWidget {
  const SearchPage({Key? key}) : super(key: key);

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();

  // 搜索历史服务
  final SearchHistoryService _searchHistoryService = SearchHistoryService();

  // API提供者
  final ApiProvider _apiProvider = ApiProvider();

  // 存储服务
  final StorageService _storageService = StorageService();

  // 标签服务
  final TagService _tagService = TagService();

  // 搜索历史列表
  List<String> _searchHistory = [];

  // 标签映射
  Map<String, Tag> _tagMap = {};

  // 搜索结果列表
  List<BookmarkItem> _searchResults = [];

  // 是否正在搜索
  bool _isSearching = false;

  // 是否显示搜索历史
  bool _showHistory = true;

  // 当前搜索关键词
  String _currentKeyword = '';

  // 当前页码
  int _currentPage = 1;

  // 每页数量
  final int _pageSize = 10;

  // 是否还有更多数据
  bool _hasMore = false;

  // 是否正在加载更多
  bool _isLoadingMore = false;

  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  // 视图模式：true为网格视图，false为列表视图
  bool _isGridView = false;

  @override
  void initState() {
    super.initState();
    _loadViewModePreference();
    _loadSearchHistory();
    _loadTagData();

    // 添加滚动监听器，用于实现上拉加载更多
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  /// 滚动监听器
  void _scrollListener() {
    // 如果滚动到底部且还有更多数据且不在加载中，加载更多
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
        _hasMore &&
        !_isLoadingMore) {
      _loadMoreResults();
    }
  }

  /// 加载用户偏好的视图模式
  Future<void> _loadViewModePreference() async {
    try {
      final isGridView = await _storageService.getGridViewMode();
      setState(() {
        _isGridView = isGridView;
      });
    } catch (e) {
      print('加载视图模式偏好失败: $e');
      // 失败时使用默认值（列表模式）
    }
  }

  /// 保存视图模式偏好
  Future<void> _saveViewModePreference(bool isGridView) async {
    try {
      await _storageService.saveGridViewMode(isGridView);
    } catch (e) {
      print('保存视图模式偏好失败: $e');
    }
  }

  /// 加载搜索历史
  Future<void> _loadSearchHistory() async {
    final history = await _searchHistoryService.getSearchHistory();
    setState(() {
      _searchHistory = history;
    });
  }

  /// 清除搜索历史
  Future<void> _clearSearchHistory() async {
    await _searchHistoryService.clearSearchHistory();
    setState(() {
      _searchHistory = [];
    });
  }

  /// 加载标签数据
  Future<void> _loadTagData() async {
    try {
      final tagObjects = await _tagService.getAllTagObjects();
      if (mounted) {
        setState(() {
          _tagMap = {for (var tag in tagObjects) tag.name: tag};
        });
      }
    } catch (e) {
      print('加载标签数据失败: $e');
    }
  }

  /// 执行搜索
  Future<void> _performSearch(String keyword) async {
    if (keyword.trim().isEmpty) {
      setState(() {
        _showHistory = true;
        _isSearching = false;
        _searchResults = [];
        _currentKeyword = '';
      });
      return;
    }

    // 保存搜索历史
    await _searchHistoryService.addSearchHistory(keyword);

    // 更新搜索历史列表
    await _loadSearchHistory();

    // 重置搜索状态
    setState(() {
      _isSearching = true;
      _showHistory = false;
      _currentKeyword = keyword;
      _currentPage = 1;
      _hasMore = false;
      _searchResults = [];
    });

    try {
      // 调用搜索API
      final result = await _apiProvider.bookmarkApi.searchBookmarks(
        keyword: keyword,
        page: _currentPage,
        pageSize: _pageSize,
      );

      // 解析结果
      final bookmarks = result['items'] as List<BookmarkItem>? ?? [];
      final total = result['total'] as int? ?? 0;

      setState(() {
        _searchResults = bookmarks;
        _isSearching = false;
        _hasMore = bookmarks.length < total;
      });

      // 调试：检查搜索结果中的标签信息
      if (bookmarks.isNotEmpty) {
        print('搜索结果数量: ${bookmarks.length}');
        for (int i = 0; i < bookmarks.length && i < 3; i++) {
          final bookmark = bookmarks[i];
          print('书签 $i: ${bookmark.title} - 标签: ${bookmark.tags}');
        }
      }
    } catch (e) {
      print('搜索失败: $e');
      setState(() {
        _isSearching = false;
        _searchResults = [];
        _hasMore = false;
      });

      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('搜索失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 加载更多搜索结果
  Future<void> _loadMoreResults() async {
    if (_isLoadingMore || !_hasMore || _currentKeyword.isEmpty) {
      return;
    }

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // 页码加ᄁ1
      final nextPage = _currentPage + 1;

      // 调用搜索API
      final result = await _apiProvider.bookmarkApi.searchBookmarks(
        keyword: _currentKeyword,
        page: nextPage,
        pageSize: _pageSize,
      );

      // 解析结果
      final bookmarks = result['items'] as List<BookmarkItem>? ?? [];
      final total = result['total'] as int? ?? 0;

      if (bookmarks.isNotEmpty) {
        setState(() {
          // 添加新的搜索结果
          _searchResults.addAll(bookmarks);
          // 更新页码
          _currentPage = nextPage;
          // 更新是否还有更多数据
          _hasMore = _searchResults.length < total;
          _isLoadingMore = false;
        });
      } else {
        setState(() {
          _hasMore = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      print('加载更多搜索结果失败: $e');
      setState(() {
        _isLoadingMore = false;
      });

      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加载更多失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(60.h),
        child: AppBar(
          backgroundColor: AppColors.background,
          elevation: 0,
          title: _buildSearchBar(),
          automaticallyImplyLeading: false,
        ),
      ),
      body: _buildBody(),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      height: 40.h,
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: () {
              // 使用无动画返回
              Navigator.of(context).pop();
            },
            child: Container(
              padding: EdgeInsets.all(8.r),
              child: Icon(
                Icons.arrow_back,
                color: AppColors.textPrimary,
                size: 24.r,
              ),
            ),
          ),
          SizedBox(width: 8.w),
          // 搜索输入框
          Expanded(
            child: Container(
              height: 40.h,
              decoration: BoxDecoration(
                color: AppColors.searchBarBackground,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 12.w),
                    child: Icon(
                      Icons.search,
                      size: 18.r,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      autofocus: true,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textPrimary,
                      ),
                      decoration: InputDecoration(
                        hintText: '搜索收藏内容',
                        hintStyle: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textHint,
                        ),
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                      onChanged: (value) {
                        // 如果清空了搜索框，显示历史记录
                        if (value.isEmpty) {
                          setState(() {
                            _showHistory = true;
                            _searchResults = [];
                          });
                        }
                      },
                      onSubmitted: (value) {
                        _performSearch(value);
                      },
                    ),
                  ),
                  // 清除按钮
                  if (_searchController.text.isNotEmpty)
                    GestureDetector(
                      onTap: () {
                        _searchController.clear();
                        setState(() {
                          _showHistory = true;
                          _searchResults = [];
                        });
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 12.w),
                        child: Icon(
                          Icons.close,
                          size: 18.r,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          SizedBox(width: 8.w),
          // 视图模式切换按钮（仅在有搜索结果时显示）
          if (!_showHistory && _searchResults.isNotEmpty) ...[
            Container(
              height: 32.h,
              decoration: BoxDecoration(
                color: AppColors.searchBarBackground,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 列表模式按钮
                  InkWell(
                    onTap: () {
                      setState(() {
                        _isGridView = false;
                      });
                      _saveViewModePreference(false);
                    },
                    borderRadius: BorderRadius.circular(4.r),
                    child: Container(
                      width: 32.h,
                      height: 32.h,
                      decoration: BoxDecoration(
                        color: !_isGridView ? AppColors.primary : Colors.transparent,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Icon(
                        Icons.view_list,
                        size: 16.r,
                        color: !_isGridView ? Colors.white : AppColors.textSecondary,
                      ),
                    ),
                  ),
                  // 大图模式按钮
                  InkWell(
                    onTap: () {
                      setState(() {
                        _isGridView = true;
                      });
                      _saveViewModePreference(true);
                    },
                    borderRadius: BorderRadius.circular(4.r),
                    child: Container(
                      width: 32.h,
                      height: 32.h,
                      decoration: BoxDecoration(
                        color: _isGridView ? AppColors.primary : Colors.transparent,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Icon(
                        Icons.grid_view,
                        size: 16.r,
                        color: _isGridView ? Colors.white : AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w),
          ],
          // 搜索按钮
          GestureDetector(
            onTap: () {
              _performSearch(_searchController.text);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                '搜索',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    // 正在搜索
    if (_isSearching) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    // 显示搜索历史
    if (_showHistory) {
      return Padding(
        padding: EdgeInsets.all(16.r),
        child: SearchHistory(
          historyList: _searchHistory,
          onHistoryItemTap: (keyword) {
            _searchController.text = keyword;
            _performSearch(keyword);
          },
          onClearHistory: _clearSearchHistory,
        ),
      );
    }

    // 显示搜索结果
    return _buildSearchResults();
  }

  /// 构建搜索结果列表
  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48.r,
              color: AppColors.textHint,
            ),
            SizedBox(height: 16.h),
            Text(
              '没有找到相关内容',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    // 根据视图模式显示不同的组件
    if (_isGridView) {
      // 大图网格模式，添加左右padding
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: BookmarkGridView(
          bookmarks: _searchResults,
          scrollController: _scrollController,
          hasMore: _hasMore,
          isLoading: _isLoadingMore,
          onLoadMore: _loadMoreResults,
          isHomePage: true, // 使用首页样式（无padding）
        ),
      );
    } else {
      // 列表模式
      return SlidableAutoCloseBehavior(
        child: ListView.builder(
          controller: _scrollController,
          padding: EdgeInsets.all(16.r),
          // 如果还有更多数据，项目数量+1，用于显示加载更多指示器
          itemCount: _searchResults.length + (_hasMore ? 1 : 0),
          itemBuilder: (context, index) {
          // 如果是最后一项且还有更多数据，显示加载更多指示器
          if (index == _searchResults.length) {
            return Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16.h),
                child: SizedBox(
                  width: 24.r,
                  height: 24.r,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    strokeWidth: 2.w,
                  ),
                ),
              ),
            );
          }

          // 使用BookmarkListItem组件
          final bookmark = _searchResults[index];

          // 调试：检查单个书签的标签信息
          if (index == 0) {
            print('第一个搜索结果书签标签: ${bookmark.tags}');
            print('当前tagMap大小: ${_tagMap.length}');
          }

          return Padding(
            padding: EdgeInsets.only(bottom: 12.h),
            child: BookmarkListItem(
              bookmark: bookmark,
              tagMap: _tagMap,
              enableSlide: true,
              onTap: () => _handleBookmarkTap(bookmark),
              onRefresh: () => _performSearch(_currentKeyword),
            ),
          );
        },
        ),
      );
    }
  }

  /// 处理书签点击
  void _handleBookmarkTap(BookmarkItem bookmark) {
    // 如果有原生跳转链接，使用原生跳转
    if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty) {
      // 检查是否是http/https开头的图片链接
      final schemeUrl = bookmark.schemeUrl!;
      final isImageUrl = schemeUrl.startsWith('http') &&
          (schemeUrl.endsWith('.jpg') ||
           schemeUrl.endsWith('.jpeg') ||
           schemeUrl.endsWith('.png') ||
           schemeUrl.endsWith('.gif') ||
           schemeUrl.endsWith('.webp') ||
           schemeUrl.contains('.jpg?') ||
           schemeUrl.contains('.jpeg?') ||
           schemeUrl.contains('.png?') ||
           schemeUrl.contains('.gif?') ||
           schemeUrl.contains('.webp?'));

      if (isImageUrl) {
        // 如果是图片链接，跳转到图片预览页面
        Navigator.pushNamed(
          context,
          AppRoutes.imagePreview,
          arguments: {'imageUrl': schemeUrl},
        );
      } else {
        // 如果不是图片链接，使用原生方法打开scheme URL
        final nativeBridge = NativeBridge();

        // 先检查悬浮窗权限
        nativeBridge.checkOverlayPermission().then((hasPermission) {
          if (!hasPermission) {
            // 如果没有悬浮窗权限，提示用户并引导开启
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('需要悬浮窗权限才能显示返回按钮'),
                action: SnackBarAction(
                  label: '去开启',
                  onPressed: () {
                    nativeBridge.openOverlayPermissionSettings();
                  },
                ),
                duration: const Duration(seconds: 5),
              ),
            );
          }

          // 无论是否有权限，都尝试打开链接
          nativeBridge.openSchemeUrl(schemeUrl, showReturnButton: true).then((success) {
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('无法打开链接，可能没有安装对应的应用'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          });
        });
      }
    } else {
      // 如果没有原生跳转链接，显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('此内容没有可打开的链接'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
}
