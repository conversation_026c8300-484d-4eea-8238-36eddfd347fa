import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../models/version_info.dart';
import '../services/version_service.dart';
import '../services/storage_service.dart';
import '../widgets/custom_toast.dart';

/// 版本更新页面
class VersionUpdatePage extends StatefulWidget {
  const VersionUpdatePage({Key? key}) : super(key: key);

  @override
  State<VersionUpdatePage> createState() => _VersionUpdatePageState();
}

class _VersionUpdatePageState extends State<VersionUpdatePage> {
  final VersionService _versionService = VersionService();
  final StorageService _storageService = StorageService();
  VersionInfo? _versionInfo;
  bool _isLoading = true;

  // 更新状态管理
  bool _isUpdating = false;
  String _updateStatus = '';
  bool _hasLocalUpdate = false;

  @override
  void initState() {
    super.initState();
    _loadVersionInfo();
  }

  /// 加载版本信息
  Future<void> _loadVersionInfo() async {
    try {
      // 同时加载版本信息和检查本地更新包
      final futures = await Future.wait([
        _versionService.getVersionInfo(),
        _checkLocalUpdate(),
      ]);

      final versionInfo = futures[0] as VersionInfo;
      final hasLocal = futures[1] as bool;

      if (mounted) {
        setState(() {
          _versionInfo = versionInfo;
          _hasLocalUpdate = hasLocal;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('加载版本信息失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 检查本地是否有待安装的更新包
  Future<bool> _checkLocalUpdate() async {
    try {
      return await _storageService.hasPendingUpdate();
    } catch (e) {
      print('检查本地更新包失败: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('版本更新'),
        backgroundColor: Colors.white,
        elevation: 0.5,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppColors.textPrimary,
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
        ),
        iconTheme: IconThemeData(
          color: AppColors.textPrimary,
        ),
      ),
      body: SafeArea(
        child: _isLoading ? _buildLoadingWidget() : _buildContent(),
      ),
    );
  }

  /// 构建加载中组件
  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          SizedBox(height: 16.h),
          Text(
            '正在检查版本信息...',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建主要内容
  Widget _buildContent() {
    if (_versionInfo == null) {
      return _buildErrorWidget();
    }

    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 应用图标和基本信息（无卡片样式）
            _buildAppInfoCard(),
            SizedBox(height: 24.h),

            // 版本信息卡片（合并版本状态和更新内容）
            _buildVersionInfoCard(),
            SizedBox(height: 24.h),

            // 更新按钮（如果有更新或有本地更新包）
            if (_versionInfo!.hasUpdate || _hasLocalUpdate) ...[
              _buildUpdateButton(),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建错误提示组件
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.r,
            color: AppColors.textHint,
          ),
          SizedBox(height: 16.h),
          Text(
            '获取版本信息失败',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadVersionInfo();
            },
            child: Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建应用信息（无卡片样式）
  Widget _buildAppInfoCard() {
    return Column(
      children: [
        // 应用图标
        Center(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(15.r),
            child: Image.asset(
              'assets/app_icon.png',
              width: 60.r,
              height: 60.r,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // 如果图标加载失败，显示默认图标
                return Container(
                  width: 60.r,
                  height: 60.r,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15.r),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primary,
                        AppColors.primaryLight,
                      ],
                    ),
                  ),
                  child: Icon(
                    Icons.bookmark_rounded,
                    size: 30.r,
                    color: Colors.white,
                  ),
                );
              },
            ),
          ),
        ),
        SizedBox(height: 12.h),

        // 当前版本
        Text(
          '当前版本 ${_versionInfo?.currentVersion ?? '未知'}',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 构建版本信息卡片（合并版本状态和更新内容）
  Widget _buildVersionInfoCard() {
    final hasUpdate = _versionInfo?.hasUpdate ?? false;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 版本状态信息
          Row(
            children: [
              Container(
                width: 40.r,
                height: 40.r,
                decoration: BoxDecoration(
                  color: hasUpdate ? AppColors.warning.withOpacity(0.1) : AppColors.success.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(
                  hasUpdate ? Icons.update : Icons.check_circle,
                  color: hasUpdate ? AppColors.warning : AppColors.success,
                  size: 24.r,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      hasUpdate ? '发现新版本' : '已是最新版本',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      hasUpdate
                        ? '最新版本 ${_versionInfo?.latestVersion ?? '未知'}'
                        : '您当前使用的是最新版本',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // 发布时间（如果有更新）
          if (hasUpdate && (_versionInfo?.updateTime?.isNotEmpty ?? false)) ...[
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: AppColors.primaryLight.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                '发布时间：${_versionInfo!.updateTime}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.primary,
                ),
              ),
            ),
          ],

          // 更新内容（如果有更新）
          if (hasUpdate && (_versionInfo?.updateContent?.isNotEmpty ?? false)) ...[
            SizedBox(height: 20.h),
            Row(
              children: [
                Icon(
                  Icons.article_outlined,
                  color: AppColors.primary,
                  size: 20.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  '更新内容',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                _versionInfo?.updateContent ?? '',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  height: 1.6,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建更新按钮
  Widget _buildUpdateButton() {
    // 确定按钮文本和状态
    String buttonText;
    bool isEnabled = !_isUpdating;

    if (_isUpdating) {
      buttonText = _updateStatus.isNotEmpty ? _updateStatus : '处理中...';
    } else if (_hasLocalUpdate) {
      buttonText = '安装更新';
    } else {
      buttonText = '立即更新';
    }

    return Container(
      width: double.infinity,
      height: 50.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: isEnabled ? [
            AppColors.primary,
            AppColors.primaryLight,
          ] : [
            Colors.grey[400]!,
            Colors.grey[300]!,
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: isEnabled ? [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ] : [],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12.r),
          onTap: isEnabled ? _handleUpdate : null,
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isUpdating) ...[
                  SizedBox(
                    width: 16.w,
                    height: 16.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 8.w),
                ],
                Text(
                  buttonText,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 处理更新按钮点击
  Future<void> _handleUpdate() async {
    if (_isUpdating) return;

    setState(() {
      _isUpdating = true;
      _updateStatus = '';
    });

    try {
      // 如果有本地更新包，直接安装
      if (_hasLocalUpdate) {
        await _installLocalUpdate();
      } else {
        // 否则开始下载新版本
        await _downloadAndInstallUpdate();
      }
    } catch (e) {
      print('更新失败: $e');
      CustomToast.show('更新失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
          _updateStatus = '';
        });
      }
    }
  }

  /// 安装本地更新包
  Future<void> _installLocalUpdate() async {
    setState(() {
      _updateStatus = '准备安装...';
    });

    try {
      final result = await _versionService.installPendingUpdate();

      if (result['success'] == true) {
        CustomToast.show('正在安装更新...');
        // 安装成功，更新本地状态
        setState(() {
          _hasLocalUpdate = false;
        });
      } else {
        if (result['needPermission'] == true) {
          // 需要权限，引导用户到设置页面
          await _handleInstallPermission();
        } else {
          // 其他安装失败情况
          String errorMessage = result['message'] ?? '安装失败';
          CustomToast.show(errorMessage);
        }
      }
    } catch (e) {
      print('安装本地更新包失败: $e');
      CustomToast.show('安装失败: $e');
    }
  }

  /// 下载并安装更新
  Future<void> _downloadAndInstallUpdate() async {
    setState(() {
      _updateStatus = '开始下载...';
    });

    try {
      // 使用VersionService的静默下载功能
      await _versionService.startSilentDownload(
        onSuccess: (filePath) async {
          print('下载完成: $filePath');
          setState(() {
            _updateStatus = '下载完成，准备安装...';
          });

          // 下载完成后直接尝试安装
          await _installDownloadedUpdate(filePath);
        },
        onError: (error) {
          print('下载失败: $error');
          CustomToast.show('下载失败: $error');
        },
      );
    } catch (e) {
      print('启动下载失败: $e');
      CustomToast.show('启动下载失败: $e');
    }
  }

  /// 安装已下载的更新包
  Future<void> _installDownloadedUpdate(String filePath) async {
    try {
      setState(() {
        _updateStatus = '正在安装...';
      });

      final result = await _versionService.installDownloadedApp(filePath);

      if (result['success'] == true) {
        CustomToast.show('正在安装更新...');
      } else {
        if (result['needPermission'] == true) {
          // 需要权限，引导用户到设置页面
          await _handleInstallPermission();
        } else {
          // 其他安装失败情况
          String errorMessage = result['message'] ?? '安装失败';
          CustomToast.show(errorMessage);
        }
      }
    } catch (e) {
      print('安装下载的更新包失败: $e');
      CustomToast.show('安装失败: $e');
    }
  }

  /// 处理安装权限问题
  Future<void> _handleInstallPermission() async {
    try {
      // 直接跳转到设置页面，不显示弹窗
      await _versionService.requestInstallPermission();
      CustomToast.show('请在设置中允许安装未知来源应用');
    } catch (e) {
      print('处理安装权限失败: $e');
      CustomToast.show('无法打开权限设置');
    }
  }
}