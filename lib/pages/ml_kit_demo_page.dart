import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../native_bridge/ml_kit_bridge.dart';

/// ML Kit 演示页面
class MLKitDemoPage extends StatefulWidget {
  const MLKitDemoPage({Key? key}) : super(key: key);

  @override
  State<MLKitDemoPage> createState() => _MLKitDemoPageState();
}

class _MLKitDemoPageState extends State<MLKitDemoPage> {
  final MLKitBridge _mlKitBridge = MLKitBridge.instance;
  final ImagePicker _imagePicker = ImagePicker();
  
  File? _selectedImage;
  bool _isProcessing = false;
  String _resultText = '';
  List<Map<String, dynamic>> _resultList = [];
  String _currentMode = 'text'; // 'text', 'label', 'face', 'barcode'

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ML Kit 演示'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 模式选择
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildModeButton('文字识别', 'text'),
                _buildModeButton('图像标签', 'label'),
                _buildModeButton('人脸检测', 'face'),
                _buildModeButton('条码扫描', 'barcode'),
              ],
            ),
            const SizedBox(height: 16),
            
            // 图片选择按钮
            ElevatedButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.photo),
              label: const Text('选择图片'),
            ),
            const SizedBox(height: 16),
            
            // 显示选择的图片
            if (_selectedImage != null)
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                ),
                child: Image.file(
                  _selectedImage!,
                  fit: BoxFit.contain,
                ),
              ),
            const SizedBox(height: 16),
            
            // 处理按钮
            ElevatedButton(
              onPressed: _selectedImage == null || _isProcessing
                  ? null
                  : _processImage,
              child: _isProcessing
                  ? const CircularProgressIndicator()
                  : Text('开始${_getModeText(_currentMode)}'),
            ),
            const SizedBox(height: 16),
            
            // 显示结果
            if (_resultText.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(_resultText),
              ),
              
            // 显示列表结果
            if (_resultList.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _buildResultWidgets(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 构建模式按钮
  Widget _buildModeButton(String text, String mode) {
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _currentMode = mode;
          _resultText = '';
          _resultList = [];
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: _currentMode == mode
            ? Theme.of(context).primaryColor
            : Colors.grey,
      ),
      child: Text(text),
    );
  }

  // 获取当前模式的文本描述
  String _getModeText(String mode) {
    switch (mode) {
      case 'text':
        return '文字识别';
      case 'label':
        return '图像标签识别';
      case 'face':
        return '人脸检测';
      case 'barcode':
        return '条形码扫描';
      default:
        return '处理';
    }
  }

  // 选择图片
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
      );
      
      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _resultText = '';
          _resultList = [];
        });
      }
    } catch (e) {
      _showError('选择图片失败: $e');
    }
  }

  // 处理图片
  Future<void> _processImage() async {
    if (_selectedImage == null) return;
    
    setState(() {
      _isProcessing = true;
      _resultText = '';
      _resultList = [];
    });
    
    try {
      switch (_currentMode) {
        case 'text':
          await _recognizeText();
          break;
        case 'label':
          await _labelImage();
          break;
        case 'face':
          await _detectFaces();
          break;
        case 'barcode':
          await _scanBarcodes();
          break;
      }
    } catch (e) {
      _showError('处理失败: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  // 文字识别
  Future<void> _recognizeText() async {
    final result = await _mlKitBridge.recognizeText(_selectedImage!);
    setState(() {
      _resultText = '识别到的文字:\n${result['text']}';
    });
  }

  // 图像标签识别
  Future<void> _labelImage() async {
    final labels = await _mlKitBridge.labelImage(_selectedImage!);
    setState(() {
      _resultList = labels;
    });
  }

  // 人脸检测
  Future<void> _detectFaces() async {
    final faces = await _mlKitBridge.detectFaces(_selectedImage!);
    setState(() {
      _resultList = faces;
    });
  }

  // 条形码扫描
  Future<void> _scanBarcodes() async {
    final barcodes = await _mlKitBridge.scanBarcodes(_selectedImage!);
    setState(() {
      _resultList = barcodes;
    });
  }

  // 构建结果显示组件
  List<Widget> _buildResultWidgets() {
    if (_resultList.isEmpty) return [];
    
    switch (_currentMode) {
      case 'label':
        return _resultList.map((label) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              '${label['text']} (${(label['confidence'] * 100).toStringAsFixed(1)}%)',
              style: const TextStyle(fontSize: 16),
            ),
          );
        }).toList();
        
      case 'face':
        return _resultList.map((face) {
          final boundingBox = face['boundingBox'] as Map<String, dynamic>;
          final smileProbability = face['smilingProbability'] as double;
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('人脸位置: (${boundingBox['left']}, ${boundingBox['top']}, '
                    '${boundingBox['right']}, ${boundingBox['bottom']})'),
                if (smileProbability >= 0)
                  Text('微笑概率: ${(smileProbability * 100).toStringAsFixed(1)}%'),
              ],
            ),
          );
        }).toList();
        
      case 'barcode':
        return _resultList.map((barcode) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('格式: ${barcode['format']}'),
                Text('类型: ${barcode['valueType']}'),
                Text('内容: ${barcode['rawValue']}'),
              ],
            ),
          );
        }).toList();
        
      default:
        return [const Text('无结果')];
    }
  }

  // 显示错误信息
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
