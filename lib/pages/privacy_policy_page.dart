import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('隐私政策'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '存点隐私政策',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '版本更新日期：2024年7月1日',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey,
              ),
            ),
            Text(
              '版本生效日期：2024年7月1日',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              '上海讯河文化传播有限公司是存点APP的运营者（以下称"存点"或"我们"），存点非常重视用户的隐私和个人信息保护。您在使用我们的产品与/或服务时，我们可能会收集和使用您的相关信息。我们希望通过《存点隐私政策》（"本隐私政策"）向您说明我们在您使用我们的产品与/或服务时如何收集、使用、保存、共享和转让这些信息，以及我们为您提供的访问、更新、删除和保护这些信息的方式。同时，我们承诺将按业界成熟的安全标准，采取相应的安全保护措施来保护您的个人信息。',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 8.h),
            Text(
              '如果您对我们处理您的个人数据有任何疑问或顾虑，请通过联系邮箱***********************或本政策列明的其他联系方式与我们联系。',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 16.h),
            _buildSection(
              '（一）我们如何收集和使用您的个人信息',
              '为了向您提供，并使您获得更好的产品和服务，当您使用我们的产品和服务时，我们会以下列方式收集您的个人信息。如果您拒绝提供相关信息，可能无法注册成为我们的用户、享受我们提供的某些服务，或者即便我们可以继续向您提供一些服务，也无法达到该服务拟达到的效果。我们只会收集您选择的服务所必需的、特定的、明确及合法目的的信息，并且不会以与这些目的不相符的方式进一步处理相关信息。根据您选择的服务，我们可能收集以下信息中的一种或多种：',
            ),
            _buildSubSection(
              '1、您在使用我们服务时主动提供或上传的信息',
              '（1）若您创建存点帐号，您将会自主提供您的手机号码设置个人注册信息；您也可以使用第三方帐号（微信、QQ、苹果）登录存点，此时您将会提供第三方账号相关昵称、头像信息；您也可以使用手机号进行注册，我们将通过发送短信验证码的方式验证您的身份是否有效。\n\n'
              '（2）若您使用存点的收藏功能，您将会自主提供您的收藏内容信息。\n\n'
              '（3）若您使用存点的图片上传功能，可能会请求摄像头权限用于拍摄图片、请求存储权限、文件管理权限、照片和视频权限用于上传本地相册的图片作为收藏图片。\n\n'
              '（4）若您使用存点的付费服务，若您进行交易时，支付功能由与我们合作的第三方支付机构向您提供。第三方支付机构可能需要收集您的姓名、银行卡类型及卡号、有效期及手机号码。银行卡号、有效期及手机号码是个人敏感信息，这些信息是支付功能所必需的信息，拒绝提供将导致您无法使用该功能，但不影响其他功能的正常使用。在您成功付款后，我们仅会从该第三方付款服务提供商处获取您已完成付款的结果验证信息，进而向您提供相应虚拟产品。\n\n'
              '（5）为便于您接收消息推送，我们将会获取您的通知权限，若您拒绝的，您将不能使用通知功能，但不影响使用我们的其他服务。\n\n'
              '（6）若您通过我们的客服或参加我们举办的活动时，您可能会提供姓名、电话、通讯地址用于活动联络。\n\n'
              '（7）为更好的向您提供服务，我们可能不时增加或调整部分特殊服务功能，若您选择使用某项特殊服务功能的，您可能会自主向我们提供使用该服务功能所需的个人信息。',
            ),
            _buildSubSection(
              '2、我们在您使用服务时获取的信息',
              '（1）用于生成用户唯一标识：设备厂商、设备型号、设备名称、硬件序列号、操作系统和应用程序版本及类型、IP地址、设备Mac地址、系统版本、独立设备标识；\n\n'
              '（2）用于收藏数据同步：网络接入方式及类型、状态、网络质量数据；\n\n'
              '（3）用于广告投放自主监测、内部统计、数据分析和研究：iOS系统广告标识符IDFA、安卓系统广告标识符OAID、Android Id、设备硬件地址（MAC））、时间戳、应用标识符、应用程序版本、应用分发渠道、语言所在地、时区和网络状态（WiFi等）；\n\n'
              '（4）用于内部统计、数据分析、业务运营及客户支持（第三方SDK收集）：设备Mac地址、唯一设备识别码（IMEI/android ID/IDFA/OPENUDID/GUID、SIM 卡 IMSI 信息）、地理位置信息，根据统计维度的不同采集如下信息：SDK/API/JS代码版本、浏览器、互联网服务提供商、平台、时间戳、应用标识符、应用程序版本、应用分发渠道、独立设备标识符、设备型号、传感器参数、终端制造厂商、终端设备操作系统版本、分辨率、会话启动/停止时间、语言所在地、时区和网络状态（WiFi等）、硬盘、CPU和电池使用情况；',
            ),
            _buildSubSection(
              '3、第三方提供的与您相关的信息',
              '我们可能通过合法途径从商业伙伴处取得的用户个人信息，其他存点用户在使用我们的产品过程分享的信息中也可能包含您的信息。例如，我们可能从微信/QQ/支付宝/苹果等第三方获取您授权共享的账户信息（头像、昵称、地区），并在您同意本隐私政策后将您的第三方账户与您的存点账户绑定，使您可以通过第三方账户直接登录并使用我们的产品与/或服务。我们会将依据与第三方的约定、对个人信息来源的合法性进行确认后，在符合相关法律和法规规定的前提下，使用您的这些个人信息。',
            ),
            _buildSubSection(
              '4、您应当知悉，以下情形中，我们收集、使用个人信息无需征得您的授权同意',
              '（1）与国家安全、国防安全有关的；\n\n'
              '（2）与公共安全、公共卫生、重大公共利益有关的；\n\n'
              '（3）与犯罪侦查、起诉、审判和判决执行等有关的；\n\n'
              '（4）出于维护个人信息主体或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；\n\n'
              '（5）所收集的个人信息是个人信息主体自行向社会公众公开的；\n\n'
              '（6）从合法公开披露的信息中收集的您的个人信息的，如合法的新闻报道、政府信息公开等渠道；\n\n'
              '（7）根据您的要求签订合同所必需的；\n\n'
              '（8）用于维护所提供的产品与/或服务的安全稳定运行所必需的，例如发现、处置产品与/或服务的故障；\n\n'
              '（9）为合法的新闻报道所必需的；\n\n'
              '（10）学术研究机构基于公共利益开展统计或学术研究所必要，且对外提供学术研究或描述的结果时，对结果中所包含的个人信息进行去标识化处理的；\n\n'
              '（11）法律法规规定的其他情形。',
            ),
            _buildSection(
              '（二） 我们使用您个人信息的规则',
              '1、基于为您提供收藏服务的目的使用：我们会在您注册存点账号、自主选择存点收藏服务等服务功能或其他本隐私政策的约定情形下，为实现我们的产品与/或服务功能对所收集的个人信息进行使用。在收集您的个人信息后，我们将通过技术手段对数据进行去标识化处理，去标识化处理的信息将无法识别主体。请您了解并同意，在此情况下我们有权使用已经去标识化的信息；并在不透露您个人信息的前提下，我们可能会对用户数据库进行分析并予以商业化的利用。\n\n'
              '2、基于客户调查或就订单与您联系的目的使用：我们可能会收集您在参与产品/服务调查时主动向我们提供的信息，以及您与我们的关联方、合作伙伴之间互动时提供的相关信息，以便于您追踪订单情况、发起用户投诉以及我们优化客户服务的质量与流程。\n\n'
              '3、基于网络安全保障、APP功能的目的使用：为提高您使用我们提供的服务的安全性，更准确地预防钓鱼网站欺诈和木马病毒，我们可能会使用或整合您的个人信息以及我们的关联方、合作伙伴取得您授权或者依法共享的信息，根据您的使用习惯等来综合判断您的账号及交易风险，包括验证身份，预防、发现、调查可能存在的欺诈、网络病毒、网络攻击等安全风险以及违反我们或关联方协议、政策或规则等行为，以保护您、其他用户、我们或关联方的合法权益，并记录一些我们认为有风险的链接("URL")。\n\n'
              '4、基于统计分析和改进运营的目的使用：我们可能将业务中收集的个人信息用于统计分析和改进运营，例如通过您所在的位置、偏好等进行统计分析，从而改进我们的产品、服务或营销计划；又如为改进我们系统而进行的技术改造、网络维护、故障排除、内部政策与流程制定、生成内部报告等。并可能会与第三方共享部分统计信息（不包含您的任何身份识别信息）以展示我们的产品与/或服务的整体使用趋势。当我们展示您的个人信息时，我们会采用包括内容替换、匿名处理方式对您的信息进行脱敏以保护您的信息安全。\n\n'
              '5、基于获得集团公司、合作伙伴和第三方服务提供商的服务的目的使用：我们与向存点提供服务的第三方共享个人数据，例如，分析、网站管理、广告、数据扩充机构、信息技术和相关基础设施提供、客户服务、审计和其他类似服务。存点与第三方服务提供商共享个人数据时，我们要求他们仅将您的个人数据用于向我们提供服务，并遵循与隐私保护政策一致个人信息保护要求。\n\n'
              '6、其他用途：当我们要将您的个人信息用于本政策未载明的其它用途时，或基于特定目的收集而来的信息用于其他目的时，会通过您主动做出勾选的形式事先征求您的同意。针对某些特定服务的特定隐私政策将更具体地说明我们在该等服务中如何使用您的信息。 请您注意，您在使用我们的产品与/或服务时所提供的所有个人信息，除非您删除或通过系统设置拒绝我们收集，否则将在您使用我们的产品与/或服务期间持续授权我们使用。在您可通过本政策第（三）条列明的路径注销存点账户及或取消隐私信息授权，以删除您的个人信息。',
            ),
            _buildSection(
              '（三） 您如何访问和控制您的信息',
              '1、我们将尽一切可能采取适当的技术手段，保证您可以访问、更新和更正您的注册信息或使用我们的服务时提供的其他个人信息。在访问、更新、更正和删除您的个人信息时，我们可能会要求您进行身份验证，以保障您的账户安全。\n\n'
              '2、访问信息\n\n'
              '（1）若您查看您的昵称，您可以通过"我的—点击头像—个人信息"进行操作。\n'
              '（2）若您查看您的头像，您可以通过"我的—点击头像-个人信息"进行操作。\n'
              '（3）若您查看您的会员状态的，您可以通过您可以通过"我的—会员信息栏"进行操作。\n\n'
              '3、更改信息\n\n'
              '（1）若您更改您的昵称、您可以通过"我的—点击头像—个人信息"进行操作。\n'
              '（2）若您更改您的头像，您可以通过"我的—点击头像-个人信息"进行操作。\n'
              '（3）若您需要开通/关闭会员状态的，您可以通过您可以通过"我的—会员信息栏"进行操作。\n\n'
              '4、删除信息\n\n'
              '若您需要删除个人信息的，您可以通过手机设置-应用管理-存点-清除存点应用数据信息，也可以通过"我的—点击头像—个人信息—注销账户"清除您的信息。\n\n'
              '5、注销账户\n\n'
              '您可以通过"我的—点击头像—注销账号"进行操作，当您注销帐号后，我们将按照《中华人民共和国网络安全法》等法律法规的规定留存你的相关信息；超出法定保存期限后，我们将删除或匿名化处理你的个人信息。\n\n'
              '6、投诉反馈\n\n'
              '若您有任何意见或建议的，您可以通过"我的—用户反馈"进行操作。\n\n'
              '7、如您不希望收到存点页面上的消息通知或信息推送，可以在首次安装存点时拒绝接收消息通知，或者在移动端操作系统中的"通知"中心关掉对应的通知功能。',
            ),
            _buildSection(
              '（四） 我们会如何共享、转让、公开披露您的个人信息',
              '1、共享：我们遵照法律法规的规定，对信息的分享进行严格的限制。原则上我们不会与存点以外的任何公司、组织和个人共享您的个人信息，但以下情况除外：\n\n'
              '(1)事先获得您明确的同意或授权；\n\n'
              '(2)实现本《隐私政策》第（一）条"我们如何收集和使用信息"部分所述目的；\n\n'
              '(3)履行我们在本《隐私政策》或我们与您达成的其他协议中的义务和行使我们的权利；\n\n'
              '(4)维护和改善我们的服务。我们可能向我们的服务提供商等合作伙伴（例如：代表我们发出电子邮件或推送通知的通讯服务提供商等）分享您的信息，以帮助我们为您提供更有针对性、更完善的服务；\n\n'
              '(5)根据适用的法律法规、法律程序的要求、强制性的行政或司法要求所必须的情况下进行提供；\n\n'
              '(6)在法律法规允许的范围内，为维护存点、存点的关联方或合作伙伴、您或其他存点用户或社会公众利益、财产或安全免遭损害而有必要提供；\n\n'
              '(7)向委托我们进行推广的合作伙伴等第三方共享，目的是为了使该等委托方了解推广的覆盖面和有效性。比如我们可以告知该委托方有多少人看了他们的推广信息或在看到这些信息后购买了委托方的商品，或者向他们提供不能识别个人身份的统计信息，帮助他们了解其受众或顾客。\n\n'
              '(8)只有共享您的信息，才能实现我们的产品与/或服务的核心功能或提供您需要的服务；\n\n'
              '(9)应您需求为您处理您与他人的纠纷或争议；\n\n'
              '(10)应您的监护人合法要求而提供您的信息；\n\n'
              '(11)根据与您签署的单项服务协议（包括在线签署的电子协议以及相应的平台规则）或其他的法律文件约定所提供；基于学术研究而使用；\n\n'
              '(12)基于符合法律法规的社会公共利益而使用；\n\n'
              '(13)仅为实现外部处理目的，经加密、匿名化处理等手段处理后，向第三方合作伙伴分享。\n\n'
              '为了您的信息安全，我们已与第三方SDK/API服务商签署严格数据安全保密协议，这些公司会严格遵守我们的数据隐私和安全要求。除非得到您的授权，我们不会与其共享您的个人身份信息。为向用户提供更多服务、保证服务稳定性和质量或升级相关功能时，我们可能会调整我们接入的第三方SDK，届时我们会及时在本页面向您说明收集用户信息的第三方SDK的最新情况。\n\n'
              '2、信息使用规则\n\n'
              '（1）我们可能会将您的个人信息与我们的关联方共享。但我们只会共享必要的个人信息，且受本隐私政策中所声明目的的约束。我们的关联方如要改变个人信息的处理目的，将再次征求您的授权同意。\n\n'
              '（2）我们仅会出于合法、正当、必要、特定、明确的目的共享您的信息。对我们与之共享信息的公司、组织和个人，我们要求他们按照我们的说明、本《隐私政策》以及其他任何相关的保密和安全措施来处理信息。\n\n'
              '（3）请您知悉，即使已经取得您的授权同意，我们也仅会出于合法、正当、必要、特定、明确的目的共享您的个人信息，并尽量对共享内容中的个人信息进行去标识化处理。\n\n'
              '3、转让：我们不会将您的个人信息出售给任何公司、组织和个人，除非（a）获得您的明确同意后，我们会向其他方转让您的个人信息；(b)在涉及合并、收购或破产清算时，如涉及到个人信息转让，我们会要求新的持有您个人信息的公司、组织继续受此隐私政策的约束，否则我们将要求该公司、组织重新向您征求授权同意。\n\n'
              '4、公开披露：我们仅会在以下情况下，且采取符合法律和业界标准的安全防护措施的前提下，才会公开披露您的个人信息:（a）获得您的明确同意；（b）基于法律法规、法律程序、诉讼或政府主管部门强制性要求。（c）遵守相关政府机关或其他法定授权组织的要求；（d）我们有理由确信需要遵守法律法规等有关规定；（e）为执行相关服务协议或本政策；（f）我们认为为遵守适用的法律法规、维护社会公共利益、或保护我们或我们的集团公司、我们的客户、其他用户或雇员的人身和财产安全或合法权益所合理必需的。',
            ),
            _buildSection(
              '（五）我们如何储存、保护、管理您的信息',
              '1、信息存储地点：我们在中华人民共和国境内收集和产生的个人信息将存储在中华人民共和国境内。如果日后为处理跨境业务，需要向境外机构传输境内收集的相关个人信息的，我们会事先征得您的同意，按照法律、行政法规和相关监管部门的规定执行，并通过签订协议、核查等有效措施，要求境外机构为所获得的个人信息保密。\n\n'
              '2、信息存储期限：我们将会按照《网络安全法》、《电子商务法》规定的期限仅会在达到本政策所述目的所必需的时限内保存您的个人信息，最低期限不少于6个月，但为了遵守适用的法律法规、法院判决或裁定、其他有权机关的要求、维护公共利益等目的，我们可能会将个人信息保存时间予以适当延长。若超出法定保存期限后，我们会将您的个人信息删除或做匿名化处理。\n\n'
              '3、信息保护管理\n\n'
              '（1）我们采用适当的技术和组织措施保护收集和处理的个人数据。我们采用的措施旨在提供与处理个人数据的风险相适应的安全级别。我们采取互联网业内标准做法来保护您的个人信息，防止信息遭到未经授权访问、披露、使用、修改、损坏或丢失。为此，我们成立了个人信息及隐私保护小组（个人信息保护相关负责人联系方式详见本政策第（十二）条"如何联系我们"），我们会采取一切合理可行的措施，保护您的个人信息并确保未收集与存点提供的服务无关的个人信息。\n\n'
              '（2）我们会尽全力保护您的个人信息，但请您理解，由于技术的限制以及可能存在的各种恶意手段，在互联网行业，不可能始终保证信息百分之百的安全。您需要了解，您接入我们的服务所用的系统和通讯网络，有可能因我们可控范围外的因素而出现问题。\n\n'
              '（3）在发生个人信息安全事件的场合，我们将按照法律法规的要求，及时向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低风险的建议、对您的补救措施等。我们将及时将事件相关情况以邮件、信函、电话、推送通知等方式告知您，难以逐一告知个人信息主体时，我们会采取合理、有效的方式发布公告。同时，我们还将按照监管部门要求，主动上报个人信息安全事件的处置情况。\n\n'
              '（4）当我们的产品或服务发生停止运营的情形时，我们将采取例如，推送通知、公告等形式通知您，并在合理的期限内删除或匿名化处理您的个人信息。',
            ),
            _buildSection(
              '（六）我们会如何使用COOKIES及同类技术',
              '您使用我们的服务时，我们或我们的第三方合作伙伴，可能通过放置安全的Cookie及同类技术（如web beacon等）收集您的信息。我们会严格要求第三方合作伙伴遵守本政策的相关规定。Cookie 对提升用户的网络使用体验十分重要，我们使用Cookie一般出于以下目的：\n\n'
              '1、身份验证：Cookie可在您接入我们的服务时通知我们，以使我们可验证您的身份信息，以确保您的账号安全。例如，Cookie技术可在您登入存点服务时通知我们，因此，我们能够在您访问所有存点服务时，识别是否您本人安全登录，向您显示与您相关的信息。\n\n'
              '2、偏好设置：Cookie可帮助我们按照您所希望的服务样式、外观设置为您提供服务。 例如， Cookie技术可记录您是否已经阅读过一些提示性的展现，防止重复提示对您造成骚扰；一些阅读展现(如字体大小等)方面您本人偏好的设定，我们也会存放于Cookie中，在您访问时自动为您调整到您上次设定的值。\n\n'
              '3、保障安全：Cookie可帮助我们保障数据和服务的安全性，排查针对我们的产品和服务的作弊、黑客、欺诈行为。例如，Cookie可以存放票据信息，能够由服务器验证是否是您自主在本站点的正常登录，通过票据中的加密信息，阻止多种类型的攻击，防止跨站信息窃取访问，防止身份冒充访问。\n\n'
              '4、功能与服务：Cookie可帮助我们为用户提供更好的产品和服务。例如，Cookie可以在您进行登录时，通过存储的信息，帮您填入您最后一次登入的账户名称，提高您的操作效率。\n\n'
              '5、提高效率：Cookie可以避免不必要的服务器负载，提高服务效率，节省资源、能源。例如，Cookie可帮助我们优化在服务器之间路由流量，并且了解不同用户加载我们服务时的速度，有时，我们或会利用Cookie让您正在使用时加载及响应得更快。\n\n'
              '6、分析与研究：我们使用这些Cookie来了解、改善我们的产品和服务，为新产品或功能的研发提供参考数据。例如，我们会使用Cookie来了解您使用我们服务的方式，以求改善用户体验。例如您在接入我们服务时，在各个步骤所耗费的时间，我们会收集此信息用以分析研究能够进一步优化和改善的方面。\n\n'
              '7、如何禁用：如您不希望个人信息保存在 Cookie 中，您可对浏览器进行配置，选择禁用 Cookie 功能。禁用 Cookie 功能后，可能影响您访问存点或不能充分取得存点提供的服务。存点不会将 Cookie 用于本政策所述目的之外的任何用途。您可根据自己的偏好管理或删除Cookie。您可以清除计算机上保存的所有 Cookie，大部分网络浏览器都设有阻止 Cookie 的功能。\n\n'
              '8、设备权限调用:存点在提供服务的过程中，可能需要您根据您所选择的特定服务开通一些设备权限，您可以根据情况选择是否开通权限，但拒绝开通权限有可能影响您所选择的服务的有效实现。您可以在设备的设置功能中随时选择关闭部分或者全部权限，从而拒绝存点收集相应的个人信息。在不同设备中，权限显示方式及关闭方式可能有所不同，具体请参考设备及系统开发方说明或指引。',
            ),
            _buildSection(
              '（七）我们向您发送的邮件和信息',
              '1、 邮件和信息推送\n\n'
              '您使用我们服务时，我们可能使用您的信息以电子邮件、新闻或推送通知或其他方式向您提供或推广我们或第三方的商品和服务。如您不希望收到这些信息，您可以按照我们向您发出的电子邮件所述指示，在设备上选择取消订阅。\n\n'
              '2、与服务有关的公告\n\n'
              '我们可能在必需时（例如当我们由于系统维护而暂停某一项服务时）向您发出与服务有关的通知、公告或邮件。您可能无法取消这些与服务有关、性质不属于推广的通知、公告或邮件。',
            ),
            _buildSection(
              '（八）我们服务中的第三方服务',
              '为确保流畅的浏览体验，您可能会收到来自存点及我们的服务提供商等合作伙伴以外的第三方（以下简称「第三方」）提供的内容或网络链接。存点对此类第三方无控制权。您可选择是否访问第三方提供的链接、内容、产品和服务。存点无法控制第三方的隐私和个人信息保护政策，此类第三方不受到本政策的约束。您在向第三方提交个人信息之前，请确保您阅读并认可这些第三方的隐私保护政策。\n\n'
              '我们的应用中使用了以下第三方SDK：\n\n'
              '使用SDK名称：友盟SDK\n'
              '服务类型：统计分析、应用性能监控平台 U-APM\n'
              '收集个人信息类型：设备信息（IMEI/MAC/Android ID/IDFA/OpenUDID/GUID/IP地址/SIM 卡 IMSI 信息等）\n'
              '隐私权政策链接：https://www.umeng.com/page/policy',
            ),
            _buildSection(
              '（九）未成年人的保护',
              '1、存点非常重视对未成年人个人信息的保护。我们鼓励父母或监护人指导未满十八岁的未成年人使用我们的服务。我们建议未成年人鼓励他们的父母或监护人阅读本政策，并建议未成年人在提交个人信息之前寻求父母或监护人的同意和指导。\n\n'
              '2、根据相关法律法规的规定，若您是18周岁以下的未成年人，在使用本服务前，应事先取得您的家长或法定监护人的同意。若您是未成年人的监护人，当您对您所监护的未成年人的个人信息有相关疑问时，请通过本政策第十二条中的联系方式与我们联系。',
            ),
            _buildSection(
              '（十）本政策的适用范围',
              '我们所有的服务均适用本政策。某些服务有其特定的隐私指引/声明，该特定隐私指引/声明更具体地说明我们在该服务中如何处理您的信息。如本政策与特定服务的隐私指引/声明有不一致之处，请以该特定隐私指引声明为准。\n\n'
              '请您注意，本政策不适用由其他公司或个人提供的服务。例如，您通过使用存点服务接触到的第三方提供的服务。您使用该等第三方服务须受其隐私政策（而非本政策）约束，您需要仔细阅读其政策内容。',
            ),
            _buildSection(
              '（十一）本政策的修改',
              '我们可能适时修订本政策内容。如该等变更属于可能会导致您在本政策项下权利受到影响的重大变更，我们将在变更生效前，通过在页面显著位置提示、向您的账号推送信息或向您发送电子邮件等合适的能触达您的方式通知您。我们会在本页面上发布对本隐私政策所做的任何变更。\n\n'
              '若您不同意该等变更可以停止使用存点平台产品和服务，若您继续使用我们的产品和/或服务，即表示您同意受修订后的本隐私政策的约束，更新后的隐私政策将于我们发布或通知（如有）您之日起的（以较后者为准）的【15】天后生效。',
            ),
            _buildSection(
              '（十二）如何联系我们',
              '当您有任何疑问、意见或建议时，您可以通过以下方式与我们联系：\n\n'
              '邮箱：<EMAIL>\n\n'
              '收件人：上海讯河文化传播有限公司\n\n'
              '我们将尽快审核所涉问题，并在验证您的用户身份后的七天内予以回复。',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          content,
          style: TextStyle(fontSize: 16.sp),
        ),
        SizedBox(height: 16.h),
      ],
    );
  }

  Widget _buildSubSection(String title, String content) {
    return Padding(
      padding: EdgeInsets.only(left: 16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            content,
            style: TextStyle(fontSize: 16.sp),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }
}