import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';

/// 聊天头像组件
class ChatAvatar extends StatelessWidget {
  final bool isUser;
  final String? userAvatarUrl;

  const ChatAvatar({
    super.key,
    required this.isUser,
    this.userAvatarUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 32.w,
      height: 32.w,
      decoration: BoxDecoration(
        color: isUser ? AppColors.primary : AppColors.success,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: isUser && userAvatarUrl != null && userAvatarUrl!.isNotEmpty
            ? Image.network(
                userAvatarUrl!,
                width: 32.w,
                height: 32.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // 如果网络头像加载失败，显示默认图标
                  return Icon(
                    Icons.person_outline,
                    color: Colors.white,
                    size: 18.r,
                  );
                },
              )
            : Icon(
                isUser ? Icons.person_outline : Icons.smart_toy_outlined,
                color: Colors.white,
                size: 18.r,
              ),
      ),
    );
  }
}
