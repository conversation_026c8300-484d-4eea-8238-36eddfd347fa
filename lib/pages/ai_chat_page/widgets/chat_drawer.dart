import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';

/// AI聊天页面抽屉组件
class ChatDrawer extends StatelessWidget {
  const ChatDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.surface,
      child: <PERSON><PERSON><PERSON>(
        child: Container(
          // 完全空白的抽屉
        ),
      ),
    );
  }
}
