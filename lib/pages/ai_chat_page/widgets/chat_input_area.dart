import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';

/// 聊天输入区域组件
class ChatInputArea extends StatelessWidget {
  final TextEditingController messageController;
  final VoidCallback onSendMessage;
  final VoidCallback onShowCollectionSearch;
  final VoidCallback onShowPromptTemplates;

  const ChatInputArea({
    super.key,
    required this.messageController,
    required this.onSendMessage,
    required this.onShowCollectionSearch,
    required this.onShowPromptTemplates,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Row(
          children: [
            // 输入框
            Expanded(
              child: TextField(
                controller: messageController,
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => onSendMessage(),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textPrimary,
                ),
                decoration: InputDecoration(
                  hintText: '输入消息...',
                  hintStyle: TextStyle(
                    color: AppColors.textHint,
                    fontSize: 14.sp,
                  ),
                  filled: true,
                  fillColor: AppColors.searchBarBackground,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24.r),
                    borderSide: BorderSide(
                      color: AppColors.divider,
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24.r),
                    borderSide: BorderSide(
                      color: AppColors.divider,
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24.r),
                    borderSide: BorderSide(
                      color: AppColors.primary,
                      width: 1,
                    ),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 12.h,
                  ),
                ),
              ),
            ),

            SizedBox(width: 12.w),

            // 引用收藏内容按钮
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: AppColors.searchBarBackground,
                borderRadius: BorderRadius.circular(24.r),
                border: Border.all(
                  color: AppColors.divider,
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onShowCollectionSearch,
                  borderRadius: BorderRadius.circular(24.r),
                  child: Icon(
                    Icons.bookmark_outline,
                    color: AppColors.textSecondary,
                    size: 20.r,
                  ),
                ),
              ),
            ),

            SizedBox(width: 12.w),

            // 提示词按钮
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: AppColors.searchBarBackground,
                borderRadius: BorderRadius.circular(24.r),
                border: Border.all(
                  color: AppColors.divider,
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onShowPromptTemplates,
                  borderRadius: BorderRadius.circular(24.r),
                  child: Icon(
                    Icons.lightbulb_outline,
                    color: AppColors.textSecondary,
                    size: 20.r,
                  ),
                ),
              ),
            ),

            SizedBox(width: 12.w),

            // 发送按钮（移到最右边）
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(24.r),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onSendMessage,
                  borderRadius: BorderRadius.circular(24.r),
                  child: Icon(
                    Icons.send_rounded,
                    color: Colors.white,
                    size: 20.r,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
