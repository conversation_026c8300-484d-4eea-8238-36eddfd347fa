import 'package:flutter/material.dart';

/// 自定义画笔，用于绘制聊天气泡的小箭头
class MessageArrowPainter extends CustomPainter {
  final bool isUser;
  final Color color;

  MessageArrowPainter({
    required this.isUser,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();

    if (isUser) {
      // 用户消息的箭头（指向右侧）- 简单三角形
      path.moveTo(0, 0);
      path.lineTo(size.width, size.height * 0.5);
      path.lineTo(0, size.height);
      path.close();
    } else {
      // AI消息的箭头（指向左侧）- 简单三角形
      path.moveTo(size.width, 0);
      path.lineTo(0, size.height * 0.5);
      path.lineTo(size.width, size.height);
      path.close();
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is MessageArrowPainter &&
        (oldDelegate.isUser != isUser || oldDelegate.color != color);
  }
}
