import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../../../constants/app_colors.dart';
import '../models/chat_message.dart';
import 'collection_content.dart';

/// 聊天消息气泡组件
class ChatMessageBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatMessageBubble({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 16.w),
      child: Column(
        crossAxisAlignment: message.isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // AI思考过程（仅对AI消息显示）
          if (!message.isUser && message.reasoningContent != null && message.reasoningContent!.isNotEmpty)
            _buildReasoningContent(message.reasoningContent!),

          // 主要消息内容（只有当消息文本不为空时才显示）
          if (message.text.trim().isNotEmpty)
            _buildMessageContainer(message),
        ],
      ),
    );
  }

  /// 获取消息背景颜色
  Color _getMessageBackgroundColor(ChatMessage message) {
    // 错误消息使用红色背景
    if (message.type == ChatMessageType.error) {
      return Colors.red.shade50;
    }

    // 收藏内容始终使用白色背景，不管是用户还是AI发送的
    if ((message.type ?? ChatMessageType.text) == ChatMessageType.collection) {
      return AppColors.surface;
    }

    // 普通文本消息保持原有逻辑
    return message.isUser ? AppColors.primary : AppColors.surface;
  }

  /// 获取消息阴影效果
  List<BoxShadow> _getMessageShadow(ChatMessage message) {
    // 收藏内容使用轻微的阴影
    if ((message.type ?? ChatMessageType.text) == ChatMessageType.collection) {
      return [
        BoxShadow(
          color: AppColors.cardShadow.withOpacity(0.08),
          blurRadius: 6,
          offset: const Offset(0, 2),
          spreadRadius: 0,
        ),
      ];
    }

    // 普通消息使用原有阴影
    return [
      BoxShadow(
        color: AppColors.cardShadow,
        blurRadius: 4,
        offset: const Offset(0, 2),
      ),
    ];
  }

  /// 构建AI思考过程内容
  Widget _buildReasoningContent(String reasoningContent) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.psychology_outlined,
                size: 14.r,
                color: AppColors.primary.withOpacity(0.7),
              ),
              SizedBox(width: 4.w),
              Text(
                'AI思考过程',
                style: TextStyle(
                  fontSize: 11.sp,
                  color: AppColors.primary.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: 6.h),
          Text(
            reasoningContent,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
              height: 1.3,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建消息容器
  Widget _buildMessageContainer(ChatMessage message) {
    // 用户消息：自适应宽度，最大宽度限制，紧贴右边
    if (message.isUser) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: 280.w, // 限制最大宽度
            ),
            padding: (message.type ?? ChatMessageType.text) == ChatMessageType.collection
                ? EdgeInsets.all(12.w)
                : EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: _getMessageBackgroundColor(message),
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: _getMessageShadow(message),
            ),
            child: (message.type ?? ChatMessageType.text) == ChatMessageType.collection
                ? CollectionContent(message: message)
                : _buildMessageContent(message),
          ),
        ],
      );
    }

    // AI消息：全屏宽度，更好地展示Markdown内容
    return Container(
      width: double.infinity,
      padding: (message.type ?? ChatMessageType.text) == ChatMessageType.collection
          ? EdgeInsets.all(12.w)
          : EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: _getMessageBackgroundColor(message),
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: _getMessageShadow(message),
      ),
      child: (message.type ?? ChatMessageType.text) == ChatMessageType.collection
          ? CollectionContent(message: message)
          : _buildMessageContent(message),
    );
  }

  /// 构建消息内容
  Widget _buildMessageContent(ChatMessage message) {
    // 错误消息使用特殊样式
    if (message.type == ChatMessageType.error) {
      return Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 16.r,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              message.text,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.red.shade700,
                height: 1.4,
              ),
            ),
          ),
        ],
      );
    }

    // 用户消息使用普通Text组件
    if (message.isUser) {
      return Text(
        message.text,
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.white,
          height: 1.4,
        ),
      );
    }

    // AI消息使用Markdown组件
    return MarkdownBody(
      data: message.text,
      shrinkWrap: true,
      styleSheet: MarkdownStyleSheet(
        // 基础文本样式
        p: TextStyle(
          fontSize: 14.sp,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        // 代码块样式
        code: TextStyle(
          fontSize: 13.sp,
          color: AppColors.primary,
          fontFamily: 'monospace',
          backgroundColor: AppColors.primary.withOpacity(0.1),
        ),
        codeblockDecoration: BoxDecoration(
          color: AppColors.searchBarBackground,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: AppColors.divider.withOpacity(0.3),
            width: 1,
          ),
        ),
        codeblockPadding: EdgeInsets.all(12.r),
        // 链接样式
        a: TextStyle(
          fontSize: 14.sp,
          color: AppColors.primary,
          decoration: TextDecoration.underline,
        ),
        // 标题样式
        h1: TextStyle(
          fontSize: 18.sp,
          color: AppColors.textPrimary,
          fontWeight: FontWeight.bold,
        ),
        h2: TextStyle(
          fontSize: 16.sp,
          color: AppColors.textPrimary,
          fontWeight: FontWeight.bold,
        ),
        h3: TextStyle(
          fontSize: 15.sp,
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        // 引用块样式
        blockquote: TextStyle(
          fontSize: 14.sp,
          color: AppColors.textSecondary,
          fontStyle: FontStyle.italic,
        ),
        blockquoteDecoration: BoxDecoration(
          border: Border(
            left: BorderSide(
              color: AppColors.primary,
              width: 3.w,
            ),
          ),
        ),
        blockquotePadding: EdgeInsets.only(left: 12.w),
        // 列表样式
        listBullet: TextStyle(
          fontSize: 14.sp,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
      ),
    );
  }

}
