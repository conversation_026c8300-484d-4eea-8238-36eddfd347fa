import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/platform_utils.dart';
import '../../../api/bookmark_api.dart';
import '../models/chat_message.dart';

/// 收藏内容展示组件
class CollectionContent extends StatelessWidget {
  final ChatMessage message;

  const CollectionContent({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    final bookmark = message.bookmarkItem;
    if (bookmark == null) {
      return Text(
        message.text,
        style: TextStyle(
          fontSize: 14.sp,
          color: message.isUser ? Colors.white : AppColors.textPrimary,
          height: 1.4,
        ),
      );
    }

    return SizedBox(
      width: 240.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 博主信息行
          Row(
            children: [
              // 博主头像或平台图标
              Container(
                width: 18.r,
                height: 18.r,
                decoration: BoxDecoration(
                  color: AppColors.searchBarBackground,
                  borderRadius: BorderRadius.circular(9.r),
                ),
                child: bookmark.influencerAvatar != null && bookmark.influencerAvatar!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(9.r),
                        child: Image.network(
                          bookmark.influencerAvatar!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _getPlatformIcon(bookmark.schemeUrl);
                          },
                        ),
                      )
                    : _getPlatformIcon(bookmark.schemeUrl),
              ),
              SizedBox(width: 6.w),
              // 博主名称
              Expanded(
                child: Text(
                  bookmark.influencerName,
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // 平台logo
              if (bookmark.schemeUrl != null && bookmark.schemeUrl!.isNotEmpty)
                Builder(
                  builder: (context) {
                    final logoAsset = PlatformUtils.getPlatformLogoFromSchemeUrl(bookmark.schemeUrl);
                    if (logoAsset != null) {
                      return Container(
                        padding: EdgeInsets.all(2.r),
                        decoration: BoxDecoration(
                          color: AppColors.searchBarBackground,
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                        child: Image.asset(
                          logoAsset,
                          width: 12.r,
                          height: 12.r,
                          errorBuilder: (context, error, stackTrace) {
                            return SizedBox.shrink();
                          },
                        ),
                      );
                    }
                    return SizedBox.shrink();
                  },
                ),
            ],
          ),

          SizedBox(height: 8.h),

          // 内容区域
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 左侧内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    if (bookmark.title != null)
                      Text(
                        bookmark.title!,
                        style: TextStyle(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                    if (bookmark.title != null && bookmark.desc != null)
                      SizedBox(height: 4.h),

                    // 描述
                    if (bookmark.desc != null)
                      Text(
                        bookmark.desc!,
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: AppColors.textSecondary,
                          height: 1.4,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),

              SizedBox(width: 10.w),

              // 右侧封面图
              if (bookmark.cover != null && bookmark.cover!.isNotEmpty)
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: SizedBox(
                    width: 64.w,
                    height: 64.w,
                    child: Image.network(
                      bookmark.cover!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppColors.searchBarBackground,
                          child: Icon(
                            Icons.image_not_supported_outlined,
                            color: AppColors.textHint,
                            size: 24.r,
                          ),
                        );
                      },
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取平台图标（适配聊天气泡）
  Widget _getPlatformIcon(String? schemeUrl) {
    if (schemeUrl == null || schemeUrl.isEmpty) {
      return Icon(
        Icons.person_outline,
        size: 12.r,
        color: AppColors.textSecondary,
      );
    }

    final logoAsset = PlatformUtils.getPlatformLogoFromSchemeUrl(schemeUrl);
    if (logoAsset != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(9.r),
        child: Image.asset(
          logoAsset,
          width: 12.r,
          height: 12.r,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Icon(
              Icons.person_outline,
              size: 12.r,
              color: AppColors.textSecondary,
            );
          },
        ),
      );
    }

    return Icon(
      Icons.person_outline,
      size: 12.r,
      color: AppColors.textSecondary,
    );
  }
}
