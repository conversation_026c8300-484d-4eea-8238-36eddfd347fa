import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../constants/app_colors.dart';
import '../../services/storage_service.dart';
import '../../api/api_provider.dart';
import '../../models/prompt_template.dart';
import '../../services/prompt_template_service.dart';
import '../../widgets/custom_toast.dart';
import '../../components/collection_search_dialog.dart';
import '../../components/prompt_template_edit_dialog.dart';
import 'models/chat_message.dart';
import 'widgets/chat_message_bubble.dart';
import 'widgets/chat_input_area.dart';
import 'widgets/typing_indicator.dart';
import 'widgets/time_label.dart';
import 'widgets/chat_drawer.dart';

class AiChatPage extends StatefulWidget {
  const AiChatPage({super.key});

  @override
  State<AiChatPage> createState() => _AiChatPageState();
}

class _AiChatPageState extends State<AiChatPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  final StorageService _storageService = StorageService();
  final ApiProvider _apiProvider = ApiProvider();
  final PromptTemplateService _promptTemplateService = PromptTemplateService();

  bool _isTyping = false;
  String? _userAvatarUrl;
  String? _userNickname;
  String _currentAiResponse = ''; // 当前AI回复的累积内容
  String _currentReasoningContent = ''; // 当前AI思考过程的累积内容
  bool _userScrolledUp = false; // 标记用户是否向上滚动
  List<PromptTemplate> _promptTemplates = []; // 提示词模板列表

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
    _setupScrollListener();
    _loadPromptTemplates();
    // 添加欢迎消息
    _messages.add(ChatMessage(
      text: '你好！我是AI助手，有什么可以帮助您的吗？',
      isUser: false,
      timestamp: DateTime.now(),
    ));
  }

  /// 设置滚动监听器
  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.hasClients) {
        // 检查用户是否向上滚动（距离底部超过100像素）
        final isAtBottom = _scrollController.position.maxScrollExtent -
            _scrollController.position.pixels < 100;

        if (_userScrolledUp != !isAtBottom) {
          setState(() {
            _userScrolledUp = !isAtBottom;
          });
        }
      }
    });
  }

  /// 加载用户信息
  Future<void> _loadUserInfo() async {
    try {
      final avatarUrl = await _storageService.getAvatarUrl();
      final nickname = await _storageService.getNickname();

      if (mounted) {
        setState(() {
          _userAvatarUrl = avatarUrl;
          _userNickname = nickname;
        });
      }
    } catch (e) {
      print('加载用户信息失败: $e');
    }
  }

  /// 加载提示词模板
  Future<void> _loadPromptTemplates() async {
    try {
      final templates = await _promptTemplateService.getAllTemplates();
      if (mounted) {
        setState(() {
          _promptTemplates = templates;
        });
      }
    } catch (e) {
      print('加载提示词模板失败: $e');
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    // 添加用户消息
    setState(() {
      _messages.add(ChatMessage(
        text: text,
        isUser: true,
        timestamp: DateTime.now(),
      ));
      _isTyping = true;
      _currentAiResponse = '';
    });

    _messageController.clear();
    // 发送新消息时重置滚动状态并滚动到底部
    _userScrolledUp = false;
    _scrollToBottom();

    try {
      // 调用真实的AI聊天接口
      await _sendMessageToAi(text);
    } catch (e) {
      // 发生错误时显示错误消息（标记为error类型，不参与AI对话上下文）
      if (mounted) {
        setState(() {
          _messages.add(ChatMessage(
            text: '抱歉，AI服务暂时不可用，请稍后再试。',
            isUser: false,
            timestamp: DateTime.now(),
            type: ChatMessageType.error, // 标记为错误消息
          ));
          _isTyping = false;
        });
        _scrollToBottomIfNeeded();
      }
    }
  }

  /// 发送消息到AI并处理流式响应
  Future<void> _sendMessageToAi(String message) async {
    try {
      // 构建对话历史（排除系统欢迎消息、错误消息、收藏内容和当前刚发送的用户消息）
      final conversationHistory = <Map<String, String>>[];
      for (int i = 1; i < _messages.length - 1; i++) { // 跳过系统欢迎消息和最后一条用户消息
        final msg = _messages[i];
        // 只包含普通文本消息，排除收藏内容和错误消息
        if (msg.type == null || msg.type == ChatMessageType.text) {
          conversationHistory.add({
            'role': msg.isUser ? 'user' : 'assistant',
            'content': msg.text,
          });
        }
      }

      // 重置当前AI回复内容
      _currentAiResponse = '';
      _currentReasoningContent = '';
      int? aiMessageIndex;

      // 监听SSE流
      await for (final chunk in _apiProvider.aiChatApi.sendMessage(
        message: message,
        conversationHistory: conversationHistory,
      )) {
        if (mounted) {
          // 处理内容和思考过程
          final content = chunk['content'];
          final reasoningContent = chunk['reasoning_content'];

          if (content != null && content.isNotEmpty) {
            _currentAiResponse += content;
          }

          if (reasoningContent != null && reasoningContent.isNotEmpty) {
            _currentReasoningContent += reasoningContent;
          }

          // 如果是第一个有效chunk，添加AI消息并停止typing指示器
          if (aiMessageIndex == null && (_currentAiResponse.isNotEmpty || _currentReasoningContent.isNotEmpty)) {
            setState(() {
              _isTyping = false; // 停止"正在思考"指示器
              _messages.add(ChatMessage(
                text: _currentAiResponse,
                isUser: false,
                timestamp: DateTime.now(),
                reasoningContent: _currentReasoningContent.isNotEmpty ? _currentReasoningContent : null,
              ));
            });
            aiMessageIndex = _messages.length - 1;
          } else if (aiMessageIndex != null) {
            // 更新现有AI消息内容
            setState(() {
              _messages[aiMessageIndex!] = ChatMessage(
                text: _currentAiResponse,
                isUser: false,
                timestamp: _messages[aiMessageIndex!].timestamp,
                reasoningContent: _currentReasoningContent.isNotEmpty ? _currentReasoningContent : null,
              );
            });
          }

          // 只在用户没有向上滚动时才自动滚动到底部
          _scrollToBottomIfNeeded();
        }
      }

      // 确保typing指示器已停止
      if (mounted) {
        setState(() {
          _isTyping = false;
        });
      }

    } catch (e) {
      debugPrint('AI聊天错误: $e');
      rethrow;
    }
  }



  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 智能滚动到底部（只在用户没有向上滚动时才滚动）
  void _scrollToBottomIfNeeded() {
    if (!_userScrolledUp) {
      _scrollToBottom();
    }
  }

  /// 显示收藏搜索弹窗
  void _showCollectionSearchDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CollectionSearchDialog(
        onCollectionSelected: (bookmarks) {
          // 添加收藏内容到聊天（支持多个）
          setState(() {
            for (final bookmark in bookmarks) {
              _messages.add(ChatMessage(
                text: bookmark.title ?? '收藏内容',
                isUser: true,
                timestamp: DateTime.now(),
                type: ChatMessageType.collection,
                bookmarkItem: bookmark,
              ));
            }
          });
          // 添加收藏内容时重置滚动状态并滚动到底部
          _userScrolledUp = false;
          _scrollToBottom();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// 显示提示词模板弹窗
  void _showPromptTemplatesDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: Column(
          children: [
            // 顶部拖拽指示器
            Container(
              margin: EdgeInsets.only(top: 6.h),
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: AppColors.divider,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: Row(
                children: [
                  Text(
                    '提示词',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Spacer(),
                  // 添加新模板按钮
                  IconButton(
                    onPressed: _addNewPromptTemplate,
                    icon: Icon(
                      Icons.add,
                      color: AppColors.primary,
                      size: 24.r,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.textSecondary,
                      size: 24.r,
                    ),
                  ),
                ],
              ),
            ),

            // 提示词列表
            Expanded(
              child: SlidableAutoCloseBehavior(
                child: ListView.builder(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  itemCount: _promptTemplates.length,
                  itemBuilder: (context, index) {
                    final template = _promptTemplates[index];
                    return _buildPromptTemplateItem(template, index);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建提示词模板项（带左滑功能）
  Widget _buildPromptTemplateItem(PromptTemplate template, int index) {
    return Slidable(
      key: Key(template.id),
      endActionPane: ActionPane(
        motion: const DrawerMotion(),
        extentRatio: 0.8, // 增加滑动区域宽度以容纳四个按钮
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(bottom: 12.h),
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(4.r),
                  bottomRight: Radius.circular(4.r),
                ),
              ),
              child: Row(
                children: [
                  // 添加到菜单按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _addToMenu(template),
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(0),
                            bottomRight: Radius.circular(0),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.add_to_home_screen_outlined,
                              color: Colors.white,
                              size: 18.r,
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              '添加到菜单',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 9.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // 版本按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showVersion(template),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Color(0xFFFF8C00), // 橘黄色
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(0),
                            bottomRight: Radius.circular(0),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.history_outlined,
                              color: Colors.white,
                              size: 18.r,
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              '版本',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 9.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // 编辑按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _editPromptTemplate(template),
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(0),
                            bottomRight: Radius.circular(0),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.edit_outlined,
                              color: Colors.white,
                              size: 18.r,
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              '编辑',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 9.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // 删除按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _deletePromptTemplate(template, index),
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.error,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(4.r),
                            bottomRight: Radius.circular(4.r),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.delete_outline,
                              color: Colors.white,
                              size: 18.r,
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              '删除',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 9.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      child: _buildPromptTemplateCard(template),
    );
  }

  /// 构建提示词模板卡片
  Widget _buildPromptTemplateCard(PromptTemplate template) {
    return Container(
      width: double.infinity, // 使用全宽度
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: AppColors.searchBarBackground,
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(
          color: AppColors.divider.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _messageController.text = template.content;
            Navigator.of(context).pop();
          },
          borderRadius: BorderRadius.circular(4.r),
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  template.title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  template.content,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  /// 添加新的提示词模板
  void _addNewPromptTemplate() {
    showDialog(
      context: context,
      builder: (context) => PromptTemplateEditDialog(
        onSuccess: () {
          _loadPromptTemplates(); // 刷新模板列表
        },
      ),
    );
  }

  /// 编辑提示词模板
  void _editPromptTemplate(PromptTemplate template) {
    showDialog(
      context: context,
      builder: (context) => PromptTemplateEditDialog(
        template: template,
        onSuccess: () {
          _loadPromptTemplates(); // 刷新模板列表
        },
      ),
    );
  }

  /// 添加到菜单
  void _addToMenu(PromptTemplate template) {
    CustomToast.showSuccess('已添加"${template.title}"到菜单');
  }

  /// 显示版本信息
  void _showVersion(PromptTemplate template) {
    CustomToast.showSuccess('查看"${template.title}"的版本信息');
  }

  /// 删除提示词模板
  void _deletePromptTemplate(PromptTemplate template, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        title: Text(
          '删除提示词模板',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        content: Text(
          '确定要删除"${template.title}"吗？此操作不可撤销。',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              '取消',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14.sp,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await _promptTemplateService.deleteTemplate(template.id);
              if (success) {
                CustomToast.showSuccess('删除成功');
                _loadPromptTemplates(); // 刷新模板列表
              } else {
                CustomToast.showError('删除失败');
              }
            },
            child: Text(
              '删除',
              style: TextStyle(
                color: AppColors.error,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 判断是否应该显示时间
  bool _shouldShowTime(int index) {
    if (index == 0) return true; // 第一条消息总是显示时间

    final currentMessage = _messages[index];
    final previousMessage = _messages[index - 1];

    // 如果两条消息间隔超过5分钟，则显示时间
    final timeDifference = currentMessage.timestamp.difference(previousMessage.timestamp);
    return timeDifference.inMinutes >= 5;
  }

  void _clearMessages() {
    setState(() {
      _messages.clear();
      _messages.add(ChatMessage(
        text: '你好！我是AI助手，有什么可以帮助您的吗？',
        isUser: false,
        timestamp: DateTime.now(),
      ));
    });
  }

  @override
  Widget build(BuildContext context) {
    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.background,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      drawer: const ChatDrawer(),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              Color(0xFFF0F4F9),
            ],
            stops: [0.0, 1.0],
          ),
        ),
        child: Column(
          children: [
            // 聊天消息列表
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                padding: EdgeInsets.symmetric(vertical: 8.h),
                itemCount: _messages.length + (_isTyping ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == _messages.length && _isTyping) {
                    return const TypingIndicator();
                  }

                  final message = _messages[index];
                  final shouldShowTime = _shouldShowTime(index);

                  return Column(
                    children: [
                      // 显示时间（如果需要）
                      if (shouldShowTime) TimeLabel(timestamp: message.timestamp),
                      // 显示消息
                      ChatMessageBubble(
                        message: message,
                      ),
                    ],
                  );
                },
              ),
            ),

            // 输入框区域
            ChatInputArea(
              messageController: _messageController,
              onSendMessage: _sendMessage,
              onShowCollectionSearch: _showCollectionSearchDialog,
              onShowPromptTemplates: _showPromptTemplatesDialog,
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.surface,
      foregroundColor: AppColors.textPrimary,
      elevation: 0,
      leading: Builder(
        builder: (BuildContext context) {
          return IconButton(
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
            icon: Icon(
              Icons.menu,
              color: AppColors.textPrimary,
              size: 24.r,
            ),
          );
        },
      ),
      title: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.smart_toy_outlined,
              color: AppColors.primary,
              size: 20.r,
            ),
          ),
          SizedBox(width: 12.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'AI助手',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                '在线',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
      centerTitle: false,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
      actions: [
        IconButton(
          onPressed: _clearMessages,
          icon: Icon(
            Icons.refresh_outlined,
            color: AppColors.textSecondary,
            size: 22.r,
          ),
        ),
      ],
    );
  }
}
