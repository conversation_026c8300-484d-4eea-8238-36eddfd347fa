import '../../../api/bookmark_api.dart';

/// 聊天消息类型枚举
enum ChatMessageType {
  text,      // 文字消息
  collection, // 收藏内容
  error      // 错误消息（不参与AI对话上下文）
}

/// 聊天消息数据模型
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? nickname; // 添加昵称字段
  final ChatMessageType? type; // 消息类型（可为null以兼容旧数据）
  final BookmarkItem? bookmarkItem; // 收藏内容（当type为collection时使用）
  final String? reasoningContent; // AI思考过程内容

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.nickname, // 可选的昵称参数
    this.type, // 可选的消息类型
    this.bookmarkItem, // 收藏内容
    this.reasoningContent, // AI思考过程
  });
}
