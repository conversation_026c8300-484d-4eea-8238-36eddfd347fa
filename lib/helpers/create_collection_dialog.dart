import 'package:flutter/material.dart';

/// 创建收藏夹对话框工具类
class CreateCollectionDialog {
  /// 显示创建收藏夹对话框
  /// 
  /// [onConfirm] 回调函数，当用户确认创建时调用，参数为收藏夹名称
  static Future<void> show(BuildContext context, {required Function(String) onConfirm}) async {
    // 将控制器移到StatefulBuilder内部来避免状态管理问题
    String collectionName = '';
    
    // 使用更稳定的对话框实现
    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        // 使用StatefulBuilder避免状态管理问题
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '创建收藏夹',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      autofocus: true,
                      decoration: const InputDecoration(
                        hintText: '请输入收藏夹名称',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        // 使用局部setState更新状态
                        setState(() {
                          collectionName = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text('取消'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: collectionName.isEmpty
                              ? null // 禁用按钮如果名称为空
                              : () {
                                  Navigator.of(context).pop();
                                  onConfirm(collectionName);
                                },
                          child: const Text('创建'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
