import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// 搜索历史服务
/// 
/// 用于管理用户的搜索历史记录
class SearchHistoryService {
  static final SearchHistoryService _instance = SearchHistoryService._internal();
  
  /// 单例模式
  factory SearchHistoryService() => _instance;
  
  /// 私有构造函数
  SearchHistoryService._internal();
  
  /// 存储键名
  static const String _searchHistoryKey = 'search_history';
  
  /// 最大历史记录数量
  static const int maxHistoryCount = 20;
  
  /// 获取搜索历史
  Future<List<String>> getSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_searchHistoryKey);
    
    if (historyJson == null || historyJson.isEmpty) {
      return [];
    }
    
    try {
      final List<dynamic> decoded = jsonDecode(historyJson);
      return decoded.map((item) => item.toString()).toList();
    } catch (e) {
      print('解析搜索历史失败: $e');
      return [];
    }
  }
  
  /// 添加搜索历史
  Future<bool> addSearchHistory(String keyword) async {
    if (keyword.trim().isEmpty) {
      return false;
    }
    
    final prefs = await SharedPreferences.getInstance();
    final history = await getSearchHistory();
    
    // 如果已存在相同关键词，先移除旧的
    history.removeWhere((item) => item == keyword);
    
    // 将新关键词添加到列表开头
    history.insert(0, keyword);
    
    // 如果超过最大数量，移除最旧的
    if (history.length > maxHistoryCount) {
      history.removeLast();
    }
    
    // 保存到 SharedPreferences
    return prefs.setString(_searchHistoryKey, jsonEncode(history));
  }
  
  /// 清除所有搜索历史
  Future<bool> clearSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.remove(_searchHistoryKey);
  }
}
