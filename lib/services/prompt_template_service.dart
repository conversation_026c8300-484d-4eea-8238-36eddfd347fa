import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/prompt_template.dart';

/// 提示词模板服务
/// 
/// 负责提示词模板的本地存储和管理
class PromptTemplateService {
  static const String _promptTemplatesKey = 'prompt_templates';
  
  /// 单例实例
  static final PromptTemplateService _instance = PromptTemplateService._internal();
  
  /// 获取单例实例
  factory PromptTemplateService() => _instance;
  
  /// 私有构造函数
  PromptTemplateService._internal();

  /// 获取默认提示词模板
  List<PromptTemplate> _getDefaultTemplates() {
    final now = DateTime.now();
    return [
      PromptTemplate(
        id: 'default_1',
        title: '总结内容',
        content: '请帮我总结一下这个内容的要点',
        createTime: now,
        updateTime: now,
      ),
      PromptTemplate(
        id: 'default_2',
        title: '分析优缺点',
        content: '请分析一下这个内容的优缺点',
        createTime: now,
        updateTime: now,
      ),
      PromptTemplate(
        id: 'default_3',
        title: '提出建议',
        content: '基于这个内容，请给我一些实用的建议',
        createTime: now,
        updateTime: now,
      ),
      PromptTemplate(
        id: 'default_4',
        title: '深入解释',
        content: '请详细解释一下这个内容中的关键概念',
        createTime: now,
        updateTime: now,
      ),
      PromptTemplate(
        id: 'default_5',
        title: '举例说明',
        content: '请用具体的例子来说明这个内容',
        createTime: now,
        updateTime: now,
      ),
      PromptTemplate(
        id: 'default_6',
        title: '对比分析',
        content: '请将这个内容与相关的其他内容进行对比分析',
        createTime: now,
        updateTime: now,
      ),
    ];
  }

  /// 获取所有提示词模板
  Future<List<PromptTemplate>> getAllTemplates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final templatesJson = prefs.getString(_promptTemplatesKey);
      
      if (templatesJson == null || templatesJson.isEmpty) {
        // 如果没有存储的模板，返回默认模板并保存
        final defaultTemplates = _getDefaultTemplates();
        await _saveTemplates(defaultTemplates);
        return defaultTemplates;
      }
      
      final List<dynamic> templatesList = jsonDecode(templatesJson);
      return templatesList.map((json) => PromptTemplate.fromJson(json)).toList();
    } catch (e) {
      print('获取提示词模板失败: $e');
      // 出错时返回默认模板
      return _getDefaultTemplates();
    }
  }

  /// 保存提示词模板列表
  Future<bool> _saveTemplates(List<PromptTemplate> templates) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final templatesJson = jsonEncode(templates.map((t) => t.toJson()).toList());
      return await prefs.setString(_promptTemplatesKey, templatesJson);
    } catch (e) {
      print('保存提示词模板失败: $e');
      return false;
    }
  }

  /// 添加新的提示词模板
  Future<bool> addTemplate(String title, String content) async {
    if (title.trim().isEmpty || content.trim().isEmpty) {
      return false;
    }

    try {
      final templates = await getAllTemplates();
      final now = DateTime.now();
      final newTemplate = PromptTemplate(
        id: 'custom_${now.millisecondsSinceEpoch}',
        title: title.trim(),
        content: content.trim(),
        createTime: now,
        updateTime: now,
      );
      
      templates.add(newTemplate);
      return await _saveTemplates(templates);
    } catch (e) {
      print('添加提示词模板失败: $e');
      return false;
    }
  }

  /// 更新提示词模板
  Future<bool> updateTemplate(String id, String title, String content) async {
    if (title.trim().isEmpty || content.trim().isEmpty) {
      return false;
    }

    try {
      final templates = await getAllTemplates();
      final index = templates.indexWhere((t) => t.id == id);
      
      if (index == -1) {
        return false;
      }
      
      templates[index] = templates[index].copyWith(
        title: title.trim(),
        content: content.trim(),
        updateTime: DateTime.now(),
      );
      
      return await _saveTemplates(templates);
    } catch (e) {
      print('更新提示词模板失败: $e');
      return false;
    }
  }

  /// 删除提示词模板
  Future<bool> deleteTemplate(String id) async {
    try {
      final templates = await getAllTemplates();
      final originalLength = templates.length;
      
      templates.removeWhere((t) => t.id == id);
      
      // 如果没有删除任何模板，返回false
      if (templates.length == originalLength) {
        return false;
      }
      
      return await _saveTemplates(templates);
    } catch (e) {
      print('删除提示词模板失败: $e');
      return false;
    }
  }

  /// 清除所有提示词模板（重置为默认）
  Future<bool> resetToDefault() async {
    try {
      final defaultTemplates = _getDefaultTemplates();
      return await _saveTemplates(defaultTemplates);
    } catch (e) {
      print('重置提示词模板失败: $e');
      return false;
    }
  }
}
