import 'dart:io';
import '../models/version_info.dart';
import '../native_bridge/version_bridge.dart';
import '../utils/version_utils.dart';
import '../api/api_provider.dart';
import 'app_download_service.dart';
import 'storage_service.dart';

/// 版本更新服务
class VersionService {
  static final VersionService _instance = VersionService._internal();
  factory VersionService() => _instance;
  VersionService._internal();

  final _apiProvider = ApiProvider();
  final _downloadService = AppDownloadService();
  final _storageService = StorageService();

  /// APP启动时检查版本更新
  ///
  /// 调用服务端的 /app/version/check 接口并打印返回结果
  Future<void> checkVersionOnAppStart() async {
    try {
      print('=== APP启动版本检查开始 ===');

      // 首先检查是否有待安装的更新包
      await checkPendingUpdateOnAppStart();

      // 从原生端获取当前应用版本信息
      final currentVersion = await VersionBridge.getVersionName();
      print('当前应用版本: $currentVersion');

      // 调用服务端版本检查接口
      print('正在调用服务端版本检查接口...');
      final serverVersionInfo = await _apiProvider.versionApi.checkVersion();

      // 检查是否有版本更新
      final hasUpdate = VersionUtils.hasNewVersion(currentVersion, serverVersionInfo.latestVersion);

      // 如果有更新，启动静默下载
      if (hasUpdate) {
        print('检测到版本更新，开始静默下载');
        await _startSilentDownload(serverVersionInfo);
      } else {
        print('当前已是最新版本，检查并清理本地安装包');
        // 当前版本与服务器版本相同，检查并删除本地可能存在的当前版本安装包
        await _cleanupCurrentVersionPackage(currentVersion);
      }

      // 打印详细的返回结果
      print('=== 服务端版本检查结果 ===');
      print('当前版本: $currentVersion');
      print('最新版本: ${serverVersionInfo.latestVersion}');
      print('是否有更新: $hasUpdate');
      print('更新内容: ${serverVersionInfo.updateContent}');
      print('=== 版本检查完成 ===');

    } catch (e) {
      print('=== APP启动版本检查失败 ===');
      print('错误信息: $e');
      print('=== 版本检查结束 ===');
    }
  }

  /// APP启动时检查待安装的更新包
  ///
  /// 检查本地是否有已下载但未安装的更新包，如果有则返回更新信息
  Future<Map<String, String>?> checkPendingUpdateOnAppStart() async {
    try {
      print('=== 检查待安装更新包 ===');

      final hasPending = await _storageService.hasPendingUpdate();
      if (!hasPending) {
        print('没有待安装的更新包');
        return null;
      }

      final updateInfo = await _storageService.getPendingUpdateInfo();
      if (updateInfo == null) {
        print('获取待安装更新包信息失败');
        return null;
      }

      print('发现待安装更新包:');
      print('  文件路径: ${updateInfo['filePath']}');
      print('  版本号: ${updateInfo['version']}');
      print('  更新内容: ${updateInfo['updateContent']}');

      // 验证文件是否仍然存在
      final filePath = updateInfo['filePath']!;
      try {
        final file = File(filePath);
        if (!await file.exists()) {
          print('更新包文件不存在: $filePath，清理过期信息');
          await _storageService.clearPendingUpdate();
          return null;
        }
      } catch (e) {
        print('验证更新包文件失败: $e，清理过期信息');
        await _storageService.clearPendingUpdate();
        return null;
      }

      print('待安装更新包验证通过，可以提示用户安装');
      return updateInfo;

    } catch (e) {
      print('检查待安装更新包失败: $e');
      return null;
    }
  }

  /// 获取版本信息
  Future<VersionInfo> getVersionInfo() async {
    try {
      // 从原生端获取当前应用版本信息
      final currentVersion = await VersionBridge.getVersionName();
      print('当前应用版本: $currentVersion');

      // 调用服务端版本检查接口
      final serverVersionInfo = await _apiProvider.versionApi.checkVersion();
      print('服务端版本信息: ${serverVersionInfo.toJson()}');

      // 使用服务端返回的信息，但更新当前版本为本地获取的版本
      return VersionInfo(
        currentVersion: currentVersion,
        latestVersion: serverVersionInfo.latestVersion,
        hasUpdate: VersionUtils.hasNewVersion(currentVersion, serverVersionInfo.latestVersion),
        updateContent: serverVersionInfo.updateContent,
        downloadUrl: serverVersionInfo.downloadUrl,
        forceUpdate: serverVersionInfo.forceUpdate,
        updateTime: serverVersionInfo.updateTime,
      );
    } catch (e) {
      print('获取版本信息失败: $e');
      // 返回默认版本信息
      final currentVersion = await VersionBridge.getVersionName().catchError((_) => '1.0.0');
      return VersionInfo(
        currentVersion: currentVersion,
        latestVersion: currentVersion,
        hasUpdate: false,
        updateContent: '无法获取版本信息',
        downloadUrl: '',
        forceUpdate: false,
        updateTime: '',
      );
    }
  }



  /// 启动静默下载
  ///
  /// 在后台静默下载新版本应用
  /// [serverVersionInfo] 服务端版本信息，避免重复调用接口
  Future<void> _startSilentDownload(dynamic serverVersionInfo) async {
    try {
      print('开始静默下载应用更新');

      final targetVersion = serverVersionInfo.latestVersion;
      print('目标版本: $targetVersion');

      // 检查本地是否已有该版本的安装包
      final hasLocalVersion = await _checkLocalVersionExists(targetVersion);
      if (hasLocalVersion) {
        print('本地已存在版本 $targetVersion 的安装包，跳过下载');
        print('后续流程将检查是否需要提示用户安装');
        return;
      }

      print('本地未找到版本 $targetVersion 的安装包，开始下载');
      await _performDownload(serverVersionInfo);
    } catch (e) {
      print('启动静默下载失败: $e');
    }
  }

  /// 检查本地是否存在指定版本的安装包
  ///
  /// [targetVersion] 目标版本号
  /// 返回true表示本地已有该版本的安装包
  Future<bool> _checkLocalVersionExists(String targetVersion) async {
    try {
      print('检查本地是否存在版本 $targetVersion 的安装包');

      // 首先检查是否有待安装的更新包信息
      final pendingUpdateInfo = await _storageService.getPendingUpdateInfo();
      if (pendingUpdateInfo != null) {
        final pendingVersion = pendingUpdateInfo['version'] ?? '';
        final pendingFilePath = pendingUpdateInfo['filePath'] ?? '';

        print('发现待安装更新包信息 - 版本: $pendingVersion, 路径: $pendingFilePath');

        if (pendingVersion == targetVersion) {
          // 直接验证存储的文件路径是否存在，而不依赖AppDownloadService的内存状态
          try {
            final file = File(pendingFilePath);
            if (await file.exists()) {
              print('本地待安装版本 $pendingVersion 文件验证通过: $pendingFilePath');
              return true;
            } else {
              print('本地待安装版本文件不存在: $pendingFilePath，清理过期信息');
              // 清理过期的待安装信息
              await _storageService.clearPendingUpdate();
            }
          } catch (e) {
            print('验证待安装文件失败: $e，清理过期信息');
            await _storageService.clearPendingUpdate();
          }
        } else {
          print('待安装版本 $pendingVersion 与目标版本 $targetVersion 不匹配');
        }
      }

      // 检查下载服务中是否有已下载的文件（但没有保存为待安装状态）
      final downloadedPath = await _downloadService.getDownloadedAppPath();
      if (downloadedPath != null) {
        print('发现已下载文件: $downloadedPath');
        // 注意：这里无法确定下载文件的版本，所以保守处理
        // 如果有明确的版本信息存储机制，可以进一步验证
        print('已下载文件存在，但无法确定版本，建议重新下载以确保版本正确');
        return false;
      }

      print('本地未找到版本 $targetVersion 的安装包');
      return false;
    } catch (e) {
      print('检查本地版本失败: $e');
      return false;
    }
  }

  /// 执行实际的下载操作
  ///
  /// [serverVersionInfo] 服务端版本信息
  Future<void> _performDownload(dynamic serverVersionInfo) async {
    await _downloadService.startSilentDownload(
      onSuccess: (filePath) async {
        print('应用下载完成: $filePath');

        // 保存待安装的更新包信息
        await _storageService.savePendingUpdate(
          filePath: filePath,
          version: serverVersionInfo.latestVersion,
          updateContent: serverVersionInfo.updateContent,
        );

        print('已保存待安装更新包信息，版本: ${serverVersionInfo.latestVersion}');
        print('下次启动时将提示用户安装');
      },
      onError: (error) {
        print('应用下载失败: $error');
      },
    );
  }

  /// 清理当前版本的安装包
  ///
  /// 当确定不需要更新时（当前版本与服务器版本相同），删除本地可能存在的当前版本安装包
  /// [currentVersion] 当前应用版本号
  Future<void> _cleanupCurrentVersionPackage(String currentVersion) async {
    try {
      print('开始检查并清理当前版本 $currentVersion 的安装包');

      // 检查是否有待安装的更新包
      final pendingUpdateInfo = await _storageService.getPendingUpdateInfo();
      if (pendingUpdateInfo != null) {
        final pendingVersion = pendingUpdateInfo['version'] ?? '';
        final pendingFilePath = pendingUpdateInfo['filePath'] ?? '';

        print('发现待安装更新包 - 版本: $pendingVersion, 路径: $pendingFilePath');

        // 如果待安装的版本与当前版本相同，说明不需要这个安装包了
        if (pendingVersion == currentVersion) {
          print('待安装版本 $pendingVersion 与当前版本 $currentVersion 相同，开始清理');

          // 删除安装包文件
          try {
            final file = File(pendingFilePath);
            if (await file.exists()) {
              await file.delete();
              print('已删除安装包文件: $pendingFilePath');
            } else {
              print('安装包文件不存在，无需删除: $pendingFilePath');
            }
          } catch (e) {
            print('删除安装包文件失败: $e');
          }

          // 清理待安装更新包信息
          await _storageService.clearPendingUpdate();
          print('已清理待安装更新包信息');

          print('当前版本安装包清理完成');
        } else {
          print('待安装版本 $pendingVersion 与当前版本 $currentVersion 不同，保留安装包');
        }
      } else {
        print('没有待安装的更新包，无需清理');
      }

      // 检查下载服务中是否有其他已下载的文件
      final downloadedPath = await _downloadService.getDownloadedAppPath();
      if (downloadedPath != null) {
        print('发现其他已下载文件: $downloadedPath');
        print('由于无法确定文件版本，建议手动检查是否需要清理');
        // 这里可以选择是否清理，目前保守处理，不自动删除
      }

    } catch (e) {
      print('清理当前版本安装包失败: $e');
    }
  }

  /// 安装待安装的更新包
  ///
  /// 安装本地存储的待安装更新包
  /// [autoRequestPermission] 是否自动请求权限（默认true）
  /// 返回安装结果信息
  Future<Map<String, dynamic>> installPendingUpdate({
    bool autoRequestPermission = true,
  }) async {
    try {
      print('开始安装待安装的更新包');

      // 获取待安装更新包信息
      final updateInfo = await _storageService.getPendingUpdateInfo();
      if (updateInfo == null) {
        return {
          'success': false,
          'needPermission': false,
          'message': '没有待安装的更新包',
        };
      }

      final filePath = updateInfo['filePath']!;
      print('安装文件路径: $filePath');

      final result = await _downloadService.installDownloadedApp(
        filePath,
        autoRequestPermission,
      );

      if (result['success'] == true) {
        print('成功启动应用安装流程');
        // 安装成功后清理待安装更新包信息
        await _storageService.clearPendingUpdate();
        print('已清理待安装更新包信息');
      } else {
        print('安装失败: ${result['message']}');
        if (result['needPermission'] == true) {
          print('需要用户在设置中授权安装未知来源应用');
        }
      }

      return result;
    } catch (e) {
      print('安装待安装更新包失败: $e');
      return {
        'success': false,
        'needPermission': false,
        'message': '安装异常: $e',
      };
    }
  }

  /// 检查是否有安装权限
  ///
  /// 返回是否有安装未知来源应用的权限
  Future<bool> hasInstallPermission() async {
    try {
      return await _downloadService.hasInstallPermission();
    } catch (e) {
      print('检查安装权限失败: $e');
      return false;
    }
  }

  /// 请求安装权限
  ///
  /// 打开设置页面让用户授权安装未知来源应用
  Future<void> requestInstallPermission() async {
    try {
      print('请求安装权限');
      await _downloadService.requestInstallPermission();
    } catch (e) {
      print('请求安装权限失败: $e');
    }
  }

  /// 获取已下载的文件路径
  ///
  /// 返回已下载文件的路径，如果没有则返回null
  String? get downloadedFilePath => _downloadService.downloadedFilePath;

  /// 手动启动下载更新
  ///
  /// 供版本更新页面手动触发下载使用
  /// [onSuccess] 下载成功回调，返回本地文件路径
  /// [onError] 下载失败回调，返回错误信息
  Future<void> startSilentDownload({
    Function(String filePath)? onSuccess,
    Function(String error)? onError,
  }) async {
    try {
      // 获取最新版本信息
      final serverVersionInfo = await _apiProvider.versionApi.checkVersion();

      // 检查本地是否已有该版本的安装包
      final hasLocalVersion = await _checkLocalVersionExists(serverVersionInfo.latestVersion);
      if (hasLocalVersion) {
        onError?.call('本地已存在该版本的安装包');
        return;
      }

      // 开始下载
      await _downloadService.startSilentDownload(
        onSuccess: (filePath) async {
          print('手动下载完成: $filePath');

          // 保存待安装的更新包信息
          await _storageService.savePendingUpdate(
            filePath: filePath,
            version: serverVersionInfo.latestVersion,
            updateContent: serverVersionInfo.updateContent,
          );

          print('已保存待安装更新包信息，版本: ${serverVersionInfo.latestVersion}');
          onSuccess?.call(filePath);
        },
        onError: onError,
      );
    } catch (e) {
      print('手动启动下载失败: $e');
      onError?.call('启动下载失败: $e');
    }
  }

  /// 安装已下载的应用
  ///
  /// 供版本更新页面直接安装指定文件使用
  /// [filePath] APK文件路径
  /// [autoRequestPermission] 是否自动请求权限（默认true）
  /// 返回安装结果信息
  Future<Map<String, dynamic>> installDownloadedApp(
    String filePath, {
    bool autoRequestPermission = true,
  }) async {
    return await _downloadService.installDownloadedApp(
      filePath,
      autoRequestPermission,
    );
  }

  /// 获取Mock更新内容
  String _getMockUpdateContent() {
    return '''
【新功能】
• 新增版本更新检查功能
• 优化收藏夹加载性能
• 支持更多平台内容收藏

【优化改进】
• 提升应用启动速度
• 优化界面交互体验
• 修复已知问题

【问题修复】
• 修复部分机型兼容性问题
• 修复收藏失败的问题
• 提升应用稳定性
''';
  }
}
