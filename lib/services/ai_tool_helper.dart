import 'dart:convert';
import 'ai_chat_service.dart';
import '../native_bridge/config_bridge.dart';

/// AI工具辅助类
/// 
/// 提供常用工具定义和工具调用处理功能
class AiToolHelper {
  /// 创建搜索工具定义
  static ToolDefinition createSearchTool() {
    return FunctionToolDefinition(
      function: FunctionDefinition(
        name: 'search_web',
        description: '在网络上搜索信息',
        parameters: {
          'type': 'object',
          'properties': {
            'query': {
              'type': 'string',
              'description': '搜索关键词',
            }
          },
          'required': ['query']
        },
      ),
    );
  }

  /// 创建HTML页面修改工具定义
  static ToolDefinition createHtmlPatchTool() {
    return FunctionToolDefinition(
      function: FunctionDefinition(
        name: 'apply_html_patch',
        description: '可以执行任意复杂度的JavaScript脚本来修改当前HTML页面内容，但是创建的脚本必须先创建document.createElement("script")，然后将逻辑放到script标签中，然后将script标签append到body中',
        parameters: {
          'type': 'object',
          'properties': {
            'script': {
              'type': 'string',
              'description': 'JavaScript脚本代码，用于修改页面, 但是创建的脚本必须先创建document.createElement("script")，然后将逻辑放到script标签中，然后将script标签append到body中',
            }
          },
          'required': ['script']
        },
      ),
    );
  }

  /// 创建获取最新DOM工具定义
  static ToolDefinition createGetLatestDomTool() {
    return FunctionToolDefinition(
      function: FunctionDefinition(
        name: 'getLatestDom',
        description: '除了第一次修改不需要获取dom信息，从第二次修改开始，每次修改前都需要获取当前页面的最新DOM结构',
        parameters: {
          'type': 'object',
          'properties': {},
          'required': []
        },
      ),
    );
  }

  /// 创建自定义工具定义
  static ToolDefinition createCustomTool({
    required String name,
    required String description,
    required Map<String, dynamic> parameters,
  }) {
    return FunctionToolDefinition(
      function: FunctionDefinition(
        name: name,
        description: description,
        parameters: parameters,
      ),
    );
  }

  /// 创建百度搜索MCP工具定义
  static ToolDefinition createBaiduSearchMcpTool() {
    final apiKey = ConfigBridge.getString('baidu_search_api_key', '');
    return McpToolDefinition(
      serverLabel: 'baidu_search',
      serverUrl: 'http://appbuilder.baidu.com/v2/ai_search/mcp/sse',
      requireApproval: 'never',
      headers: {
        'Authorization': 'Bearer $apiKey',
      },
    );
  }

  /// 解析工具调用参数
  ///
  /// [toolCall] 工具调用对象
  /// 返回解析后的参数Map
  static Map<String, dynamic>? parseToolArguments(ToolCall toolCall) {
    try {
      if (toolCall.function.arguments.isEmpty) {
        return <String, dynamic>{};
      }

      // 使用 dart:convert 的 jsonDecode 解析JSON字符串
      return jsonDecode(toolCall.function.arguments) as Map<String, dynamic>;
    } catch (e) {
      print('解析工具参数失败: $e');
      print('原始参数: ${toolCall.function.arguments}');
      return null;
    }
  }

  /// 创建工具调用结果
  /// 
  /// [success] 是否成功
  /// [data] 结果数据
  /// [error] 错误信息（如果失败）
  static String createToolResult({
    required bool success,
    dynamic data,
    String? error,
  }) {
    if (success) {
      return data?.toString() ?? '操作成功';
    } else {
      return '错误: ${error ?? "未知错误"}';
    }
  }

  /// 获取常用工具列表
  static List<ToolDefinition> getCommonTools() {
    return [
      createSearchTool(),
      createBaiduSearchMcpTool(),
    ];
  }

  /// 获取笔记编辑工具列表
  static List<ToolDefinition> getNoteEditingTools() {
    return [
      createHtmlPatchTool(),
      createGetLatestDomTool(),
    ];
  }
}
