import 'dart:async';
import 'package:flutter/foundation.dart';
import '../api/api_provider.dart';

/// AI聊天消息模型
class AiChatMessage {
  final String role; // 'user', 'assistant', 'system', 'tool'
  final String content;
  final DateTime timestamp;
  final String? reasoningContent; // AI思考过程（仅assistant消息）
  final String? toolCallId; // 工具调用ID（仅tool消息）
  final List<Map<String, dynamic>>? toolCalls; // 工具调用列表（仅assistant消息）

  AiChatMessage({
    required this.role,
    required this.content,
    required this.timestamp,
    this.reasoningContent,
    this.toolCallId,
    this.toolCalls,
  });

  /// 转换为API格式
  Map<String, dynamic> toApiFormat() {
    final result = <String, dynamic>{
      'role': role,
      'content': content,
    };

    // 如果是工具消息，添加tool_call_id
    if (role == 'tool' && toolCallId != null) {
      result['tool_call_id'] = toolCallId;
    }

    // 如果是assistant消息且包含工具调用，添加tool_calls
    if (role == 'assistant' && toolCalls != null && toolCalls!.isNotEmpty) {
      result['tool_calls'] = toolCalls;
    }

    return result;
  }

  /// 从API响应创建消息
  factory AiChatMessage.fromApiResponse({
    required String content,
    String? reasoningContent,
    List<Map<String, dynamic>>? toolCalls,
  }) {
    return AiChatMessage(
      role: 'assistant',
      content: content,
      timestamp: DateTime.now(),
      reasoningContent: reasoningContent,
      toolCalls: toolCalls,
    );
  }

  /// 创建用户消息
  factory AiChatMessage.user(String content) {
    return AiChatMessage(
      role: 'user',
      content: content,
      timestamp: DateTime.now(),
    );
  }

  /// 创建系统消息
  factory AiChatMessage.system(String content) {
    return AiChatMessage(
      role: 'system',
      content: content,
      timestamp: DateTime.now(),
    );
  }

  /// 创建工具消息
  factory AiChatMessage.tool({
    required String content,
    required String toolCallId,
  }) {
    return AiChatMessage(
      role: 'tool',
      content: content,
      timestamp: DateTime.now(),
      toolCallId: toolCallId,
    );
  }
}



/// AI聊天服务类
/// 
/// 提供AI聊天功能，包括：
/// - 系统提示词管理
/// - 对话历史自动维护
/// - 流式和非流式消息发送
/// - 对话历史操作
class AiChatService {
  final ApiProvider _apiProvider;

  /// 系统提示词
  String _systemPrompt = '你是一个有用的AI助手，请用中文回答问题。';

  /// 对话历史（不包含系统提示词）
  final List<AiChatMessage> _conversationHistory = [];

  /// 可用工具列表
  List<ToolDefinition>? _tools;

  /// 构造函数
  AiChatService({ApiProvider? apiProvider})
      : _apiProvider = apiProvider ?? ApiProvider();

  /// 获取当前系统提示词
  String get systemPrompt => _systemPrompt;

  /// 设置系统提示词
  void setSystemPrompt(String prompt) {
    _systemPrompt = prompt;
    // debugPrint('AI聊天服务：系统提示词已更新为: $_systemPrompt');
  }

  /// 获取当前工具列表
  List<ToolDefinition>? get tools => _tools;

  /// 设置工具列表
  void setTools(List<ToolDefinition>? tools) {
    _tools = tools;
    // debugPrint('AI聊天服务：工具列表已更新，工具数量: ${tools?.length ?? 0}');
  }

  /// 获取对话历史副本
  List<AiChatMessage> get conversationHistory => List.unmodifiable(_conversationHistory);

  /// 获取对话历史数量
  int get historyCount => _conversationHistory.length;

  /// 清空对话历史
  void clearHistory() {
    _conversationHistory.clear();
    // debugPrint('AI聊天服务：对话历史已清空');
  }

  /// 添加消息到历史记录
  void _addToHistory(AiChatMessage message) {
    _conversationHistory.add(message);
    // debugPrint('AI聊天服务：添加消息到历史 - ${message.role}: ${message.content.substring(0, message.content.length > 50 ? 50 : message.content.length)}...');
  }

  /// 发送消息（流式响应）
  ///
  /// [message] 用户消息内容
  /// [addToHistory] 是否将消息添加到历史记录，默认为true
  ///
  /// 返回流式响应，每个chunk包含：
  /// - content: 消息内容片段
  /// - reasoning_content: AI思考过程片段（可选）
  /// - isComplete: 是否为完整响应（最后一个chunk）
  Stream<Map<String, dynamic>> sendMessage(
    String message, {
    bool addToHistory = true,
  }) async* {
    if (message.trim().isEmpty) {
      throw ArgumentError('消息内容不能为空');
    }

    // 创建用户消息
    final userMessage = AiChatMessage.user(message);

    // 添加到历史记录
    if (addToHistory) {
      _addToHistory(userMessage);
    }

    // 准备对话历史（转换为API格式）
    final apiHistory = _conversationHistory
        .map((msg) => msg.toApiFormat())
        .toList();

    String accumulatedContent = '';
    String accumulatedReasoningContent = '';

    try {
      print('AI聊天服务：开始发送消息');
      print('系统提示词: $_systemPrompt');

      // 准备工具列表（转换为API格式）
      final apiTools = _tools?.map((tool) => tool.toApiFormat()).toList();

      // 调用API发送消息
      await for (final chunk in _apiProvider.aiChatApi.sendMessage(
        message: message,
        conversationHistory: apiHistory,
        systemPrompt: _systemPrompt,
        tools: apiTools,
      )) {
        final content = chunk['content'];
        final reasoningContent = chunk['reasoning_content'];
        final completeMessage = chunk['complete_message'] as Map<String, dynamic>?;
        final isComplete = chunk['isComplete'] == true;

        // 累积内容（用于UI显示）
        if (content != null && content.isNotEmpty) {
          accumulatedContent += content;
        }
        if (reasoningContent != null && reasoningContent.isNotEmpty) {
          accumulatedReasoningContent += reasoningContent;
        }

        // 如果收到完整消息，直接添加到历史记录
        if (isComplete && completeMessage != null && addToHistory) {
          print('AI聊天服务：收到完整的OpenAI消息');
          print('完整消息: $completeMessage');

          // 直接使用OpenAI返回的完整消息创建AiChatMessage
          final aiMessage = AiChatMessage(
            role: completeMessage['role'] as String,
            content: completeMessage['content'] as String? ?? '',
            timestamp: DateTime.now(),
            reasoningContent: completeMessage['reasoning_content'] as String?,
            toolCalls: completeMessage['tool_calls'] as List<Map<String, dynamic>>?,
          );

          _addToHistory(aiMessage);

          print('AI聊天服务：添加完整OpenAI消息到历史记录');
          print('角色: ${aiMessage.role}');
          print('内容长度: ${aiMessage.content.length}');
          print('工具调用数量: ${aiMessage.toolCalls?.length ?? 0}');
        }

        // 返回当前chunk
        // 注意：只在isComplete=true时才传递complete_message和tool_calls
        yield {
          'content': content,
          'reasoning_content': reasoningContent,
          'complete_message': isComplete ? completeMessage : null,
          'accumulated_content': accumulatedContent,
          'accumulated_reasoning_content': accumulatedReasoningContent,
          'isComplete': isComplete,
        };

        if (isComplete) break;
      }



    } catch (e) {
      print('AI聊天服务：发送消息失败 - $e');

      // 如果已经添加了用户消息到历史，需要移除
      if (addToHistory && _conversationHistory.isNotEmpty &&
          _conversationHistory.last.role == 'user' &&
          _conversationHistory.last.content == message) {
        _conversationHistory.removeLast();
      }

      rethrow;
    }
  }

  /// 发送简单消息（非流式响应）
  /// 
  /// [message] 用户消息内容
  /// [addToHistory] 是否将消息添加到历史记录，默认为true
  /// 
  /// 返回AI的完整回复
  Future<String> sendSimpleMessage(
    String message, {
    bool addToHistory = true,
  }) async {
    if (message.trim().isEmpty) {
      throw ArgumentError('消息内容不能为空');
    }

    // 创建用户消息
    final userMessage = AiChatMessage.user(message);
    
    // 添加到历史记录
    if (addToHistory) {
      _addToHistory(userMessage);
    }

    try {
      // 调用API发送消息
      final response = await _apiProvider.aiChatApi.sendSimpleMessage(
        message,
        systemPrompt: _systemPrompt,
      );

      // 创建AI回复消息并添加到历史
      if (addToHistory) {
        final aiMessage = AiChatMessage.fromApiResponse(content: response);
        _addToHistory(aiMessage);
      }

      return response;

    } catch (e) {
      // debugPrint('AI聊天服务：发送简单消息失败 - $e');

      // 如果已经添加了用户消息到历史，需要移除
      if (addToHistory && _conversationHistory.isNotEmpty &&
          _conversationHistory.last.role == 'user' &&
          _conversationHistory.last.content == message) {
        _conversationHistory.removeLast();
      }

      rethrow;
    }
  }

  /// 移除最后N条消息
  /// 
  /// [count] 要移除的消息数量，默认为1
  void removeLastMessages({int count = 1}) {
    if (count <= 0) return;
    
    final removeCount = count > _conversationHistory.length 
        ? _conversationHistory.length 
        : count;
    
    for (int i = 0; i < removeCount; i++) {
      _conversationHistory.removeLast();
    }
    
    // debugPrint('AI聊天服务：已移除最后 $removeCount 条消息');
  }

  /// 添加工具调用结果消息
  ///
  /// [toolCallId] 工具调用ID
  /// [result] 工具执行结果
  /// [addToHistory] 是否添加到历史记录，默认为true
  void addToolResult({
    required String toolCallId,
    required String result,
    bool addToHistory = true,
  }) {
    if (addToHistory) {
      final toolMessage = AiChatMessage.tool(
        content: result,
        toolCallId: toolCallId,
      );
      _addToHistory(toolMessage);
      // debugPrint('AI聊天服务：添加工具调用结果 - ID: $toolCallId');
    }
  }

  /// 在工具执行完成后继续对话
  ///
  /// 此方法用于在所有工具调用完成后，自动向OpenAI发送更新的对话历史
  /// 以获取基于工具执行结果的AI回复
  ///
  /// 返回流式响应，格式与sendMessage相同
  Stream<Map<String, dynamic>> continueAfterToolExecution() async* {
    // 准备对话历史（转换为API格式）
    final apiHistory = _conversationHistory
        .map((msg) => msg.toApiFormat())
        .toList();

    String accumulatedContent = '';
    String accumulatedReasoningContent = '';

    try {
      print('AI聊天服务：工具执行完成后继续对话');

      // 准备工具列表（转换为API格式）
      final apiTools = _tools?.map((tool) => tool.toApiFormat()).toList();

      // 调用API继续对话（不添加新的用户消息）
      await for (final chunk in _apiProvider.aiChatApi.sendMessage(
        message: '', // 空消息，因为我们只是基于现有历史继续对话
        conversationHistory: apiHistory,
        systemPrompt: _systemPrompt,
        tools: apiTools,
      )) {
        final content = chunk['content'];
        final reasoningContent = chunk['reasoning_content'];
        final completeMessage = chunk['complete_message'] as Map<String, dynamic>?;
        final isComplete = chunk['isComplete'] == true;

        // 累积内容（用于UI显示）
        if (content != null && content.isNotEmpty) {
          accumulatedContent += content;
        }
        if (reasoningContent != null && reasoningContent.isNotEmpty) {
          accumulatedReasoningContent += reasoningContent;
        }

        // 如果收到完整消息，直接添加到历史记录
        if (isComplete && completeMessage != null) {
          print('AI聊天服务：工具执行后收到完整的OpenAI消息');
          print('完整消息: $completeMessage');

          // 直接使用OpenAI返回的完整消息创建AiChatMessage
          final aiMessage = AiChatMessage(
            role: completeMessage['role'] as String,
            content: completeMessage['content'] as String? ?? '',
            timestamp: DateTime.now(),
            reasoningContent: completeMessage['reasoning_content'] as String?,
            toolCalls: completeMessage['tool_calls'] as List<Map<String, dynamic>>?,
          );

          _addToHistory(aiMessage);

          print('AI聊天服务：工具执行后添加完整OpenAI消息到历史记录');
          print('角色: ${aiMessage.role}');
          print('内容长度: ${aiMessage.content.length}');
          print('工具调用数量: ${aiMessage.toolCalls?.length ?? 0}');
        }

        // 返回当前chunk
        // 注意：只在isComplete=true时才传递complete_message和tool_calls
        yield {
          'content': content,
          'reasoning_content': reasoningContent,
          'complete_message': isComplete ? completeMessage : null,
          'accumulated_content': accumulatedContent,
          'accumulated_reasoning_content': accumulatedReasoningContent,
          'isComplete': isComplete,
        };

        if (isComplete) break;
      }



    } catch (e) {
      print('AI聊天服务：工具执行后继续对话失败 - $e');
      rethrow;
    }
  }

  /// 添加仅用于API的消息到历史记录
  ///
  /// 此方法用于添加不需要在UI中显示的消息到API历史记录
  /// [message] 要添加的消息
  void addApiOnlyMessage(AiChatMessage message) {
    _addToHistory(message);
  }



  /// 释放资源
  void dispose() {
    _conversationHistory.clear();
    // debugPrint('AI聊天服务：已释放资源');
  }
}

/// 工具函数定义
class FunctionDefinition {
  final String name;
  final String description;
  final Map<String, dynamic> parameters;

  FunctionDefinition({
    required this.name,
    required this.description,
    required this.parameters,
  });

  /// 转换为API格式
  Map<String, dynamic> toApiFormat() {
    return {
      'name': name,
      'description': description,
      'parameters': parameters,
    };
  }
}

/// 工具定义基类
abstract class ToolDefinition {
  final String type;

  ToolDefinition({required this.type});

  /// 转换为API格式
  Map<String, dynamic> toApiFormat();
}

/// 函数工具定义
class FunctionToolDefinition extends ToolDefinition {
  final FunctionDefinition function;

  FunctionToolDefinition({
    required this.function,
  }) : super(type: 'function');

  @override
  Map<String, dynamic> toApiFormat() {
    return {
      'type': type,
      'function': function.toApiFormat(),
    };
  }
}

/// MCP工具定义
class McpToolDefinition extends ToolDefinition {
  final String serverLabel;
  final String serverUrl;
  final String requireApproval;
  final Map<String, String>? headers;
  final List<String>? allowedTools;

  McpToolDefinition({
    required this.serverLabel,
    required this.serverUrl,
    this.requireApproval = 'never',
    this.headers,
    this.allowedTools,
  }) : super(type: 'mcp');

  @override
  Map<String, dynamic> toApiFormat() {
    final result = <String, dynamic>{
      'type': type,
      'server_label': serverLabel,
      'server_url': serverUrl,
      'require_approval': requireApproval,
    };

    if (headers != null && headers!.isNotEmpty) {
      result['headers'] = headers;
    }

    if (allowedTools != null && allowedTools!.isNotEmpty) {
      result['allowed_tools'] = allowedTools;
    }

    return result;
  }
}

/// 工具调用函数信息
class ToolCallFunction {
  final String name;
  final String arguments;

  ToolCallFunction({
    required this.name,
    required this.arguments,
  });

  /// 从API响应创建
  factory ToolCallFunction.fromApiResponse(Map<String, dynamic> json) {
    return ToolCallFunction(
      name: json['name'] as String,
      arguments: json['arguments'] as String,
    );
  }
}

/// 工具调用
class ToolCall {
  final String id;
  final String type;
  final ToolCallFunction function;

  ToolCall({
    required this.id,
    required this.type,
    required this.function,
  });

  /// 从API响应创建
  factory ToolCall.fromApiResponse(Map<String, dynamic> json) {
    return ToolCall(
      id: json['id'] as String,
      type: json['type'] as String,
      function: ToolCallFunction.fromApiResponse(json['function'] as Map<String, dynamic>),
    );
  }
}
