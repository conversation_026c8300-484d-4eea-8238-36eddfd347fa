import 'package:shared_preferences/shared_preferences.dart';

/// 本地存储服务
///
/// 用于管理应用的本地存储，如用户令牌、设置等
class StorageService {
  static final StorageService _instance = StorageService._internal();

  /// 单例模式
  factory StorageService() => _instance;

  /// 私有构造函数
  StorageService._internal();

  /// 存储键名
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _phoneKey = 'user_phone';
  static const String _avatarUrlKey = 'user_avatar';
  static const String _nicknameKey = 'user_nickname';

  // 应用更新相关键名
  static const String _pendingUpdateFilePathKey = 'pending_update_file_path';
  static const String _pendingUpdateVersionKey = 'pending_update_version';
  static const String _pendingUpdateContentKey = 'pending_update_content';

  // 用户偏好设置键名
  static const String _isGridViewModeKey = 'is_grid_view_mode';

  /// 保存认证令牌
  Future<bool> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setString(_tokenKey, token);
  }

  /// 获取认证令牌
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// 清除认证令牌
  Future<bool> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.remove(_tokenKey);
  }

  /// 保存用户ID
  Future<bool> saveUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setString(_userIdKey, userId);
  }

  /// 获取用户ID
  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  /// 保存用户手机号
  Future<bool> savePhone(String phone) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setString(_phoneKey, phone);
  }

  /// 获取用户手机号
  Future<String?> getPhone() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_phoneKey);
  }

  /// 保存用户头像URL
  Future<bool> saveAvatarUrl(String avatarUrl) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setString(_avatarUrlKey, avatarUrl);
  }

  /// 获取用户头像URL
  Future<String?> getAvatarUrl() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_avatarUrlKey);
  }

  /// 保存用户昵称
  Future<bool> saveNickname(String nickname) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setString(_nicknameKey, nickname);
  }

  /// 获取用户昵称
  Future<String?> getNickname() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_nicknameKey);
  }

  /// 保存用户信息
  Future<void> saveUserInfo({
    required String userId,
    String? phone,
    required String token,
    String? avatarUrl,
    String? nickname,
  }) async {
    await saveUserId(userId);
    if (phone != null && phone.isNotEmpty) {
      await savePhone(phone);
    }
    await saveToken(token);

    if (avatarUrl != null && avatarUrl.isNotEmpty) {
      await saveAvatarUrl(avatarUrl);
    }

    if (nickname != null && nickname.isNotEmpty) {
      await saveNickname(nickname);
    }
  }

  /// 清除所有用户信息
  Future<void> clearUserInfo() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userIdKey);
    await prefs.remove(_phoneKey);
    await prefs.remove(_avatarUrlKey);
    await prefs.remove(_nicknameKey);
  }

  /// 检查用户是否已登录
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // ==================== 应用更新相关方法 ====================

  /// 保存待安装的更新包信息
  ///
  /// [filePath] APK文件路径
  /// [version] 新版本号
  /// [updateContent] 更新内容描述
  Future<bool> savePendingUpdate({
    required String filePath,
    required String version,
    String? updateContent,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    // 保存文件路径
    await prefs.setString(_pendingUpdateFilePathKey, filePath);

    // 保存版本号
    await prefs.setString(_pendingUpdateVersionKey, version);

    // 保存更新内容（可选）
    if (updateContent != null && updateContent.isNotEmpty) {
      await prefs.setString(_pendingUpdateContentKey, updateContent);
    }

    return true;
  }

  /// 获取待安装的更新包文件路径
  Future<String?> getPendingUpdateFilePath() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_pendingUpdateFilePathKey);
  }

  /// 获取待安装的更新包版本号
  Future<String?> getPendingUpdateVersion() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_pendingUpdateVersionKey);
  }

  /// 获取待安装的更新包内容描述
  Future<String?> getPendingUpdateContent() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_pendingUpdateContentKey);
  }

  /// 检查是否有待安装的更新包
  Future<bool> hasPendingUpdate() async {
    final filePath = await getPendingUpdateFilePath();
    return filePath != null && filePath.isNotEmpty;
  }

  /// 获取完整的待安装更新信息
  Future<Map<String, String>?> getPendingUpdateInfo() async {
    final filePath = await getPendingUpdateFilePath();
    if (filePath == null || filePath.isEmpty) {
      return null;
    }

    final version = await getPendingUpdateVersion() ?? '';
    final content = await getPendingUpdateContent() ?? '';

    return {
      'filePath': filePath,
      'version': version,
      'updateContent': content,
    };
  }

  /// 清除待安装的更新包信息
  Future<void> clearPendingUpdate() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_pendingUpdateFilePathKey);
    await prefs.remove(_pendingUpdateVersionKey);
    await prefs.remove(_pendingUpdateContentKey);
  }

  // ==================== 用户偏好设置相关方法 ====================

  /// 保存大图模式偏好设置
  ///
  /// [isGridView] true为大图模式，false为列表模式
  Future<bool> saveGridViewMode(bool isGridView) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setBool(_isGridViewModeKey, isGridView);
  }

  /// 获取大图模式偏好设置
  ///
  /// 默认返回false（列表模式）
  Future<bool> getGridViewMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isGridViewModeKey) ?? false;
  }

  /// 检查指定版本是否已存在本地
  ///
  /// [targetVersion] 目标版本号
  /// 返回true表示本地已有该版本的安装包
  Future<bool> hasVersionDownloaded(String targetVersion) async {
    final pendingVersion = await getPendingUpdateVersion();
    return pendingVersion != null && pendingVersion == targetVersion;
  }

  /// 检查本地安装包版本是否与目标版本匹配
  ///
  /// [targetVersion] 目标版本号
  /// [filePath] 文件路径
  /// 返回匹配结果信息
  Future<Map<String, dynamic>> checkVersionMatch(String targetVersion, String filePath) async {
    try {
      final pendingUpdateInfo = await getPendingUpdateInfo();

      if (pendingUpdateInfo == null) {
        return {
          'matched': false,
          'reason': '没有待安装的更新包信息',
        };
      }

      final pendingVersion = pendingUpdateInfo['version'] ?? '';
      final pendingFilePath = pendingUpdateInfo['filePath'] ?? '';

      // 检查版本号是否匹配
      if (pendingVersion != targetVersion) {
        return {
          'matched': false,
          'reason': '版本号不匹配，本地: $pendingVersion, 目标: $targetVersion',
        };
      }

      // 检查文件路径是否匹配
      if (pendingFilePath != filePath) {
        return {
          'matched': false,
          'reason': '文件路径不匹配，本地: $pendingFilePath, 目标: $filePath',
        };
      }

      return {
        'matched': true,
        'reason': '版本和文件路径完全匹配',
        'version': pendingVersion,
        'filePath': pendingFilePath,
      };
    } catch (e) {
      return {
        'matched': false,
        'reason': '检查版本匹配失败: $e',
      };
    }
  }
}
