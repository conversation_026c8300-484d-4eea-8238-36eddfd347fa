import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import '../utils/config_util.dart';

/// 微信服务类
///
/// 封装与原生微信SDK的交互，提供登录、分享等功能
class WechatService {
  // 单例实例
  static final WechatService _instance = WechatService._internal();

  // 工厂构造函数
  factory WechatService() => _instance;

  // 内部构造函数
  WechatService._internal();

  // 方法通道
  static const MethodChannel _channel = MethodChannel('com.xunhe.aishoucang/method_channel');

  // 事件通道
  static const EventChannel _eventChannel = EventChannel('com.xunhe.aishoucang/wechat_events');

  // 事件流
  Stream<dynamic>? _eventStream;

  // 登录结果监听器列表
  final List<Function(Map<String, dynamic>)> _loginListeners = [];

  // 分享结果监听器列表
  final List<Function(Map<String, dynamic>)> _shareListeners = [];

  // 是否已初始化
  bool _isInitialized = false;

  // 配置工具
  final _configUtil = ConfigUtil();

  // 微信AppID
  String? _appId;

  /// 初始化微信服务
  Future<void> init() async {
    if (_isInitialized) return;

    // 从配置中读取微信AppID
    _appId = await _configUtil.getWechatAppId();
    print('微信AppID: $_appId');

    // 监听微信事件
    _eventStream = _eventChannel.receiveBroadcastStream();
    _eventStream?.listen(_handleWechatEvent);

    _isInitialized = true;
  }

  /// 处理微信事件
  void _handleWechatEvent(dynamic event) {
    print('收到微信事件原始数据: $event');
    print('事件数据类型: ${event.runtimeType}');

    if (event is! Map) {
      print('事件不是Map类型，无法处理');
      return;
    }

    // 使用安全的类型转换方式
    final Map<dynamic, dynamic> originalMap = event as Map<dynamic, dynamic>;
    print('原始Map内容: $originalMap');

    final Map<String, dynamic> eventMap = {};

    // 手动复制键值对，确保类型安全
    originalMap.forEach((key, value) {
      print('处理键值对: key=$key (${key.runtimeType}), value=$value (${value.runtimeType})');
      if (key is String) {
        eventMap[key] = value;
      }
    });

    print('转换后的Map内容: $eventMap');

    final String type = eventMap['type'] as String? ?? '';
    final String result = eventMap['result'] as String? ?? '';

    print('事件类型: $type, 结果: $result');

    if (type == 'auth') {
      // 处理登录事件
      if (result == 'success') {
        // 登录成功
        final String code = eventMap['code'] as String? ?? '';
        _notifyLoginListeners({
          'success': true,
          'code': code,
        });
      } else if (result == 'cancel') {
        // 登录取消
        _notifyLoginListeners({
          'success': false,
          'error': '用户取消登录',
        });
      } else if (result == 'denied') {
        // 登录拒绝
        _notifyLoginListeners({
          'success': false,
          'error': '用户拒绝授权',
        });
      } else if (result == 'fail') {
        // 登录失败
        final int errCode = eventMap['errCode'] is int ? eventMap['errCode'] as int : -1;
        final String errMsg = eventMap['errMsg'] as String? ?? '未知错误';
        _notifyLoginListeners({
          'success': false,
          'error': '登录失败: $errMsg (错误码: $errCode)',
          'errCode': errCode,
          'errMsg': errMsg,
        });
      }
    } else if (type == 'share') {
      // 处理分享事件
      if (result == 'success') {
        // 分享成功
        _notifyShareListeners({
          'success': true,
        });
      } else if (result == 'cancel') {
        // 分享取消
        _notifyShareListeners({
          'success': false,
          'error': '用户取消分享',
        });
      } else if (result == 'fail') {
        // 分享失败
        final int errCode = eventMap['errCode'] is int ? eventMap['errCode'] as int : -1;
        final String errMsg = eventMap['errMsg'] as String? ?? '未知错误';
        _notifyShareListeners({
          'success': false,
          'error': '分享失败: $errMsg (错误码: $errCode)',
          'errCode': errCode,
          'errMsg': errMsg,
        });
      }
    }
  }

  /// 通知所有登录监听器
  void _notifyLoginListeners(Map<String, dynamic> result) {
    for (var listener in _loginListeners) {
      listener(result);
    }
  }

  /// 通知所有分享监听器
  void _notifyShareListeners(Map<String, dynamic> result) {
    for (var listener in _shareListeners) {
      listener(result);
    }
  }

  /// 添加登录结果监听器
  void addLoginListener(Function(Map<String, dynamic>) listener) {
    if (!_loginListeners.contains(listener)) {
      _loginListeners.add(listener);
    }
  }

  /// 移除登录结果监听器
  void removeLoginListener(Function(Map<String, dynamic>) listener) {
    _loginListeners.remove(listener);
  }

  /// 添加分享结果监听器
  void addShareListener(Function(Map<String, dynamic>) listener) {
    if (!_shareListeners.contains(listener)) {
      _shareListeners.add(listener);
    }
  }

  /// 移除分享结果监听器
  void removeShareListener(Function(Map<String, dynamic>) listener) {
    _shareListeners.remove(listener);
  }

  /// 检查微信是否已安装
  Future<bool> isWechatInstalled() async {
    try {
      final bool result = await _channel.invokeMethod('isWechatInstalled');
      return result;
    } on PlatformException catch (e) {
      print('检查微信是否已安装时出错: ${e.message}');
      return false;
    }
  }

  /// 获取微信支持的API版本
  Future<int> getWechatSupportedApi() async {
    try {
      final int result = await _channel.invokeMethod('getWechatSupportedApi');
      return result;
    } on PlatformException catch (e) {
      print('获取微信支持的API版本时出错: ${e.message}');
      return 0;
    }
  }

  /// 打开微信
  Future<bool> openWechat() async {
    try {
      final bool result = await _channel.invokeMethod('openWechat');
      return result;
    } on PlatformException catch (e) {
      print('打开微信时出错: ${e.message}');
      return false;
    }
  }

  /// 发起微信登录
  Future<bool> login({String? scope, String? state}) async {
    try {
      print('发起微信登录: scope=$scope, state=$state');
      final dynamic rawResult = await _channel.invokeMethod('wechatLogin', {
        'scope': scope,
        'state': state,
      });

      print('微信登录原始结果: $rawResult (${rawResult.runtimeType})');

      // 安全地处理返回值
      if (rawResult is Map) {
        final Map<dynamic, dynamic> resultMap = rawResult as Map<dynamic, dynamic>;
        print('微信登录结果Map: $resultMap');

        final success = resultMap['success'];
        print('微信登录success值: $success (${success.runtimeType})');

        return success == true;
      } else {
        print('微信登录结果不是Map类型');
        return false;
      }
    } on PlatformException catch (e) {
      print('发起微信登录时出错: ${e.message}');
      return false;
    } catch (e) {
      print('发起微信登录时发生未知错误: $e');
      return false;
    }
  }

  /// 分享文本到微信
  Future<bool> shareText(String text, {int scene = 0}) async {
    try {
      final dynamic rawResult = await _channel.invokeMethod('wechatShareText', {
        'text': text,
        'scene': scene,
      });

      // 安全地处理返回值
      if (rawResult is Map) {
        final Map<dynamic, dynamic> resultMap = rawResult as Map<dynamic, dynamic>;
        return resultMap['success'] == true;
      }
      return false;
    } on PlatformException catch (e) {
      print('分享文本到微信时出错: ${e.message}');
      return false;
    } catch (e) {
      print('分享文本到微信时发生未知错误: $e');
      return false;
    }
  }

  /// 分享图片到微信
  Future<bool> shareImage(String imagePath, {int scene = 0}) async {
    try {
      final dynamic rawResult = await _channel.invokeMethod('wechatShareImage', {
        'imagePath': imagePath,
        'scene': scene,
      });

      // 安全地处理返回值
      if (rawResult is Map) {
        final Map<dynamic, dynamic> resultMap = rawResult as Map<dynamic, dynamic>;
        return resultMap['success'] == true;
      }
      return false;
    } on PlatformException catch (e) {
      print('分享图片到微信时出错: ${e.message}');
      return false;
    } catch (e) {
      print('分享图片到微信时发生未知错误: $e');
      return false;
    }
  }

  /// 分享网页到微信
  Future<bool> shareWebpage(String url, String title, String description, {String? thumbImagePath, int scene = 0}) async {
    try {
      final dynamic rawResult = await _channel.invokeMethod('wechatShareWebpage', {
        'url': url,
        'title': title,
        'description': description,
        'thumbImagePath': thumbImagePath,
        'scene': scene,
      });

      // 安全地处理返回值
      if (rawResult is Map) {
        final Map<dynamic, dynamic> resultMap = rawResult as Map<dynamic, dynamic>;
        return resultMap['success'] == true;
      }
      return false;
    } on PlatformException catch (e) {
      print('分享网页到微信时出错: ${e.message}');
      return false;
    } catch (e) {
      print('分享网页到微信时发生未知错误: $e');
      return false;
    }
  }
}
