import 'dart:io';
import 'dart:developer' as developer;
import '../native_bridge/config_bridge.dart';
import '../native_bridge/download_bridge.dart';

/// 应用下载服务
///
/// 负责处理应用的静默下载功能，支持Android各版本兼容性
class AppDownloadService {
  static final AppDownloadService _instance = AppDownloadService._internal();
  factory AppDownloadService() => _instance;
  AppDownloadService._internal();

  // 下载状态
  bool _isDownloading = false;
  String? _downloadedFilePath;

  /// 是否正在下载
  bool get isDownloading => _isDownloading;

  /// 已下载的文件路径
  String? get downloadedFilePath => _downloadedFilePath;

  /// 开始静默下载应用
  ///
  /// 从配置中获取下载地址，静默下载到本地
  /// [onSuccess] 下载成功回调，返回本地文件路径
  /// [onError] 下载失败回调，返回错误信息
  Future<void> startSilentDownload({
    Function(String filePath)? onSuccess,
    Function(String error)? onError,
  }) async {
    if (_isDownloading) {
      developer.log('应用下载已在进行中，忽略重复请求');
      onError?.call('下载已在进行中');
      return;
    }

    try {
      _isDownloading = true;
      developer.log('开始静默下载应用');

      // 从配置中获取下载地址
      final downloadUrl = ConfigBridge.getString('app_download_url');
      if (downloadUrl.isEmpty) {
        throw Exception('未配置应用下载地址');
      }

      developer.log('应用下载地址: $downloadUrl');

      // 调用原生下载功能
      await DownloadBridge.downloadApp(
        url: downloadUrl,
        onSuccess: (filePath) {
          _downloadedFilePath = filePath;
          _isDownloading = false;
          developer.log('应用下载成功: $filePath');
          onSuccess?.call(filePath);
        },
        onError: (error) {
          _isDownloading = false;
          developer.log('应用下载失败: $error');
          onError?.call(error);
        },
      );

    } catch (e) {
      _isDownloading = false;
      final errorMsg = '启动下载失败: $e';
      developer.log(errorMsg);
      onError?.call(errorMsg);
    }
  }

  /// 安装已下载的应用
  ///
  /// 调用系统安装器安装已下载的APK文件
  /// [filePath] APK文件路径，如果为null则使用最后下载的文件
  /// [autoRequestPermission] 是否自动请求权限（默认true）
  /// 返回安装结果信息
  Future<Map<String, dynamic>> installDownloadedApp([
    String? filePath,
    bool autoRequestPermission = true,
  ]) async {
    try {
      final targetPath = filePath ?? _downloadedFilePath;
      if (targetPath == null || targetPath.isEmpty) {
        developer.log('没有可安装的应用文件');
        return {
          'success': false,
          'needPermission': false,
          'message': '没有可安装的应用文件',
        };
      }

      // 检查文件是否存在
      final file = File(targetPath);
      if (!await file.exists()) {
        developer.log('应用文件不存在: $targetPath');
        return {
          'success': false,
          'needPermission': false,
          'message': '应用文件不存在',
        };
      }

      developer.log('开始安装应用: $targetPath');

      // 调用带权限检查的安装功能
      final result = await DownloadBridge.installAppWithPermissionCheck(
        targetPath,
        autoRequestPermission: autoRequestPermission,
      );

      if (result['success'] == true) {
        developer.log('成功启动应用安装流程');
      } else {
        developer.log('安装失败: ${result['message']}');
      }

      return result;
    } catch (e) {
      developer.log('安装应用失败: $e');
      return {
        'success': false,
        'needPermission': false,
        'message': '安装异常: $e',
      };
    }
  }

  /// 检查是否有安装权限
  ///
  /// 返回是否有安装未知来源应用的权限
  Future<bool> hasInstallPermission() async {
    try {
      return await DownloadBridge.hasInstallPermission();
    } catch (e) {
      developer.log('检查安装权限失败: $e');
      return false;
    }
  }

  /// 请求安装权限
  ///
  /// 打开设置页面让用户授权安装未知来源应用
  Future<void> requestInstallPermission() async {
    try {
      developer.log('请求安装权限');
      await DownloadBridge.requestInstallPermission();
    } catch (e) {
      developer.log('请求安装权限失败: $e');
    }
  }

  /// 取消下载
  ///
  /// 取消正在进行的下载任务
  Future<void> cancelDownload() async {
    if (!_isDownloading) {
      developer.log('没有正在进行的下载任务');
      return;
    }

    try {
      developer.log('取消应用下载');
      await DownloadBridge.cancelDownload();
      _isDownloading = false;
      developer.log('应用下载已取消');
    } catch (e) {
      developer.log('取消下载失败: $e');
    }
  }

  /// 清理下载的文件
  ///
  /// 删除已下载的APK文件
  Future<void> cleanupDownloadedFile() async {
    if (_downloadedFilePath == null) {
      return;
    }

    try {
      final file = File(_downloadedFilePath!);
      if (await file.exists()) {
        await file.delete();
        developer.log('已删除下载的应用文件: $_downloadedFilePath');
      }
      _downloadedFilePath = null;
    } catch (e) {
      developer.log('删除下载文件失败: $e');
    }
  }

  /// 检查是否有已下载的应用文件
  ///
  /// 返回已下载文件的路径，如果没有则返回null
  Future<String?> getDownloadedAppPath() async {
    if (_downloadedFilePath == null) {
      return null;
    }

    try {
      final file = File(_downloadedFilePath!);
      if (await file.exists()) {
        return _downloadedFilePath;
      } else {
        // 文件不存在，清理路径
        _downloadedFilePath = null;
        return null;
      }
    } catch (e) {
      developer.log('检查下载文件失败: $e');
      return null;
    }
  }

  /// 获取下载文件大小
  ///
  /// 返回已下载文件的大小（字节），如果文件不存在返回0
  Future<int> getDownloadedFileSize() async {
    final filePath = await getDownloadedAppPath();
    if (filePath == null) {
      return 0;
    }

    try {
      final file = File(filePath);
      return await file.length();
    } catch (e) {
      developer.log('获取文件大小失败: $e');
      return 0;
    }
  }
}
