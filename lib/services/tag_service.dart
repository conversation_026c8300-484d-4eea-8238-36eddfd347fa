import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';
import '../api/api_provider.dart';
import '../models/tag.dart';
import '../widgets/custom_toast.dart';

/// 标签管理服务
///
/// 用于管理用户的标签数据，包括增删改查等操作
class TagService {
  static final TagService _instance = TagService._internal();

  /// 单例模式
  factory TagService() => _instance;

  /// 私有构造函数
  TagService._internal();

  /// API提供者
  ApiProvider get _apiProvider => ApiProvider();

  /// 存储键名（用于本地缓存）
  static const String _tagsKey = 'user_tags_cache';

  /// 内存缓存
  List<Tag>? _cachedTagObjects;
  List<String>? _cachedTagNames;

  /// 默认标签颜色列表 - 淡雅女孩子喜欢的色系
  static const List<String> _defaultColors = [
    '#FFE4E1', // 薄雾玫瑰
    '#FFF0F5', // 薰衣草腮红
    '#E0F2E9', // 薄荷绿
    '#E0F7FA', // 淡青色
    '#FFF5E1', // 香草色
    '#F5F0FF', // 淡紫色
    '#FFFDE7', // 柠檬雪纺
    '#FCE4EC', // 粉红色
    '#F3E5F5', // 兰花色
    '#E8F5E9', // 蜜瓜绿
    '#FBE9E7', // 桃花色
    '#E1F5FE', // 天空蓝
    '#FFF8E1', // 香槟色
    '#F9FBE7', // 青柠色
    '#F1F8E9', // 春绿色
    '#E8EAF6', // 淡靛蓝
    '#EDE7F6', // 淡紫罗兰
    '#F9FBE7', // 嫩绿色
    '#FFF3E0', // 杏仁色
    '#F0F4C3'  // 淡黄绿
  ];

  /// 对应的文字颜色列表
  static const List<String> _defaultTextColors = [
    '#FF6B81', // 珊瑚粉
    '#E75480', // 深粉红
    '#6DBE9D', // 海绿色
    '#64B5F6', // 蓝色
    '#FFA07A', // 淡鲑鱼色
    '#A974FF', // 紫色
    '#FBC02D', // 金黄色
    '#EC407A', // 粉红色
    '#BA68C8', // 中兰花紫
    '#66BB6A', // 绿色
    '#FF7043', // 深橙色
    '#29B6F6', // 浅蓝色
    '#FFCA28', // 琥珀色
    '#D4E157', // 柠檬绿
    '#9CCC65', // 浅绿色
    '#5C6BC0', // 靛蓝色
    '#7E57C2', // 深紫色
    '#C0CA33', // 柠檬色
    '#FFA726', // 橙色
    '#AFB42B', // 橄榄绿
  ];

  /// 获取所有标签
  Future<List<String>> getAllTags({bool forceRefresh = false}) async {
    // 如果有缓存且不强制刷新，直接返回缓存
    if (!forceRefresh && _cachedTagNames != null) {
      return _cachedTagNames!;
    }

    try {
      // 尝试从API获取标签列表
      final response = await _apiProvider.tagApi.getTagList();
      final tagNames = response.tags.map((tag) => tag.name).toList();

      // 更新内存缓存
      _cachedTagNames = tagNames;
      _cachedTagObjects = response.tags;

      // 缓存到本地
      await _cacheTagNames(tagNames);

      return tagNames;
    } catch (e) {
      print('从API获取标签失败: $e');

      // 如果有内存缓存，返回缓存
      if (_cachedTagNames != null) {
        return _cachedTagNames!;
      }

      // 如果API失败，尝试从本地缓存获取
      return await _getTagsFromCache();
    }
  }

  /// 获取完整的标签对象列表
  Future<List<Tag>> getAllTagObjects({bool forceRefresh = false}) async {
    // 如果有缓存且不强制刷新，直接返回缓存
    if (!forceRefresh && _cachedTagObjects != null) {
      return _cachedTagObjects!;
    }

    try {
      // 从API获取标签列表
      final response = await _apiProvider.tagApi.getTagList();

      // 更新内存缓存
      _cachedTagObjects = response.tags;
      _cachedTagNames = response.tags.map((tag) => tag.name).toList();

      // 缓存标签名称到本地存储
      await _cacheTagNames(_cachedTagNames!);

      return _cachedTagObjects!;
    } catch (e) {
      print('从API获取标签对象失败: $e');

      // 如果有内存缓存，返回缓存
      if (_cachedTagObjects != null) {
        return _cachedTagObjects!;
      }

      return [];
    }
  }

  /// 从本地缓存获取标签
  Future<List<String>> _getTagsFromCache() async {
    final prefs = await SharedPreferences.getInstance();
    final tagsJson = prefs.getString(_tagsKey);

    if (tagsJson != null && tagsJson.isNotEmpty) {
      try {
        final List<dynamic> tagsList = json.decode(tagsJson);
        return tagsList.cast<String>();
      } catch (e) {
        print('解析缓存标签数据失败: $e');
        return [];
      }
    } else {
      return [];
    }
  }

  /// 缓存标签名称到本地
  Future<void> _cacheTagNames(List<String> tagNames) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tagsJson = json.encode(tagNames);
      await prefs.setString(_tagsKey, tagsJson);
    } catch (e) {
      print('缓存标签失败: $e');
    }
  }

  /// 添加新标签
  Future<bool> addTag(String tagName, {String? backgroundColor, String? textColor}) async {
    if (tagName.trim().isEmpty) {
      return false;
    }

    try {
      // 如果没有提供颜色，随机选择一个颜色组合
      String bgColor;
      String txtColor;

      if (backgroundColor != null && textColor != null) {
        bgColor = backgroundColor;
        txtColor = textColor;
      } else {
        final colorPair = _getRandomColorPair();
        bgColor = colorPair['background']!;
        txtColor = colorPair['text']!;
      }

      // 调用API创建标签
      await _apiProvider.tagApi.createTag(
        tagName.trim(),
        backgroundColor: bgColor,
        textColor: txtColor,
      );

      // 创建成功，强制刷新缓存
      await refreshCache();

      return true;
    } catch (e) {
      print('创建标签失败: $e');
      return false;
    }
  }

  /// 获取随机颜色组合（背景色和对应的文字色）
  Map<String, String> _getRandomColorPair() {
    final random = Random();
    final index = random.nextInt(_defaultColors.length);
    return {
      'background': _defaultColors[index],
      'text': _defaultTextColors[index],
    };
  }

  /// 获取随机背景颜色（保持向后兼容）
  String _getRandomColor() {
    final random = Random();
    return _defaultColors[random.nextInt(_defaultColors.length)];
  }

  /// 删除标签
  Future<bool> removeTag(String tagName) async {
    try {
      // 首先获取完整的标签列表，找到对应的标签ID
      final tagObjects = await getAllTagObjects();
      final targetTag = tagObjects.firstWhere(
        (tag) => tag.name == tagName,
        orElse: () => throw Exception('标签不存在'),
      );

      // 调用API删除标签
      await _apiProvider.tagApi.deleteTag(targetTag.id);

      // 删除成功，强制刷新缓存
      await refreshCache();

      return true;
    } catch (e) {
      print('删除标签失败: $e');

      // 根据错误类型显示不同的中文提示
      String errorMessage = '删除标签失败，请重试';

      if (e.toString().contains('ApiException')) {
        // 解析API错误
        final errorStr = e.toString();
        if (errorStr.contains('203')) {
          errorMessage = '未登录或身份验证失败，请重新登录';
        } else if (errorStr.contains('313')) {
          errorMessage = '标签不存在';
        } else if (errorStr.contains('403')) {
          errorMessage = '无权限删除此标签';
        } else if (errorStr.contains('101')) {
          errorMessage = '服务不可用，请稍后重试';
        } else if (errorStr.contains('102')) {
          errorMessage = '数据库错误，请稍后重试';
        }
      } else if (e.toString().contains('标签不存在')) {
        errorMessage = '标签不存在';
      }

      // 显示错误提示
      CustomToast.show(errorMessage);

      return false;
    }
  }

  /// 更新标签名称（暂时预留）
  Future<bool> updateTag(String oldName, String newName) async {
    // TODO: 实现标签更新API
    return false;
  }

  /// 检查标签是否存在
  Future<bool> tagExists(String tagName) async {
    final currentTags = await getAllTags();
    return currentTags.contains(tagName.trim());
  }

  /// 刷新缓存
  Future<void> refreshCache() async {
    // 清空内存缓存
    _cachedTagObjects = null;
    _cachedTagNames = null;

    // 强制从API重新获取数据
    await getAllTagObjects(forceRefresh: true);
  }

  /// 清空本地缓存
  Future<bool> clearCache() async {
    try {
      // 清空内存缓存
      _cachedTagObjects = null;
      _cachedTagNames = null;

      // 清空本地存储缓存
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_tagsKey);
    } catch (e) {
      print('清空缓存失败: $e');
      return false;
    }
  }

  /// 获取标签数量
  Future<int> getTagCount() async {
    final tags = await getAllTags();
    return tags.length;
  }

  /// 搜索标签
  Future<List<String>> searchTags(String keyword) async {
    if (keyword.trim().isEmpty) {
      return await getAllTags();
    }

    final allTags = await getAllTags();
    final lowerKeyword = keyword.toLowerCase();

    return allTags.where((tag) =>
      tag.toLowerCase().contains(lowerKeyword)
    ).toList();
  }
}
