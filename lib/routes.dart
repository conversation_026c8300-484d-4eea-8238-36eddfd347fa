import 'package:flutter/material.dart';
import 'pages/app/index.dart';
import 'pages/main_tab_page.dart';
import 'pages/notes_page.dart';
import 'pages/detail_page.dart';
import 'pages/login_page.dart';
import 'pages/register_page.dart';
import 'pages/agreement_page.dart';
import 'pages/privacy_policy_page.dart';
import 'pages/debug_page.dart';
import 'pages/image_preview_page.dart';
import 'pages/search_page.dart';
import 'pages/supported_platforms_page.dart';
import 'pages/wechat_login_page.dart';
import 'pages/version_update_page.dart';
import 'pages/ai_chat_page/index.dart';
import 'pages/note_edit_page.dart';

/// 应用程序路由配置
class AppRoutes {
  // 路由名称常量
  static const String home = '/';
  static const String mainTab = '/main_tab';
  static const String notes = '/notes';
  static const String detail = '/detail';
  static const String settings = '/settings';
  static const String login = '/login';
  static const String register = '/register';
  static const String agreement = '/agreement';
  static const String privacyPolicy = '/privacy_policy';
  static const String debug = '/debug';
  static const String imagePreview = '/image_preview';
  static const String supportedPlatforms = '/supported_platforms';
  static const String search = '/search';
  static const String wechatLogin = '/wechat_login';
  static const String versionUpdate = '/version_update';
  static const String aiChat = '/ai_chat';
  static const String noteEdit = '/note_edit';

  /// 获取应用的路由表
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      home: (context) => const HomePage(title: '首页'),
      mainTab: (context) => const MainTabPage(),
      notes: (context) => const NotesPage(),
      login: (context) => const LoginPage(),
      register: (context) => const RegisterPage(),
      agreement: (context) => const AgreementPage(),
      privacyPolicy: (context) => const PrivacyPolicyPage(),
      debug: (context) => const DebugPage(title: '调试页面'),
      supportedPlatforms: (context) => const SupportedPlatformsPage(),
      search: (context) => const SearchPage(),
      wechatLogin: (context) => const WechatLoginPage(),
      versionUpdate: (context) => const VersionUpdatePage(),
      aiChat: (context) => const AiChatPage(),
      noteEdit: (context) => const NoteEditPage(),
      // 注意：带参数的路由通常不在这里定义，而是通过onGenerateRoute处理
    };
  }

  /// 处理未知路由
  static Route<dynamic> onUnknownRoute(RouteSettings settings) {
    return NoAnimationPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(
          title: const Text('页面未找到'),
        ),
        body: const Center(
          child: Text('找不到请求的页面'),
        ),
      ),
    );
  }

  /// 路由生成方法 - 使用无动画路由
  static Route<dynamic> generateRoute(RouteSettings settings) {
    // 解析路由名称和参数
    if (settings.name == detail) {
      // 提取参数
      final args = settings.arguments as Map<String, dynamic>?;
      final id = args?['id'] as String? ?? '0';

      // 使用无动画路由
      return NoAnimationPageRoute(
        settings: settings,
        builder: (context) => DetailPage(id: id),
      );
    } else if (settings.name == imagePreview) {
      // 处理图片预览页面路由
      final args = settings.arguments as Map<String, dynamic>?;
      final imageUrl = args?['imageUrl'] as String? ?? '';

      // 使用无动画路由
      return NoAnimationPageRoute(
        settings: settings,
        builder: (context) => ImagePreviewPage(imageUrl: imageUrl),
      );
    }

    // 获取路由构建函数
    final routes = getRoutes();
    final WidgetBuilder? builder = routes[settings.name];

    // 如果找到了路由，使用无动画路由
    if (builder != null) {
      return NoAnimationPageRoute(
        settings: settings,
        builder: (context) => builder(context),
      );
    }

    // 如果找不到路由，返回未知路由
    return onUnknownRoute(settings);
  }

  /// 无动画页面路由
  /// 继承PageRouteBuilder，实现无动画过渡效果
  static Route<dynamic> noAnimationRoute(Widget page, RouteSettings settings) {
    return NoAnimationPageRoute(
      settings: settings,
      builder: (_) => page,
    );
  }
}

/// 无动画页面路由
/// 自定义路由类，继承PageRoute，取消所有动画效果
class NoAnimationPageRoute<T> extends PageRoute<T> {
  NoAnimationPageRoute({
    required this.builder,
    RouteSettings? settings,
    this.maintainState = true,
    bool fullscreenDialog = false,
  }) : super(settings: settings, fullscreenDialog: fullscreenDialog);

  final WidgetBuilder builder;

  @override
  final bool maintainState;

  @override
  Duration get transitionDuration => Duration.zero;

  @override
  Color? get barrierColor => null;

  @override
  String? get barrierLabel => null;

  @override
  bool get opaque => true;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    final result = builder(context);
    return result;
  }

  @override
  Widget buildTransitions(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    // 返回原始页面，不应用任何动画
    return child;
  }
}