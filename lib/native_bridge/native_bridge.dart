import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/services.dart';

/// 原生功能桥接类
/// 用于处理与原生平台的通信，包括剪贴板监听等功能
class NativeBridge {
  // 单例模式
  static final NativeBridge _instance = NativeBridge._internal();
  factory NativeBridge() => _instance;
  NativeBridge._internal();

  // 方法通道
  static const MethodChannel _methodChannel = MethodChannel('com.xunhe.aishoucang/method_channel');
  // 事件通道，用于从原生端接收事件
  static const EventChannel _clipboardEventChannel = EventChannel('com.xunhe.aishoucang/clipboard_events');

  // 剪贴板监听回调
  Stream<String>? _clipboardStream;
  StreamSubscription<String>? _clipboardSubscription;

  // 最后一次剪贴板内容
  String? _lastClipboardContent;

  // 悬浮窗状态
  bool _isFloatingWindowEnabled = false;
  bool get isFloatingWindowEnabled => _isFloatingWindowEnabled;



  // 悬浮窗关闭事件监听器
  StreamSubscription? _floatingWindowClosedSubscription;
  Function? _onFloatingWindowClosed;

  // Toast 显示时长常量
  static const int TOAST_LENGTH_SHORT = 0;
  static const int TOAST_LENGTH_LONG = 1;

  /// 初始化桥接
  Future<void> init() async {
    try {
      developer.log('开始初始化剪贴板监听');
      await _methodChannel.invokeMethod('initClipboardMonitor');

      // 获取当前剪贴板内容作为初始值
      _lastClipboardContent = await getClipboardText();
      developer.log('初始剪贴板内容: $_lastClipboardContent');

      // 确保只有一个活跃的监听
      await _setupClipboardListener();

      developer.log('剪贴板监听初始化完成');
    } catch (e) {
      developer.log('初始化剪贴板监听失败: $e', error: e);
    }
  }

  /// 设置剪贴板监听器
  Future<void> _setupClipboardListener() async {
    // 取消之前的订阅
    await _clipboardSubscription?.cancel();

    // 创建新的订阅
    _clipboardSubscription = clipboardStream.listen(
      (String content) {
        developer.log('收到剪贴板内容变化: $content');
        if (content != _lastClipboardContent) {
          _lastClipboardContent = content;
          developer.log('剪贴板内容已更新: $content');
        }
      },
      onError: (error) {
        developer.log('剪贴板监听错误: $error', error: error);
      },
    );
  }

  /// 获取剪贴板内容流
  Stream<String> get clipboardStream {
    _clipboardStream ??= _clipboardEventChannel
        .receiveBroadcastStream()
        .map<String>((dynamic event) {
          developer.log('原生剪贴板事件: $event');
          return event.toString();
        });
    return _clipboardStream!;
  }

  /// 手动获取当前剪贴板内容
  Future<String?> getClipboardText() async {
    try {
      developer.log('手动获取剪贴板内容');
      final String? result = await _methodChannel.invokeMethod('getClipboardText');
      developer.log('获取到剪贴板内容: $result');
      return result;
    } catch (e) {
      developer.log('获取剪贴板内容失败: $e', error: e);
      return null;
    }
  }

  /// 初始化剪贴板监听
  Future<void> initClipboardMonitor() async {
    try {
      developer.log('初始化剪贴板监听');
      await _methodChannel.invokeMethod('initClipboardMonitor');
      developer.log('剪贴板监听初始化成功');

      // 如果悬浮窗已启用，确保它能正常显示
      if (_isFloatingWindowEnabled) {
        developer.log('悬浮窗已启用，确保其正常工作');
        // 重新获取当前剪贴板内容并更新悬浮窗
        final clipText = await getClipboardText();
        if (clipText != null) {
          await enableFloatingWindow();
        }
      }
    } catch (e) {
      developer.log('初始化剪贴板监听失败: $e', error: e);
    }
  }

  /// 停止剪贴板监听
  Future<void> stopClipboardMonitor() async {
    try {
      developer.log('停止剪贴板监听');
      await _clipboardSubscription?.cancel();
      _clipboardSubscription = null;
      await _methodChannel.invokeMethod('stopClipboardMonitor');
      developer.log('剪贴板监听已停止');

      // 确保悬浮窗也被关闭
      if (_isFloatingWindowEnabled) {
        await disableFloatingWindow();
      }
    } catch (e) {
      developer.log('停止剪贴板监听失败: $e', error: e);
    }
  }

  /// 检查是否有悬浮窗权限
  Future<bool> checkOverlayPermission() async {
    try {
      developer.log('检查悬浮窗权限');
      final bool hasPermission = await _methodChannel.invokeMethod('checkOverlayPermission');
      developer.log('悬浮窗权限检查结果: $hasPermission');
      return hasPermission;
    } catch (e) {
      developer.log('检查悬浮窗权限失败: $e', error: e);
      return false;
    }
  }

  /// 打开悬浮窗权限设置页面
  Future<void> openOverlayPermissionSettings() async {
    try {
      developer.log('打开悬浮窗权限设置页面');
      await _methodChannel.invokeMethod('openOverlayPermissionSettings');
    } catch (e) {
      developer.log('打开悬浮窗权限设置页面失败: $e', error: e);
    }
  }

  /// 检查是否有无障碍服务权限
  Future<bool> checkAccessibilityPermission() async {
    try {
      developer.log('检查无障碍服务权限');
      final bool hasPermission = await _methodChannel.invokeMethod('checkAccessibilityPermission');
      developer.log('无障碍服务权限检查结果: $hasPermission');
      return hasPermission;
    } catch (e) {
      developer.log('检查无障碍服务权限失败: $e', error: e);
      return false;
    }
  }

  /// 打开无障碍服务设置页面
  Future<void> openAccessibilitySettings() async {
    try {
      developer.log('打开无障碍服务设置页面');
      await _methodChannel.invokeMethod('openAccessibilitySettings');
    } catch (e) {
      developer.log('打开无障碍服务设置页面失败: $e', error: e);
    }
  }

  /// 启用悬浮窗
  Future<bool> enableFloatingWindow() async {
    try {
      developer.log('启用悬浮窗');

      // 先检查悬浮窗权限
      final bool hasOverlayPermission = await checkOverlayPermission();
      if (!hasOverlayPermission) {
        developer.log('没有悬浮窗权限，无法启用');
        return false;
      }

      // 检查无障碍服务权限（如果没有权限，会在日志中记录，但不会阻止启用悬浮窗）
      final bool hasAccessibilityPermission = await checkAccessibilityPermission();
      if (!hasAccessibilityPermission) {
        developer.log('警告：没有无障碍服务权限，悬浮窗将只能在应用前台工作');
        // 这里可以选择是否要引导用户开启无障碍服务权限
      }

      await _methodChannel.invokeMethod('enableFloatingWindow', {'enabled': true});
      _isFloatingWindowEnabled = true;
      developer.log('悬浮窗已启用');
      return true;
    } catch (e) {
      developer.log('启用悬浮窗失败: $e', error: e);
      return false;
    }
  }

  /// 禁用悬浮窗
  Future<void> disableFloatingWindow() async {
    try {
      developer.log('禁用悬浮窗');
      await _methodChannel.invokeMethod('enableFloatingWindow', {'enabled': false});
      _isFloatingWindowEnabled = false;
      developer.log('悬浮窗已禁用');
    } catch (e) {
      developer.log('禁用悬浮窗失败: $e', error: e);
    }
  }

  /// 切换悬浮窗状态
  Future<void> toggleFloatingWindow() async {
    if (isFloatingWindowEnabled) {
      await disableFloatingWindow();
    } else {
      await enableFloatingWindow();
    }
  }

  /// 检查悬浮窗是否正在显示
  Future<bool> isFloatingWindowShowing() async {
    try {
      developer.log('检查悬浮窗是否正在显示');
      final bool isShowing = await _methodChannel.invokeMethod('isFloatingWindowShowing');
      developer.log('悬浮窗显示状态: $isShowing');
      // 更新内存中的状态
      _isFloatingWindowEnabled = isShowing;
      return isShowing;
    } catch (e) {
      developer.log('检查悬浮窗显示状态失败: $e', error: e);
      return false;
    }
  }

  /// 注册悬浮窗关闭事件监听器
  void registerFloatingWindowClosedListener(Function callback) {
    // 先取消之前的监听器（如果有）
    _floatingWindowClosedSubscription?.cancel();
    _onFloatingWindowClosed = callback;

    // 注册新的监听器
    const EventChannel eventChannel = EventChannel('com.xunhe.aishoucang/floating_window_events');
    _floatingWindowClosedSubscription = eventChannel.receiveBroadcastStream().listen((event) {
      if (event == 'closed' && _onFloatingWindowClosed != null) {
        // 更新悬浮窗状态
        _isFloatingWindowEnabled = false;
        // 调用回调函数
        _onFloatingWindowClosed!();
        developer.log('收到悬浮窗关闭事件');
      }
    }, onError: (error) {
      developer.log('监听悬浮窗关闭事件出错: $error', error: error);
    });

    developer.log('已注册悬浮窗关闭事件监听器');
  }

  /// 取消注册悬浮窗关闭事件监听器
  void unregisterFloatingWindowClosedListener() {
    _floatingWindowClosedSubscription?.cancel();
    _floatingWindowClosedSubscription = null;
    _onFloatingWindowClosed = null;
    developer.log('已取消注册悬浮窗关闭事件监听器');
  }

  /// 显示分享面板
  Future<void> showPanel() async {
    try {
      developer.log('显示分享面板');
      await _methodChannel.invokeMethod('showPanel');
      developer.log('分享面板已显示');
    } catch (e) {
      developer.log('显示分享面板失败: $e', error: e);
    }
  }

  /// 显示自定义Toast
  ///
  /// [message] - 要显示的消息文本
  /// [isLong] - 是否长时间显示，默认为短暂显示
  Future<bool> showCustomToast(String message, {bool isLong = false}) async {
    try {
      developer.log('显示自定义Toast: $message, 持续时间: ${isLong ? "长" : "短"}');
      final bool result = await _methodChannel.invokeMethod('showCustomToast', {
        'message': message,
        'duration': isLong ? 3500 : 2000, // 对应Android端的LENGTH_LONG和LENGTH_SHORT
      });
      developer.log('自定义Toast显示${result ? "成功" : "失败，回退到系统Toast"}');
      return result;
    } catch (e) {
      developer.log('显示自定义Toast失败: $e', error: e);
      return false;
    }
  }

  /// 显示自定义Toast Modal
  ///
  /// [title] - 弹窗标题
  /// [content] - 弹窗内容
  /// [cancelText] - 取消按钮文本
  /// [confirmText] - 确认按钮文本
  Future<bool> showCustomToastModal({
    String title = '确认删除',
    String content = '确认要删除这个收藏吗？',
    String cancelText = '取消',
    String confirmText = '确定',
  }) async {
    try {
      developer.log('显示自定义Toast Modal: $title - $content');
      final bool result = await _methodChannel.invokeMethod('showCustomToastModal', {
        'title': title,
        'content': content,
        'cancelText': cancelText,
        'confirmText': confirmText,
      });
      developer.log('自定义Toast Modal显示${result ? "成功" : "失败，需要悬浮窗权限"}');
      return result;
    } catch (e) {
      developer.log('显示自定义Toast Modal失败: $e', error: e);
      return false;
    }
  }

  /// 查找并点击指定ID的元素
  /// 需要无障碍服务权限
  Future<bool> findAndClickElementById(String elementId) async {
    try {
      developer.log('尝试查找并点击元素: $elementId');
      final bool result = await _methodChannel.invokeMethod('findAndClickElementById', {
        'elementId': elementId,
      });
      developer.log('查找并点击元素结果: $result');
      return result;
    } catch (e) {
      developer.log('查找并点击元素失败: $e', error: e);
      return false;
    }
  }

  /// 使用 FFmpeg 下载视频
  ///
  /// [url] 视频URL
  /// [userAgent] 用户代理
  /// [referer] 引用页
  /// [outputPath] 输出路径，如果为null，则使用默认路径
  /// [saveToGallery] 是否将视频保存到相册，默认为true
  ///
  /// 返回一个包含下载结果的Map，格式为：
  /// ```dart
  /// {
  ///   'success': true/false,
  ///   'filePath': '下载文件路径', // 成功时
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> downloadVideo({
    required String url,
    String? userAgent,
    String? referer,
    String? outputPath,
    bool saveToGallery = true,
  }) async {
    try {
      developer.log('开始下载视频: $url');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('downloadVideo', {
        'url': url,
        'userAgent': userAgent,
        'referer': referer,
        'outputPath': outputPath,
        'saveToGallery': saveToGallery,
      });

      if (result == null) {
        developer.log('下载视频失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('下载视频结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('下载视频失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 将本地视频文件转换为音频
  ///
  /// [videoPath] 本地视频文件路径
  /// [outputPath] 输出路径，如果为null，则使用默认路径
  /// [audioCodec] 音频编码器，默认为libmp3lame
  /// [audioBitrate] 音频比特率，默认为192k
  ///
  /// 返回一个包含转换结果的Map，格式为：
  /// ```dart
  /// {
  ///   'success': true/false,
  ///   'filePath': '输出文件路径', // 成功时
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> convertVideoToAudio({
    required String videoPath,
    String? outputPath,
    String audioCodec = 'mp3',
    String audioBitrate = '192k',
    bool saveToGallery = true,
  }) async {
    try {
      developer.log('开始将视频转换为音频: $videoPath');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('convertVideoToAudio', {
        'videoPath': videoPath,
        'outputPath': outputPath,
        'audioCodec': audioCodec,
        'audioBitrate': audioBitrate,
        'saveToGallery': saveToGallery,
      });

      if (result == null) {
        developer.log('视频转音频失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('视频转音频结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('视频转音频失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 取消所有正在执行的 FFmpeg 任务
  Future<void> cancelFFmpegTasks() async {
    try {
      developer.log('取消所有 FFmpeg 任务');
      await _methodChannel.invokeMethod('cancelFFmpegTasks');
      developer.log('已取消所有 FFmpeg 任务');
    } catch (e) {
      developer.log('取消 FFmpeg 任务失败: $e', error: e);
    }
  }



  /// 切割音频文件
  ///
  /// [audioPath] 音频文件路径
  /// [startTime] 开始时间（秒）
  /// [duration] 持续时间（秒）
  /// [segmentIndex] 片段索引，用于生成文件名
  ///
  /// 返回一个包含切割结果的Map，格式为：
  /// ```dart
  /// {
  ///   'success': true/false,
  ///   'filePath': '输出文件路径', // 成功时
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> splitAudio({
    required String audioPath,
    required int startTime,
    required int duration,
    required int segmentIndex,
  }) async {
    try {
      developer.log('切割音频: $audioPath, 开始时间: ${startTime}s, 持续时间: ${duration}s, 片段索引: $segmentIndex');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('splitAudio', {
        'audioPath': audioPath,
        'startTime': startTime,
        'duration': duration,
        'segmentIndex': segmentIndex,
      });

      if (result == null) {
        developer.log('切割音频失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('切割音频结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('切割音频失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 使用 FFmpeg 下载抖音视频的便捷方法
  ///
  /// [url] 抖音视频URL
  /// [outputPath] 输出路径，如果为null，则使用默认路径
  Future<Map<String, dynamic>> downloadDouyinVideo({
    required String url,
    String? outputPath,
  }) async {
    const String defaultUserAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36';
    const String douyinReferer = 'https://www.douyin.com';

    return downloadVideo(
      url: url,
      userAgent: defaultUserAgent,
      referer: douyinReferer,
      outputPath: outputPath,
    );
  }

  /// 将视频转换为每隔500ms一帧的图片序列
  ///
  /// [videoPath] 视频文件路径
  /// [outputDir] 输出目录，如果为null，则使用默认路径
  Future<Map<String, dynamic>> extractFramesFromVideo({
    required String videoPath,
    String? outputDir,
  }) async {
    try {
      developer.log('开始提取视频帧: $videoPath');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('extractFramesFromVideo', {
        'videoPath': videoPath,
        'outputDir': outputDir,
      });

      if (result == null) {
        developer.log('提取视频帧失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('提取视频帧结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('提取视频帧失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 下载视频并提取文字（一体化流程）
  ///
  /// [url] 视频URL
  /// [userAgent] 用户代理
  /// [referer] 引用页
  /// [useChinese] 是否使用中文OCR
  Future<Map<String, dynamic>> downloadVideoAndExtractText({
    required String url,
    String? userAgent,
    String? referer,
    bool useChinese = true,
  }) async {
    try {
      developer.log('开始下载视频并提取文字: $url');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('downloadVideoAndExtractText', {
        'url': url,
        'userAgent': userAgent,
        'referer': referer,
        'useChinese': useChinese,
      });

      if (result == null) {
        developer.log('下载视频并提取文字失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('下载视频并提取文字结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('下载视频并提取文字失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 下载抖音视频并提取文字的便捷方法
  ///
  /// [url] 抖音视频URL
  /// [useChinese] 是否使用中文OCR
  Future<Map<String, dynamic>> downloadDouyinVideoAndExtractText({
    required String url,
    bool useChinese = true,
  }) async {
    const String defaultUserAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36';
    const String douyinReferer = 'https://www.douyin.com';

    return downloadVideoAndExtractText(
      url: url,
      userAgent: defaultUserAgent,
      referer: douyinReferer,
      useChinese: useChinese,
    );
  }

  /// 从网页提取视频链接并下载视频提取文字（一体化流程）
  ///
  /// [webpageUrl] 网页URL
  /// [timeout] 提取视频链接的超时时间（毫秒），默认30秒
  /// [useChinese] 是否使用中文OCR
  ///
  /// 返回一个包含提取结果的Map，格式为：
  /// ```dart
  /// {
  ///   'success': true/false,
  ///   'texts': ['提取到的文字1', '提取到的文字2'], // 成功时
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> extractVideoUrlAndExtractText({
    required String webpageUrl,
    int timeout = 30000,
    bool useChinese = true,
  }) async {
    try {
      developer.log('开始从网页提取视频链接并提取文字: $webpageUrl');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('extractVideoUrlAndExtractText', {
        'webpageUrl': webpageUrl,
        'timeout': timeout,
        'useChinese': useChinese,
      });

      if (result == null) {
        developer.log('从网页提取视频链接并提取文字失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('从网页提取视频链接并提取文字结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('从网页提取视频链接并提取文字失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 下载视频并通过OCR识别字幕文字
  ///
  /// [url] 视频URL
  /// [userAgent] 用户代理
  /// [referer] 引用页
  /// [useChinese] 是否使用中文OCR
  ///
  /// 返回一个包含识别结果的Map，格式为：
  /// ```dart
  /// {
  ///   'success': true/false,
  ///   'subtitles': ['字幕1', '字幕2'], // 成功时
  ///   'subtitleCount': 2, // 字幕数量
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> downloadVideoAndExtractSubtitles({
    required String url,
    String? userAgent,
    String? referer,
    bool useChinese = true,
  }) async {
    try {
      developer.log('开始下载视频并OCR识别字幕: $url');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('downloadVideoAndExtractSubtitles', {
        'url': url,
        'userAgent': userAgent,
        'referer': referer,
        'useChinese': useChinese,
      });

      if (result == null) {
        developer.log('下载视频并OCR识别字幕失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('下载视频并OCR识别字幕结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('下载视频并OCR识别字幕失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 从网页中提取视频链接
  ///
  /// 创建一个离屏的WebView，加载URL并提取视频元素的src属性
  ///
  /// [url] 网页URL
  /// [timeout] 超时时间（毫秒），默认30秒
  ///
  /// 返回一个包含提取结果的Map，格式为：
  /// ```
  /// {
  ///   'success': true/false,
  ///   'videoUrl': '视频URL', // 成功时
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> extractVideoUrlFromWebpage({
    required String url,
    int timeout = 30000,
  }) async {
    try {
      developer.log('开始从网页提取视频链接: $url');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('extractVideoUrlFromWebpage', {
        'url': url,
        'timeout': timeout,
      });

      if (result == null) {
        developer.log('提取视频链接失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('提取视频链接结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('提取视频链接失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 使用WebView打开网页
  ///
  /// [url] 网页URL
  /// [useSystemBrowser] 是否使用系统浏览器打开，默认为false（使用应用内WebView）
  ///
  /// 返回一个布尔值，表示是否成功打开网页
  Future<bool> openWebPage({
    required String url,
    bool useSystemBrowser = false,
  }) async {
    try {
      developer.log('开始打开网页: $url, 使用系统浏览器: $useSystemBrowser');
      final bool? result = await _methodChannel.invokeMethod('openWebPage', {
        'url': url,
        'useSystemBrowser': useSystemBrowser,
      });

      developer.log('打开网页结果: $result');
      return result ?? false;
    } catch (e) {
      developer.log('打开网页失败: $e', error: e);
      return false;
    }
  }

  /// 显示系统Toast
  Future<void> showToast(String message, {int duration = TOAST_LENGTH_SHORT}) async {
    try {
      await _methodChannel.invokeMethod('showToast', {
        'message': message,
        'duration': duration,
      });
    } catch (e) {
      developer.log('显示Toast失败: $e', error: e);
      rethrow;
    }
  }

  /// 将字符串值存储到原生 SharedPreferences
  ///
  /// [key] 键名
  /// [value] 要存储的字符串值
  ///
  /// 返回一个布尔值，表示是否成功存储
  Future<bool> putString(String key, String value) async {
    try {
      developer.log('存储字符串值: key=$key, value=$value');
      final bool? result = await _methodChannel.invokeMethod('putString', {
        'key': key,
        'value': value,
      });

      developer.log('存储字符串值结果: $result');
      return result ?? false;
    } catch (e) {
      developer.log('存储字符串值失败: $e', error: e);
      return false;
    }
  }

  /// 从原生 SharedPreferences 获取字符串值
  ///
  /// [key] 键名
  /// [defaultValue] 默认值，当键不存在时返回此值
  ///
  /// 返回存储的字符串值，如果键不存在则返回默认值
  Future<String> getString(String key, {String defaultValue = ''}) async {
    try {
      developer.log('获取字符串值: key=$key');
      final String? result = await _methodChannel.invokeMethod('getString', {
        'key': key,
        'defaultValue': defaultValue,
      });

      developer.log('获取字符串值结果: $result');
      return result ?? defaultValue;
    } catch (e) {
      developer.log('获取字符串值失败: $e', error: e);
      return defaultValue;
    }
  }

  /// 将布尔值存储到原生 SharedPreferences
  ///
  /// [key] 键名
  /// [value] 要存储的布尔值
  ///
  /// 返回一个布尔值，表示是否成功存储
  Future<bool> putBool(String key, bool value) async {
    try {
      developer.log('存储布尔值: key=$key, value=$value');
      final bool? result = await _methodChannel.invokeMethod('putBool', {
        'key': key,
        'value': value,
      });

      developer.log('存储布尔值结果: $result');
      return result ?? false;
    } catch (e) {
      developer.log('存储布尔值失败: $e', error: e);
      return false;
    }
  }

  /// 从原生 SharedPreferences 获取布尔值
  ///
  /// [key] 键名
  /// [defaultValue] 默认值，当键不存在时返回此值
  ///
  /// 返回存储的布尔值，如果键不存在则返回默认值
  Future<bool?> getBool(String key, {bool? defaultValue}) async {
    try {
      developer.log('获取布尔值: key=$key');
      final bool? result = await _methodChannel.invokeMethod('getBool', {
        'key': key,
        'defaultValue': defaultValue,
      });

      developer.log('获取布尔值结果: $result');
      return result ?? defaultValue;
    } catch (e) {
      developer.log('获取布尔值失败: $e', error: e);
      return defaultValue;
    }
  }

  /// 打开Scheme URL（例如：小红书的xhsdiscover://）
  ///
  /// [schemeUrl] 要打开的scheme URL
  /// [showReturnButton] 是否显示返回按钮悬浮窗，默认为true
  ///
  /// 返回一个布尔值，表示是否成功打开
  Future<bool> openSchemeUrl(String schemeUrl, {bool showReturnButton = true}) async {
    try {
      if (schemeUrl.isEmpty) {
        developer.log('Scheme URL为空，无法打开');
        return false;
      }

      developer.log('尝试打开Scheme URL: $schemeUrl');

      // 使用通用的打开URL方法，设置useSystemBrowser为true让系统选择合适的应用处理
      final bool? result = await _methodChannel.invokeMethod('openWebPage', {
        'url': schemeUrl,
        'useSystemBrowser': true,
      });

      developer.log('打开Scheme URL结果: $result');

      // 如果成功打开并且需要显示返回按钮，则显示返回按钮悬浮窗
      if (result == true && showReturnButton) {
        await this.showReturnButton();
      }

      return result ?? false;
    } catch (e) {
      developer.log('打开Scheme URL失败: $e', error: e);
      return false;
    }
  }

  /// 显示返回按钮悬浮窗
  Future<bool> showReturnButton() async {
    try {
      developer.log('显示返回按钮悬浮窗');

      // 先检查悬浮窗权限
      final bool hasOverlayPermission = await checkOverlayPermission();
      if (!hasOverlayPermission) {
        developer.log('没有悬浮窗权限，无法显示返回按钮');
        return false;
      }

      await _methodChannel.invokeMethod('showReturnButton');
      developer.log('返回按钮悬浮窗已显示');
      return true;
    } catch (e) {
      developer.log('显示返回按钮悬浮窗失败: $e', error: e);
      return false;
    }
  }

  /// 隐藏返回按钮悬浮窗
  Future<bool> hideReturnButton() async {
    try {
      developer.log('隐藏返回按钮悬浮窗');
      await _methodChannel.invokeMethod('hideReturnButton');
      developer.log('返回按钮悬浮窗已隐藏');
      return true;
    } catch (e) {
      developer.log('隐藏返回按钮悬浮窗失败: $e', error: e);
      return false;
    }
  }

  /// 检查返回按钮悬浮窗是否正在显示
  Future<bool> isReturnButtonShowing() async {
    try {
      developer.log('检查返回按钮悬浮窗是否正在显示');
      final bool? isShowing = await _methodChannel.invokeMethod('isReturnButtonShowing');
      developer.log('返回按钮悬浮窗显示状态: $isShowing');
      return isShowing ?? false;
    } catch (e) {
      developer.log('检查返回按钮悬浮窗显示状态失败: $e', error: e);
      return false;
    }
  }

  /// 将网络资源链接转换为OSS链接
  ///
  /// [url] - 要转换的网络资源链接
  /// 返回包含结果信息的Map，success表示是否成功，ossUrl表示转换后的链接，error表示错误信息
  Future<Map<String, dynamic>> convertToOssLink({required String url}) async {
    try {
      developer.log('转换资源链接: $url');
      final result = await _methodChannel.invokeMethod('convertToOssLink', {'url': url});

      // 确保返回的结果是Map类型
      if (result is Map) {
        return Map<String, dynamic>.from(result);
      } else if (result is String) {
        // 如果返回的是字符串，认为是成功的URL
        return {'success': true, 'ossUrl': result};
      } else {
        return {'success': false, 'error': '返回结果格式不正确'};
      }
    } catch (e) {
      developer.log('转换资源链接失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 上传文件到OSS
  ///
  /// [filePath] - 本地文件路径
  /// [objectKey] - OSS对象键，如果为null则自动生成
  /// 返回包含结果信息的Map，success表示是否成功，ossUrl表示OSS文件URL，error表示错误信息
  Future<Map<String, dynamic>> uploadFileToOss({
    required String filePath,
    String? objectKey,
  }) async {
    try {
      developer.log('上传文件到OSS: $filePath, objectKey: $objectKey');

      final result = await _methodChannel.invokeMethod('uploadFileToOss', {
        'filePath': filePath,
        'objectKey': objectKey,
      });

      // 确保返回的结果是Map类型
      if (result is Map) {
        return Map<String, dynamic>.from(result);
      } else if (result is String) {
        // 如果返回的是字符串，认为是成功的URL
        return {'success': true, 'ossUrl': result};
      } else {
        return {'success': false, 'error': '返回结果格式不正确'};
      }
    } catch (e) {
      developer.log('上传文件到OSS失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 从URL中提取HTML内容
  ///
  /// 创建一个离屏的WebView，加载URL并提取完整的HTML内容
  ///
  /// [url] 网页URL
  ///
  /// 返回一个包含提取结果的Map，格式为：
  /// ```
  /// {
  ///   'success': true/false,
  ///   'html': 'HTML内容', // 成功时
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> extractHtmlFromUrl({
    required String url,
  }) async {
    try {
      developer.log('开始从URL提取HTML内容: $url');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('extractHtmlFromUrl', {
        'url': url,
      });

      if (result == null) {
        developer.log('提取HTML内容失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('提取HTML内容结果: ${result['success']}');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('提取HTML内容失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 显示自定义通知面板
  ///
  /// 在通知栏显示一个包含APP图标、悬浮窗按钮和开关按钮的自定义面板
  Future<void> showCustomNotificationPanel() async {
    try {
      developer.log('显示自定义通知面板');
      await _methodChannel.invokeMethod('showCustomNotificationPanel');
      developer.log('自定义通知面板已显示');
    } catch (e) {
      developer.log('显示自定义通知面板失败: $e', error: e);
      rethrow;
    }
  }

  /// 隐藏自定义通知面板
  Future<void> hideCustomNotificationPanel() async {
    try {
      developer.log('隐藏自定义通知面板');
      await _methodChannel.invokeMethod('hideCustomNotificationPanel');
      developer.log('自定义通知面板已隐藏');
    } catch (e) {
      developer.log('隐藏自定义通知面板失败: $e', error: e);
      rethrow;
    }
  }

  /// 获取自定义通知面板开关状态
  Future<bool> getCustomNotificationPanelSwitchState() async {
    try {
      developer.log('获取自定义通知面板开关状态');
      final bool switchState = await _methodChannel.invokeMethod('getCustomNotificationPanelSwitchState');
      developer.log('自定义通知面板开关状态: $switchState');
      return switchState;
    } catch (e) {
      developer.log('获取自定义通知面板开关状态失败: $e', error: e);
      return false;
    }
  }

  /// 检查通知权限
  ///
  /// 检查应用是否有显示通知的权限
  /// Android 13+ 需要用户手动授权通知权限
  Future<bool> checkNotificationPermission() async {
    try {
      developer.log('检查通知权限');
      final bool hasPermission = await _methodChannel.invokeMethod('checkNotificationPermission');
      developer.log('通知权限检查结果: $hasPermission');
      return hasPermission;
    } catch (e) {
      developer.log('检查通知权限失败: $e', error: e);
      return false;
    }
  }

  /// 打开笔记WebView
  ///
  /// [noteId] 笔记ID（必填）
  ///
  /// 返回一个Map，包含success字段表示是否成功，error字段表示错误信息（如果有）
  Future<Map<String, dynamic>> openNoteWebview({
    required String noteId,
  }) async {
    try {
      if (noteId.isEmpty) {
        developer.log('笔记ID为空，无法打开笔记详情');
        return {
          'success': false,
          'error': '笔记ID不能为空',
        };
      }

      developer.log('打开笔记WebView: noteId=$noteId');

      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMapMethod('openNoteWebview', {
        'noteId': noteId,
      });

      if (result != null) {
        final Map<String, dynamic> resultMap = Map<String, dynamic>.from(result);
        developer.log('打开笔记WebView结果: $resultMap');
        return resultMap;
      } else {
        developer.log('打开笔记WebView失败: 返回结果为空');
        return {
          'success': false,
          'error': '返回结果为空',
        };
      }
    } catch (e) {
      developer.log('打开笔记WebView失败: $e', error: e);
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 将音频文件转换为PCM格式
  ///
  /// [audioPath] 音频文件路径
  /// [sampleRate] 采样率，默认16000Hz
  /// [channels] 声道数，默认1（单声道）
  /// [saveToGallery] 是否保存到相册，默认false
  ///
  /// 返回一个包含转换结果的Map，格式为：
  /// ```dart
  /// {
  ///   'success': true/false,
  ///   'filePath': '输出文件路径', // 成功时
  ///   'duration': 音频时长（秒）, // 成功时，从转换日志中获取
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> convertAudioToPCM({
    required String audioPath,
    int sampleRate = 16000,
    int channels = 1,
    bool saveToGallery = false,
  }) async {
    try {
      developer.log('开始将音频转换为PCM: $audioPath');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('convertAudioToPCM', {
        'audioPath': audioPath,
        'sampleRate': sampleRate,
        'channels': channels,
        'saveToGallery': saveToGallery,
      });

      if (result == null) {
        developer.log('音频转PCM失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      developer.log('音频转PCM结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      developer.log('音频转PCM失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 显示侧边栏
  Future<bool> showSidebar() async {
    try {
      developer.log('显示侧边栏');
      final bool result = await _methodChannel.invokeMethod('showSidebar');
      developer.log('侧边栏显示结果: $result');
      return result;
    } catch (e) {
      developer.log('显示侧边栏失败: $e', error: e);
      return false;
    }
  }

  /// 隐藏侧边栏
  Future<bool> hideSidebar() async {
    try {
      developer.log('隐藏侧边栏');
      await _methodChannel.invokeMethod('hideSidebar');
      developer.log('侧边栏已隐藏');
      return true;
    } catch (e) {
      developer.log('隐藏侧边栏失败: $e', error: e);
      return false;
    }
  }

  /// 检查侧边栏是否正在显示
  Future<bool> isSidebarShowing() async {
    try {
      final bool isShowing = await _methodChannel.invokeMethod('isSidebarShowing');
      developer.log('侧边栏显示状态: $isShowing');
      return isShowing;
    } catch (e) {
      developer.log('检查侧边栏状态失败: $e', error: e);
      return false;
    }
  }

  /// 在指定位置显示侧边栏
  Future<bool> showSidebarAtPosition(int x, int y) async {
    try {
      developer.log('在位置($x, $y)显示侧边栏');
      final bool result = await _methodChannel.invokeMethod('showSidebarAtPosition', {
        'x': x,
        'y': y,
      });
      developer.log('侧边栏在指定位置显示结果: $result');
      return result;
    } catch (e) {
      developer.log('在指定位置显示侧边栏失败: $e', error: e);
      return false;
    }
  }

  /// 获取侧边栏位置
  Future<Map<String, int>?> getSidebarPosition() async {
    try {
      final Map<dynamic, dynamic>? position = await _methodChannel.invokeMethod('getSidebarPosition');
      if (position != null) {
        final result = {
          'x': position['x'] as int,
          'y': position['y'] as int,
        };
        developer.log('侧边栏位置: $result');
        return result;
      }
      developer.log('侧边栏位置为空');
      return null;
    } catch (e) {
      developer.log('获取侧边栏位置失败: $e', error: e);
      return null;
    }
  }

  /// 显示侧边栏悬浮菜单
  Future<bool> showSidebarFloatingMenu() async {
    try {
      developer.log('显示侧边栏悬浮菜单');
      final bool result = await _methodChannel.invokeMethod('showSidebarFloatingMenu');
      developer.log('侧边栏悬浮菜单显示结果: $result');
      return result;
    } catch (e) {
      developer.log('显示侧边栏悬浮菜单失败: $e', error: e);
      return false;
    }
  }

  /// 隐藏侧边栏悬浮菜单
  Future<bool> hideSidebarFloatingMenu() async {
    try {
      developer.log('隐藏侧边栏悬浮菜单');
      await _methodChannel.invokeMethod('hideSidebarFloatingMenu');
      developer.log('侧边栏悬浮菜单已隐藏');
      return true;
    } catch (e) {
      developer.log('隐藏侧边栏悬浮菜单失败: $e', error: e);
      return false;
    }
  }

  /// 检查侧边栏悬浮菜单是否正在显示
  Future<bool> isSidebarFloatingMenuShowing() async {
    try {
      final bool isShowing = await _methodChannel.invokeMethod('isSidebarFloatingMenuShowing');
      developer.log('侧边栏悬浮菜单显示状态: $isShowing');
      return isShowing;
    } catch (e) {
      developer.log('检查侧边栏悬浮菜单状态失败: $e', error: e);
      return false;
    }
  }

  /// 执行业务特定的JavaScript
  ///
  /// 使用WebView加载URL并执行业务特定的JavaScript文件
  ///
  /// [url] 网页URL
  /// [businessName] 业务名称，对应assets/js/目录下的JavaScript文件名
  /// [replacements] 替换参数的键值对，默认为空
  ///
  /// 返回一个包含执行结果的Map，格式为：
  /// ```
  /// {
  ///   'success': true/false,
  ///   'result': 'JavaScript执行结果', // 成功时
  ///   'error': '错误信息'  // 失败时
  /// }
  /// ```
  Future<Map<String, dynamic>> executeBusinessJavaScript({
    required String url,
    required String businessName,
    Map<String, String> replacements = const {},
  }) async {
    try {
      developer.log('开始执行业务JavaScript: $businessName, URL: $url');
      final Map<dynamic, dynamic>? result = await _methodChannel.invokeMethod('executeBusinessJavaScript', {
        'url': url,
        'businessName': businessName,
        'replacements': replacements,
      });

      if (result == null) {
        developer.log('执行业务JavaScript失败: 返回结果为空');
        return {'success': false, 'error': '返回结果为空'};
      }

      final bool success = result['success'] as bool? ?? false;
      if (success) {
        final String jsResult = result['result'] as String? ?? '';
        developer.log('执行业务JavaScript成功: $jsResult');
        return {'success': true, 'result': jsResult};
      } else {
        final String error = result['error'] as String? ?? '未知错误';
        developer.log('执行业务JavaScript失败: $error');
        return {'success': false, 'error': error};
      }
    } catch (e) {
      developer.log('执行业务JavaScript异常: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }
}
