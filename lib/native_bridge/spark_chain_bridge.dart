import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'dart:async';
import 'config_bridge.dart';
import '../utils/audio_segment_processor.dart';

/// SparkChain 大模型识别桥接类
/// 封装与原生 SparkChain SDK 的交互
class SparkChainBridge {
  static const MethodChannel _channel = MethodChannel('com.xunhe.aishoucang/method_channel');
  static const EventChannel _progressEventChannel = EventChannel('com.xunhe.aishoucang/progress_events');

  static StreamSubscription? _progressSubscription;

  /// 从配置文件初始化 SparkChain SDK
  ///
  /// 自动从配置中读取讯飞的 APPID、APIKey、APISecret
  /// [logLevel] 日志级别 (0-5, 100为关闭)
  /// [logPath] 日志路径 (可选)
  /// [uid] 用户标识 (可选)
  static Future<Map<String, dynamic>> initSparkChainFromConfig({
    int logLevel = 2,
    String? logPath,
    String? uid,
  }) async {
    try {
      // 从配置中读取讯飞参数
      final appId = ConfigBridge.getString('xunfei_APPID');
      final apiKey = ConfigBridge.getString('xunfei_APIKey');
      final apiSecret = ConfigBridge.getString('xunfei_APISecret');

      if (appId.isEmpty || apiKey.isEmpty || apiSecret.isEmpty) {
        return {
          'success': false,
          'message': '配置文件中缺少讯飞SDK参数，请检查 xunfei_APPID、xunfei_APIKey、xunfei_APISecret 配置',
        };
      }

      return await initSparkChain(
        appId: appId,
        apiKey: apiKey,
        apiSecret: apiSecret,
        logLevel: logLevel,
        logPath: logPath,
        uid: uid,
      );
    } catch (e) {
      return {
        'success': false,
        'message': '从配置初始化失败: $e',
      };
    }
  }

  /// 初始化 SparkChain SDK
  ///
  /// [appId] 应用ID
  /// [apiKey] API密钥
  /// [apiSecret] API密钥
  /// [logLevel] 日志级别 (0-5, 100为关闭)
  /// [logPath] 日志路径 (可选)
  /// [uid] 用户标识 (可选)
  static Future<Map<String, dynamic>> initSparkChain({
    required String appId,
    required String apiKey,
    required String apiSecret,
    int logLevel = 2,
    String? logPath,
    String? uid,
  }) async {
    try {
      final result = await _channel.invokeMethod('initSparkChain', {
        'appId': appId,
        'apiKey': apiKey,
        'apiSecret': apiSecret,
        'logLevel': logLevel,
        'logPath': logPath,
        'uid': uid,
      });
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'message': '初始化失败: $e',
      };
    }
  }

  /// 检查 SparkChain SDK 是否已初始化
  static Future<bool> isSparkChainInitialized() async {
    try {
      final result = await _channel.invokeMethod('isSparkChainInitialized');
      return result as bool;
    } catch (e) {
      return false;
    }
  }

  /// 启动语音识别
  /// 
  /// [sessionId] 会话ID，用于区分不同的识别会话
  /// [language] 识别语种 (zh_cn: 中文, mul_cn: 多语种)
  /// [domain] 应用领域 (slm: 大模型识别)
  /// [accent] 方言 (mandarin: 中文普通话)
  /// [sampleRate] 采样率 (8000 或 16000)
  /// [encoding] 编码格式 (raw, speex, speex-wb, lame)
  /// [channels] 声道数 (1: 单声道, 2: 双声道)
  /// [bitdepth] 位深 (8 或 16)
  /// [frameSize] 帧大小 (0-1024)
  /// 其他配置参数...
  static Future<void> startRecognition({
    String sessionId = 'default',
    String language = 'zh_cn',
    String domain = 'slm',
    String accent = 'mandarin',
    int sampleRate = 16000,
    String encoding = 'raw',
    int channels = 1,
    int bitdepth = 16,
    int frameSize = 0,
    // 功能参数
    int? vgap,
    int? vadEos,
    bool? vinfo,
    String? dwa,
    bool? ptt,
    bool? smth,
    bool? nunum,
    String? rlang,
    String? ln,
  }) async {
    final params = <String, dynamic>{
      'sessionId': sessionId,
      'language': language,
      'domain': domain,
      'accent': accent,
      'sampleRate': sampleRate,
      'encoding': encoding,
      'channels': channels,
      'bitdepth': bitdepth,
      'frameSize': frameSize,
    };

    // 添加可选参数
    if (vgap != null) params['vgap'] = vgap;
    if (vadEos != null) params['vadEos'] = vadEos;
    if (vinfo != null) params['vinfo'] = vinfo;
    if (dwa != null) params['dwa'] = dwa;
    if (ptt != null) params['ptt'] = ptt;
    if (smth != null) params['smth'] = smth;
    if (nunum != null) params['nunum'] = nunum;
    if (rlang != null) params['rlang'] = rlang;
    if (ln != null) params['ln'] = ln;

    await _channel.invokeMethod('startSparkChainRecognition', params);
  }

  /// 发送音频数据
  /// 
  /// [audioData] 音频数据字节数组
  static Future<Map<String, dynamic>> writeAudioData(Uint8List audioData) async {
    try {
      final result = await _channel.invokeMethod('writeSparkChainAudioData', {
        'audioData': audioData,
      });
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'message': '发送音频数据失败: $e',
      };
    }
  }

  /// 停止语音识别
  ///
  /// [immediate] 是否立即停止 (true: 立即停止, false: 等待最终结果)
  static Future<Map<String, dynamic>> stopRecognition({bool immediate = false}) async {
    try {
      final result = await _channel.invokeMethod('stopSparkChainRecognition', {
        'immediate': immediate,
      });
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'message': '停止识别失败: $e',
      };
    }
  }

  /// 初始化进度监听
  static void initProgressListener() {
    print('🚀 开始初始化进度监听器...');
    _progressSubscription?.cancel();
    _progressSubscription = _progressEventChannel.receiveBroadcastStream().listen(
      (dynamic event) {
        print('📨 收到原始事件: $event (类型: ${event.runtimeType})');
        if (event is Map) {
          // 将 Map<Object?, Object?> 转换为 Map<String, dynamic>
          final convertedEvent = Map<String, dynamic>.from(event);
          print('✅ 事件格式转换成功，转发给AudioSegmentProcessor');
          AudioSegmentProcessor.handleProgressEvent(convertedEvent);
        } else {
          print('❌ 事件格式错误，期望Map，实际: ${event.runtimeType}');
        }
      },
      onError: (error) {
        print('❌ 进度事件监听错误: $error');
      },
    );
    print('✅ 进度事件监听器已初始化');
  }

  /// 清理进度监听
  static void cleanupProgressListener() {
    _progressSubscription?.cancel();
    _progressSubscription = null;
    print('进度事件监听器已清理');
  }

  /// 识别音频文件
  ///
  /// [audioFilePath] 音频文件路径
  /// [language] 识别语种 (zh_cn: 中文, en_us: 英文)
  /// [domain] 应用领域 (slm: 大模型识别, iat: 日常用语)
  /// [accent] 方言 (mandarin: 普通话)
  static Future<Map<String, dynamic>> recognizeAudioFile({
    required String audioFilePath,
    String language = 'zh_cn',
    String domain = 'slm',
    String accent = 'mandarin',
  }) async {
    try {
      final result = await _channel.invokeMethod('recognizeAudioFile', {
        'audioFilePath': audioFilePath,
        'language': language,
        'domain': domain,
        'accent': accent,
      });
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'message': '识别音频文件失败: $e',
      };
    }
  }

  /// 识别音频文件（支持进度回调）
  ///
  /// [audioFilePath] 音频文件路径
  /// [language] 识别语种 (zh_cn: 中文, en_us: 英文)
  /// [domain] 应用领域 (slm: 大模型识别, iat: 日常用语)
  /// [accent] 方言 (mandarin: 普通话)
  /// [sessionId] 会话ID，用于区分不同的识别任务
  static Future<Map<String, dynamic>> recognizeAudioFileWithProgress({
    required String audioFilePath,
    String language = 'zh_cn',
    String domain = 'slm',
    String accent = 'mandarin',
    String? sessionId,
  }) async {
    try {
      final result = await _channel.invokeMethod('recognizeAudioFileWithProgress', {
        'audioFilePath': audioFilePath,
        'language': language,
        'domain': domain,
        'accent': accent,
        'sessionId': sessionId ?? 'session_${DateTime.now().millisecondsSinceEpoch}',
      });
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'message': '识别音频文件失败: $e',
      };
    }
  }

  /// 检查是否有活跃的识别会话
  static Future<bool> isRecognitionActive() async {
    try {
      final result = await _channel.invokeMethod('isSparkChainRecognitionActive');
      return result as bool;
    } catch (e) {
      return false;
    }
  }

  /// 逆初始化 SparkChain SDK
  static Future<Map<String, dynamic>> uninitSparkChain() async {
    try {
      final result = await _channel.invokeMethod('uninitSparkChain');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'message': '逆初始化失败: $e',
      };
    }
  }
}

/// SparkChain 识别结果数据类
class SparkChainResult {
  final bool success;
  final String? text;
  final int? status; // 0: 开始, 1: 中间, 2: 结束
  final String? sid;
  final String? sessionId;
  final int? errorCode;
  final String? errorMessage;

  SparkChainResult({
    required this.success,
    this.text,
    this.status,
    this.sid,
    this.sessionId,
    this.errorCode,
    this.errorMessage,
  });

  factory SparkChainResult.fromMap(Map<String, dynamic> map) {
    return SparkChainResult(
      success: map['success'] ?? false,
      text: map['text'],
      status: map['status'],
      sid: map['sid'],
      sessionId: map['sessionId'],
      errorCode: map['errorCode'],
      errorMessage: map['errorMessage'],
    );
  }

  /// 是否为最终结果
  bool get isFinal => status == 2;

  /// 是否为中间结果
  bool get isPartial => status == 1;

  /// 是否为开始结果
  bool get isStart => status == 0;

  @override
  String toString() {
    return 'SparkChainResult{success: $success, text: $text, status: $status, sid: $sid, sessionId: $sessionId, errorCode: $errorCode, errorMessage: $errorMessage}';
  }
}

/// SparkChain 识别状态枚举
enum SparkChainStatus {
  /// 未初始化
  uninitialized,
  /// 已初始化
  initialized,
  /// 识别中
  recognizing,
  /// 识别完成
  completed,
  /// 错误状态
  error,
}

/// SparkChain 语言类型
class SparkChainLanguage {
  static const String chinese = 'zh_cn';
  static const String multiLanguage = 'mul_cn';
}

/// SparkChain 领域类型
class SparkChainDomain {
  static const String largeModel = 'slm';
}

/// SparkChain 方言类型
class SparkChainAccent {
  static const String mandarin = 'mandarin';
}

/// SparkChain 编码格式
class SparkChainEncoding {
  static const String raw = 'raw';
  static const String speex = 'speex';
  static const String speexWb = 'speex-wb';
  static const String lame = 'lame';
}
