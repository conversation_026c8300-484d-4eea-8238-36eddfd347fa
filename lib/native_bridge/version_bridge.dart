import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';

/// 版本信息桥接类
/// 用于从原生端获取应用版本信息
class VersionBridge {
  // 方法通道
  static const MethodChannel _methodChannel = MethodChannel('com.xunhe.aishoucang/method_channel');

  /// 获取应用版本信息
  /// 返回包含版本信息的Map
  static Future<Map<String, String>> getVersionInfo() async {
    try {
      developer.log('获取应用版本信息');
      final Map<dynamic, dynamic>? versionInfo = await _methodChannel.invokeMapMethod('getVersionInfo');
      
      if (versionInfo != null) {
        final result = Map<String, String>.from(versionInfo);
        developer.log('版本信息获取成功: $result');
        return result;
      } else {
        developer.log('版本信息获取失败: 返回结果为空');
        return {
          'versionName': '1.0.0',
          'versionCode': '1',
          'packageName': 'com.xunhe.aishoucang',
        };
      }
    } catch (e) {
      developer.log('获取版本信息失败: $e', error: e);
      return {
        'versionName': '1.0.0',
        'versionCode': '1',
        'packageName': 'com.xunhe.aishoucang',
      };
    }
  }

  /// 获取版本名称
  static Future<String> getVersionName() async {
    final versionInfo = await getVersionInfo();
    return versionInfo['versionName'] ?? '1.0.0';
  }

  /// 获取版本代码
  static Future<String> getVersionCode() async {
    final versionInfo = await getVersionInfo();
    return versionInfo['versionCode'] ?? '1';
  }

  /// 获取包名
  static Future<String> getPackageName() async {
    final versionInfo = await getVersionInfo();
    return versionInfo['packageName'] ?? 'com.xunhe.aishoucang';
  }
}
