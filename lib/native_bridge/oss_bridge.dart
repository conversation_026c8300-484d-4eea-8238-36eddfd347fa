import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';

/// OSS桥接类
/// 用于调用原生OSS功能
class OssBridge {
  // 方法通道
  static const MethodChannel _methodChannel = MethodChannel('com.xunhe.aishoucang/method_channel');

  /// 上传文件到OSS
  ///
  /// [filePath] - 本地文件路径
  /// [objectKey] - OSS对象键，如果为null则自动生成
  /// 返回包含结果信息的Map，success表示是否成功，ossUrl表示OSS文件URL，error表示错误信息
  static Future<Map<String, dynamic>> uploadFileToOss({
    required String filePath,
    String? objectKey,
  }) async {
    try {
      developer.log('上传文件到OSS: $filePath, objectKey: $objectKey');
      
      final result = await _methodChannel.invokeMethod('uploadFileToOss', {
        'filePath': filePath,
        'objectKey': objectKey,
      });

      // 确保返回的结果是Map类型
      if (result is Map) {
        return Map<String, dynamic>.from(result);
      } else if (result is String) {
        // 如果返回的是字符串，认为是成功的URL
        return {'success': true, 'ossUrl': result};
      } else {
        return {'success': false, 'error': '返回结果格式不正确'};
      }
    } catch (e) {
      developer.log('上传文件到OSS失败: $e', error: e);
      return {'success': false, 'error': e.toString()};
    }
  }
}
