import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';

/// 下载桥接类
///
/// 用于与原生端进行下载相关的通信
class DownloadBridge {
  // 方法通道
  static const MethodChannel _methodChannel = MethodChannel('com.xunhe.aishoucang/method_channel');

  /// 下载应用
  ///
  /// [url] 下载地址
  /// [onSuccess] 成功回调，返回本地文件路径
  /// [onError] 失败回调，返回错误信息
  static Future<void> downloadApp({
    required String url,
    Function(String filePath)? onSuccess,
    Function(String error)? onError,
  }) async {
    try {
      developer.log('开始下载应用: $url');

      // 调用原生下载方法
      final result = await _methodChannel.invokeMethod('downloadApp', {
        'url': url,
      });

      if (result != null && result is Map) {
        final success = result['success'] as bool? ?? false;
        if (success) {
          final filePath = result['filePath'] as String? ?? '';
          developer.log('应用下载成功: $filePath');
          onSuccess?.call(filePath);
        } else {
          final error = result['error'] as String? ?? '下载失败';
          developer.log('应用下载失败: $error');
          onError?.call(error);
        }
      } else {
        onError?.call('下载返回结果异常');
      }

    } catch (e) {
      developer.log('下载应用异常: $e');
      onError?.call('下载异常: $e');
    }
  }

  /// 安装应用
  ///
  /// [filePath] APK文件路径
  /// 返回安装结果：
  /// - true: 成功启动安装流程
  /// - false: 安装失败（可能是权限不足或文件不存在）
  static Future<bool> installApp(String filePath) async {
    try {
      developer.log('安装应用: $filePath');

      // 先检查是否有安装权限
      final hasPermission = await hasInstallPermission();
      if (!hasPermission) {
        developer.log('没有安装权限，需要用户授权');
        return false;
      }

      final result = await _methodChannel.invokeMethod('installApp', {
        'filePath': filePath,
      });

      return result as bool? ?? false;
    } catch (e) {
      developer.log('安装应用异常: $e');
      return false;
    }
  }

  /// 安装应用（带权限检查和引导）
  ///
  /// [filePath] APK文件路径
  /// [autoRequestPermission] 是否自动请求权限（默认true）
  /// 返回安装结果和权限状态
  static Future<Map<String, dynamic>> installAppWithPermissionCheck(
    String filePath, {
    bool autoRequestPermission = true,
  }) async {
    try {
      developer.log('安装应用（带权限检查）: $filePath');

      // 检查是否有安装权限
      final hasPermission = await hasInstallPermission();

      if (!hasPermission) {
        developer.log('没有安装权限');

        // 不在这里自动跳转到设置页面，避免与UI层的权限弹窗冲突
        // 让UI层决定如何处理权限问题
        return {
          'success': false,
          'needPermission': true,
          'message': '需要安装未知来源应用权限',
        };
      }

      // 有权限，直接安装
      final success = await installApp(filePath);

      return {
        'success': success,
        'needPermission': false,
        'message': success ? '安装流程已启动' : '安装失败',
      };

    } catch (e) {
      developer.log('安装应用异常: $e');
      return {
        'success': false,
        'needPermission': false,
        'message': '安装异常: $e',
      };
    }
  }

  /// 取消下载
  ///
  /// 取消正在进行的下载任务
  static Future<void> cancelDownload() async {
    try {
      developer.log('取消下载');
      await _methodChannel.invokeMethod('cancelDownload');
    } catch (e) {
      developer.log('取消下载异常: $e');
    }
  }

  /// 检查是否有安装权限
  ///
  /// 检查是否有安装未知来源应用的权限
  static Future<bool> hasInstallPermission() async {
    try {
      final result = await _methodChannel.invokeMethod('hasInstallPermission');
      return result as bool? ?? false;
    } catch (e) {
      developer.log('检查安装权限异常: $e');
      return false;
    }
  }

  /// 请求安装权限
  ///
  /// 打开设置页面请求安装未知来源应用的权限
  static Future<void> requestInstallPermission() async {
    try {
      developer.log('请求安装权限');
      await _methodChannel.invokeMethod('requestInstallPermission');
    } catch (e) {
      developer.log('请求安装权限异常: $e');
    }
  }

  /// 获取下载目录
  ///
  /// 获取应用下载文件的存储目录
  static Future<String?> getDownloadDirectory() async {
    try {
      final result = await _methodChannel.invokeMethod('getDownloadDirectory');
      return result as String?;
    } catch (e) {
      developer.log('获取下载目录异常: $e');
      return null;
    }
  }

  /// 清理下载缓存
  ///
  /// 清理下载目录中的临时文件
  static Future<void> cleanDownloadCache() async {
    try {
      developer.log('清理下载缓存');
      await _methodChannel.invokeMethod('cleanDownloadCache');
    } catch (e) {
      developer.log('清理下载缓存异常: $e');
    }
  }

  // 防止实例化
  DownloadBridge._();
}
