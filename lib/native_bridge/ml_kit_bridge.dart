import 'dart:io';
import 'package:flutter/services.dart';

/// ML Kit 桥接类
/// 用于调用原生Android的ML Kit功能
class MLKitBridge {
  /// 方法通道
  static const MethodChannel _channel =
      MethodChannel('com.xunhe.aishoucang/method_channel');

  /// 单例实例
  static final MLKitBridge _instance = MLKitBridge._internal();

  /// 工厂构造函数
  factory MLKitBridge() => _instance;

  /// 内部构造函数
  MLKitBridge._internal();

  /// 获取单例实例
  static MLKitBridge get instance => _instance;

  /// 文字识别
  /// [imageFile] 图片文件
  /// [useChinese] 是否使用中文识别器
  Future<Map<String, dynamic>> recognizeText(
    File imageFile, {
    bool useChinese = true,
  }) async {
    try {
      if (!Platform.isAndroid) {
        throw PlatformException(
          code: 'UNSUPPORTED_PLATFORM',
          message: '当前平台不支持ML Kit功能',
        );
      }

      final result = await _channel.invokeMethod<Map<dynamic, dynamic>>(
        'recognizeText',
        {
          'imageUri': imageFile.path,
          'useChinese': useChinese,
        },
      );

      if (result == null) {
        throw PlatformException(
          code: 'NULL_RESULT',
          message: '识别结果为空',
        );
      }

      return _convertDynamicMap(result);
    } catch (e) {
      rethrow;
    }
  }

  /// 图像标签识别
  /// [imageFile] 图片文件
  Future<List<Map<String, dynamic>>> labelImage(File imageFile) async {
    try {
      if (!Platform.isAndroid) {
        throw PlatformException(
          code: 'UNSUPPORTED_PLATFORM',
          message: '当前平台不支持ML Kit功能',
        );
      }

      final result = await _channel.invokeMethod<List<dynamic>>(
        'labelImage',
        {
          'imageUri': imageFile.path,
        },
      );

      if (result == null) {
        throw PlatformException(
          code: 'NULL_RESULT',
          message: '识别结果为空',
        );
      }

      return result
          .map((item) => _convertDynamicMap(item as Map<dynamic, dynamic>))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  /// 人脸检测
  /// [imageFile] 图片文件
  Future<List<Map<String, dynamic>>> detectFaces(File imageFile) async {
    try {
      if (!Platform.isAndroid) {
        throw PlatformException(
          code: 'UNSUPPORTED_PLATFORM',
          message: '当前平台不支持ML Kit功能',
        );
      }

      final result = await _channel.invokeMethod<List<dynamic>>(
        'detectFaces',
        {
          'imageUri': imageFile.path,
        },
      );

      if (result == null) {
        throw PlatformException(
          code: 'NULL_RESULT',
          message: '检测结果为空',
        );
      }

      return result
          .map((item) => _convertDynamicMap(item as Map<dynamic, dynamic>))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  /// 条形码/二维码扫描
  /// [imageFile] 图片文件
  Future<List<Map<String, dynamic>>> scanBarcodes(File imageFile) async {
    try {
      if (!Platform.isAndroid) {
        throw PlatformException(
          code: 'UNSUPPORTED_PLATFORM',
          message: '当前平台不支持ML Kit功能',
        );
      }

      final result = await _channel.invokeMethod<List<dynamic>>(
        'scanBarcodes',
        {
          'imageUri': imageFile.path,
        },
      );

      if (result == null) {
        throw PlatformException(
          code: 'NULL_RESULT',
          message: '扫描结果为空',
        );
      }

      return result
          .map((item) => _convertDynamicMap(item as Map<dynamic, dynamic>))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  /// 将动态Map转换为静态Map
  Map<String, dynamic> _convertDynamicMap(Map<dynamic, dynamic> map) {
    return map.map((key, value) {
      if (value is Map<dynamic, dynamic>) {
        return MapEntry(key.toString(), _convertDynamicMap(value));
      } else if (value is List<dynamic>) {
        return MapEntry(
          key.toString(),
          value.map((item) {
            if (item is Map<dynamic, dynamic>) {
              return _convertDynamicMap(item);
            }
            return item;
          }).toList(),
        );
      }
      return MapEntry(key.toString(), value);
    });
  }
}
