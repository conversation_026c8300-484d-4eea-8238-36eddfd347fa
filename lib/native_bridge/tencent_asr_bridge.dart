import 'package:flutter/services.dart';
import 'dart:io';

/// 腾讯云ASR桥接类
/// 封装与原生腾讯云ASR SDK的交互
class TencentAsrBridge {
  static const MethodChannel _channel = MethodChannel('com.xunhe.aishoucang/method_channel');

  /// 单例实例
  static final TencentAsrBridge _instance = TencentAsrBridge._internal();

  /// 工厂构造函数
  factory TencentAsrBridge() => _instance;

  /// 内部构造函数
  TencentAsrBridge._internal();

  /// 获取单例实例
  static TencentAsrBridge get instance => _instance;

  /// 初始化腾讯云ASR SDK
  /// 
  /// 从配置文件中自动读取appId、secretId、secretKey
  static Future<Map<String, dynamic>> initTencentAsrSdk() async {
    try {
      final result = await _channel.invokeMethod('initTencentAsrSdk');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'error': '初始化腾讯云ASR SDK失败: $e',
      };
    }
  }

  /// 通过音频数据进行语音识别
  /// 
  /// [audioData] 音频数据
  /// [voiceFormat] 音频格式 (mp3, wav, m4a等)
  /// [engineModelType] 引擎模型类型 (16k_zh: 16k中文通用, 8k_zh: 8k中文通用等)
  /// [recognitionType] 识别类型 (oneSentence: 一句话识别, flash: 录音文件识别极速版)
  static Future<Map<String, dynamic>> recognizeAudioByData({
    required List<int> audioData,
    String voiceFormat = 'mp3',
    String engineModelType = '16k_zh',
    String recognitionType = 'flash',
  }) async {
    try {
      final result = await _channel.invokeMethod('recognizeAudioByData', {
        'audioData': audioData,
        'voiceFormat': voiceFormat,
        'engineModelType': engineModelType,
        'recognitionType': recognitionType,
      });
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'error': '音频数据识别失败: $e',
      };
    }
  }

  /// 通过音频文件进行语音识别
  /// 
  /// [filePath] 音频文件路径
  /// [voiceFormat] 音频格式 (mp3, wav, m4a等)
  /// [engineModelType] 引擎模型类型 (16k_zh: 16k中文通用, 8k_zh: 8k中文通用等)
  /// [recognitionType] 识别类型 (oneSentence: 一句话识别, flash: 录音文件识别极速版)
  static Future<Map<String, dynamic>> recognizeAudioByFile({
    required String filePath,
    String voiceFormat = 'mp3',
    String engineModelType = '16k_zh',
    String recognitionType = 'flash',
  }) async {
    try {
      // 检查文件是否存在
      final file = File(filePath);
      if (!file.existsSync()) {
        return {
          'success': false,
          'error': '音频文件不存在: $filePath',
        };
      }

      final result = await _channel.invokeMethod('recognizeAudioByFile', {
        'filePath': filePath,
        'voiceFormat': voiceFormat,
        'engineModelType': engineModelType,
        'recognitionType': recognitionType,
      });
      return Map<String, dynamic>.from(result);
    } catch (e) {
      return {
        'success': false,
        'error': '音频文件识别失败: $e',
      };
    }
  }

  /// 一句话识别 - 通过音频数据
  /// 
  /// [audioData] 音频数据
  /// [voiceFormat] 音频格式
  /// [engineModelType] 引擎模型类型
  static Future<Map<String, dynamic>> recognizeOneSentenceByData({
    required List<int> audioData,
    String voiceFormat = 'mp3',
    String engineModelType = '16k_zh',
  }) async {
    return recognizeAudioByData(
      audioData: audioData,
      voiceFormat: voiceFormat,
      engineModelType: engineModelType,
      recognitionType: 'oneSentence',
    );
  }

  /// 一句话识别 - 通过音频文件
  /// 
  /// [filePath] 音频文件路径
  /// [voiceFormat] 音频格式
  /// [engineModelType] 引擎模型类型
  static Future<Map<String, dynamic>> recognizeOneSentenceByFile({
    required String filePath,
    String voiceFormat = 'mp3',
    String engineModelType = '16k_zh',
  }) async {
    return recognizeAudioByFile(
      filePath: filePath,
      voiceFormat: voiceFormat,
      engineModelType: engineModelType,
      recognitionType: 'oneSentence',
    );
  }

  /// 录音文件识别极速版 - 通过音频数据
  /// 
  /// [audioData] 音频数据
  /// [voiceFormat] 音频格式
  /// [engineModelType] 引擎模型类型
  static Future<Map<String, dynamic>> recognizeFlashByData({
    required List<int> audioData,
    String voiceFormat = 'mp3',
    String engineModelType = '16k_zh',
  }) async {
    return recognizeAudioByData(
      audioData: audioData,
      voiceFormat: voiceFormat,
      engineModelType: engineModelType,
      recognitionType: 'flash',
    );
  }

  /// 录音文件识别极速版 - 通过音频文件
  /// 
  /// [filePath] 音频文件路径
  /// [voiceFormat] 音频格式
  /// [engineModelType] 引擎模型类型
  static Future<Map<String, dynamic>> recognizeFlashByFile({
    required String filePath,
    String voiceFormat = 'mp3',
    String engineModelType = '16k_zh',
  }) async {
    return recognizeAudioByFile(
      filePath: filePath,
      voiceFormat: voiceFormat,
      engineModelType: engineModelType,
      recognitionType: 'flash',
    );
  }
}
