import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';

/// 配置桥接类
/// 用于从原生端获取配置，使用动态方法访问
class ConfigBridge {
  // 方法通道
  static const MethodChannel _methodChannel = MethodChannel('com.xunhe.aishoucang/method_channel');

  // 配置缓存
  static Map<String, dynamic> _configCache = {};

  /// 初始化配置
  static Future<void> init() async {
    try {
      developer.log('初始化配置桥接');
      final Map<dynamic, dynamic>? config = await _methodChannel.invokeMapMethod('getConfig');
      
      if (config != null) {
        _configCache.clear();
        _configCache.addAll(Map<String, dynamic>.from(config));
        developer.log('配置获取成功: $_configCache');
      } else {
        developer.log('配置获取失败');
        _configCache.clear();
      }
    } catch (e) {
      developer.log('初始化配置桥接失败: $e', error: e);
      _configCache.clear();
    }
  }
  
  /// 动态获取任意配置项
  /// 用法: ConfigBridge.getValue<类型>('key名', 默认值)
  static T? getValue<T>(String key, [T? defaultValue]) {
    final value = _configCache[key];
    if (value == null) {
      return defaultValue;
    }
    
    try {
      return value as T;
    } catch (e) {
      developer.log('配置类型转换失败: $key, $e', error: e);
      return defaultValue;
    }
  }
  
  /// 获取字符串配置
  /// 用法: ConfigBridge.getString('key名', '默认值')
  static String getString(String key, [String defaultValue = '']) {
    return getValue<String>(key, defaultValue) ?? defaultValue;
  }
  
  /// 获取整数配置
  /// 用法: ConfigBridge.getInt('key名', 默认值)
  static int getInt(String key, [int defaultValue = 0]) {
    return getValue<int>(key, defaultValue) ?? defaultValue;
  }
  
  /// 获取布尔配置
  /// 用法: ConfigBridge.getBool('key名', 默认值)
  static bool getBool(String key, [bool defaultValue = false]) {
    return getValue<bool>(key, defaultValue) ?? defaultValue;
  }
  
  /// 获取浮点数配置
  /// 用法: ConfigBridge.getDouble('key名', 默认值)
  static double getDouble(String key, [double defaultValue = 0.0]) {
    return getValue<double>(key, defaultValue) ?? defaultValue;
  }
  
  /// 获取所有配置
  /// 用法: ConfigBridge.getAllConfig()
  static Map<String, dynamic> getAllConfig() {
    return Map<String, dynamic>.from(_configCache);
  }
  
  // 防止实例化
  ConfigBridge._();
} 