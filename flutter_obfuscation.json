{"description": "Flutter代码混淆配置文件", "obfuscation": {"enabled": true, "split_debug_info": true, "dart_defines": {"FLUTTER_WEB_AUTO_DETECT": "false"}}, "build_commands": {"android_release_obfuscated": "flutter build apk --release --obfuscate --split-debug-info=build/app/outputs/symbols --dart-define=APP_ENV=prd", "android_release_standard": "flutter build apk --release --dart-define=APP_ENV=pre", "ios_release_obfuscated": "flutter build ios --release --obfuscate --split-debug-info=build/ios/symbols --dart-define=APP_ENV=prd"}, "notes": ["使用 --obfuscate 参数启用Dart代码混淆", "使用 --split-debug-info 将调试信息分离到单独文件", "生产环境(prd)使用完整混淆，预发布环境(pre)使用标准混淆", "混淆后的符号表保存在build/app/outputs/symbols目录"]}