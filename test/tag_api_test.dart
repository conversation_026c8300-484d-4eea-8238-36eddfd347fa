import 'package:flutter_test/flutter_test.dart';
import 'package:aishoucang/api/tag_api.dart';
import 'package:aishoucang/api/api_client.dart';
import 'package:aishoucang/models/tag.dart';

void main() {
  group('TagApi 删除标签测试', () {
    late TagApi tagApi;
    late ApiClient apiClient;

    setUp(() {
      // 创建测试用的API客户端
      apiClient = ApiClient(baseUrl: 'http://test.example.com');
      tagApi = TagApi(apiClient);
    });

    test('deleteTag 方法应该正确构造请求参数', () {
      // 这个测试验证deleteTag方法的参数构造是否正确
      expect(tagApi, isNotNull);
      expect(tagApi.deleteTag, isA<Function>());
    });

    test('Tag模型应该包含必要的字段', () {
      // 测试Tag模型是否包含删除操作需要的id字段
      final tag = Tag(
        id: '123',
        name: '测试标签',
        backgroundColor: '#1890ff',
        textColor: '#ffffff',
        createTime: 1234567890,
        updateTime: 1234567890,
        count: 5,
      );

      expect(tag.id, equals('123'));
      expect(tag.name, equals('测试标签'));
      expect(tag.color, equals('#1890ff'));
      expect(tag.count, equals(5));
    });

    test('Tag.fromJson 应该正确解析JSON数据', () {
      final json = {
        'id': '456',
        'name': '从JSON创建的标签',
        'color': '#52c41a',
        'create_time': 1234567890,
        'update_time': 1234567890,
        'count': 10,
      };

      final tag = Tag.fromJson(json);

      expect(tag.id, equals('456'));
      expect(tag.name, equals('从JSON创建的标签'));
      expect(tag.color, equals('#52c41a'));
      expect(tag.createTime, equals(1234567890));
      expect(tag.updateTime, equals(1234567890));
      expect(tag.count, equals(10));
    });

    test('Tag.toJson 应该正确转换为JSON', () {
      final tag = Tag(
        id: '789',
        name: '转换为JSON的标签',
        backgroundColor: '#fa8c16',
        textColor: '#ffffff',
        createTime: 1234567890,
        updateTime: 1234567890,
        count: 3,
      );

      final json = tag.toJson();

      expect(json['id'], equals('789'));
      expect(json['name'], equals('转换为JSON的标签'));
      expect(json['color'], equals('#fa8c16'));
      expect(json['create_time'], equals(1234567890));
      expect(json['update_time'], equals(1234567890));
      expect(json['count'], equals(3));
    });
  });
}
