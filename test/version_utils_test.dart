import 'package:flutter_test/flutter_test.dart';
import 'package:aishoucang/utils/version_utils.dart';

void main() {
  group('VersionUtils Tests', () {
    test('compareVersions should work correctly', () {
      // 测试相等版本
      expect(VersionUtils.compareVersions('1.0.0', '1.0.0'), 0);
      expect(VersionUtils.compareVersions('1.2.3', '1.2.3'), 0);
      
      // 测试第一个版本小于第二个版本
      expect(VersionUtils.compareVersions('1.0.0', '1.0.1'), -1);
      expect(VersionUtils.compareVersions('1.0.0', '1.1.0'), -1);
      expect(VersionUtils.compareVersions('1.0.0', '2.0.0'), -1);
      expect(VersionUtils.compareVersions('1.2.3', '1.2.4'), -1);
      
      // 测试第一个版本大于第二个版本
      expect(VersionUtils.compareVersions('1.0.1', '1.0.0'), 1);
      expect(VersionUtils.compareVersions('1.1.0', '1.0.0'), 1);
      expect(VersionUtils.compareVersions('2.0.0', '1.0.0'), 1);
      expect(VersionUtils.compareVersions('1.2.4', '1.2.3'), 1);
      
      // 测试带构建号的版本
      expect(VersionUtils.compareVersions('1.0.0+1', '1.0.0+2'), 0); // 构建号应该被忽略
      expect(VersionUtils.compareVersions('1.0.0+1', '1.0.1+1'), -1);
    });

    test('hasNewVersion should work correctly', () {
      expect(VersionUtils.hasNewVersion('1.0.0', '1.0.1'), true);
      expect(VersionUtils.hasNewVersion('1.0.0', '1.1.0'), true);
      expect(VersionUtils.hasNewVersion('1.0.0', '2.0.0'), true);
      
      expect(VersionUtils.hasNewVersion('1.0.1', '1.0.0'), false);
      expect(VersionUtils.hasNewVersion('1.0.0', '1.0.0'), false);
      
      // 测试带构建号的版本
      expect(VersionUtils.hasNewVersion('1.0.0+1', '1.0.1+1'), true);
      expect(VersionUtils.hasNewVersion('1.0.1+1', '1.0.0+1'), false);
    });

    test('isValidVersion should work correctly', () {
      // 有效版本
      expect(VersionUtils.isValidVersion('1.0.0'), true);
      expect(VersionUtils.isValidVersion('1.2.3'), true);
      expect(VersionUtils.isValidVersion('10.20.30'), true);
      expect(VersionUtils.isValidVersion('1.0.0+1'), true);
      expect(VersionUtils.isValidVersion('1.2.3+10'), true);
      
      // 无效版本
      expect(VersionUtils.isValidVersion(''), false);
      expect(VersionUtils.isValidVersion('1.0'), false);
      expect(VersionUtils.isValidVersion('1.0.0.0'), false);
      expect(VersionUtils.isValidVersion('v1.0.0'), false);
      expect(VersionUtils.isValidVersion('1.0.a'), false);
    });

    test('formatVersionForDisplay should work correctly', () {
      expect(VersionUtils.formatVersionForDisplay('1.0.0'), '1.0.0');
      expect(VersionUtils.formatVersionForDisplay('1.2.3+1'), '1.2.3');
      expect(VersionUtils.formatVersionForDisplay('1.2.3+10'), '1.2.3');
    });

    test('getVersionParts should work correctly', () {
      expect(VersionUtils.getVersionParts('1.2.3'), [1, 2, 3]);
      expect(VersionUtils.getVersionParts('1.2.3+1'), [1, 2, 3]);
      expect(VersionUtils.getVersionParts('10.20.30'), [10, 20, 30]);
      
      // 无效版本应该返回默认值
      expect(VersionUtils.getVersionParts('invalid'), [0, 0, 0]);
    });

    test('getMajorVersion should work correctly', () {
      expect(VersionUtils.getMajorVersion('1.2.3'), 1);
      expect(VersionUtils.getMajorVersion('10.20.30'), 10);
      expect(VersionUtils.getMajorVersion('1.2.3+1'), 1);
    });

    test('getMinorVersion should work correctly', () {
      expect(VersionUtils.getMinorVersion('1.2.3'), 2);
      expect(VersionUtils.getMinorVersion('10.20.30'), 20);
      expect(VersionUtils.getMinorVersion('1.2.3+1'), 2);
    });

    test('getPatchVersion should work correctly', () {
      expect(VersionUtils.getPatchVersion('1.2.3'), 3);
      expect(VersionUtils.getPatchVersion('10.20.30'), 30);
      expect(VersionUtils.getPatchVersion('1.2.3+1'), 3);
    });

    test('getUpdateTypeDescription should work correctly', () {
      expect(VersionUtils.getUpdateTypeDescription('1.0.0', '1.0.0'), '当前已是最新版本');
      expect(VersionUtils.getUpdateTypeDescription('1.0.1', '1.0.0'), '当前已是最新版本');
      
      expect(VersionUtils.getUpdateTypeDescription('1.0.0', '2.0.0'), '重大版本更新');
      expect(VersionUtils.getUpdateTypeDescription('1.0.0', '1.1.0'), '功能版本更新');
      expect(VersionUtils.getUpdateTypeDescription('1.0.0', '1.0.1'), '修复版本更新');
    });
  });
}
