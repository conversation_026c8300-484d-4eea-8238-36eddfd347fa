import 'package:flutter_test/flutter_test.dart';
import 'dart:convert';
import '../lib/api/ai_chat_api.dart';

void main() {
  group('JSON流拼接器修复测试', () {
    late GenericJsonStreamAssembler assembler;

    setUp(() {
      assembler = GenericJsonStreamAssembler();
    });

    test('应该正确处理中文字符的流式拼接', () {
      // 模拟中文字符被分割的情况
      final chunks = [
        '{"choices":[{"delta":{"reasoning_content":"我们正在处理一个修改背景色的请求。用户希望将背景色改为紫色。"}}]}',
        '{"choices":[{"delta":{"reasoning_content":"根据当前页面结构，背景色由body元素的background样式控制。"}}]}',
        '{"choices":[{"delta":{"reasoning_content":"我们需要生成一个JavaScript脚本来修改body的背景色。"}}]}',
        '{"choices":[{"delta":{"reasoning_content":"但是，注意不要使用innerHTML，而是直接修改样式。"}}]}',
        '{"choices":[{"delta":{"reasoning_content":"由于用户没有指定具体的紫色"}}]}',
      ];

      print('=== 开始测试中文字符拼接 ===');

      // 处理每个chunk
      for (int i = 0; i < chunks.length; i++) {
        print('\\n--- 处理第${i + 1}个chunk ---');
        print('原始数据: ${chunks[i]}');
        assembler.handleChunk(chunks[i]);
      }

      // 验证拼接结果
      final reasoningContent = assembler.getValue('choices.0.delta.reasoning_content');
      print('\\n=== 最终拼接结果 ===');
      print('reasoning_content: $reasoningContent');

      // 验证内容完整性
      expect(reasoningContent, contains('我们正在处理一个修改背景色的请求'));
      expect(reasoningContent, contains('根据当前页面结构'));
      expect(reasoningContent, contains('我们需要生成一个JavaScript脚本'));
      expect(reasoningContent, contains('但是，注意不要使用innerHTML'));
      expect(reasoningContent, contains('由于用户没有指定具体的紫色'));

      // 验证没有乱码字符
      expect(reasoningContent, isNot(contains('�')));
    });

    test('应该正确处理reasoning_content字段中间为null的情况', () {
      // 模拟实际日志中的数据流
      final chunks = [
        '{"choices":[{"delta":{"reasoning_content":"我们正在修改页面的背景色。用户希望将背景色改成嫩绿色。\\n 根据页面结构，背景色是由body元素的background样式控制的。"}}]}',
        '{"choices":[{"delta":{"reasoning_content":"\\n 因此，我们需要修改body的background样式属性。"}}]}',
        '{"choices":[{"delta":{"reasoning_content":"\\n 注意：用户说的是\\"嫩绿色\\"，我们可以用颜色代码来表示。"}}]}',
        '{"choices":[{"delta":{"content":"","reasoning_content":null},"finish_reason":"tool_calls"}]}', // 这里reasoning_content为null
      ];

      print('=== 开始测试reasoning_content拼接 ===');
      
      // 处理每个chunk
      for (int i = 0; i < chunks.length; i++) {
        print('\\n--- 处理第${i + 1}个chunk ---');
        print('原始数据: ${chunks[i]}');
        assembler.handleChunk(chunks[i]);
      }

      // 验证拼接结果
      final reasoningContent = assembler.getValue('choices.0.delta.reasoning_content');
      print('\\n=== 最终拼接结果 ===');
      print('reasoning_content: $reasoningContent');
      
      // 验证内容完整性
      expect(reasoningContent, contains('我们正在修改页面的背景色'));
      expect(reasoningContent, contains('根据页面结构'));
      expect(reasoningContent, contains('因此，我们需要修改body'));
      expect(reasoningContent, contains('注意：用户说的是'));
      
      // 验证finish_reason也被正确设置
      final finishReason = assembler.getValue('choices.0.finish_reason');
      expect(finishReason, equals('tool_calls'));
      
      print('finish_reason: $finishReason');
    });

    test('应该正确处理tool_calls arguments字段的拼接', () {
      // 模拟日志中的tool_calls数据
      final chunks = [
        '{"choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":"{\\"script\\":"}}]}}]}',
        '{"choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":"document.body.style.background = "}}]}}]}',
        '{"choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":"\'#eaffd7\';"}}]}}]}',
        '{"choices":[{"delta":{"tool_calls":[{"index":0,"function":{"arguments":"\\"}"}]}}]}',
      ];

      print('\\n=== 开始测试tool_calls arguments拼接 ===');
      
      for (int i = 0; i < chunks.length; i++) {
        print('\\n--- 处理第${i + 1}个chunk ---');
        print('原始数据: ${chunks[i]}');
        assembler.handleChunk(chunks[i]);
      }

      final arguments = assembler.getValue('choices.0.delta.tool_calls.0.function.arguments');
      print('\\n=== 最终拼接结果 ===');
      print('arguments: $arguments');
      
      // 验证JSON格式正确
      expect(arguments, contains('script'));
      expect(arguments, contains('document.body.style.background'));
      expect(arguments, contains('#eaffd7'));
      
      // 尝试解析JSON
      try {
        final parsedArgs = jsonDecode(arguments);
        expect(parsedArgs['script'], isNotNull);
        print('解析后的JSON: $parsedArgs');
      } catch (e) {
        print('JSON解析失败: $e');
        fail('arguments字段应该是有效的JSON格式');
      }
    });

    test('应该正确处理content字段的拼接', () {
      final chunks = [
        '{"choices":[{"delta":{"content":"好的亲~马上帮你把背景色改成嫩绿色！"}}]}',
        '{"choices":[{"delta":{"content":"稍等一秒钟哦！✨"}}]}',
        '{"choices":[{"delta":{"content":"\\n\\n(正在悄悄修改中...)"}}]}',
        '{"choices":[{"delta":{"content":"","reasoning_content":null},"finish_reason":"tool_calls"}]}', // content为空字符串
      ];

      print('\\n=== 开始测试content拼接 ===');
      
      for (int i = 0; i < chunks.length; i++) {
        print('\\n--- 处理第${i + 1}个chunk ---');
        assembler.handleChunk(chunks[i]);
      }

      final content = assembler.getValue('choices.0.delta.content');
      print('\\n=== 最终拼接结果 ===');
      print('content: $content');
      
      expect(content, contains('好的亲~马上帮你把背景色改成嫩绿色！'));
      expect(content, contains('稍等一秒钟哦！'));
      expect(content, contains('正在悄悄修改中'));
    });
  });
}
