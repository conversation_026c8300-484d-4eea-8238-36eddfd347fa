import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SSE思考过程解析测试', () {
    test('应该正确解析reasoning_content', () {
      const sseData = '''{"id":"50797e6e-87f2-466c-8be1-342427af4faa","object":"chat.completion.chunk","created":1749175632,"model":"deepseek-reasoner","system_fingerprint":"fp_393bca965e_prod0425fp8","choices":[{"index":0,"delta":{"content":null,"reasoning_content":"回应"},"logprobs":null,"finish_reason":null}]}''';
      
      final jsonData = jsonDecode(sseData);
      final choices = jsonData['choices'] as List?;
      final delta = choices![0]['delta'] as Map<String, dynamic>?;
      final content = delta!['content'] as String?;
      final reasoningContent = delta['reasoning_content'] as String?;
      
      expect(content, isNull);
      expect(reasoningContent, equals('回应'));
    });

    test('应该同时处理content和reasoning_content', () {
      const sseData = '''{"id":"test","choices":[{"index":0,"delta":{"content":"你好","reasoning_content":"思考中"},"logprobs":null,"finish_reason":null}]}''';
      
      final jsonData = jsonDecode(sseData);
      final choices = jsonData['choices'] as List?;
      final delta = choices![0]['delta'] as Map<String, dynamic>?;
      final content = delta!['content'] as String?;
      final reasoningContent = delta['reasoning_content'] as String?;
      
      expect(content, equals('你好'));
      expect(reasoningContent, equals('思考中'));
    });

    test('应该处理只有content没有reasoning_content的情况', () {
      const sseData = '''{"id":"test","choices":[{"index":0,"delta":{"content":"普通回复"},"logprobs":null,"finish_reason":null}]}''';
      
      final jsonData = jsonDecode(sseData);
      final choices = jsonData['choices'] as List?;
      final delta = choices![0]['delta'] as Map<String, dynamic>?;
      final content = delta!['content'] as String?;
      final reasoningContent = delta['reasoning_content'] as String?;
      
      expect(content, equals('普通回复'));
      expect(reasoningContent, isNull);
    });
  });
}
