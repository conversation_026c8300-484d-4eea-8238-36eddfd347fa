import 'package:flutter_test/flutter_test.dart';
import 'package:aishoucang/pages/ai_chat_page/models/chat_message.dart';

void main() {
  group('对话历史构建测试', () {
    test('应该正确构建对话历史，排除系统消息和当前用户消息', () {
      // 模拟消息列表
      final messages = [
        // 系统欢迎消息 (index 0) - 应该被排除
        ChatMessage(
          text: '你好！我是AI助手，有什么可以帮助您的吗？',
          isUser: false,
          timestamp: DateTime.now(),
        ),
        // 第一轮对话 (index 1, 2) - 应该被包含
        ChatMessage(
          text: '你好',
          isUser: true,
          timestamp: DateTime.now(),
        ),
        ChatMessage(
          text: '你好！很高兴为你提供帮助～😊',
          isUser: false,
          timestamp: DateTime.now(),
        ),
        // 当前用户消息 (index 3) - 应该被排除
        ChatMessage(
          text: '你叫什么名字',
          isUser: true,
          timestamp: DateTime.now(),
        ),
      ];

      // 模拟构建对话历史的逻辑
      final conversationHistory = <Map<String, String>>[];
      for (int i = 1; i < messages.length - 1; i++) { // 跳过系统欢迎消息和最后一条用户消息
        final msg = messages[i];
        if (msg.type != ChatMessageType.collection) { // 只包含文本消息
          conversationHistory.add({
            'role': msg.isUser ? 'user' : 'assistant',
            'content': msg.text,
          });
        }
      }

      // 验证结果
      expect(conversationHistory.length, equals(2));
      expect(conversationHistory[0]['role'], equals('user'));
      expect(conversationHistory[0]['content'], equals('你好'));
      expect(conversationHistory[1]['role'], equals('assistant'));
      expect(conversationHistory[1]['content'], equals('你好！很高兴为你提供帮助～😊'));
    });

    test('应该排除收藏内容类型的消息', () {
      final messages = [
        // 系统欢迎消息
        ChatMessage(
          text: '你好！我是AI助手，有什么可以帮助您的吗？',
          isUser: false,
          timestamp: DateTime.now(),
        ),
        // 普通文本消息
        ChatMessage(
          text: '你好',
          isUser: true,
          timestamp: DateTime.now(),
        ),
        // 收藏内容消息 - 应该被排除
        ChatMessage(
          text: '收藏内容',
          isUser: true,
          timestamp: DateTime.now(),
          type: ChatMessageType.collection,
        ),
        // AI回复
        ChatMessage(
          text: 'AI回复',
          isUser: false,
          timestamp: DateTime.now(),
        ),
        // 当前用户消息
        ChatMessage(
          text: '新问题',
          isUser: true,
          timestamp: DateTime.now(),
        ),
      ];

      final conversationHistory = <Map<String, String>>[];
      for (int i = 1; i < messages.length - 1; i++) {
        final msg = messages[i];
        if (msg.type != ChatMessageType.collection) {
          conversationHistory.add({
            'role': msg.isUser ? 'user' : 'assistant',
            'content': msg.text,
          });
        }
      }

      // 应该只包含普通文本消息，排除收藏内容
      expect(conversationHistory.length, equals(2));
      expect(conversationHistory[0]['content'], equals('你好'));
      expect(conversationHistory[1]['content'], equals('AI回复'));
    });
  });
}
