import 'package:flutter_test/flutter_test.dart';
import 'package:aishoucang/models/tag.dart';

void main() {
  group('更新后的标签API测试', () {
    test('Tag模型应该支持新的字段结构', () {
      final json = {
        'id': '1',
        'name': '工作',
        'background_color': '#1890ff',
        'text_color': '#ffffff',
        'create_time': 1714392123000,
        'update_time': 1714392123000,
        'count': 5,
      };

      final tag = Tag.fromJson(json);

      expect(tag.id, equals('1'));
      expect(tag.name, equals('工作'));
      expect(tag.backgroundColor, equals('#1890ff'));
      expect(tag.textColor, equals('#ffffff'));
      expect(tag.createTime, equals(1714392123000));
      expect(tag.updateTime, equals(1714392123000));
      expect(tag.count, equals(5));
    });

    test('Tag模型应该提供默认值', () {
      final json = {
        'id': '2',
        'name': '生活',
        'create_time': 1714392123000,
        'update_time': 1714392123000,
      };

      final tag = Tag.fromJson(json);

      expect(tag.backgroundColor, equals('#1890ff')); // 默认背景色
      expect(tag.textColor, equals('#ffffff')); // 默认文字色
      expect(tag.count, equals(0)); // 默认计数
    });

    test('Tag模型应该正确序列化为JSON', () {
      final tag = Tag(
        id: '3',
        name: '学习',
        backgroundColor: '#52c41a',
        textColor: '#ffffff',
        createTime: 1714392123000,
        updateTime: 1714392123000,
        count: 10,
      );

      final json = tag.toJson();

      expect(json['id'], equals('3'));
      expect(json['name'], equals('学习'));
      expect(json['background_color'], equals('#52c41a'));
      expect(json['text_color'], equals('#ffffff'));
      expect(json['create_time'], equals(1714392123000));
      expect(json['update_time'], equals(1714392123000));
      expect(json['count'], equals(10));
    });

    test('CreateTagResponse模型应该支持新的字段结构', () {
      final json = {
        'id': '4',
        'name': '娱乐',
        'background_color': '#fa8c16',
        'text_color': '#ffffff',
        'create_time': 1714392123000,
        'update_time': 1714392123000,
      };

      final response = CreateTagResponse.fromJson(json);

      expect(response.id, equals('4'));
      expect(response.name, equals('娱乐'));
      expect(response.backgroundColor, equals('#fa8c16'));
      expect(response.textColor, equals('#ffffff'));
      expect(response.createTime, equals(1714392123000));
      expect(response.updateTime, equals(1714392123000));
    });

    test('CreateTagResponse模型应该提供默认值', () {
      final json = {
        'id': '5',
        'name': '运动',
        'create_time': 1714392123000,
        'update_time': 1714392123000,
      };

      final response = CreateTagResponse.fromJson(json);

      expect(response.backgroundColor, equals('#1890ff')); // 默认背景色
      expect(response.textColor, equals('#ffffff')); // 默认文字色
    });

    test('向后兼容性：color属性应该返回backgroundColor', () {
      final tag = Tag(
        id: '6',
        name: '测试',
        backgroundColor: '#eb2f96',
        textColor: '#ffffff',
        createTime: 1714392123000,
        updateTime: 1714392123000,
        count: 0,
      );

      // 测试向后兼容的color属性
      expect(tag.color, equals('#eb2f96'));
      expect(tag.color, equals(tag.backgroundColor));
    });
  });
}
