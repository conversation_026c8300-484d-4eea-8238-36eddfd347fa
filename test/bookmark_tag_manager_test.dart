import 'package:flutter_test/flutter_test.dart';
import 'package:aishoucang/api/bookmark_api.dart';
import 'package:aishoucang/components/bookmark_tag_manager_dialog.dart';

void main() {
  group('书签标签管理测试', () {
    test('BookmarkItem 应该支持标签字段', () {
      final bookmark = BookmarkItem(
        id: '123',
        influencerName: '测试博主',
        parentId: 'parent123',
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
        tags: ['美食', '旅行', '生活'],
      );

      expect(bookmark.tags, equals(['美食', '旅行', '生活']));
      expect(bookmark.tags.length, equals(3));
    });

    test('BookmarkItem.fromJson 应该正确解析标签', () {
      final json = {
        'id': '456',
        'influencer_name': '测试博主2',
        'parent_id': 'parent456',
        'create_time': '2024-01-01T00:00:00.000Z',
        'update_time': '2024-01-01T00:00:00.000Z',
        'tags': ['科技', '数码'],
      };

      final bookmark = BookmarkItem.fromJson(json);

      expect(bookmark.id, equals('456'));
      expect(bookmark.influencerName, equals('测试博主2'));
      expect(bookmark.tags, equals(['科技', '数码']));
      expect(bookmark.tags.length, equals(2));
    });

    test('BookmarkItem.fromJson 应该处理空标签列表', () {
      final json = {
        'id': '789',
        'influencer_name': '测试博主3',
        'parent_id': 'parent789',
        'create_time': '2024-01-01T00:00:00.000Z',
        'update_time': '2024-01-01T00:00:00.000Z',
        'tags': [],
      };

      final bookmark = BookmarkItem.fromJson(json);

      expect(bookmark.tags, isEmpty);
    });

    test('BookmarkItem.fromJson 应该处理缺失的标签字段', () {
      final json = {
        'id': '101',
        'influencer_name': '测试博主4',
        'parent_id': 'parent101',
        'create_time': '2024-01-01T00:00:00.000Z',
        'update_time': '2024-01-01T00:00:00.000Z',
        // 没有tags字段
      };

      final bookmark = BookmarkItem.fromJson(json);

      expect(bookmark.tags, isEmpty);
    });

    test('BookmarkItem.toJson 应该包含标签字段', () {
      final bookmark = BookmarkItem(
        id: '202',
        influencerName: '测试博主5',
        parentId: 'parent202',
        createTime: DateTime.parse('2024-01-01T00:00:00.000Z'),
        updateTime: DateTime.parse('2024-01-01T00:00:00.000Z'),
        tags: ['健身', '运动'],
      );

      final json = bookmark.toJson();

      expect(json['id'], equals('202'));
      expect(json['influencer_name'], equals('测试博主5'));
      expect(json['tags'], equals(['健身', '运动']));
    });

    test('BookmarkItem 应该支持复制并更新标签', () {
      final originalBookmark = BookmarkItem(
        id: '303',
        influencerName: '测试博主6',
        parentId: 'parent303',
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
        tags: ['原始标签'],
      );

      // 模拟更新标签
      final updatedBookmark = BookmarkItem(
        id: originalBookmark.id,
        influencerName: originalBookmark.influencerName,
        influencerAvatar: originalBookmark.influencerAvatar,
        cover: originalBookmark.cover,
        title: originalBookmark.title,
        desc: originalBookmark.desc,
        schemeUrl: originalBookmark.schemeUrl,
        parentId: originalBookmark.parentId,
        createTime: originalBookmark.createTime,
        updateTime: DateTime.now(),
        tags: ['新标签1', '新标签2'],
      );

      expect(originalBookmark.tags, equals(['原始标签']));
      expect(updatedBookmark.tags, equals(['新标签1', '新标签2']));
      expect(updatedBookmark.id, equals(originalBookmark.id));
    });
  });
}
