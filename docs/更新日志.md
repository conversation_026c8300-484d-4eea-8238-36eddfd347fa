#### v3.1.29
- 修复实时语音非压缩模式适配问题

#### v3.1.28
- 修复了一些已知问题

#### v3.1.27
- 修复了一些已知问题
- 
#### v3.1.26
- 修复网络与录音线程同步问题导致的偶现4008错误

#### v3.1.25
- demo删除第三方加载UI库 victor

#### v3.1.24
- 完善log组件

#### v3.1.23
- [实时语音识别] 修复因宿主层调用时序问题引起SDK生命周期错误，导致初始化时偶现crash的问题。

#### v3.1.22
- [实时语音识别] 增强热词参数标记废弃
- [一句话识别] 增强热词参数标记废弃
- [录音文件识别极速版] 增强热词参数标记废弃

#### v3.1.21
- [实时语音识别] 标记AudioRecognizeStateListener#onVoiceVolume为废弃接口

#### v3.1.20
- [实时语音识别] 修复录音异常导致的crash

#### v3.1.19
- [实时语音识别] 修复格式化浮点数字时异常的问题

#### v3.1.18
- [实时语音识别] 调整onStop回调时机

#### v3.1.17
- [实时语音识别] 新增强制断开vad的参数(见AudioRecognizeRequest#setMaxSpeakTime)

#### v3.1.16
- [实时语音识别] 支持x86架构

#### v3.1.15
- [实时语音识别] 调整混淆规则(保留原始包名，避免和宿主层混淆冲突)

#### v3.1.14
- [实时语音识别] 支持获取实时分贝(AudioRecognizeStateListener#onVoiceDb)
- [实时语音识别] 移除废弃的x86架构
- [一句话识别] 增加录音数据长度为0时的异常保护以及异常错误码

#### v3.1.13
- [实时语音识别] 优化JSON异常处理
- [一句话识别] 修复Android高系统版本保存临时录音文件失败
- [一句话识别] 修复未授予录音权限导致的崩溃问题

#### v3.1.12
- [实时语音识别] 增加回音消除示例

#### v3.1.11
- [一句话识别] 修复音量回调崩溃

#### v3.1.10
- [实时语音识别] 支持自定义参数
- [录音文件识别极速版] 支持customization_id和hotword_id

#### v3.1.9
- [实时语音识别] 修复分片时间设置问题

#### v3.1.8
- [一句话识别] 修复内存泄漏问题
- [一句话识别] 内置录音器实现不再依赖Service
- [一句话识别] 修改音量回调计算

#### v3.1.7
- [录音文件识别极速版] 支持sentence_max_length

#### v3.1.6
- [一句话识别] 修复参数不生效问题

#### v3.1.5
- [实时语音识别] 增加noise_threshold参数

#### v3.1.4
- [实时语音识别] 修复空指针问题

#### v3.1.3
- [录音文件极速版] 增加热词增强功能
- [一句话识别] 增加热词增强功能

#### v3.1.2
- [实时语音识别] 增加reinforce_hotword参数
- [一句话识别] 增加音量回调

#### v3.1.1

- [实时语音识别] 优化音频压缩器性能及效果

#### v3.1.0

- [实时语音识别] 使用新的音频压缩方案，对识别准确度、时延均有较大幅度提升
- [实时语音识别] 开放音频压缩开关选项，允许关闭音频压缩
- [实时语音识别] 添加触发静音事件是否要自动停止识别的配置接口，可配置触发静音事件但不停止识别

#### v3.0.2

-  [录音文件识别极速版] 支持设置超时时间
- Demo优化

#### v3.0.1

- [一句话识别] SDK内置录音器采样率由8k提升至16k