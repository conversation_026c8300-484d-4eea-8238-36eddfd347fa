# 笔记WebView功能使用说明

## 功能概述

笔记WebView功能允许在应用内通过WebView展示笔记详情，支持从API获取笔记数据并渲染HTML内容。

## API接口

### 获取笔记详情
- **请求方式**: GET
- **请求路径**: `/note/detail`
- **请求参数**: 
  - `id` (string, 必填): 笔记ID

### 响应格式
```json
{
    "code": 0,
    "message": "获取笔记详情成功",
    "data": {
        "id": "123",
        "parent_id": "456",
        "user_id": "789",
        "title": "我的笔记标题",
        "cover": "https://example.com/cover.jpg",
        "desc": "这是笔记的描述",
        "content": "这是笔记的详细内容...",
        "html": "<h1>笔记HTML内容</h1><p>这是HTML格式的笔记内容</p>",
        "create_time": "2023-12-01T10:00:00Z",
        "update_time": "2023-12-01T12:00:00Z"
    }
}
```

## 使用方式

### 1. 使用NoteWebviewHelper工具类（推荐）

```dart
import 'package:aishoucang/utils/note_webview_helper.dart';

// 直接打开笔记详情WebView
final helper = NoteWebviewHelper();
final success = await helper.openNoteDetail("123");

if (success) {
  print("成功打开笔记详情");
} else {
  print("打开笔记详情失败");
}
```

### 2. 使用原生Bridge方法

```dart
import 'package:aishoucang/native_bridge/native_bridge.dart';

final nativeBridge = NativeBridge();
final result = await nativeBridge.openNoteWebview(noteId: "123");

if (result['success'] == true) {
  print("成功打开笔记详情");
} else {
  print("打开失败: ${result['error']}");
}
```

### 3. 仅获取笔记数据（不打开WebView）

```dart
// 获取笔记详情数据
final noteData = await helper.getNoteDetail("123");
if (noteData != null) {
  print("笔记标题: ${noteData['title']}");
}

// 仅获取HTML内容
final html = await helper.getNoteHtml("123");
if (html != null) {
  print("HTML内容长度: ${html.length}");
}
```

## 测试功能

在Debug页面中已添加测试按钮：

1. 打开应用的Debug页面
2. 在"核心功能"区域找到"笔记详情(ID:2)"按钮
3. 点击按钮测试打开ID为2的笔记

## 技术实现

### Android端
- **NoteWebviewHelper**: 原生Android辅助类，处理API请求和WebView配置
- **NoteWebviewActivity**: 专门的Activity用于展示笔记内容
- **NoteDetail**: 数据模型类，映射API响应结构

### Flutter端
- **NoteApi**: 标准化的API调用类
- **NoteWebviewHelper**: Flutter端工具类，提供便捷方法
- **NativeBridge**: 原生桥接方法

### WebView加载逻辑
使用`webView.loadDataWithBaseURL()`方法：
- **baseUrl**: `"file:///android_asset/"` - 支持相对路径资源
- **data**: HTML内容字符串
- **mimeType**: `"text/html"`
- **encoding**: `"UTF-8"` - 支持中文显示
- **historyUrl**: `null` - 不添加到浏览历史

## 内容展示策略

1. **优先显示HTML**: 如果API返回的`html`字段不为空，直接在WebView中渲染
2. **回退到基本内容**: 如果HTML为空，使用其他字段（title、desc、content等）构建显示页面
3. **错误处理**: 网络错误或API错误时显示友好的错误页面

## 注意事项

1. **网络权限**: 确保应用有网络访问权限
2. **API配置**: 确保配置文件中的`api_base_url`正确
3. **认证**: API请求会自动携带用户认证token
4. **错误处理**: 所有网络请求都有完善的错误处理机制

## 扩展功能

可以根据需要扩展以下功能：
- 支持笔记编辑
- 添加分享功能
- 支持离线缓存
- 添加评论功能
- 支持多媒体内容
