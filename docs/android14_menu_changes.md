# Android 14+ 悬浮窗菜单适配说明

## 背景

由于Android 14引入了更严格的MediaProjection权限要求，第三方应用在使用截图和录屏功能时会遇到权限问题。为了提供更好的用户体验，我们在Android 14及以上版本的设备上隐藏了悬浮窗菜单中的截图相关功能。

## 修改内容

### 1. 菜单项隐藏逻辑

在 `MenuHelper.kt` 中添加了Android版本检查：

```kotlin
// 在Android 14+设备上隐藏截图相关菜单项
if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14 (API 34)
    Log.d(TAG, "Android 14+设备，隐藏截图相关菜单项")
    // 隐藏截图收藏菜单项
    menuContent.findViewById<LinearLayout>(R.id.menu_item_share)?.visibility = View.GONE
    // 隐藏截长图菜单项
    menuContent.findViewById<LinearLayout>(R.id.menu_item_long_screenshot)?.visibility = View.GONE
}
```

### 2. 点击事件处理

同时修改了菜单项点击事件的设置逻辑，确保在Android 14+设备上不会为隐藏的菜单项设置点击事件：

```kotlin
// 检查是否为Android 14+设备
val isAndroid14Plus = Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE

// 截图收藏菜单项 - 仅在Android 14以下设备上设置点击事件
if (!isAndroid14Plus) {
    // 设置截图收藏点击事件
}

// 截长图菜单项 - 仅在Android 14以下设备上设置点击事件
if (!isAndroid14Plus) {
    // 设置截长图点击事件
}
```

## 影响的菜单项

### 隐藏的菜单项（Android 14+）
- **截图收藏** (`menu_item_share`)
- **截长图** (`menu_item_long_screenshot`)

### 保留的菜单项（所有版本）
- **关闭悬浮窗** (`menu_item_close_floating_window`)

## 用户体验

### Android 13及以下版本
- 悬浮窗菜单显示完整功能：截图收藏、截长图、关闭悬浮窗
- 所有功能正常可用

### Android 14及以上版本
- 悬浮窗菜单仅显示：关闭悬浮窗
- 截图相关功能被隐藏，避免用户遇到权限错误

## 技术细节

### 版本检查
使用 `Build.VERSION_CODES.UPSIDE_DOWN_CAKE` (API 34) 作为Android 14的版本标识。

### 视图控制
通过设置 `View.GONE` 来完全隐藏菜单项，而不是仅仅禁用它们。

### 日志记录
添加了详细的日志记录，便于调试和问题排查：
```kotlin
Log.d(TAG, "Android 14+设备，隐藏截图相关菜单项")
```

## 测试

创建了单元测试 `MenuHelperTest.kt` 来验证：
1. Android 13设备上截图菜单项可见
2. Android 14设备上截图菜单项隐藏
3. 关闭悬浮窗菜单项在所有版本上都可见

## 未来改进

当解决了Android 14的权限问题后（如集成Shizuku或其他解决方案），可以移除这些版本检查，恢复完整的菜单功能。

## 相关文件

- `android/app/src/main/kotlin/com/xunhe/aishoucang/lib/MenuHelper.kt` - 主要修改文件
- `android/app/src/test/kotlin/com/xunhe/aishoucang/lib/MenuHelperTest.kt` - 单元测试
- `android/app/src/main/res/layout/floating_menu_layout.xml` - 菜单布局文件
