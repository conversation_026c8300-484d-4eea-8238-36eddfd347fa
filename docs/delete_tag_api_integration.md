# 删除标签API接入文档

## 概述

本文档描述了删除标签API的接入实现，包括API调用、错误处理和用户界面集成。

## API接口

### 请求方式
```
POST /tags/delete
```

### 请求参数
```json
{
    "id": "标签ID，必填，字符串类型"
}
```

### 响应示例

#### 成功响应
```json
{
    "code": 0,
    "message": "删除标签成功",
    "data": null
}
```

#### 错误响应
```json
{
    "code": 203,
    "message": "未登录或身份验证失败",
    "data": null
}
```

## 实现细节

### 1. TagApi类实现

在 `lib/api/tag_api.dart` 中实现了 `deleteTag` 方法：

```dart
/// 删除标签
Future<bool> deleteTag(String tagId) async {
  final requestData = <String, dynamic>{
    'id': tagId,
  };

  final response = await _apiClient.post('/tags/delete', requestData);

  // 检查响应状态
  if (response['code'] == 0) {
    return true;
  } else {
    throw ApiException(
      code: response['code'],
      message: response['message'],
      data: response['data'],
    );
  }
}
```

### 2. TagService类更新

在 `lib/services/tag_service.dart` 中更新了 `removeTag` 方法：

- 通过 `getAllTagObjects()` 获取完整标签列表
- 根据标签名称找到对应的标签ID
- 调用API删除标签
- 成功后刷新本地缓存
- 提供详细的中文错误提示

### 3. 错误处理

实现了完整的错误处理机制：

| 错误码 | 中文提示 |
|--------|----------|
| 203 | 未登录或身份验证失败，请重新登录 |
| 313 | 标签不存在 |
| 403 | 无权限删除此标签 |
| 101 | 服务不可用，请稍后重试 |
| 102 | 数据库错误，请稍后重试 |
| 其他 | 删除标签失败，请重试 |

### 4. UI集成

在 `lib/pages/app/widgets/filter_menu_dialog.dart` 中：

- 使用 CustomToast 替代 SnackBar 显示提示
- 删除确认对话框保持不变
- 成功删除后更新本地状态

## 使用方式

### 1. 长按标签进入编辑模式
用户可以长按标签芯片进入编辑模式，此时会显示删除按钮。

### 2. 点击删除按钮
点击红色的删除按钮会弹出确认对话框。

### 3. 确认删除
用户确认后，系统会：
1. 调用删除API
2. 显示相应的提示信息
3. 更新本地标签列表

## 测试

创建了基础测试文件 `test/tag_api_test.dart` 来验证：
- TagApi类的方法存在性
- Tag模型的字段完整性
- JSON序列化和反序列化功能

## 注意事项

1. **权限检查**：删除操作需要用户登录且有相应权限
2. **数据一致性**：删除成功后会自动刷新本地缓存
3. **用户体验**：使用CustomToast提供友好的中文提示
4. **错误恢复**：网络错误时提供重试机制

## 相关文件

- `lib/api/tag_api.dart` - API接口实现
- `lib/services/tag_service.dart` - 业务逻辑服务
- `lib/pages/app/widgets/filter_menu_dialog.dart` - UI界面
- `lib/pages/app/widgets/tag_chip.dart` - 标签组件
- `test/tag_api_test.dart` - 测试文件
