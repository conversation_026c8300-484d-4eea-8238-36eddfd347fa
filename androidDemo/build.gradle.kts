// Android项目依赖配置示例
// 在你的app模块的build.gradle.kts文件中添加以下依赖

dependencies {
    // 网络请求
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
    
    // JSON序列化
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0")
    
    // 协程
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
    
    // Android核心库
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    
    // 日志
    implementation("com.jakewharton.timber:timber:5.0.1")
}

// 在项目级别的build.gradle.kts中添加序列化插件
plugins {
    id("org.jetbrains.kotlin.plugin.serialization") version "1.9.10"
}
