# 腾讯云ASR语音识别 Android SDK

基于Rust项目中的腾讯云ASR语音识别接口改编的Kotlin版本，适用于Android本地执行。

## 功能特性

- ✅ 完整的腾讯云ASR API调用实现
- ✅ TC3-HMAC-SHA256签名算法
- ✅ 支持协程和回调两种调用方式
- ✅ 完善的错误处理机制
- ✅ 支持自定义配置参数
- ✅ 批量任务处理支持
- ✅ 详细的日志记录

## 快速开始

### 1. 添加依赖

在你的`app/build.gradle.kts`文件中添加必要的依赖：

```kotlin
dependencies {
    // 网络请求
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    
    // JSON序列化
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0")
    
    // 协程
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
}
```

在项目级别的`build.gradle.kts`中添加序列化插件：

```kotlin
plugins {
    id("org.jetbrains.kotlin.plugin.serialization") version "1.9.10"
}
```

### 2. 配置腾讯云密钥

```kotlin
val config = TencentCloudConfig(
    secretId = "你的腾讯云SecretId",
    secretKey = "你的腾讯云SecretKey"
)
```

### 3. 创建ASR管理器

```kotlin
val asrManager = TencentAsrManager(context, config)
```

### 4. 创建语音识别任务

#### 方式1：简单调用（回调方式）

```kotlin
asrManager.createSimpleAsrTask("https://example.com/audio.wav", 
    object : TencentAsrManager.AsrTaskCallback {
        override fun onSuccess(taskId: Long, requestId: String) {
            Log.i("ASR", "任务创建成功，任务ID: $taskId")
        }
        
        override fun onError(error: TencentAsrServiceError) {
            Log.e("ASR", "任务创建失败: ${error.message}")
        }
    }
)
```

#### 方式2：协程方式

```kotlin
lifecycleScope.launch {
    val config = TencentAsrManager.AsrTaskConfig(
        url = "https://example.com/audio.wav",
        callbackUrl = "https://your-server.com/callback"
    )
    
    when (val result = asrManager.createAsrTaskSuspend(config)) {
        is TencentAsrManager.AsrTaskResult.Success -> {
            Log.i("ASR", "任务ID: ${result.taskId}")
        }
        is TencentAsrManager.AsrTaskResult.Error -> {
            Log.e("ASR", "错误: ${result.error.message}")
        }
    }
}
```

#### 方式3：自定义配置

```kotlin
val customConfig = TencentAsrManager.AsrTaskConfig(
    url = "https://example.com/audio.wav",
    sourceType = 0, // 语音URL
    engineModelType = "16k_zh_video", // 视频模型
    channelNum = 1, // 单声道
    speakerNumber = 2, // 指定2个说话人分离
    callbackUrl = "https://your-server.com/callback",
    resTextFormat = 3 // 详细格式
)

asrManager.createAsrTask(customConfig, callback)
```

## API参数说明

### AsrTaskConfig 参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| url | String | ✅ | - | 音频文件的下载地址 |
| sourceType | Int | ❌ | 0 | 语音数据来源，0：语音URL，1：语音数据 |
| engineModelType | String | ❌ | "16k_zh_video" | 引擎模型类型 |
| channelNum | Int | ❌ | 1 | 音频声道数，仅支持单声道 |
| customizationId | String? | ❌ | null | 自定义热词ID |
| hotwordId | String? | ❌ | null | 热词ID |
| speakerNumber | Int | ❌ | 0 | 说话人分离人数，0代表自动分离 |
| callbackUrl | String? | ❌ | null | 回调地址 |
| resTextFormat | Int | ❌ | 3 | 识别结果文本格式，0：基础，3：详细 |

### 支持的引擎模型类型

- `16k_zh`: 中文通用模型
- `16k_zh_video`: 中文视频模型（推荐）
- `16k_en`: 英文模型
- `16k_ca`: 粤语模型
- `16k_ja`: 日语模型
- `16k_zh_edu`: 中文教育模型

## 错误处理

SDK提供了完善的错误处理机制：

```kotlin
private fun handleError(error: TencentAsrServiceError) {
    when (error) {
        is TencentAsrServiceError.HttpError -> {
            // 网络错误，建议重试
        }
        is TencentAsrServiceError.ApiError -> {
            // API错误，检查参数或配置
        }
        is TencentAsrServiceError.JsonError -> {
            // JSON解析错误
        }
        is TencentAsrServiceError.SignatureError -> {
            // 签名错误，检查密钥配置
        }
        is TencentAsrServiceError.ParameterError -> {
            // 参数错误
        }
    }
}
```

## 回调数据处理

如果设置了回调URL，腾讯云会向该地址发送识别结果：

```kotlin
// 回调数据模型
data class TencentAsrCallbackData(
    val code: Long, // 0为成功，其他为失败
    val message: String?, // 错误信息
    val requestId: Long, // 任务ID
    val text: String?, // 识别结果文本
    val audioTime: Double? // 音频时长
)

// 错误码处理
val errorMessage = TencentAsrErrorCode.getErrorMessage(callbackData.code)
val isSuccess = TencentAsrErrorCode.isSuccess(callbackData.code)
```

## 注意事项

1. **密钥安全**: 不要在代码中硬编码密钥，建议从安全的配置文件或环境变量中读取
2. **网络权限**: 确保应用有网络访问权限
3. **音频格式**: 支持常见的音频格式，推荐使用16kHz采样率的单声道音频
4. **请求频率**: 避免过于频繁的请求，建议添加适当的延迟
5. **资源清理**: 在不需要时调用`asrManager.destroy()`清理资源

## 完整示例

参考`TencentAsrExample.kt`文件中的完整使用示例，包含：

- 简单语音识别任务
- 带回调URL的任务
- 自定义配置任务
- 协程方式调用
- 批量任务处理

## 技术实现

- 基于OkHttp进行网络请求
- 使用Kotlinx Serialization进行JSON序列化
- 实现了完整的TC3-HMAC-SHA256签名算法
- 支持Kotlin协程异步处理
- 提供了完善的错误处理和日志记录

## 许可证

本项目基于原Rust项目改编，遵循相同的许可证协议。
