plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

// 获取环境变量，默认为develop环境
val appEnv = System.getenv("APP_ENV") ?: "develop"
println("当前构建环境: $appEnv")

android {
    namespace = "com.xunhe.aishoucang"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.xunhe.aishoucang"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        // 微信SDK配置
        manifestPlaceholders["WX_APPID"] = "wxb0872bb3945f31bc"

        // 只打包ARM64-v8a架构
        ndk {
            abiFilters.add("arm64-v8a")
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")

            // 根据环境变量设置混淆级别
            isMinifyEnabled = true

            // 生产环境使用最高级别混淆
            if (appEnv == "prd") {
                println("生产环境构建：启用最高级别混淆和代码保护")
                isShrinkResources = true // 启用资源压缩
                isDebuggable = false // 禁用调试

                // 使用高级混淆配置
                proguardFiles(
                    getDefaultProguardFile("proguard-android-optimize.txt"),
                    "proguard-rules.pro",
                    "proguard-rules-advanced.pro"
                )
            } else {
                println("非生产环境构建：使用标准混淆")
                isShrinkResources = false
                isDebuggable = true

                // 使用标准混淆配置
                proguardFiles(
                    getDefaultProguardFile("proguard-android-optimize.txt"),
                    "proguard-rules.pro"
                )
            }
        }
        debug {
            isMinifyEnabled = false
            isDebuggable = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
}

flutter {
    source = "../.."
}

// 添加一个任务，根据环境变量复制对应的配置文件
android.applicationVariants.all {
    val variant = this
    val variantName = variant.name

    // 创建一个任务，将环境特定的配置文件复制到assets目录
    val copyConfigTask = tasks.register<Copy>("copy${variantName.capitalize()}Config") {
        from("${rootProject.projectDir}/../config/config.${appEnv}.json")
        into("src/main/assets")
        rename("config.${appEnv}.json", "config.json")

        // 打印日志
        doFirst {
            println("正在复制环境配置: config.${appEnv}.json -> config.json")
        }
    }

    // 正确声明任务依赖关系
    // 确保mergeAssets任务依赖于copyConfig任务
    tasks.named("merge${variantName.capitalize()}Assets").configure {
        dependsOn(copyConfigTask)
    }

    // 将任务添加到构建过程中
    variant.mergeResourcesProvider.get().dependsOn(copyConfigTask)
}

dependencies {
    implementation("com.arthenica:ffmpeg-kit-https:6.0")
    implementation("androidx.recyclerview:recyclerview:1.3.2")

    // 添加AppCompat库依赖
    implementation("androidx.appcompat:appcompat:1.6.1")

    // OkHttp3依赖
    implementation("com.squareup.okhttp3:okhttp:4.11.0")

    // SwipeRefreshLayout依赖
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")

    // PaddleOCR SDK 依赖
    implementation(files("libs/paddleocr-sdk-release-2.0.aar"))

    // 阿里云OSS SDK依赖
    implementation("com.aliyun.dpa:oss-android-sdk:2.9.21")

    // Glide图片加载库依赖
    implementation("com.github.bumptech.glide:glide:4.16.0")
    annotationProcessor("com.github.bumptech.glide:compiler:4.16.0")

    // Google Play Core库依赖（解决R8混淆问题）
    implementation("com.google.android.play:core:1.10.3")

    // 友盟基础组件库（所有友盟业务SDK都依赖基础组件库）
    implementation("com.umeng.umsdk:common:9.4.4") // （必选）
    implementation("com.umeng.umsdk:asms:1.4.1") // asms包依赖(必选)
    implementation("com.umeng.umsdk:apm:1.5.2") // U-APM包依赖(必选)

    // 微信开放平台SDK
    implementation("com.tencent.mm.opensdk:wechat-sdk-android:6.8.0")

    // SparkChain 大模型识别 SDK
    implementation(files("libs/SparkChain.aar"))

    // 腾讯云ASR SDK
    implementation(files("libs/asr-file-recognize-release.aar"))  // 录音文件识别极速版
    implementation(files("libs/asr-one-sentence-release.aar"))    // 一句话识别
    implementation("com.google.code.gson:gson:2.8.5")            // 腾讯云ASR SDK依赖
}
