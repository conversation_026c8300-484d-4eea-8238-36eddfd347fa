# ========== 高级混淆配置 ==========
# 启用最激进的混淆优化
-optimizationpasses 7
-allowaccessmodification
-mergeinterfacesaggressively
-overloadaggressively
-repackageclasses ''
-flattenpackagehierarchy

# 混淆字典 - 使用无意义的字符
-obfuscationdictionary obfuscation-dictionary.txt
-classobfuscationdictionary obfuscation-dictionary.txt
-packageobfuscationdictionary obfuscation-dictionary.txt

# 移除调试信息
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 移除System.out.println
-assumenosideeffects class java.lang.System {
    public static void out.println(...);
    public static void err.println(...);
}

# ========== 必要的保留规则 ==========
# 阿里云OSS SDK混淆规则
-keep class com.alibaba.sdk.android.oss.** { *; }
-dontwarn okio.**
-dontwarn org.apache.commons.codec.binary.**

# Flutter核心类（仅保留必要的）
-keep class io.flutter.app.FlutterApplication { *; }
-keep class io.flutter.plugin.common.** { *; }
-keep class io.flutter.plugin.platform.PlatformView { *; }
-keep class io.flutter.view.FlutterMain { *; }
-keep class io.flutter.view.FlutterView { *; }
-keep class io.flutter.embedding.** { *; }
-dontwarn io.flutter.embedding.**

# Kotlin必要保留
-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**
-keepclassmembers class **$WhenMappings {
    <fields>;
}

# OkHttp混淆规则
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# Glide混淆规则
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-dontwarn com.bumptech.glide.load.resource.bitmap.VideoDecoder

# ========== 自定义应用代码混淆策略 ==========
# 仅保留必要的入口点，其他全部混淆
-keep class com.xunhe.aishoucang.MainActivity { *; }
-keep class com.xunhe.aishoucang.MainApplication { *; }

# 保留Native方法调用的类和方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留反射调用的类（如果有的话）
# -keep class com.xunhe.aishoucang.reflection.** { *; }

# 其他所有自定义代码都进行混淆
-keep,allowobfuscation class com.xunhe.aishoucang.** { *; }

# ========== Android系统组件保留规则 ==========
# 保留四大组件（仅保留必要的）
-keep public class * extends android.app.Activity {
    public void *(android.view.View);
    public void *(android.view.MenuItem);
}
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# 保留枚举类
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留Parcelable
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# 保留R文件中的静态字段
-keepclassmembers class **.R$* {
    public static <fields>;
}

# 保留序列化类
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# ========== 反射保护 ==========
# 保留通过反射访问的类和方法
-keepattributes Signature,InnerClasses,EnclosingMethod
-keepattributes RuntimeVisibleAnnotations,RuntimeVisibleParameterAnnotations

# 保留注解
-keepattributes *Annotation*

# 保留泛型信息
-keepattributes Signature

# ========== 第三方SDK保留规则 ==========
# Google Play Core库相关规则
-keep class com.google.android.play.core.** { *; }

# 友盟SDK混淆规则
-keep class com.umeng.** {*;}
-keep class org.repackage.** {*;}
-keep class com.uyumao.** { *; }
-keep class com.uc.** {*;}
-keep class com.efs.** {*;}

# 微信SDK保留规则
-keep class com.tencent.mm.opensdk.** { *; }
-keep class com.tencent.wxop.** { *; }
-keep class com.tencent.mm.sdk.** { *; }

# PaddleOCR SDK保留规则
-keep class com.baidu.paddle.** { *; }

# FFmpeg Kit保留规则
-keep class com.arthenica.ffmpegkit.** { *; }

# ========== JSON序列化保留规则 ==========
-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

# Gson序列化保留规则（如果使用）
-keepattributes Signature
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }

# ========== 高级混淆技术 ==========
# 控制流混淆
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*,!code/allocation/variable

# 字符串加密（需要额外插件支持）
# -applymapping mapping.txt

# 类名混淆
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# 移除未使用的代码
-dontshrink
-dontoptimize

# 保留行号信息（用于调试，生产环境可以移除）
# -keepattributes SourceFile,LineNumberTable

# ========== 安全增强 ==========
# 移除调试相关的类和方法
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# 移除断言
-assumenosideeffects class java.lang.System {
    public static void setProperty(...);
    public static String getProperty(...);
}

# 防止反编译工具识别
-renamesourcefileattribute SourceFile
