# ========== 高级混淆配置文件 ==========
# 此文件包含更激进的混淆规则，用于最大程度保护代码

# ========== 字符串混淆 ==========
# 移除所有日志输出
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
    public static int wtf(...);
}

# 移除System.out和System.err
-assumenosideeffects class java.lang.System {
    public static void out.println(...);
    public static void err.println(...);
    public static void out.print(...);
    public static void err.print(...);
}

# 移除printStackTrace
-assumenosideeffects class java.lang.Throwable {
    public void printStackTrace();
}

# ========== 控制流混淆 ==========
# 启用最激进的优化
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 7
-allowaccessmodification
-mergeinterfacesaggressively
-overloadaggressively

# ========== 类和包名混淆 ==========
# 重新打包所有类到根包
-repackageclasses ''
-flattenpackagehierarchy

# 使用短名称
-dontusemixedcaseclassnames
-keeppackagenames doNotKeepAThing

# ========== 反调试保护 ==========
# 移除调试信息
-assumenosideeffects class java.lang.Class {
    public java.lang.String getName();
    public java.lang.String getSimpleName();
    public java.lang.String getCanonicalName();
}

# 移除反射相关方法
-assumenosideeffects class java.lang.reflect.Method {
    public java.lang.String getName();
}

-assumenosideeffects class java.lang.reflect.Field {
    public java.lang.String getName();
}

# ========== 资源混淆 ==========
# 移除未使用的资源
-dontshrink
-dontoptimize

# ========== 字节码保护 ==========
# 添加虚假的方法调用
-adaptclassstrings
-adaptresourcefilenames
-adaptresourcefilecontents

# ========== 反编译保护 ==========
# 混淆源文件名
-renamesourcefileattribute ""

# 移除行号信息（生产环境）
# -printmapping mapping.txt

# ========== 自定义保护规则 ==========
# 保护关键业务逻辑类（根据实际情况调整）
-keep,allowobfuscation class com.xunhe.aishoucang.api.** { *; }
-keep,allowobfuscation class com.xunhe.aishoucang.models.** { *; }
-keep,allowobfuscation class com.xunhe.aishoucang.services.** { *; }
-keep,allowobfuscation class com.xunhe.aishoucang.utils.** { *; }

# 保护Native Bridge类的方法名（因为Flutter需要调用）
-keepclassmembers class com.xunhe.aishoucang.native_bridge.** {
    public *;
}
