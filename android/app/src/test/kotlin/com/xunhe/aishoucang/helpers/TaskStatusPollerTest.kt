package com.xunhe.aishoucang.helpers

import org.junit.Test
import org.junit.Assert.*

/**
 * TaskStatusPoller的单元测试
 */
class TaskStatusPollerTest {

    @Test
    fun testGetInstance() {
        // 测试单例模式
        val instance1 = TaskStatusPoller.getInstance()
        val instance2 = TaskStatusPoller.getInstance()
        
        assertNotNull(instance1)
        assertNotNull(instance2)
        assertSame(instance1, instance2)
    }

    @Test
    fun testGetPollingTaskCount() {
        val poller = TaskStatusPoller.getInstance()
        
        // 初始状态应该没有轮询任务
        assertEquals(0, poller.getPollingTaskCount())
    }

    @Test
    fun testIsPolling() {
        val poller = TaskStatusPoller.getInstance()
        val testTaskId = "test_task_123"
        
        // 初始状态应该不在轮询
        assertFalse(poller.isPolling(testTaskId))
    }

    @Test
    fun testStopAllPolling() {
        val poller = TaskStatusPoller.getInstance()
        
        // 停止所有轮询应该不会抛出异常
        poller.stopAllPolling()
        assertEquals(0, poller.getPollingTaskCount())
    }
}
