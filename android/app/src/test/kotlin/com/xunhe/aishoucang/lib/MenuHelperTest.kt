package com.xunhe.aishoucang.lib

import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.test.core.app.ApplicationProvider
import com.xunhe.aishoucang.R
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.util.ReflectionHelpers
import kotlin.test.assertEquals

/**
 * MenuHelper的单元测试
 * 测试在不同Android版本下菜单项的显示/隐藏逻辑
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.TIRAMISU, Build.VERSION_CODES.UPSIDE_DOWN_CAKE])
class MenuHelperTest {

    private lateinit var context: Context
    private lateinit var menuHelper: Menu<PERSON>elper

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        menuHelper = MenuHelper.getInstance(context)
    }

    @Test
    @Config(sdk = [Build.VERSION_CODES.TIRAMISU]) // Android 13
    fun `test screenshot menu items visible on Android 13`() {
        // 模拟Android 13环境
        ReflectionHelpers.setStaticField(Build.VERSION::class.java, "SDK_INT", Build.VERSION_CODES.TIRAMISU)
        
        // 创建菜单布局
        val inflater = LayoutInflater.from(context)
        val menuContent = inflater.inflate(R.layout.floating_menu_layout, null)
        
        // 模拟MenuHelper中的逻辑
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            menuContent.findViewById<LinearLayout>(R.id.menu_item_share)?.visibility = View.GONE
            menuContent.findViewById<LinearLayout>(R.id.menu_item_long_screenshot)?.visibility = View.GONE
        }
        
        // 验证菜单项在Android 13上应该可见
        val shareMenuItem = menuContent.findViewById<LinearLayout>(R.id.menu_item_share)
        val longScreenshotMenuItem = menuContent.findViewById<LinearLayout>(R.id.menu_item_long_screenshot)
        
        assertEquals(View.VISIBLE, shareMenuItem?.visibility, "截图收藏菜单项在Android 13上应该可见")
        assertEquals(View.VISIBLE, longScreenshotMenuItem?.visibility, "截长图菜单项在Android 13上应该可见")
    }

    @Test
    @Config(sdk = [Build.VERSION_CODES.UPSIDE_DOWN_CAKE]) // Android 14
    fun `test screenshot menu items hidden on Android 14`() {
        // 模拟Android 14环境
        ReflectionHelpers.setStaticField(Build.VERSION::class.java, "SDK_INT", Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
        
        // 创建菜单布局
        val inflater = LayoutInflater.from(context)
        val menuContent = inflater.inflate(R.layout.floating_menu_layout, null)
        
        // 模拟MenuHelper中的逻辑
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            menuContent.findViewById<LinearLayout>(R.id.menu_item_share)?.visibility = View.GONE
            menuContent.findViewById<LinearLayout>(R.id.menu_item_long_screenshot)?.visibility = View.GONE
        }
        
        // 验证菜单项在Android 14上应该隐藏
        val shareMenuItem = menuContent.findViewById<LinearLayout>(R.id.menu_item_share)
        val longScreenshotMenuItem = menuContent.findViewById<LinearLayout>(R.id.menu_item_long_screenshot)
        
        assertEquals(View.GONE, shareMenuItem?.visibility, "截图收藏菜单项在Android 14上应该隐藏")
        assertEquals(View.GONE, longScreenshotMenuItem?.visibility, "截长图菜单项在Android 14上应该隐藏")
    }

    @Test
    fun `test close floating window menu item always visible`() {
        // 创建菜单布局
        val inflater = LayoutInflater.from(context)
        val menuContent = inflater.inflate(R.layout.floating_menu_layout, null)
        
        // 关闭悬浮窗菜单项应该始终可见，不受Android版本影响
        val closeMenuItem = menuContent.findViewById<LinearLayout>(R.id.menu_item_close_floating_window)
        
        assertEquals(View.VISIBLE, closeMenuItem?.visibility, "关闭悬浮窗菜单项应该始终可见")
    }
}
