<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:ordering="together"
    android:fillAfter="true">
    <!-- 透明度动画：从1渐变到0 -->
    <alpha
        android:duration="200"
        android:fromAlpha="1.0"
        android:toAlpha="0.0"
        android:interpolator="@android:interpolator/fast_out_linear_in" />

    <!-- 缩放动画：从正常大小变小 -->
    <scale
        android:duration="200"
        android:fromXScale="1.0"
        android:fromYScale="1.0"
        android:pivotX="50%"
        android:pivotY="0%"
        android:toXScale="0.95"
        android:toYScale="0.95"
        android:interpolator="@android:interpolator/fast_out_linear_in" />

    <!-- 位置动画：向上移动一点点 -->
    <translate
        android:duration="200"
        android:fromYDelta="0%p"
        android:toYDelta="-3%p"
        android:interpolator="@android:interpolator/fast_out_linear_in" />
</set>
