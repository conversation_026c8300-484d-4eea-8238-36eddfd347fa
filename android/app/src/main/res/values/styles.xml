<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
        <item name="android:navigationBarColor">@android:color/white</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>
    <!-- 分享面板动画样式 -->
    <style name="SharePanelAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
    </style>
    <!-- Toast动画样式 -->
    <style name="ToastAnimation">
        <item name="android:windowEnterAnimation">@anim/toast_in</item>
        <item name="android:windowExitAnimation">@anim/toast_out</item>
    </style>
    <!-- 菜单动画样式 -->
    <style name="MenuAnimation">
        <item name="android:windowEnterAnimation">@anim/menu_fade_in</item>
        <item name="android:windowExitAnimation">@anim/menu_fade_out</item>
    </style>

    <!-- 透明对话框样式 -->
    <style name="TransparentDialog" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowMinWidthMajor">80%</item>
        <item name="android:windowMinWidthMinor">80%</item>
    </style>

    <!-- 对话框动画样式 -->
    <style name="DialogAnimation">
        <item name="android:windowEnterAnimation">@anim/dialog_fade_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_fade_out</item>
    </style>
</resources>
