<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/create_note_preview_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/create_note_preview_background"
    android:padding="20dp"
    android:elevation="16dp">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="创建笔记预览"
            android:textSize="16sp"
            android:textColor="#333333"
            android:textStyle="bold" />

    </RelativeLayout>

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/content_background"
        android:padding="16dp"
        android:layout_marginBottom="20dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="当前剪切板内容："
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginBottom="8dp" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="200dp">

            <TextView
                android:id="@+id/note_content_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="暂无内容"
                android:textSize="14sp"
                android:textColor="#333333"
                android:lineSpacingExtra="4dp"
                android:textIsSelectable="true"
                android:background="@android:color/transparent" />

        </ScrollView>

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/cancel_button"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="取消"
            android:textSize="14sp"
            android:textColor="#666666"
            android:background="@drawable/cancel_button_background"
            android:clickable="true"
            android:focusable="true" />

        <Button
            android:id="@+id/confirm_button"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="确定"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:background="@drawable/confirm_button_background"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</LinearLayout>
