<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/sidebar_floating_menu_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/sidebar_floating_menu_background"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    android:paddingTop="14dp"
    android:paddingBottom="14dp"
    android:elevation="12dp">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:text="AI工具箱"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/close_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="0dp"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="#FFFFFF"
            android:background="@drawable/circle_button_background"
            android:clickable="true"
            android:focusable="true"
            android:padding="8dp" />

    </RelativeLayout>

    <!-- 第一行功能网格 -->
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="3"
        android:rowCount="1"
        android:alignmentMode="alignBounds"
        android:useDefaultMargins="false">

        <!-- 录屏 -->
        <LinearLayout
            android:id="@+id/menu_item_record_screen"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/ic_media_play"
                android:tint="#FFFFFF"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="录屏"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

        <!-- 截屏 -->
        <LinearLayout
            android:id="@+id/menu_item_screenshot"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/ic_menu_camera"
                android:tint="#FFFFFF"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="截屏"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

        <!-- 投屏 -->
        <LinearLayout
            android:id="@+id/menu_item_cast_screen"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/ic_media_play"
                android:tint="#FFFFFF"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="投屏"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

    </GridLayout>

    <!-- 第二行功能网格 -->
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="3"
        android:rowCount="1"
        android:alignmentMode="alignBounds"
        android:useDefaultMargins="false">

        <!-- 息屏听剧 -->
        <LinearLayout
            android:id="@+id/menu_item_screen_off_listening"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/ic_media_pause"
                android:tint="#FFFFFF"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="息屏听剧"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

        <!-- 识别BGM -->
        <LinearLayout
            android:id="@+id/menu_item_recognize_bgm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/ic_media_play"
                android:tint="#FFFFFF"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="识别BGM"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

        <!-- 实时字幕 -->
        <LinearLayout
            android:id="@+id/menu_item_real_time_subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/ic_menu_edit"
                android:tint="#FFFFFF"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="实时字幕"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

    </GridLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#1AFFFFFF"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp" />

    <!-- 第三行功能网格 -->
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="3"
        android:rowCount="1"
        android:alignmentMode="alignBounds"
        android:useDefaultMargins="false">

        <!-- 创建笔记 -->
        <LinearLayout
            android:id="@+id/menu_item_create_note"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/ic_menu_edit"
                android:tint="#00bfff"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="创建笔记"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

        <!-- 提取文案 -->
        <LinearLayout
            android:id="@+id/menu_item_extract_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/ic_menu_sort_by_size"
                android:tint="#00bfff"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="提取文案"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

        <!-- 下载视频 -->
        <LinearLayout
            android:id="@+id/menu_item_download_video"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@android:drawable/stat_sys_download"
                android:tint="#FFFFFF"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="下载视频"
                android:textSize="11sp"
                android:textColor="#d1d3d6"
                android:singleLine="true" />

        </LinearLayout>

    </GridLayout>

</LinearLayout>
