<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/dialog_background"
    android:elevation="8dp"
    android:layout_margin="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="添加到收藏夹"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="标题"
        android:textSize="14sp"
        android:layout_marginBottom="4dp" />

    <EditText
        android:id="@+id/edit_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入标题（必填）"
        android:inputType="text"
        android:maxLines="1"
        android:layout_marginBottom="12dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="描述"
        android:textSize="14sp"
        android:layout_marginBottom="4dp" />

    <EditText
        android:id="@+id/edit_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入描述（选填）"
        android:inputType="textMultiLine"
        android:minLines="2"
        android:maxLines="4"
        android:layout_marginBottom="12dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="收藏夹"
        android:textSize="14sp"
        android:layout_marginBottom="4dp" />

    <TextView
        android:id="@+id/text_favorite"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="加载中..."
        android:padding="12dp"
        android:background="#F5F5F5"
        android:drawableEnd="@android:drawable/arrow_down_float"
        android:layout_marginBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:layout_marginEnd="8dp"
            style="?android:attr/buttonBarButtonStyle" />

        <Button
            android:id="@+id/btn_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="保存"
            style="?android:attr/buttonBarButtonStyle" />
    </LinearLayout>
</LinearLayout>
