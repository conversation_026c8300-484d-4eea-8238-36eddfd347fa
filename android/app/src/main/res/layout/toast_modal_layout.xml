<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modal_overlay_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="0dp">

    <!-- Modal容器 - 直接居中显示 -->
    <LinearLayout
        android:id="@+id/modal_container"
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:background="@drawable/modal_background"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingStart="24dp"
        android:paddingTop="32dp"
        android:paddingEnd="24dp"
        android:paddingBottom="24dp">

        <!-- 标题 -->
        <TextView
            android:id="@+id/modal_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="确认删除"
            android:textColor="#FF333333"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />

        <!-- 内容 -->
        <TextView
            android:id="@+id/modal_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="确认要删除这个收藏吗？"
            android:textColor="#FF666666"
            android:textSize="14sp"
            android:gravity="center"
            android:lineSpacingExtra="2dp" />

        <!-- 按钮容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- 取消按钮 -->
            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:background="@drawable/modal_button_cancel_background"
                android:text="取消"
                android:textColor="#FF666666"
                android:textSize="16sp"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

            <!-- 确定按钮 -->
            <TextView
                android:id="@+id/btn_confirm"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:background="@drawable/modal_button_confirm_background"
                android:text="确定"
                android:textColor="#FFFFFFFF"
                android:textSize="16sp"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
