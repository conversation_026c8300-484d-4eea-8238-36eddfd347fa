<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background_with_shadow">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_title_background"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:paddingStart="24dp"
        android:paddingEnd="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="创建收藏夹"
            android:textSize="18sp"
            android:textColor="#2C3E50"
            android:fontFamily="sans-serif-medium"
            android:layout_centerInParent="true" />
    </RelativeLayout>

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="请输入收藏夹名称"
            android:textSize="15sp"
            android:textColor="#2C3E50"
            android:fontFamily="sans-serif"
            android:layout_marginBottom="12dp" />

        <EditText
            android:id="@+id/favorite_name_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="例如：我的收藏"
            android:padding="14dp"
            android:background="@drawable/modern_edit_text_background"
            android:textSize="15sp"
            android:textColor="#2C3E50"
            android:textColorHint="#AEB6BF"
            android:maxLines="1"
            android:inputType="text"
            android:layout_marginBottom="24dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:layout_marginTop="8dp">

            <Button
                android:id="@+id/cancel_dialog_button"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="44dp"
                android:text="取消"
                android:textColor="#5D6D7E"
                android:textSize="15sp"
                android:background="@drawable/secondary_button_background"
                android:elevation="0dp"
                android:stateListAnimator="@null"
                android:layout_marginEnd="12dp" />

            <Button
                android:id="@+id/create_favorite_button"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="44dp"
                android:text="创建"
                android:textColor="#FFFFFF"
                android:textSize="15sp"
                android:elevation="0dp"
                android:stateListAnimator="@null"
                android:background="@drawable/primary_button_background" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
