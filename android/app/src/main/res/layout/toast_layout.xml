<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="0dp">

    <!-- 内部容器 - 实际的Toast内容 -->
    <LinearLayout
        android:id="@+id/toast_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/toast_background"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingTop="10dp"
        android:paddingEnd="16dp"
        android:paddingBottom="10dp">

        <TextView
            android:id="@+id/toast_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout> 