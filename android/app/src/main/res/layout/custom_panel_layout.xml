<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/custom_panel_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/modern_panel_background"
    android:orientation="vertical"
    android:padding="20dp">

    <TextView
        android:id="@+id/custom_title_text_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="手动收藏"
        android:textColor="#2C3E50"
        android:textSize="18sp"
        android:fontFamily="sans-serif-medium"
        android:layout_marginBottom="24dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="20dp"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 链接输入框 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="链接"
                android:textColor="#2C3E50"
                android:textSize="14sp"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="6dp" />

            <EditText
                android:id="@+id/link_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/modern_input_background"
                android:hint="请输入或粘贴链接"
                android:padding="14dp"
                android:textSize="14sp"
                android:textColorHint="#AEB6BF"
                android:inputType="textUri"
                android:layout_marginBottom="20dp" />

            <!-- 标题输入框 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="标题"
                android:textColor="#2C3E50"
                android:textSize="14sp"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="6dp" />

            <EditText
                android:id="@+id/title_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/modern_input_background"
                android:hint="请输入标题"
                android:padding="14dp"
                android:textSize="14sp"
                android:textColorHint="#AEB6BF"
                android:inputType="text"
                android:layout_marginBottom="20dp" />

            <!-- 描述输入框 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="描述"
                android:textColor="#2C3E50"
                android:textSize="14sp"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="6dp" />

            <EditText
                android:id="@+id/description_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/modern_input_background"
                android:hint="请输入描述（可选）"
                android:padding="14dp"
                android:textSize="14sp"
                android:textColorHint="#AEB6BF"
                android:inputType="textMultiLine"
                android:minLines="2"
                android:gravity="top|start"
                android:layout_marginBottom="20dp" />

            <!-- 收藏夹选择 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="收藏夹"
                android:textColor="#2C3E50"
                android:textSize="14sp"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="6dp" />

            <Spinner
                android:id="@+id/folder_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/modern_spinner_background"
                android:padding="14dp"
                android:layout_marginBottom="20dp" />
        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/custom_cancel_button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/modern_secondary_button"
            android:text="取消"
            android:textColor="#5D6D7E"
            android:textSize="15sp"
            android:fontFamily="sans-serif-medium"
            android:layout_marginEnd="8dp"
            android:elevation="0dp" />

        <Button
            android:id="@+id/custom_confirm_button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/modern_primary_button"
            android:text="确认"
            android:textColor="#FFFFFF"
            android:textSize="15sp"
            android:fontFamily="sans-serif-medium"
            android:layout_marginStart="8dp"
            android:elevation="0dp" />
    </LinearLayout>
</LinearLayout>
