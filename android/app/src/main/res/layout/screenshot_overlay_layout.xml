<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/screenshot_overlay_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 自定义选择视图 -->
    <com.xunhe.aishoucang.lib.ScreenshotSelectionView
        android:id="@+id/selection_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 底部操作按钮容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左下角删除按钮 -->
        <ImageButton
            android:id="@+id/delete_button"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:background="@drawable/circle_button_background"
            android:src="@android:drawable/ic_menu_delete"
            android:contentDescription="删除"
            android:padding="12dp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- 右下角确认按钮 -->
        <ImageButton
            android:id="@+id/confirm_button"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:background="@drawable/circle_button_background"
            android:src="@android:drawable/ic_menu_send"
            android:contentDescription="确认"
            android:padding="12dp" />
    </LinearLayout>
</FrameLayout>
