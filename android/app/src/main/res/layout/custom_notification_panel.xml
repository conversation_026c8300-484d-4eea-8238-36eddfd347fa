<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingStart="8dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:paddingEnd="0dp"
    android:background="#FFFFFF">

    <!-- APP图标 -->
    <ImageView
        android:id="@+id/app_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:scaleType="centerCrop"
        android:contentDescription="应用图标" />

    <!-- 应用名称 -->
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="点击右侧按钮打开悬浮窗"
        android:textSize="14sp"
        android:textColor="#333333"
        android:gravity="center_vertical" />

    <!-- 开关按钮 (超大尺寸，贴近右边) -->
    <ImageView
        android:id="@+id/switch_button"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@drawable/ic_switch_off"
        android:padding="4dp"
        android:scaleType="centerInside"
        android:contentDescription="开关" />

</LinearLayout>
