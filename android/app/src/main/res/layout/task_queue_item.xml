<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/task_item_background"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="4dp"
    android:padding="16dp"
    android:clickable="true"
    android:focusable="true">

    <!-- 标题和状态行 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 任务标题 -->
        <TextView
            android:id="@+id/task_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="任务标题"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 任务状态 -->
        <TextView
            android:id="@+id/task_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="进行中"
            android:textColor="#3D7AF5"
            android:textSize="12sp"
            android:textStyle="bold"
            android:background="@drawable/status_badge_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp" />
    </LinearLayout>

    <!-- 任务描述 - 隐藏副标题 -->
    <TextView
        android:id="@+id/task_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="任务描述信息"
        android:textColor="#CCCCCC"
        android:textSize="14sp"
        android:maxLines="2"
        android:ellipsize="end"
        android:visibility="gone" />

    <!-- 平台信息和时间行 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 平台信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 平台图标 -->
            <ImageView
                android:id="@+id/platform_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_launcher_foreground"
                android:scaleType="centerCrop"
                android:visibility="visible" />

            <!-- 平台名称 -->
            <TextView
                android:id="@+id/platform_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="B站"
                android:textColor="#999999"
                android:textSize="12sp" />
        </LinearLayout>

        <!-- 时间 -->
        <TextView
            android:id="@+id/task_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="12-25 14:30"
            android:textColor="#999999"
            android:textSize="12sp" />
    </LinearLayout>
</LinearLayout>
