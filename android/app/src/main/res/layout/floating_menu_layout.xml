<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/floating_menu_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/floating_menu_card_background"
    android:padding="4dp">

    <!-- 菜单项1：截图收藏 -->
    <LinearLayout
        android:id="@+id/menu_item_share"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp"
        android:background="?android:attr/selectableItemBackground">

        <ImageView
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@android:drawable/ic_menu_camera"
            android:contentDescription="截图收藏"
            android:tint="#1EB9EF" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="截图收藏"
            android:textColor="#2C3E50"
            android:textSize="15sp"
            android:fontFamily="sans-serif" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:background="#ECF0F1" />

    <!-- 菜单项2：截长图 -->
    <LinearLayout
        android:id="@+id/menu_item_long_screenshot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp"
        android:background="?android:attr/selectableItemBackground">

        <ImageView
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@android:drawable/ic_menu_crop"
            android:contentDescription="截长图"
            android:tint="#1EB9EF" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="截长图"
            android:textColor="#2C3E50"
            android:textSize="15sp"
            android:fontFamily="sans-serif" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:background="#ECF0F1" />

    <!-- 菜单项5：关闭悬浮窗 -->
    <LinearLayout
        android:id="@+id/menu_item_close_floating_window"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp"
        android:background="?android:attr/selectableItemBackground">

        <ImageView
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:contentDescription="关闭悬浮窗"
            android:tint="#E74C3C" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="关闭悬浮窗"
            android:textColor="#E74C3C"
            android:textSize="15sp"
            android:fontFamily="sans-serif" />
    </LinearLayout>
</LinearLayout>
