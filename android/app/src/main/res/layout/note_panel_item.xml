<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:orientation="vertical"
  android:gravity="center"
  android:paddingLeft="10dp"
  android:paddingRight="10dp"
  android:paddingTop="0dp"
  android:paddingBottom="10dp"
  android:background="?android:attr/selectableItemBackground"
  android:clickable="true">

  <FrameLayout
    android:layout_width="56dp"
    android:layout_height="56dp"
    android:background="@drawable/favorite_item_frame_background"
    android:clipChildren="true"
    android:clipToOutline="true"
    android:layout_marginBottom="6dp">

    <!-- 背景颜色视图 -->
    <View
      android:id="@+id/note_background"
      android:layout_width="56dp"
      android:layout_height="56dp"
      android:background="#F6F6F6" />

    <!-- 封面图片 -->
    <ImageView
      android:id="@+id/note_cover"
      android:layout_width="56dp"
      android:layout_height="56dp"
      android:scaleType="centerCrop"
      android:visibility="gone" />
  </FrameLayout>

  <TextView
    android:id="@+id/note_text"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="笔记"
    android:textColor="#FFFFFF"
    android:textSize="13sp"
    android:gravity="center"
    android:maxLines="1"
    android:ellipsize="end" />
</LinearLayout>
