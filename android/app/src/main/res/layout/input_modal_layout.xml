<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modal_overlay_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="0dp">

    <!-- Modal容器 -->
    <LinearLayout
        android:id="@+id/modal_container"
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:background="@drawable/modal_background"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingStart="24dp"
        android:paddingTop="32dp"
        android:paddingEnd="24dp"
        android:paddingBottom="24dp">

        <!-- 标题 -->
        <TextView
            android:id="@+id/modal_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请输入"
            android:textColor="#FF333333"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />

        <!-- 输入框 -->
        <EditText
            android:id="@+id/modal_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:background="@drawable/edit_text_background"
            android:hint="请输入内容"
            android:textColor="#FF333333"
            android:textColorHint="#FF999999"
            android:textSize="14sp"
            android:paddingStart="12dp"
            android:paddingTop="12dp"
            android:paddingEnd="12dp"
            android:paddingBottom="12dp"
            android:inputType="text"
            android:maxLines="1"
            android:singleLine="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:imeOptions="actionDone" />

        <!-- 按钮容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- 取消按钮 -->
            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:background="@drawable/modal_button_cancel_background"
                android:text="取消"
                android:textColor="#FF666666"
                android:textSize="16sp"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

            <!-- 确定按钮 -->
            <TextView
                android:id="@+id/btn_confirm"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:background="@drawable/modal_button_confirm_background"
                android:text="确定"
                android:textColor="#FFFFFFFF"
                android:textSize="16sp"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
