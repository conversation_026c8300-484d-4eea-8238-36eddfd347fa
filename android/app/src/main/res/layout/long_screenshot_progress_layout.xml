<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/floating_menu_background"
        android:orientation="vertical"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="长截图"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold" />

            <ImageButton
                android:id="@+id/cancel_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="取消"
                android:src="@android:drawable/ic_menu_close_clear_cancel"
                android:tint="#FFFFFF" />
        </LinearLayout>

        <TextView
            android:id="@+id/long_screenshot_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="正在准备..."
            android:textColor="#FFFFFF"
            android:textSize="12sp" />

        <ProgressBar
            android:id="@+id/long_screenshot_progress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:max="100"
            android:progress="0" />

    </LinearLayout>
</FrameLayout>
