<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:gravity="center"
  android:orientation="vertical"
  android:padding="10dp">

  <FrameLayout
    android:id="@+id/item_frame"
    android:layout_width="56dp"
    android:layout_height="56dp"
    android:background="@drawable/favorite_item_frame_background"
    android:clipChildren="true"
    android:clipToOutline="true">

    <!-- 背景颜色视图 -->
    <View
      android:id="@+id/share_icon"
      android:layout_width="56dp"
      android:layout_height="56dp"
      android:background="#6B9EFF" />

    <!-- 封面图片 -->
    <ImageView
      android:id="@+id/cover_image"
      android:layout_width="56dp"
      android:layout_height="56dp"
      android:scaleType="centerCrop"
      android:visibility="gone" />

    <!-- 书签图标 -->
    <ImageView
      android:id="@+id/bookmark_icon"
      android:layout_width="24dp"
      android:layout_height="24dp"
      android:layout_gravity="center"
      android:src="@drawable/ic_bookmark"
      android:tint="#FFFFFF" />
  </FrameLayout>

  <TextView
    android:id="@+id/share_name"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="6dp"
    android:text="分享渠道"
    android:textColor="#2C3E50"
    android:textSize="13sp"
    android:fontFamily="sans-serif"
    android:maxLines="1"
    android:ellipsize="end" />
</LinearLayout>
