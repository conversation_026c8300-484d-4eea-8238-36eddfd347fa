(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;

    // 提取小红书商品数据的函数
    function extractXiaohongshuGoods() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            var data = {};

            // 小红书商品页面特定的选择器
            var selectors = {
                title: '.goods-name, .description-container .goods-name',
                price: '.price-container .price',
                images: '.carousel-image, .swiper-slide img',
                shopName: '.seller-name',
                shopAvatar: '.seller-left .logo'
            };

            // 提取标题
            var titleElement = document.querySelector(selectors.title);
            if (titleElement) {
                data.title = titleElement.textContent.trim();
            }

            // 提取价格
            var priceElement = document.querySelector(selectors.price);
            if (priceElement) {
                var priceText = priceElement.textContent.trim();
                var priceMatch = priceText.match(/[\d\.]+/);
                data.price = priceMatch ? priceMatch[0] : priceText;

                if (priceText.includes('¥')) {
                    data.price = '¥' + data.price;
                }
            }

            // 提取店铺信息
            var shopNameElement = document.querySelector(selectors.shopName);
            var shopAvatarElement = document.querySelector(selectors.shopAvatar);

            if (shopNameElement || shopAvatarElement) {
                data.shop = {};

                if (shopNameElement) {
                    data.shop.name = shopNameElement.textContent.trim();
                }

                if (shopAvatarElement) {
                    data.shop.avatar = shopAvatarElement.src || '';
                }
            }

            // 提取图片列表
            var imageElements = document.querySelectorAll(selectors.images);
            if (imageElements && imageElements.length > 0) {
                data.images = [];
                for (var i = 0; i < imageElements.length; i++) {
                    var imgSrc = imageElements[i].src || '';
                    if (imgSrc) {
                        if (imgSrc.startsWith('//')) {
                            imgSrc = 'https:' + imgSrc;
                        }
                        data.images.push(imgSrc);
                    }
                }

                // 提取封面图
                data.coverImage = data.images.length > 0 ? data.images[0] : '';
            }

            // 提取URL
            data.url = window.location.href;

            // 构建schemeURL
            try {
                var currentUrl = window.location.href;
                var goodsIdMatch = currentUrl.match(/\/goods-detail\/([^?]+)/);

                if (goodsIdMatch && goodsIdMatch[1]) {
                    var goodsId = goodsIdMatch[1];
                    data.schemeURL = "xhsdiscover://goods_detail/" + goodsId;
                } else {
                    // 尝试从instation_link参数中提取
                    var instationLinkMatch = currentUrl.match(/instation_link=([^&]+)/);
                    if (instationLinkMatch && instationLinkMatch[1]) {
                        var decodedLink = decodeURIComponent(instationLinkMatch[1]);
                        var goodsDetailMatch = decodedLink.match(/goods_detail\/([^?&]+)/);
                        if (goodsDetailMatch && goodsDetailMatch[1]) {
                            data.schemeURL = "xhsdiscover://goods_detail/" + goodsDetailMatch[1];
                        }
                    }
                }
            } catch (error) {
                console.error("构建schemeURL时出错:", error);
            }

            // 标记结果已发送
            resultSent = true;

            // 清除所有定时器
            clearAllTimers();

            // 返回结果
            Android.findResult(JSON.stringify(data));
        } catch (e) {
            // 标记结果已发送
            resultSent = true;

            // 清除所有定时器
            clearAllTimers();

            // 发生错误，返回错误信息
            Android.findResult(JSON.stringify({
                error: e.message,
                url: window.location.href
            }));
        }
    }

    // 存储所有定时器ID
    var timers = [];

    // 清除所有定时器的函数
    function clearAllTimers() {
        for (var i = 0; i < timers.length; i++) {
            clearTimeout(timers[i]);
        }
        timers = [];
    }

    // 定义轮询函数，每500ms检查一次页面是否加载完成
    function pollForData() {
        // 如果结果已经发送，不再继续轮询
        if (resultSent) {
            return;
        }

        // 检查数据是否已加载
        var dataLoaded = false;

        // 检查DOM元素是否存在
        var selectors = [
            '.goods-name',
            '.description-container .goods-name',
            '.price-container .price',
            '.carousel-image',
            '.seller-name'
        ];

        for (var i = 0; i < selectors.length; i++) {
            if (document.querySelector(selectors[i])) {
                dataLoaded = true;
                break;
            }
        }

        if (dataLoaded) {
            // 数据已加载，提取数据
            extractXiaohongshuGoods();
        } else {
            // 数据未加载，继续轮询
            var timerId = setTimeout(pollForData, 500);
            timers.push(timerId);
        }
    }

    // 开始轮询
    pollForData();

    // 设置最大轮询时间（8秒）
    var timeoutId = setTimeout(function() {
        // 超时，尝试提取当前数据
        extractXiaohongshuGoods();
    }, 8000);
    timers.push(timeoutId);
})();
