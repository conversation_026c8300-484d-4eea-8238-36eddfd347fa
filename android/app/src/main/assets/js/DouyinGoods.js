(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;

    // 从URL中提取detail_schema参数并解码
    function extractDetailSchemaFromUrl(url) {
        try {
            console.log("开始提取detail_schema参数，URL:", url);

            // 创建URL对象来解析查询参数
            var urlObj = new URL(url);
            var detailSchema = urlObj.searchParams.get('detail_schema');

            if (!detailSchema) {
                console.log("未找到detail_schema参数");
                return null;
            }

            console.log("找到detail_schema参数（编码前）:", detailSchema);

            // 对detail_schema参数进行URL解码
            // 可能需要多次解码，因为参数可能被多次编码
            var decodedSchema = detailSchema;
            var maxDecodeAttempts = 3; // 最多尝试解码3次

            for (var i = 0; i < maxDecodeAttempts; i++) {
                try {
                    var previousSchema = decodedSchema;
                    decodedSchema = decodeURIComponent(decodedSchema);
                    console.log("解码第" + (i + 1) + "次:", decodedSchema);

                    // 如果解码后没有变化，说明已经完全解码
                    if (decodedSchema === previousSchema) {
                        break;
                    }
                } catch (decodeError) {
                    console.error("解码第" + (i + 1) + "次失败:", decodeError);
                    break;
                }
            }

            console.log("最终解码结果:", decodedSchema);
            return decodedSchema;

        } catch (error) {
            console.error("提取detail_schema参数时出错:", error);
            return null;
        }
    }

    // 提取抖音商品数据的函数
    function extractDouyinGoods() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            var data = {};

            // 抖音商品页面特定的选择器
            var selectors = {
                title: '.title-info',
                coverImage: '.default_container .swiper-slide div',
                shopName: '.shop-component__shop-content__basic-info__title-area__name',
                shopAvatar: '.shop-component__shop-content__logo'
            };

            // 提取标题
            var titleElement = document.querySelector(selectors.title);
            if (titleElement) {
                data.title = titleElement.textContent.trim();
            }

            // 提取封面图 - 从backgroundImage样式中获取
            var coverElements = document.querySelectorAll(selectors.coverImage);
            if (coverElements && coverElements.length > 0) {
                var firstCoverElement = coverElements[0];
                var backgroundImage = window.getComputedStyle(firstCoverElement).backgroundImage;
                if (backgroundImage && backgroundImage !== 'none') {
                    // 从 url("...") 格式中提取URL
                    var urlMatch = backgroundImage.match(/url\(["']?([^"')]+)["']?\)/);
                    if (urlMatch && urlMatch[1]) {
                        data.coverImage = urlMatch[1];
                        // 确保URL是完整的
                        if (data.coverImage.startsWith('//')) {
                            data.coverImage = 'https:' + data.coverImage;
                        }
                    }
                }
            }

            // 提取店铺昵称
            var shopNameElement = document.querySelector(selectors.shopName);
            if (shopNameElement) {
                data.shopName = shopNameElement.textContent.trim();
            }

            // 提取店铺头像 - 从backgroundImage样式中获取
            var shopAvatarElement = document.querySelector(selectors.shopAvatar);
            if (shopAvatarElement) {
                var avatarBackgroundImage = window.getComputedStyle(shopAvatarElement).backgroundImage;
                if (avatarBackgroundImage && avatarBackgroundImage !== 'none') {
                    // 从 url("...") 格式中提取URL
                    var avatarUrlMatch = avatarBackgroundImage.match(/url\(["']?([^"')]+)["']?\)/);
                    if (avatarUrlMatch && avatarUrlMatch[1]) {
                        data.shopAvatar = avatarUrlMatch[1];
                        // 确保URL是完整的
                        if (data.shopAvatar.startsWith('//')) {
                            data.shopAvatar = 'https:' + data.shopAvatar;
                        }
                    }
                }
            }

            // 提取URL
            data.url = window.location.href;

            // 构建schemeURL - 从detail_schema参数中提取
            try {
                var currentUrl = window.location.href;
                console.log("当前URL:", currentUrl);

                // 尝试从URL中提取detail_schema参数
                var detailSchemaUrl = extractDetailSchemaFromUrl(currentUrl);

                if (detailSchemaUrl) {
                    console.log("从detail_schema参数提取到scheme URL:", detailSchemaUrl);
                    data.schemeURL = detailSchemaUrl;
                } else {
                    console.log("未找到detail_schema参数，使用原始URL");
                    data.schemeURL = currentUrl;
                }
            } catch (error) {
                console.error("构建schemeURL时出错:", error);
                data.schemeURL = window.location.href;
            }

            // 标记结果已发送
            resultSent = true;

            // 清除所有定时器
            clearAllTimers();

            // 返回结果
            console.log("抖音商品JS执行完成，返回结果");
            Android.findResult(JSON.stringify(data));
        } catch (e) {
            // 标记结果已发送
            resultSent = true;

            // 清除所有定时器
            clearAllTimers();

            // 发生错误，返回错误信息
            console.error("抖音商品JS执行出错: " + e.message);
            Android.findResult(JSON.stringify({
                error: e.message,
                url: window.location.href
            }));
        }
    }

    // 存储所有定时器ID
    var timers = [];

    // 清除所有定时器的函数
    function clearAllTimers() {
        for (var i = 0; i < timers.length; i++) {
            clearTimeout(timers[i]);
        }
        timers = [];
    }

    // 定义轮询函数，每500ms检查一次页面是否加载完成
    function pollForData() {
        // 如果结果已经发送，不再继续轮询
        if (resultSent) {
            return;
        }

        // 检查数据是否已加载
        var dataLoaded = false;

        // 检查DOM元素是否存在 - 根据抖音商品页面结构更新选择器
        var selectors = [
            '.title-info',
            '.shop-component__shop-content__basic-info__title-area__name',
            '.shop-component__shop-content__logo',
            '.default_container .swiper-slide div'
        ];

        for (var i = 0; i < selectors.length; i++) {
            if (document.querySelector(selectors[i])) {
                dataLoaded = true;
                break;
            }
        }

        if (dataLoaded) {
            // 数据已加载，提取数据
            extractDouyinGoods();
        } else {
            // 数据未加载，继续轮询
            var timerId = setTimeout(pollForData, 500);
            timers.push(timerId);
        }
    }

    // 开始轮询
    pollForData();

    // 设置最大轮询时间（8秒）
    var timeoutId = setTimeout(function() {
        // 超时，尝试提取当前数据
        extractDouyinGoods();
    }, 8000);
    timers.push(timeoutId);
})();
