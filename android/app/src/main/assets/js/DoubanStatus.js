(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;
    var checkingData = false;
    var timers = [];

    // 简单的sleep函数
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 清除所有定时器的函数
    function clearAllTimers() {
        for (var i = 0; i < timers.length; i++) {
            clearTimeout(timers[i]);
        }
        timers = [];
    }

    // 提取豆瓣状态数据的函数
    function extractDoubanStatus() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            var data = {};

            // 检查是否是有效的豆瓣链接
            var currentUrl = window.location.href;
            if (!isValidDoubanUrl(currentUrl)) {
                throw new Error("不是有效的豆瓣链接");
            }

            // 提取标题
            var titleElement = document.querySelector('.title');
            if (titleElement) {
                data.title = titleElement.textContent.trim();
            } else {
                // 兼容性取法：尝试从root下的h1获取标题
                var rootH1Element = document.querySelector('#root') && document.querySelector('#root').querySelector('h1');
                if (rootH1Element) {
                    data.title = rootH1Element.textContent.trim();
                } else {
                    // 如果没有找到标题，使用页面标题
                    data.title = document.title.replace(' - 豆瓣', '').trim();
                }
            }

            // 提取作者头像
            var avatarElement = document.querySelector('img.author-icon');
            if (avatarElement) {
                data.avatarUrl = avatarElement.src || '';
                // 确保头像URL是完整的
                if (data.avatarUrl && data.avatarUrl.startsWith('//')) {
                    data.avatarUrl = 'https:' + data.avatarUrl;
                }
            } else {
                // 兼容性取法：尝试从root下的img获取头像
                var rootImgElement = document.querySelector('#root') && document.querySelector('#root').querySelector('img');
                if (rootImgElement) {
                    data.avatarUrl = rootImgElement.src || '';
                    // 确保头像URL是完整的
                    if (data.avatarUrl && data.avatarUrl.startsWith('//')) {
                        data.avatarUrl = 'https:' + data.avatarUrl;
                    }
                }
            }

            // 提取作者昵称
            var nameElement = document.querySelector('.user-title .name');
            if (nameElement) {
                data.authorName = nameElement.textContent.trim();
            } else {
                // 兼容性取法：尝试从root下的p获取作者
                var rootPElement = document.querySelector('#root') && document.querySelector('#root').querySelector('p');
                if (rootPElement) {
                    data.authorName = rootPElement.textContent.trim();
                }
            }

            // 提取正文内容
            var contentElement = document.querySelector('#content');
            if (contentElement) {
                var contentText = contentElement.innerText;
                // 使用正则表达式提取中文字符
                var chineseText = contentText.match(/[\u4e00-\u9fa5]+/g);
                if (chineseText && chineseText.length > 0) {
                    // 将所有中文片段连接起来
                    var fullChineseText = chineseText.join('');
                    // 截取前100个字符
                    data.description = fullChineseText.substring(0, 100);
                } else {
                    // 如果没有中文，则使用原始文本的前100个字符
                    data.description = contentText.substring(0, 100);
                }
            } else {
                // 兼容性取法：尝试从.shownotes下的p获取描述
                var shownotesElement = document.querySelector('.shownotes');
                if (shownotesElement) {
                    var shownotesP = shownotesElement.querySelector('p');
                    if (shownotesP) {
                        var shownotesText = shownotesP.innerText;
                        // 使用正则表达式提取中文字符
                        var chineseText = shownotesText.match(/[\u4e00-\u9fa5]+/g);
                        if (chineseText && chineseText.length > 0) {
                            // 将所有中文片段连接起来
                            var fullChineseText = chineseText.join('');
                            // 截取前50个字符
                            data.description = fullChineseText.substring(0, 50);
                        } else {
                            // 如果没有中文，则使用原始文本的前50个字符
                            data.description = shownotesText.substring(0, 50);
                        }
                    }
                }
            }

            // 提取封面图片和所有图片
            var imageElements = document.querySelectorAll('#note-body img, .phone-item img');
            var hasImages = imageElements && imageElements.length > 0;

            // 初始化图片数组
            data.images = [];

            // 如果找到了常规图片，处理它们
            if (hasImages) {
                // 设置第一张图片为封面
                var coverElement = imageElements[0];
                data.imageUrl = coverElement.src || '';
                // 确保封面URL是完整的
                if (data.imageUrl && data.imageUrl.startsWith('//')) {
                    data.imageUrl = 'https:' + data.imageUrl;
                }

                // 处理所有图片
                for (var i = 0; i < imageElements.length; i++) {
                    var imgSrc = imageElements[i].src || '';
                    if (imgSrc) {
                        // 确保图片URL是完整的
                        if (imgSrc.startsWith('//')) {
                            imgSrc = 'https:' + imgSrc;
                        }
                        data.images.push(imgSrc);
                    }
                }
            } else {
                // 兼容性取法：尝试从root下的img获取封面
                var rootImgForCover = document.querySelector('#root') && document.querySelector('#root').querySelector('img');
                if (rootImgForCover) {
                    data.imageUrl = rootImgForCover.src || '';
                    // 确保封面URL是完整的
                    if (data.imageUrl && data.imageUrl.startsWith('//')) {
                        data.imageUrl = 'https:' + data.imageUrl;
                    }
                    // 也将这张图片添加到图片数组
                    if (data.imageUrl) {
                        data.images.push(data.imageUrl);
                    }
                } else {
                    // 兜底逻辑：如果没有找到常规图片和root下的img，尝试获取所有宽度和高度都大于100的图片
                    var allImages = document.querySelectorAll('img');
                    var largeImages = [];

                    // 过滤同时满足宽度大于100且高度大于100的图片
                    for (var j = 0; j < allImages.length; j++) {
                        var img = allImages[j];
                        // 检查图片宽度是否大于100且高度大于100
                        if (img.width > 100 && img.height > 100) {
                            largeImages.push(img);
                            // 将图片添加到图片数组
                            var imgSrc = img.src || '';
                            if (imgSrc) {
                                // 确保图片URL是完整的
                                if (imgSrc.startsWith('//')) {
                                    imgSrc = 'https:' + imgSrc;
                                }
                                data.images.push(imgSrc);
                            }
                        }
                    }

                    // 如果有符合条件的大图片，取最后一张作为封面
                    if (largeImages.length > 0) {
                        var lastLargeImage = largeImages[largeImages.length - 1];
                        data.imageUrl = lastLargeImage.src || '';
                        // 确保封面URL是完整的
                        if (data.imageUrl && data.imageUrl.startsWith('//')) {
                            data.imageUrl = 'https:' + data.imageUrl;
                        }
                    }
                }
            }

            // 提取原生协议schemeURL
            // 注意：这里需要在WebView中拦截跳转请求来获取
            data.schemeURL = currentUrl;

            // 标记结果已发送
            resultSent = true;

            // 返回结果
            Android.findResult(JSON.stringify(data));
        } catch (e) {
            // 标记结果已发送
            resultSent = true;

            // 发生错误，返回错误信息
            Android.findResult(JSON.stringify({
                error: e.message,
                url: window.location.href
            }));
        }
    }

    // 检查是否是有效的豆瓣链接
    function isValidDoubanUrl(url) {
        // 简单检查是否是http或https链接
        return /^https?:\/\//.test(url);
    }

    // 检查数据是否已加载
    function checkDataLoaded() {
        // 检查DOM元素是否存在
        var selectors = [
            '.title',
            '.shownotes',
            '#root',
            '.content',
            'img.author-icon',
            '.user-title .name',
            '#content',
            '#note-body img',
            '.phone-item img'
        ];

        for (var i = 0; i < selectors.length; i++) {
            if (document.querySelector(selectors[i])) {
                return true;
            }
            console.log(`未找到选择器: ${selectors[i]}`)
        }

        return false;
    }

    // 轮询检查数据是否已加载
    function pollForData() {
        // 如果结果已经发送或者正在检查中，不再继续轮询
        if (resultSent || checkingData) {
            return;
        }

        checkingData = true;

        return new Promise(function(resolve) {
            var startTime = Date.now();
            var maxCheckTime = 15000; // 最多检查5秒

            function check() {
                // 如果数据已加载，解析Promise
                if (checkDataLoaded()) {
                    checkingData = false;
                    clearAllTimers();
                    resolve(true);
                    return;
                }

                // 检查是否超时
                if (Date.now() - startTime > maxCheckTime) {
                    checkingData = false;
                    clearAllTimers();
                    resolve(false);
                    return;
                }

                // 继续检查
                var timerId = setTimeout(check, 500);
                timers.push(timerId);
            }

            // 开始检查
            check();
        });
    }

    // 主函数：检查数据是否已加载，然后直接提取内容
    function main() {
        // 如果结果已经发送，不再处理
        if (resultSent) {
            return;
        }

        // 开始轮询检查数据是否已加载
        pollForData().then(function(dataLoaded) {
            if (dataLoaded) {
                // 数据已加载，直接提取数据
                extractDoubanStatus();
            } else {
                // 5秒内数据未加载，返回空结果
                if (!resultSent) {
                    resultSent = true;
                    Android.findResult(JSON.stringify({
                        error: "页面数据未加载",
                        url: window.location.href
                    }));
                }
            }
        });
    }

    // 执行主函数
    main();
})();
