(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;

    // 提取抖音数据的函数
    function extractDouyinData() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            console.log("抖音JS开始执行...");

            // 创建数据对象
            var data = {
                url: window.location.href,
                timestamp: new Date().getTime()
            };

            // 记录日志
            console.log("当前页面URL: " + data.url);
            console.log("当前时间戳: " + data.timestamp);
            console.log("页面标题: " + document.title);

            // 抖音视频页面特定的选择器 - 使用更通用的选择器以适应页面变化
            var selectors = {
                title: '.title, .video-card__title, .multi-line_text-span, .video-card__title',
                authorName: '.author-name, .video-card__author__nickname, .author-container p',
                authorAvatar: '.avatar_thumb, .video-card__author__avatar',
                coverImage: '.poster, .end-page-info__cover__img, .video-card__cover'
            };

            // 尝试获取页面上的一些基本信息
            data.title = document.title || "抖音视频";

            // 提取视频标题
            var titleElement = document.querySelector(selectors.title);
            if (titleElement) {
                data.title = titleElement.textContent.trim();
                console.log("找到视频标题: " + data.title);
            }

            // 提取作者名称
            var authorNameElement = document.querySelector(selectors.authorName);
            if (authorNameElement) {
                data.authorName = authorNameElement.textContent.trim();
                console.log("找到作者名称: " + data.authorName);
            }

            // 提取作者头像
            var authorAvatarElement = document.querySelector(selectors.authorAvatar);
            if (authorAvatarElement) {
                // 如果元素本身是img标签
                if (authorAvatarElement.tagName.toLowerCase() === 'img' && authorAvatarElement.src) {
                    data.authorAvatar = authorAvatarElement.src;
                    console.log("找到作者头像(img标签): " + data.authorAvatar);
                }
                // 如果元素不是img但有背景图片
                else if (window.getComputedStyle(authorAvatarElement).backgroundImage) {
                    var bgImg = window.getComputedStyle(authorAvatarElement).backgroundImage;
                    // 从background-image: url("...") 中提取URL
                    var matches = bgImg.match(/url\(['"]?(.*?)['"]?\)/);
                    if (matches && matches[1]) {
                        data.authorAvatar = matches[1];
                        console.log("找到作者头像(背景图片): " + data.authorAvatar);
                    }
                }
                // 如果元素包含img子元素
                else {
                    var imgChild = authorAvatarElement.querySelector('img');
                    if (imgChild && imgChild.src) {
                        data.authorAvatar = imgChild.src;
                        console.log("找到作者头像(子元素): " + data.authorAvatar);
                    }
                }
            }

            // 提取视频封面图
            var coverImageElement = document.querySelector(selectors.coverImage);
            if (coverImageElement) {
                // 检查是否是video元素的poster属性
                if (coverImageElement.tagName.toLowerCase() === 'video' && coverImageElement.poster) {
                    data.coverImage = coverImageElement.poster;
                    console.log("从video poster属性找到封面图: " + data.coverImage);
                }
                // 检查是否是img元素的src属性
                else if (coverImageElement.tagName.toLowerCase() === 'img' && coverImageElement.src) {
                    data.coverImage = coverImageElement.src;
                    console.log("从img src属性找到封面图: " + data.coverImage);
                }
                // 检查是否有背景图片
                else if (window.getComputedStyle(coverImageElement).backgroundImage) {
                    var bgImg = window.getComputedStyle(coverImageElement).backgroundImage;
                    // 从background-image: url("...") 中提取URL
                    var matches = bgImg.match(/url\(['"]?(.*?)['"]?\)/);
                    if (matches && matches[1]) {
                        data.coverImage = matches[1];
                        console.log("从背景图片找到封面图: " + data.coverImage);
                    }
                }
                // 检查是否包含img子元素
                else {
                    var imgChild = coverImageElement.querySelector('img');
                    if (imgChild && imgChild.src) {
                        data.coverImage = imgChild.src;
                        console.log("从子元素找到封面图: " + data.coverImage);
                    }
                }
            }

            // 如果上面的方法都没找到封面图，尝试直接查找页面上的所有图片
            if (!data.coverImage) {
                var allImages = document.querySelectorAll('.video-card__cover, .end-page-info__cover__img, .poster');
                for (var i = 0; i < allImages.length; i++) {
                    var img = allImages[i];
                    if (img.tagName.toLowerCase() === 'img' && img.src) {
                        data.coverImage = img.src;
                        console.log("从页面图片列表找到封面图: " + data.coverImage);
                        break;
                    }
                }
            }

            // 标记结果已发送
            resultSent = true;

            // 清除所有定时器
            clearAllTimers();

            // 返回结果
            console.log("抖音JS执行完成，返回结果");
            Android.findResult(JSON.stringify(data));
        } catch (e) {
            // 标记结果已发送
            resultSent = true;

            // 清除所有定时器
            clearAllTimers();

            // 发生错误，返回错误信息
            console.error("抖音JS执行出错: " + e.message);
            Android.findResult(JSON.stringify({
                error: e.message,
                url: window.location.href
            }));
        }
    }

    // 存储所有定时器ID
    var timers = [];

    // 清除所有定时器的函数
    function clearAllTimers() {
        for (var i = 0; i < timers.length; i++) {
            clearTimeout(timers[i]);
        }
        timers = [];
    }

    // 定义轮询函数，每500ms检查一次页面是否加载完成
    function pollForData() {
        // 如果结果已经发送，不再继续轮询
        if (resultSent) {
            return;
        }

        console.log("抖音JS轮询中...");

        // 检查数据是否已加载
        var dataLoaded = false;

        // 检查DOM元素是否存在 - 根据抖音页面结构更新选择器
        var selectors = [
            '.avatar_thumb',
        ];

        for (var i = 0; i < selectors.length; i++) {
            if (document.querySelector(selectors[i])) {
                console.log("找到关键元素: " + selectors[i]);
                dataLoaded = true;
                break;
            }
        }

        if (dataLoaded) {
            // 数据已加载，提取数据
            console.log("关键元素已加载，开始提取数据");
            extractDouyinData();
        } else {
            // 数据未加载，继续轮询
            console.log("关键元素未加载，继续轮询");
            var timerId = setTimeout(pollForData, 500);
            timers.push(timerId);
        }
    }

    // 开始轮询
    console.log("抖音JS开始轮询...");
    pollForData();

    // 设置最大轮询时间（8秒）
    var timeoutId = setTimeout(function() {
        console.log("抖音JS超时，尝试提取当前数据");
        // 超时，尝试提取当前数据
        extractDouyinData();
    }, 8000);
    timers.push(timeoutId);
})();
