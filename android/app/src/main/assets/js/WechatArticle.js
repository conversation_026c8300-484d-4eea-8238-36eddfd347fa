(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;
    // 存储所有定时器ID，以便在完成时清理
    var timers = [];

    // 清理所有定时器的函数
    function clearAllTimers() {
        for (var i = 0; i < timers.length; i++) {
            clearTimeout(timers[i]);
        }
        timers = [];
    }

    // 提取微信公众号文章数据的函数
    function extractWechatArticle() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            console.log("开始提取微信公众号文章数据...");

            // 创建数据对象
            var data = {
                url: window.location.href,
                htmlStructure: {}
            };

            // 提取文章标题
            var titleElement = document.querySelector('#activity-name');
            if (titleElement) {
                data.title = titleElement.textContent.trim();
                console.log("提取到标题:", data.title);
            }

            // 提取作者信息
            var authorElement = document.querySelector('#js_name');
            if (authorElement) {
                data.author = authorElement.textContent.trim();
                console.log("提取到作者:", data.author);
            }

            // 提取公众号头像
            var avatarElement = document.querySelector('.wx_follow_avatar_pic');
            if (avatarElement) {
                data.authorAvatar = avatarElement.src;
                console.log("提取到公众号头像:", data.authorAvatar);
            } else {
                // 尝试其他可能的选择器
                var alternativeSelectors = [
                    '.profile_avatar img',
                    '.wx_profile_info_avatar_wrp img',
                    '#js_profile_qrcode img'
                ];

                for (var i = 0; i < alternativeSelectors.length; i++) {
                    var altElement = document.querySelector(alternativeSelectors[i]);
                    if (altElement) {
                        data.authorAvatar = altElement.src;
                        console.log("提取到公众号头像(备选方法):", data.authorAvatar);
                        break;
                    }
                }

                // 如果仍未找到，尝试查找包含头像的父元素
                if (!data.authorAvatar) {
                    var profileArea = document.querySelector('.profile_inner') ||
                                     document.querySelector('.wx_profile_info_avatar');
                    if (profileArea) {
                        var imgElement = profileArea.querySelector('img');
                        if (imgElement) {
                            data.authorAvatar = imgElement.src;
                            console.log("提取到公众号头像(备选方法2):", data.authorAvatar);
                        }
                    }
                }
            }

            // 提取发布时间
            var publishTimeElement = document.querySelector('#publish_time');
            if (publishTimeElement) {
                data.publishTime = publishTimeElement.textContent.trim();
                console.log("提取到发布时间:", data.publishTime);
            }

            // 提取封面图片
            var coverImageElement = document.querySelector('.rich_media_thumb_wrp img');
            if (coverImageElement) {
                data.coverImage = coverImageElement.src;
                console.log("提取到封面图片:", data.coverImage);
            }

            // 提取文章内容
            var contentElement = document.querySelector('#js_content');
            if (contentElement) {
                // 只提取文本内容，避免过大
                data.content = contentElement.textContent.trim().substring(0, 500) + '...';
                console.log("提取到文章内容(部分):", data.content);
            }

            // 提取所有图片
            var images = [];
            var imgElements = document.querySelectorAll('#js_content img');
            for (var i = 0; i < Math.min(imgElements.length, 5); i++) {
                var img = imgElements[i];
                if (img.dataset.src) {
                    images.push(img.dataset.src);
                } else if (img.src) {
                    images.push(img.src);
                }
            }
            data.images = images;
            console.log("提取到图片数量:", images.length);

            // 分析HTML结构
            data.htmlStructure = analyzeHtmlStructure();

            // 标记结果已发送
            resultSent = true;

            // 清理所有定时器
            clearAllTimers();

            // 调用Android接口返回结果
            console.log("调用Android.findResult返回数据");
            Android.findResult(JSON.stringify(data));
        } catch (error) {
            console.error("提取微信公众号文章数据时出错:", error);

            // 即使出错也尝试返回部分数据
            if (!resultSent) {
                resultSent = true;
                clearAllTimers();

                // 返回错误信息
                Android.findResult(JSON.stringify({
                    error: error.toString(),
                    url: window.location.href,
                    htmlStructure: analyzeHtmlStructure()
                }));
            }
        }
    }

    // 分析HTML结构的函数
    function analyzeHtmlStructure() {
        var structure = {
            bodyChildren: [],
            mainElements: {}
        };

        // 获取body的直接子元素
        var bodyChildren = document.body.children;
        for (var i = 0; i < Math.min(bodyChildren.length, 10); i++) {
            var child = bodyChildren[i];
            structure.bodyChildren.push({
                tagName: child.tagName,
                id: child.id,
                className: child.className,
                childCount: child.children.length
            });
        }

        // 获取主要元素
        var mainSelectors = [
            '#activity-name',
            '#js_name',
            '#js_content',
            '.rich_media_content',
            '.rich_media_thumb_wrp',
            '.wx_follow_avatar_pic',
            '.profile_avatar img',
            '.wx_profile_info_avatar_wrp img',
            '#js_profile_qrcode img',
            '.profile_inner',
            '.wx_profile_info_avatar'
        ];

        for (var i = 0; i < mainSelectors.length; i++) {
            var selector = mainSelectors[i];
            var element = document.querySelector(selector);
            if (element) {
                structure.mainElements[selector] = {
                    exists: true,
                    tagName: element.tagName,
                    childCount: element.children.length
                };
            } else {
                structure.mainElements[selector] = {
                    exists: false
                };
            }
        }

        return structure;
    }

    // 定义轮询函数，每500ms检查一次页面是否加载完成
    function pollForData() {
        // 如果结果已经发送，不再继续轮询
        if (resultSent) {
            return;
        }

        // 检查数据是否已加载
        var dataLoaded = false;

        // 检查DOM元素是否存在
        var selectors = [
            '#activity-name',
            '#js_name',
            '#js_content',
            '.wx_follow_avatar_pic',
            '.profile_avatar img',
            '.wx_profile_info_avatar_wrp img',
            '#js_profile_qrcode img',
            '.profile_inner',
            '.wx_profile_info_avatar'
        ];

        for (var i = 0; i < selectors.length; i++) {
            if (document.querySelector(selectors[i])) {
                dataLoaded = true;
                break;
            }
        }

        if (dataLoaded) {
            // 数据已加载，提取数据
            extractWechatArticle();
        } else {
            // 数据未加载，继续轮询
            var timerId = setTimeout(pollForData, 500);
            timers.push(timerId);
        }
    }

    // 开始轮询
    pollForData();

    // 设置最大轮询时间（8秒）
    var timeoutId = setTimeout(function() {
        // 超时，尝试提取当前数据
        extractWechatArticle();
    }, 8000);
    timers.push(timeoutId);
})();
