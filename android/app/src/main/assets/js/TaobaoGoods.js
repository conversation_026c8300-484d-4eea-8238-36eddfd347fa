(function() {
    // 定义提取淘宝商品数据的函数
    function extractTaobaoGoods() {
        try {
            var data = {};

            // 1. 作者头像：Array.from(document.querySelectorAll('img')).filter(item => item.className.indexOf('shop_logo') != -1)
            var shopLogoImages = Array.from(document.querySelectorAll('img')).filter(function(item) {
                return item.className.indexOf('shop_logo') != -1;
            });

            if (shopLogoImages.length > 0) {
                data.shopAvatar = shopLogoImages[0].src || shopLogoImages[0].getAttribute('data-src') || '';
            } else {
                data.shopAvatar = '';
            }

            // 2. 作者名称：Array.from(document.querySelectorAll('div')).filter(item => item.className.indexOf('shop_info') != -1)
            var shopInfoDivs = Array.from(document.querySelectorAll('div')).filter(function(item) {
                return item.className.indexOf('shop_info') != -1;
            });

            if (shopInfoDivs.length > 0) {
                data.shopName = shopInfoDivs[0].textContent.trim();
            } else {
                data.shopName = '';
            }

            // 3. 封面：#main_pic
            var mainPicElement = document.querySelector('#main_pic');

            if (mainPicElement) {
                data.coverImage = mainPicElement.src || mainPicElement.getAttribute('data-src') || '';
            } else {
                data.coverImage = '';
            }

            // 提取URL
            data.url = window.location.href;

            // 返回结果
            Android.findResult(JSON.stringify(data));
        } catch (e) {
            // 发生错误，返回错误信息
            Android.findResult(JSON.stringify({
                error: e.message,
                url: window.location.href
            }));
        }
    }

    // 定义轮询函数，等待数据加载完成
    function pollForData() {
        // 检查页面是否已加载完成
        var dataLoaded = false;

        // 检查是否能取到#main_pic元素
        var mainPicElement = document.querySelector('#main_pic');

        if (mainPicElement) {
            dataLoaded = true;
        }

        if (dataLoaded) {
            // 数据已加载，提取数据
            extractTaobaoGoods();
        } else {
            // 数据未加载，继续轮询
            setTimeout(pollForData, 300);
        }
    }

    // 开始轮询
    pollForData();

    // 设置最大轮询时间（10秒）
    setTimeout(function() {
        // 超时，尝试提取当前数据
        extractTaobaoGoods();
    }, 10000);
})();
