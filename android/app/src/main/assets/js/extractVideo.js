// 视频提取脚本
// 轮询页面中的video标签，提取src属性和相关信息

(function() {
    console.log('视频提取脚本开始执行');

    // 设置超时时间
    const TIMEOUT_MS = 20000; // 10秒超时
    const POLL_INTERVAL = 100; // 100ms轮询间隔
    let timeoutId;
    let pollIntervalId;
    let isCompleted = false;

    // 封面选择器数组 - 按优先级排序
    const COVER_SELECTORS = [
        // video标签的poster属性
        'video[poster]',

        // 通用封面选择器
        '.cover img', '.poster img', '.thumbnail img',
        '.video-cover img', '.video-poster img',
        '.main-image img', '.hero-image img',

        // 抖音平台
        '.poster', '.end-page-info__cover__img', '.video-card__cover',

        // 小红书平台
        '.carousel-image', '.swiper-slide img', '#video_note_poster',

        // 微信平台
        '.rich_media_thumb_wrp img',

        // 淘宝平台
        '#main_pic',

        // B站平台
        '.bfs-img img[src*="/bfs/archive/"]',

        // 通用图片选择器（最后检查）
        'img[src*="cover"]', 'img[src*="poster"]', 'img[src*="thumb"]'
    ];

    // 完成处理的函数
    function completeWithResult(result) {
        if (isCompleted) return;
        isCompleted = true;

        // 清理定时器
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        if (pollIntervalId) {
            clearInterval(pollIntervalId);
        }

        console.log('视频提取完成:', result);

        // 调用Android.findResult方法返回结果
        if (typeof Android !== 'undefined' && typeof Android.findResult === 'function') {
            Android.findResult(JSON.stringify(result));
        }
    }

    // 错误处理函数
    function completeWithError(error) {
        console.error('视频提取失败:', error);
        completeWithResult({
            success: false,
            error: error,
            timestamp: new Date().toISOString()
        });
    }





    // 提取封面图片的函数
    function extractCoverImage() {
        console.log('开始检查封面选择器...');

        for (let i = 0; i < COVER_SELECTORS.length; i++) {
            const selector = COVER_SELECTORS[i];
            console.log(`检查选择器 ${i + 1}/${COVER_SELECTORS.length}: ${selector}`);

            try {
                if (selector === 'video[poster]') {
                    // 处理video的poster属性
                    const videoElements = document.querySelectorAll('video[poster]');
                    for (const video of videoElements) {
                        if (video.poster) {
                            console.log(`从video poster找到封面: ${video.poster}`);
                            return {
                                src: video.poster,
                                selector: selector,
                                type: 'video-poster'
                            };
                        }
                    }
                } else {
                    // 处理普通选择器
                    const element = document.querySelector(selector);
                    if (element) {
                        let coverUrl = null;

                        // 如果是img元素
                        if (element.tagName && element.tagName.toLowerCase() === 'img') {
                            coverUrl = element.src || element.getAttribute('data-src');
                        }
                        // 如果是其他元素，查找其中的img
                        else {
                            const imgChild = element.querySelector('img');
                            if (imgChild) {
                                coverUrl = imgChild.src || imgChild.getAttribute('data-src');
                            }
                        }

                        if (coverUrl && coverUrl.startsWith('http')) {
                            console.log(`从选择器找到封面: ${coverUrl}`);
                            return {
                                src: coverUrl,
                                selector: selector,
                                type: 'element'
                            };
                        }
                    }
                }
            } catch (error) {
                console.warn(`检查选择器 ${selector} 时出错:`, error);
            }
        }

        console.log('未找到任何封面图片');
        return null;
    }

    // 提取视频信息的函数
    function extractVideoInfo() {
        try {
            // 查找页面中的所有video标签
            const videoElements = document.querySelectorAll('video');

            if (videoElements.length === 0) {
                return null; // 没有找到video标签，继续轮询
            }

            console.log(`找到 ${videoElements.length} 个video标签`);

            const videos = [];

            videoElements.forEach((video, index) => {
                const videoInfo = {
                    index: index,
                    src: null,
                    width: null,
                    height: null,
                    duration: null,
                    currentSrc: null,
                    sources: []
                };

                // 获取src属性
                if (video.src) {
                    videoInfo.src = video.src;
                }

                // 获取currentSrc（实际播放的源）
                if (video.currentSrc) {
                    videoInfo.currentSrc = video.currentSrc;
                }

                // 获取尺寸信息
                if (video.videoWidth) {
                    videoInfo.width = video.videoWidth;
                }
                if (video.videoHeight) {
                    videoInfo.height = video.videoHeight;
                }

                // 获取时长信息
                if (video.duration && !isNaN(video.duration)) {
                    videoInfo.duration = video.duration;
                }

                // 获取source标签信息
                const sources = video.querySelectorAll('source');
                sources.forEach(source => {
                    videoInfo.sources.push({
                        src: source.src || null,
                        type: source.type || null
                    });
                });

                videos.push(videoInfo);
            });

            // 提取封面图片
            const coverImage = extractCoverImage();

            // 返回提取结果
            const result = {
                success: true,
                videoCount: videos.length,
                videos: videos,
                coverImage: coverImage,
                pageUrl: window.location.href,
                pageTitle: document.title || '',
                timestamp: new Date().toISOString()
            };

            return result;

        } catch (error) {
            console.error('提取视频信息时发生错误:', error);
            return {
                success: false,
                error: error.message || '未知错误',
                timestamp: new Date().toISOString()
            };
        }
    }

    // 轮询函数
    function pollForVideo() {
        if (isCompleted) {
            return;
        }

        console.log('轮询检查video标签和封面...');

        // 检查video标签
        const videoElements = document.querySelectorAll('video');
        const hasVideo = videoElements.length > 0;

        // 检查封面
        const coverImage = extractCoverImage();
        const hasCover = coverImage !== null;

        console.log(`检查结果: video=${hasVideo}, cover=${hasCover}`);

        // 如果同时找到了video和封面，立即返回
        if (hasVideo && hasCover) {
            console.log('同时找到video和封面，立即返回结果');
            const result = extractVideoInfo();
            if (result) {
                completeWithResult(result);
                return;
            }
        }

        // 如果只找到video但没有封面，继续轮询等待封面
        // 如果只找到封面但没有video，继续轮询等待video
        // 如果都没找到，继续轮询
    }

    // 设置超时处理
    timeoutId = setTimeout(() => {
        if (!isCompleted) {
            console.log('轮询超时，提取最终结果');

            // 检查是否有video标签
            const videoElements = document.querySelectorAll('video');
            const hasVideo = videoElements.length > 0;

            if (hasVideo) {
                // 如果有video，提取完整的video信息（包含封面）
                const result = extractVideoInfo();
                if (result) {
                    result.note = '超时后找到video标签和封面信息';
                    completeWithResult(result);
                    return;
                }
            }

            // 如果没有video，只检查封面
            const coverImage = extractCoverImage();
            completeWithResult({
                success: true,
                videoCount: 0,
                videos: [],
                coverImage: coverImage,
                pageUrl: window.location.href,
                pageTitle: document.title || '',
                timestamp: new Date().toISOString(),
                note: coverImage ? '超时未找到video标签，但找到封面' : '超时未找到video标签和封面'
            });
        }
    }, TIMEOUT_MS);

    // 开始轮询
    console.log('开始轮询video标签，间隔:', POLL_INTERVAL, 'ms');
    
    // 立即执行一次
    pollForVideo();
    
    // 设置定时轮询
    pollIntervalId = setInterval(pollForVideo, POLL_INTERVAL);

})();
