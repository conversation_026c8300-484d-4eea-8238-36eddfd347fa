(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;

    // 存储所有定时器ID
    var timers = [];

    // 清除所有定时器的函数
    function clearAllTimers() {
        for (var i = 0; i < timers.length; i++) {
            clearTimeout(timers[i]);
        }
        timers = [];
    }

    // 提取B站页面数据的函数
    function extractBilibiliData() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            console.log("开始提取B站页面数据");

            // 创建数据对象
            var data = {
                url: window.location.href
            };

            // 从HTML中提取视频标题
            var titleElement = document.querySelector('h1.title-text');
            if (titleElement) {
                data.title = titleElement.textContent.trim();
            } else {
                // 尝试从title标签提取
                data.title = document.title.replace(/_哔哩哔哩_bilibili$/, '').trim();
            }
            console.log("提取到标题:", data.title);

            // 提取封面图片URL
            var coverImageElements = document.querySelectorAll('.bfs-img img[src*="/bfs/archive/"]');
            if (coverImageElements && coverImageElements.length > 0) {
                // 获取第一个封面图片
                var coverUrl = coverImageElements[0].src;
                // 处理webp格式，获取原始图片URL
                coverUrl = coverUrl.replace(/@.*$/, '');
                data.coverImage = coverUrl;
                console.log("提取到封面图片:", data.coverImage);
            }

            // 提取UP主信息
            var authorNameElement = document.querySelector('.author');
            if (authorNameElement) {
                data.authorName = authorNameElement.textContent.trim();
                console.log("提取到UP主名称:", data.authorName);
            }

            // 提取UP主头像
            var avatarElement = document.querySelector('.user-face img');
            if (avatarElement) {
                var avatarUrl = avatarElement.src;
                // 处理webp格式，获取原始图片URL
                avatarUrl = avatarUrl.replace(/@.*$/, '');
                data.authorAvatar = avatarUrl;
                console.log("提取到UP主头像:", data.authorAvatar);
            }

            // 不再从URL构建scheme URL，将使用原来的拦截原生跳转协议方式获取
            console.log("不构建scheme URL，将使用原来的拦截原生跳转协议方式获取");

            // 打印完整的HTML结构，用于调试
            console.log("页面HTML结构:");
            console.log(document.documentElement.outerHTML);

            // 标记结果已发送
            resultSent = true;

            // 清除所有定时器
            clearAllTimers();

            // 返回结果
            console.log("提取完成，返回数据:", JSON.stringify(data));
            Android.findResult(JSON.stringify(data));
        } catch (e) {
            // 标记结果已发送
            resultSent = true;

            // 清除所有定时器
            clearAllTimers();

            // 发生错误，返回错误信息
            console.error("提取B站页面数据时出错:", e);
            Android.findResult(JSON.stringify({
                error: e.message,
                url: window.location.href
            }));
        }
    }

    // 定义轮询函数，每500ms检查一次.user-avatar元素是否存在
    function pollForData() {
        // 如果结果已经发送，不再继续轮询
        if (resultSent) {
            return;
        }

        // 检查.user-avatar元素是否存在
        var userAvatarExists = document.querySelector('.user-avatar') !== null;

        if (userAvatarExists) {
            // 找到.user-avatar元素，提取数据
            console.log("找到.user-avatar元素，开始提取数据");
            extractBilibiliData();
        } else {
            // 未找到.user-avatar元素，继续轮询
            console.log("页面加载中，继续轮询...");
            var timerId = setTimeout(pollForData, 500);
            timers.push(timerId);
        }
    }

    // 开始轮询
    console.log("开始轮询检查.user-avatar元素");
    pollForData();

    // 设置最大轮询时间（10秒）
    var timeoutId = setTimeout(function() {
        console.log("轮询超时，尝试提取当前数据");
        // 超时，尝试提取当前数据
        extractBilibiliData();
    }, 10000);
    timers.push(timeoutId);
})();
