(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;

    // 提取拼多多商品数据的函数
    function extractPinDuoDuoGoods() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            var data = {};

            // 获取当前页面URL
            data.url = window.location.href;

            // 构建拼多多scheme URL
            try {
                var currentUrl = window.location.href;
                var targetUrl = currentUrl;

                // 检查是否是登录页面，如果是，需要从from参数中提取真实的商品页面URL
                if (currentUrl.indexOf('/login.html') !== -1 && currentUrl.indexOf('from=') !== -1) {
                    console.log("检测到登录页面，尝试从from参数提取真实URL");

                    // 提取from参数的值
                    var fromMatch = currentUrl.match(/[?&]from=([^&]+)/);
                    if (fromMatch && fromMatch[1]) {
                        // 解码from参数
                        try {
                            targetUrl = decodeURIComponent(fromMatch[1]);
                            console.log("从from参数解码得到目标URL: " + targetUrl);
                        } catch (decodeError) {
                            console.error("解码from参数失败:", decodeError);
                            targetUrl = currentUrl;
                        }
                    }
                }

                // 尝试从目标URL中提取商品ID
                // 拼多多商品页面URL格式通常为：https://mobile.yangkeduo.com/goods.html?goods_id=xxx 或 goods1.html
                var goodsIdMatch = targetUrl.match(/goods_id=([^&]+)/);

                if (goodsIdMatch && goodsIdMatch[1]) {
                    var goodsId = goodsIdMatch[1];
                    data.schemeURL = "pinduoduo://com.xunmeng.pinduoduo/mall_quality_assurance.html?_t_timestamp=comm_share_landing&goods_id=" + goodsId;
                    console.log("构建拼多多scheme URL: " + data.schemeURL);
                    console.log("提取到的商品ID: " + goodsId);
                } else {
                    // 尝试其他可能的URL格式
                    var goodsDetailMatch = targetUrl.match(/\/goods[0-9]*\.html.*[?&]goods_id=([^&]+)/);
                    if (goodsDetailMatch && goodsDetailMatch[1]) {
                        var goodsId = goodsDetailMatch[1];
                        data.schemeURL = "pinduoduo://com.xunmeng.pinduoduo/mall_quality_assurance.html?_t_timestamp=comm_share_landing&goods_id=" + goodsId;
                        console.log("构建拼多多scheme URL (备用方案): " + data.schemeURL);
                        console.log("提取到的商品ID: " + goodsId);
                    } else {
                        console.log("无法从URL中提取商品ID");
                        console.log("当前URL: " + currentUrl);
                        console.log("目标URL: " + targetUrl);
                    }
                }
            } catch (error) {
                console.error("构建拼多多scheme URL时出错:", error);
            }

            // 标记结果已发送
            resultSent = true;

            // 清理所有定时器
            for (var i = 0; i < timers.length; i++) {
                clearTimeout(timers[i]);
            }

            // 调用Android接口返回结果
            if (typeof Android !== 'undefined' && Android.findResult) {
                console.log("调用Android.findResult，返回数据:", JSON.stringify(data));
                Android.findResult(JSON.stringify(data));
            } else {
                console.error("Android接口不可用");
            }

        } catch (error) {
            console.error("提取拼多多商品数据时出错:", error);

            // 即使出错也要标记结果已发送，避免重复调用
            resultSent = true;

            // 清理所有定时器
            for (var i = 0; i < timers.length; i++) {
                clearTimeout(timers[i]);
            }

            // 返回错误信息
            if (typeof Android !== 'undefined' && Android.findResult) {
                Android.findResult(JSON.stringify({
                    error: "提取数据时出错: " + error.message,
                    url: window.location.href
                }));
            }
        }
    }

    // 存储定时器ID的数组，用于清理
    var timers = [];

    // 定义轮询函数，每500ms检查一次页面是否加载完成
    function pollForData() {
        // 如果结果已经发送，不再继续轮询
        if (resultSent) {
            return;
        }

        // 检查页面是否基本加载完成
        var pageLoaded = document.readyState === 'complete' ||
                        document.readyState === 'interactive';

        if (pageLoaded) {
            // 页面已加载，提取数据
            extractPinDuoDuoGoods();
        } else {
            // 页面未加载完成，继续轮询
            var timerId = setTimeout(pollForData, 500);
            timers.push(timerId);
        }
    }

    // 开始轮询
    pollForData();

    // 设置最大轮询时间（5秒）
    var timeoutId = setTimeout(function() {
        // 超时，尝试提取当前数据
        extractPinDuoDuoGoods();
    }, 5000);
    timers.push(timeoutId);
})();
