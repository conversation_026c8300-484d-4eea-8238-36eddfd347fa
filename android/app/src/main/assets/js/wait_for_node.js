(function() {
    // 定义轮询函数
    function pollForElement(selector) {
        var element = document.querySelector(selector);
        if (element) {
            // 节点找到，调用findResult方法
            Android.findResult(document.documentElement.outerHTML);
            return;
        }
        
        // 节点未找到，继续轮询
        setTimeout(function() {
            pollForElement(selector);
        }, 300);
    }
    
    // 开始轮询
    pollForElement('${selector}');
    
    // 设置最大轮询时间（10秒）
    setTimeout(function() {
        // 超时，返回当前HTML
        Android.findResult(document.documentElement.outerHTML);
    }, 10000);
})();
