(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;
    var checkingData = false;
    var timers = [];

    // 简单的sleep函数
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 清除所有定时器的函数
    function clearAllTimers() {
        for (var i = 0; i < timers.length; i++) {
            clearTimeout(timers[i]);
        }
        timers = [];
    }

    // 提取豆瓣播客数据的函数
    function extractDoubanBoke() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            var data = {};

            // 检查是否是有效的豆瓣链接
            var currentUrl = window.location.href;
            if (!isValidDoubanUrl(currentUrl)) {
                throw new Error("不是有效的豆瓣链接");
            }

            // 提取标题
            var titleElement = document.querySelector('.title');
            if (titleElement) {
                data.title = titleElement.textContent.trim();
            } else {
                // 尝试使用兼容方式获取标题
                var altTitleElement = document.querySelector('h1');
                if (altTitleElement) {
                    data.title = altTitleElement.textContent.trim();
                } else {
                    // 如果没有找到标题，使用页面标题
                    data.title = document.title.replace(' - 豆瓣', '').trim();
                }
            }

            // 提取作者（播客主播）
            var authorElement = document.querySelector('.podcast-creator');
            if (authorElement) {
                data.authorName = authorElement.textContent.trim();
            } else {
                // 尝试使用兼容方式获取作者
                var metaElement = document.querySelector('.meta');
                if (metaElement && metaElement.textContent) {
                    var metaText = metaElement.textContent.trim();
                    // 查找第一个/的位置
                    var slashIndex = metaText.indexOf('/');
                    if (slashIndex !== -1) {
                        // 提取第一个/之前的内容
                        data.authorName = metaText.substring(0, slashIndex).trim();
                    } else {
                        data.authorName = metaText.trim();
                    }
                } else {
                    data.authorName = "";
                }
            }

            // 提取描述
            var descriptionElement = document.querySelector('.podcast-intro');
            var processedDescription = "";

            if (descriptionElement) {
                // 获取原始文本
                var rawDescription = descriptionElement.textContent.trim();

                // 处理文本：去除所有换行符和多余空格
                // 1. 将所有换行符替换为空字符串（完全去除）
                processedDescription = rawDescription.replace(/\n/g, '');

                // 2. 将连续的空格替换为单个空格
                processedDescription = processedDescription.replace(/[ \t]{2,}/g, ' ');
            } else {
                // 尝试使用兼容方式获取描述
                var altDescriptionElement = document.querySelector('.intro');
                if (altDescriptionElement) {
                    // 获取原始文本
                    var altRawDescription = altDescriptionElement.textContent.trim();

                    // 处理文本：去除所有换行符和多余空格
                    processedDescription = altRawDescription.replace(/\n/g, '');
                    processedDescription = processedDescription.replace(/[ \t]{2,}/g, ' ');
                } else {
                    // 尝试使用meta标签获取描述
                    var metaDescriptionElement = document.querySelector('meta[name="description"]');
                    if (metaDescriptionElement) {
                        processedDescription = metaDescriptionElement.getAttribute('content') || "";
                    }
                }
            }

            // 设置描述
            data.description = processedDescription;

            // 限制描述长度为100个字符
            if (data.description.length > 100) {
                data.description = data.description.substring(0, 100) + '...';
            }

            // 提取封面图片
            var coverElement = document.querySelector('.podcast-cover img');
            if (coverElement) {
                data.imageUrl = coverElement.src || '';
                // 确保封面URL是完整的
                if (data.imageUrl && data.imageUrl.startsWith('//')) {
                    data.imageUrl = 'https:' + data.imageUrl;
                }
            } else {
                // 尝试使用兼容方式获取封面
                var altCoverElement = document.querySelector('.cover img');
                if (altCoverElement) {
                    data.imageUrl = altCoverElement.src || '';
                    // 确保封面URL是完整的
                    if (data.imageUrl && data.imageUrl.startsWith('//')) {
                        data.imageUrl = 'https:' + data.imageUrl;
                    }
                } else {
                    // 尝试查找任何大图片作为封面
                    var allImages = document.querySelectorAll('img');
                    for (var i = 0; i < allImages.length; i++) {
                        var img = allImages[i];
                        // 检查图片宽度是否大于100且高度大于100
                        if (img.width > 100 && img.height > 100) {
                            data.imageUrl = img.src || '';
                            // 确保封面URL是完整的
                            if (data.imageUrl && data.imageUrl.startsWith('//')) {
                                data.imageUrl = 'https:' + data.imageUrl;
                            }
                            break;
                        }
                    }
                }
            }

            // 使用封面图片作为头像
            data.avatarUrl = data.imageUrl;

            // 提取原生协议schemeURL
            // 注意：这里需要在WebView中拦截跳转请求来获取
            data.schemeURL = currentUrl;

            // 标记结果已发送
            resultSent = true;

            // 返回结果
            Android.findResult(JSON.stringify(data));
        } catch (e) {
            // 标记结果已发送
            resultSent = true;

            // 发生错误，返回错误信息
            Android.findResult(JSON.stringify({
                error: e.message,
                url: window.location.href
            }));
        }
    }

    // 检查是否是有效的豆瓣链接
    function isValidDoubanUrl(url) {
        // 简单检查是否是http或https链接
        return /^https?:\/\//.test(url);
    }

    // 检查数据是否已加载
    function checkDataLoaded() {
        // 检查DOM元素是否存在
        var selectors = [
            '.title',
            'h1',
            '.podcast-creator',
            '.meta',
            '.podcast-intro',
            '.intro',
            '.podcast-cover img',
            '.cover img'
        ];

        for (var i = 0; i < selectors.length; i++) {
            if (document.querySelector(selectors[i])) {
                return true;
            }
        }

        return false;
    }

    // 轮询检查数据是否已加载
    async function pollForData() {
        if (checkingData) {
            return false;
        }

        checkingData = true;

        // 检查数据是否已加载，最多等待5秒
        const maxAttempts = 30;
        const interval = 500; // 500毫秒检查一次
        let attempts = 0;

        while (attempts < maxAttempts) {
            if (checkDataLoaded()) {
                checkingData = false;
                return true;
            }

            attempts++;
            await sleep(interval);
        }

        checkingData = false;
        return false;
    }

    // 主函数：检查数据是否已加载，然后直接提取内容
    function main() {
        // 如果结果已经发送，不再处理
        if (resultSent) {
            return;
        }

        // 开始轮询检查数据是否已加载
        pollForData().then(function(dataLoaded) {
            if (dataLoaded) {
                // 数据已加载，直接提取数据
                extractDoubanBoke();
            } else {
                // 5秒内数据未加载，返回空结果
                if (!resultSent) {
                    resultSent = true;
                    Android.findResult(JSON.stringify({
                        error: "页面数据未加载",
                        url: window.location.href
                    }));
                }
            }
        });
    }

    // 执行主函数
    main();
})();
