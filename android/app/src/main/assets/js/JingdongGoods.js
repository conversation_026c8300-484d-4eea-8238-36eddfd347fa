// 京东商品信息提取脚本
// 参考小红书的逻辑，提取京东商品的相关信息

(function() {
    console.log('京东商品信息提取脚本开始执行');

    // 设置超时时间
    const TIMEOUT_MS = 10000; // 10秒超时
    let timeoutId;
    let isCompleted = false;

    // 完成处理的函数
    function completeWithResult(result) {
        if (isCompleted) return;
        isCompleted = true;

        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        console.log('京东商品信息提取完成:', result);

        // 调用Android.findResult方法返回结果
        if (typeof Android !== 'undefined' && typeof Android.findResult === 'function') {
            Android.findResult(JSON.stringify(result));
        }
    }

    // 完成处理的函数（错误情况）
    function completeWithError(error) {
        if (isCompleted) return;
        isCompleted = true;

        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        console.error('京东商品信息提取失败:', error);

        // 调用Android.findResult方法返回错误
        if (typeof Android !== 'undefined' && typeof Android.findResult === 'function') {
            Android.findResult(JSON.stringify({ error: error }));
        }
    }

    // 设置超时处理
    timeoutId = setTimeout(() => {
        completeWithError('提取京东商品信息超时');
    }, TIMEOUT_MS);

    // 提取商品信息的函数
    function extractJingdongProductInfo() {
        try {
            const result = {
                title: null,
                price: null,
                coverImage: null,
                shop: {
                    name: null,
                    avatar: null
                },
                schemeURL: null
            };

            // 1. 提取商品标题：#itemName中匹配中文
            const titleElement = document.querySelector('#itemName');
            if (titleElement && titleElement.textContent.trim()) {
                const titleText = titleElement.textContent.trim();
                // 匹配中文字符
                const chineseMatch = titleText.match(/[\u4e00-\u9fa5]+/g);
                if (chineseMatch) {
                    result.title = chineseMatch.join('');
                    console.log('找到商品标题:', result.title);
                }
            }

            // 2. 提取商品封面图：#firstImg
            const imageElement = document.querySelector('#firstImg');
            if (imageElement && imageElement.src) {
                result.coverImage = imageElement.src;
                console.log('找到商品封面图:', result.coverImage);
            }

            // 3. 提取店铺头像：#shopLogo
            const avatarElement = document.querySelector('#shopLogo');
            if (avatarElement && avatarElement.src) {
                result.shop.avatar = avatarElement.src;
                console.log('找到店铺头像:', result.shop.avatar);
            }

            // 4. 提取店铺昵称：#shopNameInfo .name
            const shopNameElement = document.querySelector('#shopNameInfo .name');
            if (shopNameElement && shopNameElement.textContent.trim()) {
                result.shop.name = shopNameElement.textContent.trim();
                console.log('找到店铺名称:', result.shop.name);
            }

            // 构造scheme URL
            // 从当前URL中提取商品ID
            const currentUrl = window.location.href;
            console.log('当前URL:', currentUrl);

            // 匹配商品ID的正则表达式
            const productIdPatterns = [
                /\/(\d+)\.html/,  // item.jd.com/123456.html
                /product\/(\d+)\.html/,  // item.m.jd.com/product/123456.html
                /skuId[=:](\d+)/,  // skuId=123456
                /id[=:](\d+)/  // id=123456
            ];

            let productId = null;
            for (const pattern of productIdPatterns) {
                const match = currentUrl.match(pattern);
                if (match && match[1]) {
                    productId = match[1];
                    console.log('提取到商品ID:', productId);
                    break;
                }
            }

            if (productId) {
                // 构造京东的scheme URL
                result.schemeURL = `openapp.jdmobile://virtual?params={"category":"jump","des":"productDetail","skuId":"${productId}"}`;
                console.log('构造的scheme URL:', result.schemeURL);
            }

            // 检查是否提取到了基本信息
            if (result.title || result.coverImage) {
                console.log('京东商品信息提取成功');
                completeWithResult(result);
            } else {
                console.log('未能提取到京东商品的基本信息，继续等待页面加载');
                return false; // 继续等待
            }

            return true;
        } catch (error) {
            console.error('提取京东商品信息时出错:', error);
            completeWithError('提取商品信息时出错: ' + error.message);
            return true;
        }
    }

    // 检查关键元素是否存在
    function checkKeyElementsLoaded() {
        const keySelectors = [
            '#itemName',    // 商品标题
            '#firstImg',    // 商品封面图
            '#shopLogo',    // 店铺头像
            '#shopNameInfo .name'  // 店铺名称
        ];

        for (const selector of keySelectors) {
            if (document.querySelector(selector)) {
                console.log('找到关键元素:', selector);
                return true;
            }
        }

        console.log('关键元素未加载完成');
        return false;
    }

    // 等待页面加载完成
    function waitForPageLoad() {
        // 先检查关键元素是否已经存在
        if (checkKeyElementsLoaded()) {
            console.log('关键元素已加载，开始提取信息');
            if (extractJingdongProductInfo()) {
                return; // 已经完成
            }
        }

        // 如果页面还没有加载完成，等待一段时间后重试
        let attempts = 0;
        const maxAttempts = 20; // 最多尝试20次，每次间隔500ms

        const checkInterval = setInterval(() => {
            attempts++;
            console.log(`第${attempts}次尝试提取京东商品信息...`);

            // 先检查关键元素，再尝试提取
            if (checkKeyElementsLoaded() && extractJingdongProductInfo()) {
                clearInterval(checkInterval);
            } else if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                if (!isCompleted) {
                    completeWithError('达到最大尝试次数，未能提取到商品信息');
                }
            }
        }, 500);
    }

    // 开始执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForPageLoad);
    } else {
        waitForPageLoad();
    }

})();
