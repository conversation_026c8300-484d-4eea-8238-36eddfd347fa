(function() {
    // 使用标志变量跟踪findResult是否已被调用
    var resultSent = false;
    var checkingData = false;
    var timers = [];

    // 简单的sleep函数
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 清除所有定时器的函数
    function clearAllTimers() {
        for (var i = 0; i < timers.length; i++) {
            clearTimeout(timers[i]);
        }
        timers = [];
    }

    // 提取豆瓣电影数据的函数
    function extractDoubanMovie() {
        // 如果结果已经发送，不再重复处理
        if (resultSent) {
            return;
        }

        try {
            var data = {};

            // 检查是否是有效的豆瓣链接
            var currentUrl = window.location.href;
            if (!isValidDoubanUrl(currentUrl)) {
                throw new Error("不是有效的豆瓣链接");
            }

            // 提取标题
            var titleElement = document.querySelector('.sub-original-title');
            if (titleElement) {
                data.title = titleElement.textContent.trim();
            } else {
                // 尝试使用兼容方式获取标题
                var altTitleElement = document.querySelector('.event-info h1');
                if (altTitleElement) {
                    data.title = altTitleElement.textContent.trim();
                } else {
                    // 兼容性取法1：尝试从.subject-basic-title获取标题
                    var subjectBasicTitleElement = document.querySelector('.subject-basic-title');
                    if (subjectBasicTitleElement) {
                        data.title = subjectBasicTitleElement.textContent.trim();
                    } else {
                        // 兼容性取法2：尝试从.group-name获取标题
                        var groupNameElement = document.querySelector('.group-name');
                        if (groupNameElement) {
                            data.title = groupNameElement.textContent.trim();
                        } else {
                            // 兼容性取法3：尝试从root下的h1获取标题
                            var rootH1Element = document.querySelector('#root') && document.querySelector('#root').querySelector('h1');
                            if (rootH1Element) {
                                data.title = rootH1Element.textContent.trim();
                            } else {
                                // 如果没有找到标题，使用页面标题
                                data.title = document.title.replace(' - 豆瓣', '').trim();
                            }
                        }
                    }
                }
            }

            // 提取作者（导演）
            var authorElement = document.querySelector('.sub-title');
            if (authorElement) {
                data.authorName = authorElement.textContent.trim();
            } else {
                // 尝试使用兼容方式1获取作者：查找class为.pl且内容包含"类型"的节点
                var typeLabels = document.querySelectorAll('.pl');
                var authorFound = false;

                for (var i = 0; i < typeLabels.length; i++) {
                    var label = typeLabels[i];
                    if (label.textContent.indexOf('类型') !== -1) {
                        // 找到了包含"类型"的节点，获取它的下一个兄弟节点
                        var nextSibling = label.nextSibling;
                        if (nextSibling && nextSibling.textContent) {
                            data.authorName = nextSibling.textContent.trim();
                            authorFound = true;
                            break;
                        }
                    }
                }

                // 如果仍未找到作者，尝试使用兼容方式2：从.meta内容中提取第一个/之前的内容
                if (!authorFound) {
                    var metaElement = document.querySelector('.meta');
                    if (metaElement && metaElement.textContent) {
                        var metaText = metaElement.textContent.trim();
                        // 查找第一个/的位置
                        var slashIndex = metaText.indexOf('/');
                        if (slashIndex !== -1) {
                            // 提取第一个/之前的内容
                            data.authorName = metaText.substring(0, slashIndex).trim();
                            authorFound = true;
                        }
                    }
                }

                // 如果仍未找到作者，尝试兼容性取法1：从.subject-basic-title > div获取作者
                if (!authorFound) {
                    var subjectBasicTitleDiv = document.querySelector('.subject-basic-title > div');
                    if (subjectBasicTitleDiv) {
                        data.authorName = subjectBasicTitleDiv.textContent.trim();
                        authorFound = true;
                    }
                }

                // 如果仍未找到作者，尝试兼容性取法2：从.group-name获取作者
                if (!authorFound) {
                    var groupNameForAuthor = document.querySelector('.group-name');
                    if (groupNameForAuthor) {
                        var groupName = groupNameForAuthor.textContent.trim();
                        data.authorName = '豆瓣小组 ' + groupName;
                        authorFound = true;
                    }
                }

                // 如果仍未找到作者，尝试兼容性取法3：从root下的p获取作者
                if (!authorFound) {
                    var rootPElement = document.querySelector('#root') && document.querySelector('#root').querySelector('p');
                    if (rootPElement) {
                        data.authorName = rootPElement.textContent.trim();
                        authorFound = true;
                    }
                }

                if (!authorFound) {
                    data.authorName = "";
                }
            }

            // 提取描述（电影元数据）
            var descriptionElement = document.querySelector('.sub-meta');
            var processedDescription = "";

            if (descriptionElement) {
                // 获取原始文本
                var rawDescription = descriptionElement.textContent.trim();

                // 处理文本：去除所有换行符和多余空格
                // 1. 将所有换行符替换为空字符串（完全去除）
                processedDescription = rawDescription.replace(/\n/g, '');

                // 2. 将连续的空格替换为单个空格
                processedDescription = processedDescription.replace(/[ \t]{2,}/g, ' ');
            } else {
                // 尝试使用兼容方式1获取描述：calendar-str-item + micro-address
                var calendarItem = document.querySelector('.calendar-str-item');
                var microAddressSpans = document.querySelectorAll('.micro-address span');

                var descriptionParts = [];

                // 添加日历项内容
                if (calendarItem) {
                    descriptionParts.push(calendarItem.textContent.trim());
                }

                // 添加地址span内容
                if (microAddressSpans && microAddressSpans.length > 0) {
                    for (var i = 0; i < microAddressSpans.length; i++) {
                        var spanText = microAddressSpans[i].textContent.trim();
                        // 去除&nbsp;
                        spanText = spanText.replace(/\u00A0/g, ' ');
                        if (spanText) {
                            descriptionParts.push(spanText);
                        }
                    }
                }

                // 合并所有部分
                if (descriptionParts.length > 0) {
                    processedDescription = descriptionParts.join(' ');
                    // 将连续的空格替换为单个空格
                    processedDescription = processedDescription.replace(/[ \t]{2,}/g, ' ');
                } else {
                    // 尝试使用兼容方式2获取描述：.meta
                    var metaElement = document.querySelector('.meta');
                    if (metaElement) {
                        // 获取原始文本
                        var rawMetaDescription = metaElement.textContent.trim();

                        // 处理文本：去除所有换行符和多余空格
                        // 1. 将所有换行符替换为空字符串（完全去除）
                        processedDescription = rawMetaDescription.replace(/\n/g, '');

                        // 2. 将连续的空格替换为单个空格
                        processedDescription = processedDescription.replace(/[ \t]{2,}/g, ' ');

                        // 3. 去除&nbsp;
                        processedDescription = processedDescription.replace(/\u00A0/g, ' ');
                    } else {
                        // 兼容性取法1：尝试从.subject-basic-subtitle span获取描述
                        var subjectBasicSubtitleSpans = document.querySelectorAll('.subject-basic-subtitle span');
                        if (subjectBasicSubtitleSpans && subjectBasicSubtitleSpans.length > 0) {
                            var subtitleParts = [];
                            for (var k = 0; k < subjectBasicSubtitleSpans.length; k++) {
                                var spanText = subjectBasicSubtitleSpans[k].textContent.trim();
                                if (spanText) {
                                    subtitleParts.push(spanText);
                                }
                            }
                            if (subtitleParts.length > 0) {
                                processedDescription = subtitleParts.join(' ');
                                // 将连续的空格替换为单个空格
                                processedDescription = processedDescription.replace(/[ \t]{2,}/g, ' ');
                            }
                        } else {
                            // 兼容性取法2：尝试从.shownotes下的p获取描述
                            var shownotesElement = document.querySelector('.shownotes');
                            if (shownotesElement) {
                                var shownotesP = shownotesElement.querySelector('p');
                                if (shownotesP) {
                                    var shownotesText = shownotesP.innerText;
                                    // 使用正则表达式提取中文字符
                                    var chineseText = shownotesText.match(/[\u4e00-\u9fa5]+/g);
                                    if (chineseText && chineseText.length > 0) {
                                        // 将所有中文片段连接起来
                                        var fullChineseText = chineseText.join('');
                                        // 截取前50个字符
                                        processedDescription = fullChineseText.substring(0, 50);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 设置描述
            data.description = processedDescription;

            // 限制描述长度为100个字符
            if (data.description.length > 100) {
                data.description = data.description.substring(0, 100) + '...';
            }

            // 提取封面图片
            var coverElement = document.querySelector('.sub-cover img');
            if (coverElement) {
                data.coverUrl = coverElement.src || '';
                // 确保封面URL是完整的
                if (data.coverUrl && data.coverUrl.startsWith('//')) {
                    data.coverUrl = 'https:' + data.coverUrl;
                }
            } else {
                // 尝试使用兼容方式获取封面 - 方式1：poster-img
                var altCoverElement = document.querySelector('#poster-img');
                if (altCoverElement) {
                    data.coverUrl = altCoverElement.src || '';
                    // 确保封面URL是完整的
                    if (data.coverUrl && data.coverUrl.startsWith('//')) {
                        data.coverUrl = 'https:' + data.coverUrl;
                    }
                } else {
                    // 尝试使用兼容方式获取封面 - 方式2：.cover
                    var altCoverElement2 = document.querySelector('.cover');
                    if (altCoverElement2) {
                        // 检查是否是img元素
                        if (altCoverElement2.tagName.toLowerCase() === 'img') {
                            data.coverUrl = altCoverElement2.src || '';
                        } else {
                            // 如果不是img元素，尝试查找其中的img
                            var imgInCover = altCoverElement2.querySelector('img');
                            if (imgInCover) {
                                data.coverUrl = imgInCover.src || '';
                            }
                        }

                        // 确保封面URL是完整的
                        if (data.coverUrl && data.coverUrl.startsWith('//')) {
                            data.coverUrl = 'https:' + data.coverUrl;
                        }
                    } else {
                        // 兼容性取法1：尝试从.subject-basic-cover获取封面
                        var subjectBasicCoverElement = document.querySelector('.subject-basic-cover');
                        if (subjectBasicCoverElement) {
                            // 检查是否是img元素
                            if (subjectBasicCoverElement.tagName.toLowerCase() === 'img') {
                                data.coverUrl = subjectBasicCoverElement.src || '';
                            } else {
                                // 如果不是img元素，尝试查找其中的img
                                var imgInSubjectCover = subjectBasicCoverElement.querySelector('img');
                                if (imgInSubjectCover) {
                                    data.coverUrl = imgInSubjectCover.src || '';
                                }
                            }
                            // 确保封面URL是完整的
                            if (data.coverUrl && data.coverUrl.startsWith('//')) {
                                data.coverUrl = 'https:' + data.coverUrl;
                            }
                        } else {
                            // 兼容性取法2：尝试从.group-avatar获取封面
                            var groupAvatarElement = document.querySelector('.group-avatar');
                            if (groupAvatarElement) {
                                // 检查是否是img元素
                                if (groupAvatarElement.tagName.toLowerCase() === 'img') {
                                    data.coverUrl = groupAvatarElement.src || '';
                                } else {
                                    // 如果不是img元素，尝试查找其中的img
                                    var imgInGroupAvatar = groupAvatarElement.querySelector('img');
                                    if (imgInGroupAvatar) {
                                        data.coverUrl = imgInGroupAvatar.src || '';
                                    }
                                }
                                // 确保封面URL是完整的
                                if (data.coverUrl && data.coverUrl.startsWith('//')) {
                                    data.coverUrl = 'https:' + data.coverUrl;
                                }
                            } else {
                                // 兼容性取法3：尝试从root下的img获取封面
                                var rootImgForCover = document.querySelector('#root') && document.querySelector('#root').querySelector('img');
                                if (rootImgForCover) {
                                    data.coverUrl = rootImgForCover.src || '';
                                    // 确保封面URL是完整的
                                    if (data.coverUrl && data.coverUrl.startsWith('//')) {
                                        data.coverUrl = 'https:' + data.coverUrl;
                                    }
                                } else {
                                    data.coverUrl = "";
                                }
                            }
                        }
                    }
                }
            }

            // 使用封面图片作为头像
            data.avatarUrl = data.coverUrl;

            // 如果没有获取到封面图片，尝试直接从.cover元素获取头像
            if (!data.avatarUrl) {
                var avatarElement = document.querySelector('.cover');
                if (avatarElement) {
                    // 检查是否是img元素
                    if (avatarElement.tagName.toLowerCase() === 'img') {
                        data.avatarUrl = avatarElement.src || '';
                    } else {
                        // 如果不是img元素，尝试查找其中的img
                        var imgInAvatar = avatarElement.querySelector('img');
                        if (imgInAvatar) {
                            data.avatarUrl = imgInAvatar.src || '';
                        }
                    }

                    // 确保头像URL是完整的
                    if (data.avatarUrl && data.avatarUrl.startsWith('//')) {
                        data.avatarUrl = 'https:' + data.avatarUrl;
                    }
                } else {
                    // 兼容性取法1：尝试从.subject-basic-cover获取头像
                    var subjectBasicCoverForAvatar = document.querySelector('.subject-basic-cover');
                    if (subjectBasicCoverForAvatar) {
                        // 检查是否是img元素
                        if (subjectBasicCoverForAvatar.tagName.toLowerCase() === 'img') {
                            data.avatarUrl = subjectBasicCoverForAvatar.src || '';
                        } else {
                            // 如果不是img元素，尝试查找其中的img
                            var imgInSubjectCoverForAvatar = subjectBasicCoverForAvatar.querySelector('img');
                            if (imgInSubjectCoverForAvatar) {
                                data.avatarUrl = imgInSubjectCoverForAvatar.src || '';
                            }
                        }
                        // 确保头像URL是完整的
                        if (data.avatarUrl && data.avatarUrl.startsWith('//')) {
                            data.avatarUrl = 'https:' + data.avatarUrl;
                        }
                    } else {
                        // 兼容性取法2：尝试从.group-avatar获取头像
                        var groupAvatarForAvatar = document.querySelector('.group-avatar');
                        if (groupAvatarForAvatar) {
                            // 检查是否是img元素
                            if (groupAvatarForAvatar.tagName.toLowerCase() === 'img') {
                                data.avatarUrl = groupAvatarForAvatar.src || '';
                            } else {
                                // 如果不是img元素，尝试查找其中的img
                                var imgInGroupAvatarForAvatar = groupAvatarForAvatar.querySelector('img');
                                if (imgInGroupAvatarForAvatar) {
                                    data.avatarUrl = imgInGroupAvatarForAvatar.src || '';
                                }
                            }
                            // 确保头像URL是完整的
                            if (data.avatarUrl && data.avatarUrl.startsWith('//')) {
                                data.avatarUrl = 'https:' + data.avatarUrl;
                            }
                        } else {
                            // 兼容性取法3：尝试从root下的img获取头像
                            var rootImgElement = document.querySelector('#root') && document.querySelector('#root').querySelector('img');
                            if (rootImgElement) {
                                data.avatarUrl = rootImgElement.src || '';
                                // 确保头像URL是完整的
                                if (data.avatarUrl && data.avatarUrl.startsWith('//')) {
                                    data.avatarUrl = 'https:' + data.avatarUrl;
                                }
                            }
                        }
                    }
                }
            }

            // 初始化图片数组
            data.images = [];

            // 添加封面图片到图片数组
            if (data.coverUrl) {
                data.images.push(data.coverUrl);
            }

            // 提取原生协议schemeURL
            // 注意：这里需要在WebView中拦截跳转请求来获取
            data.schemeURL = currentUrl;

            // 标记结果已发送
            resultSent = true;

            // 返回结果
            Android.findResult(JSON.stringify(data));
        } catch (e) {
            // 标记结果已发送
            resultSent = true;

            // 发生错误，返回错误信息
            Android.findResult(JSON.stringify({
                error: e.message,
                url: window.location.href
            }));
        }
    }

    // 检查是否是有效的豆瓣链接
    function isValidDoubanUrl(url) {
        // 简单检查是否是http或https链接
        return /^https?:\/\//.test(url);
    }

    // 检查数据是否已加载
    function checkDataLoaded() {
        // 检查DOM元素是否存在
        var selectors = [
            '.sub-original-title',
            '.subject-basic-title',
            '.group-name',
            '.event-info h1',
            '#poster-img',
            '.group-avatar',
            '#root',
            '.poster-img',
            '.sub-title',
            '.sub-meta',
            '.meta',
            '.calendar-str-item',
            '.micro-address span',
            '.cover',
            '.sub-cover img',
            '.subject-basic-cover',
            '.subject-basic-subtitle span',
            '.pl'
        ];

        for (var i = 0; i < selectors.length; i++) {
            if (document.querySelector(selectors[i])) {
                return true;
            }
        }

        return false;
    }

    // 轮询检查数据是否已加载
    async function pollForData() {
        if (checkingData) {
            return false;
        }

        checkingData = true;

        // 检查数据是否已加载，最多等待5秒
        const maxAttempts = 30;
        const interval = 500; // 500毫秒检查一次
        let attempts = 0;

        while (attempts < maxAttempts) {
            if (checkDataLoaded()) {
                checkingData = false;
                return true;
            }

            attempts++;
            await sleep(interval);
        }

        checkingData = false;
        return false;
    }

    // 主函数：检查数据是否已加载，然后直接提取内容
    function main() {
        // 如果结果已经发送，不再处理
        if (resultSent) {
            return;
        }

        // 开始轮询检查数据是否已加载
        pollForData().then(function(dataLoaded) {
            if (dataLoaded) {
                // 数据已加载，直接提取数据
                extractDoubanMovie();
            } else {
                // 5秒内数据未加载，返回空结果
                if (!resultSent) {
                    resultSent = true;
                    Android.findResult(JSON.stringify({
                        error: "页面数据未加载",
                        url: window.location.href
                    }));
                }
            }
        });
    }

    // 执行主函数
    main();
})();
