<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- 网络访问权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 友盟SDK所需权限 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 腾讯云ASR SDK所需权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!-- 添加剪贴板访问权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="29" />
    <!-- SparkChain SDK 所需权限 -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- Android 10+ 需要指定前台服务类型 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <!-- 添加悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <!-- 添加前台服务权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <!-- 添加后台运行权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 安装未知来源应用权限 (Android 8.0+) -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Android 14+ MediaProjection前台服务所需的额外权限 -->
    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" tools:ignore="ProtectedPermissions" />
    <application
        android:label="@string/app_name"
        android:usesCleartextTraffic="true"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher"
        android:enableOnBackInvokedCallback="true"
        android:requestLegacyExternalStorage="true">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

            <!-- 友盟集成测试配置 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="um.682ad77dbc47b67d836a0987" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <!-- 微信回调Activity -->
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="com.xunhe.aishoucang"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <!-- 微信支付回调Activity（虽然不使用支付功能，但需要此Activity） -->
        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="com.xunhe.aishoucang"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <!-- 微信AppID配置 -->
        <meta-data
            android:name="WX_APPID"
            android:value="${WX_APPID}" />

        <!-- 注册WebView活动 -->
        <activity
            android:name=".WebViewActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode" />

        <!-- 注册笔记WebView活动 -->
        <activity
            android:name=".NoteWebviewActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode" />

        <!-- 注册悬浮窗服务 -->
        <service
            android:name=".lib.FloatingWindowHelper$FloatingWindowService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 注册返回按钮悬浮窗服务 -->
        <service
            android:name=".lib.ReturnButtonHelper$ReturnButtonService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 注册侧边栏服务 -->
        <service
            android:name=".lib.SidebarService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 注册侧边栏悬浮菜单服务 -->
        <service
            android:name=".lib.SidebarFloatingMenuService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 注册无障碍服务，用于辅助操作UI元素和获取前台Activity信息 -->
        <service
            android:name=".lib.AccessibilityHelper$AppAccessibilityService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- 注册截图服务 -->
        <service
            android:name=".lib.ScreenshotService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <!-- 注册长截图服务 -->
        <service
            android:name=".lib.LongScreenshotService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <!-- 注册截图权限请求Activity -->
        <activity
            android:name=".lib.ScreenshotPermissionActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:launchMode="singleTask"
            android:excludeFromRecents="true"
            android:noHistory="true" />

        <!-- 注册长截图权限请求Activity -->
        <activity
            android:name=".lib.LongScreenshotPermissionActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:launchMode="singleTask"
            android:excludeFromRecents="true"
            android:noHistory="true" />

        <!-- FileProvider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 注册自定义通知面板广播接收器 -->
        <receiver
            android:name=".lib.CustomNotificationPanel$NotificationPanelReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.xunhe.aishoucang.TOGGLE_FLOATING_WINDOW" />
                <action android:name="com.xunhe.aishoucang.TOGGLE_SWITCH" />
            </intent-filter>
        </receiver>

    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
        <!-- 微信SDK需要查询微信包 -->
        <package android:name="com.tencent.mm" />
    </queries>
</manifest>
