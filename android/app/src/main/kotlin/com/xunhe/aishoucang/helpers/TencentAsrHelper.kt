package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.tencent.cloud.qcloudasrsdk.filerecognize.QCloudFlashRecognizer
import com.tencent.cloud.qcloudasrsdk.filerecognize.QCloudFlashRecognizerListener
import com.tencent.cloud.qcloudasrsdk.filerecognize.param.QCloudFlashRecognitionParams
import com.tencent.cloud.qcloudasrsdk.onesentence.QCloudOneSentenceRecognizer
import com.tencent.cloud.qcloudasrsdk.onesentence.QCloudOneSentenceRecognizerListener
import com.tencent.cloud.qcloudasrsdk.onesentence.network.QCloudOneSentenceRecognitionParams
import com.tencent.cloud.qcloudasrsdk.onesentence.common.QCloudSourceType
import java.io.File
import java.io.FileInputStream
import com.xunhe.aishoucang.lib.FFmpegHelper
import com.xunhe.aishoucang.helpers.CustomToastHelper
import org.json.JSONObject
import org.json.JSONException

/**
 * 腾讯云ASR SDK助手类
 * 封装腾讯云ASR SDK的初始化和使用逻辑
 */
class TencentAsrHelper private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "TencentAsrHelper"
        
        @Volatile
        private var instance: TencentAsrHelper? = null
        
        fun getInstance(context: Context): TencentAsrHelper {
            return instance ?: synchronized(this) {
                instance ?: TencentAsrHelper(context.applicationContext).also { instance = it }
            }
        }
    }
    
    // 腾讯云配置
    data class TencentCloudConfig(
        val appId: String,
        val secretId: String,
        val secretKey: String
    )
    
    // 识别结果回调
    interface RecognitionCallback {
        fun onSuccess(result: String)
        fun onError(error: String)
    }
    
    // 当前配置
    private var currentConfig: TencentCloudConfig? = null

    // 错误码映射
    private val errorCodeMap = mapOf(
        4001 to "参数不合法",
        4002 to "鉴权失败",
        4004 to "服务过热，请稍后再试",
        4005 to "服务过热，请稍后再试",
        4006 to "当前使用人数过多，请稍后再试",
        4008 to "上传视频超时，网络不稳定",
        4009 to "服务异常断开",
        4011 to "识别失败啦",
        5001 to "服务异常",
        5002 to "服务异常",
        5003 to "服务异常"
    )
    
    /**
     * 初始化腾讯云ASR配置
     */
    fun initializeConfig(config: TencentCloudConfig): Boolean {
        return try {
            currentConfig = config
            Log.i(TAG, "腾讯云ASR配置初始化成功，AppId: ${config.appId}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "腾讯云ASR配置初始化失败", e)
            false
        }
    }

    /**
     * 处理识别结果，检查错误码并显示CustomToast
     * @param result 识别结果JSON字符串
     * @param callback 回调接口
     * @return true表示有错误码，已处理；false表示无错误，继续执行回调
     */
    private fun handleRecognitionResult(result: String?, callback: RecognitionCallback): Boolean {
        if (result.isNullOrEmpty()) {
            callback.onError("识别结果为空")
            return true
        }

        try {
            val jsonResult = JSONObject(result)
            val code = jsonResult.optInt("code", -1)

            if (code != 0) {
                // 有错误码，显示CustomToast
                val errorMessage = errorCodeMap[code] ?: "未知错误(错误码: $code)"
                Log.e(TAG, "腾讯云ASR识别失败，错误码: $code, 错误信息: $errorMessage")

                // 显示CustomToast提示用户
                CustomToastHelper.showLongToast(context, "语音识别失败: $errorMessage")

                // 不执行回调，直接返回
                return true
            }

            // 没有错误码，继续处理
            return false

        } catch (e: JSONException) {
            Log.e(TAG, "解析识别结果JSON失败", e)
            callback.onError("解析识别结果失败: ${e.message}")
            return true
        }
    }

    /**
     * 一句话识别 - 通过音频数据
     */
    fun recognizeOneSentenceByData(
        audioData: ByteArray,
        voiceFormat: String = "mp3",
        engineModelType: String = "16k_zh",
        callback: RecognitionCallback
    ) {
        val config = currentConfig
        if (config == null) {
            callback.onError("腾讯云ASR未初始化，请先调用initializeConfig")
            return
        }
        
        try {
            val recognizer = QCloudOneSentenceRecognizer(config.appId, config.secretId, config.secretKey)
            
            recognizer.setCallback(object : QCloudOneSentenceRecognizerListener {
                override fun didStartRecord() {
                    Log.d(TAG, "开始录音")
                }
                
                override fun didStopRecord() {
                    Log.d(TAG, "停止录音")
                }
                
                override fun recognizeResult(
                    recognizer: QCloudOneSentenceRecognizer?,
                    result: String?,
                    exception: Exception?
                ) {
                    if (exception != null) {
                        Log.e(TAG, "一句话识别失败", exception)
                        callback.onError("识别失败: ${exception.message}")
                    } else {
                        // 先检查错误码，如果有错误就显示CustomToast并不执行回调
                        if (!handleRecognitionResult(result, callback)) {
                            // 没有错误码，执行成功回调
                            Log.i(TAG, "一句话识别成功: $result")
                            callback.onSuccess(result!!)
                        }
                    }
                }
            })
            
            val params = QCloudOneSentenceRecognitionParams.defaultRequestParams() as QCloudOneSentenceRecognitionParams
            params.setSourceType(QCloudSourceType.QCloudSourceTypeData)
            params.setData(audioData)
            params.setVoiceFormat(voiceFormat)
            params.setEngSerViceType(engineModelType)
            params.setFilterDirty(0)  // 不过滤脏话
            params.setFilterModal(0)  // 不过滤语气词
            params.setFilterPunc(0)   // 不过滤句号
            params.setConvertNumMode(1)  // 智能转换数字
            
            recognizer.recognize(params)
            
        } catch (e: Exception) {
            Log.e(TAG, "一句话识别异常", e)
            callback.onError("识别异常: ${e.message}")
        }
    }
    
    /**
     * 一句话识别 - 通过文件路径
     */
    fun recognizeOneSentenceByFile(
        filePath: String,
        voiceFormat: String = "mp3",
        engineModelType: String = "16k_zh",
        callback: RecognitionCallback
    ) {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                callback.onError("音频文件不存在: $filePath")
                return
            }
            
            val audioData = FileInputStream(file).use { it.readBytes() }
            recognizeOneSentenceByData(audioData, voiceFormat, engineModelType, callback)
            
        } catch (e: Exception) {
            Log.e(TAG, "读取音频文件失败", e)
            callback.onError("读取音频文件失败: ${e.message}")
        }
    }
    
    /**
     * 录音文件识别极速版 - 通过音频数据
     */
    fun recognizeFlashByData(
        audioData: ByteArray,
        voiceFormat: String = "pcm",
        engineModelType: String = "16k_zh_video",
        callback: RecognitionCallback
    ) {
        val config = currentConfig
        if (config == null) {
            callback.onError("腾讯云ASR未初始化，请先调用initializeConfig")
            return
        }
        
        try {
            val recognizer = QCloudFlashRecognizer(config.appId, config.secretId, config.secretKey)
            
            recognizer.setCallback(object : QCloudFlashRecognizerListener {
                override fun recognizeResult(
                    recognizer: QCloudFlashRecognizer?,
                    result: String?,
                    exception: Exception?
                ) {
                    if (exception != null) {
                        Log.e(TAG, "录音文件识别失败", exception)
                        callback.onError("识别失败: ${exception.message}")
                    } else {
                        // 先检查错误码，如果有错误就显示CustomToast并不执行回调
                        if (!handleRecognitionResult(result, callback)) {
                            // 没有错误码，执行成功回调
                            Log.i(TAG, "录音文件识别成功: $result")
                            callback.onSuccess(result!!)
                        }
                    }
                }
            })
            
            val params = QCloudFlashRecognitionParams.defaultRequestParams() as QCloudFlashRecognitionParams
            params.setData(audioData)
            params.setVoiceFormat(voiceFormat)
            params.setEngineModelType(engineModelType)
            params.setFilterDirty(0)  // 不过滤脏话
            params.setFilterModal(0)  // 不过滤语气词
            params.setFilterPunc(0)   // 不过滤句号
            params.setConvertNumMode(1)  // 智能转换数字
            params.setSpeakerDiarization(0)  // 不开启说话人分离
            params.setFirstChannelOnly(1)   // 只识别首个声道
            params.setWordInfo(0)  // 不显示词级别时间戳

            // 设置超时时间
            params.setConnectTimeout(30 * 1000)  // 30秒连接超时
            params.setReadTimeout(600 * 1000)    // 10分钟读取超时
            
            recognizer.recognize(params)
            
        } catch (e: Exception) {
            Log.e(TAG, "录音文件识别异常", e)
            callback.onError("识别异常: ${e.message}")
        }
    }
    
    /**
     * 录音文件识别极速版 - 通过文件路径
     */
    fun recognizeFlashByFile(
        filePath: String,
        voiceFormat: String = "pcm",
        engineModelType: String = "16k_zh_video",
        callback: RecognitionCallback
    ) {
        val config = currentConfig
        if (config == null) {
            callback.onError("腾讯云ASR未初始化，请先调用initializeConfig")
            return
        }
        
        try {
            val file = File(filePath)
            if (!file.exists()) {
                callback.onError("音频文件不存在: $filePath")
                return
            }
            
            val recognizer = QCloudFlashRecognizer(config.appId, config.secretId, config.secretKey)
            
            recognizer.setCallback(object : QCloudFlashRecognizerListener {
                override fun recognizeResult(
                    recognizer: QCloudFlashRecognizer?,
                    result: String?,
                    exception: Exception?
                ) {
                    if (exception != null) {
                        Log.e(TAG, "录音文件识别失败", exception)
                        callback.onError("识别失败: ${exception.message}")
                    } else {
                        // 先检查错误码，如果有错误就显示CustomToast并不执行回调
                        if (!handleRecognitionResult(result, callback)) {
                            // 没有错误码，执行成功回调
                            Log.i(TAG, "录音文件识别成功: $result")
                            callback.onSuccess(result!!)
                        }
                    }
                }
            })
            
            val params = QCloudFlashRecognitionParams.defaultRequestParams() as QCloudFlashRecognitionParams
            params.setPath(filePath)  // 使用文件路径
            params.setVoiceFormat(voiceFormat)
            params.setEngineModelType(engineModelType)
            params.setFilterDirty(0)
            params.setFilterModal(0)
            params.setFilterPunc(0)
            params.setConvertNumMode(1)
            params.setSpeakerDiarization(0)
            params.setFirstChannelOnly(1)
            params.setWordInfo(0)

            // 设置超时时间
            params.setConnectTimeout(30 * 1000)
            params.setReadTimeout(600 * 1000)
            
            recognizer.recognize(params)
            
        } catch (e: Exception) {
            Log.e(TAG, "录音文件识别异常", e)
            callback.onError("识别异常: ${e.message}")
        }
    }

    /**
     * 录音文件识别极速版 - 通过MP4文件路径（自动转换为PCM格式）
     */
    fun recognizeFlashByMp4File(
        mp4FilePath: String,
        engineModelType: String = "16k_zh_video",
        callback: RecognitionCallback
    ) {
        val config = currentConfig
        if (config == null) {
            callback.onError("腾讯云ASR未初始化，请先调用initializeConfig")
            return
        }

        try {
            val mp4File = File(mp4FilePath)
            if (!mp4File.exists()) {
                callback.onError("MP4文件不存在: $mp4FilePath")
                return
            }

            Log.i(TAG, "开始将MP4文件转换为PCM格式: $mp4FilePath")

            // 使用FFmpegHelper将MP4文件转换为PCM格式
            val ffmpegHelper = FFmpegHelper.getInstance(context)
            ffmpegHelper.convertAudioToPCM(
                audioPath = mp4FilePath,
                outputPath = null, // 使用默认路径
                sampleRate = 16000,
                channels = 1
            ) { convertSuccess: Boolean, pcmFilePath: String?, duration: Double?, convertError: String? ->
                if (!convertSuccess || pcmFilePath == null) {
                    Log.e(TAG, "MP4转PCM失败: $convertError")
                    callback.onError("MP4转PCM失败: $convertError")
                    return@convertAudioToPCM
                }

                Log.i(TAG, "MP4转PCM成功，开始进行语音识别: $pcmFilePath")

                // 转换成功后，使用PCM文件进行识别
                recognizeFlashByFile(
                    filePath = pcmFilePath,
                    voiceFormat = "pcm",
                    engineModelType = engineModelType,
                    callback = object : RecognitionCallback {
                        override fun onSuccess(result: String) {
                            // 识别成功后删除临时PCM文件
                            try {
                                val pcmFile = File(pcmFilePath)
                                if (pcmFile.exists() && pcmFile.delete()) {
                                    Log.d(TAG, "已删除临时PCM文件: $pcmFilePath")
                                } else {
                                    Log.w(TAG, "删除临时PCM文件失败: $pcmFilePath")
                                }
                            } catch (e: Exception) {
                                Log.w(TAG, "删除临时PCM文件异常: ${e.message}")
                            }

                            callback.onSuccess(result)
                        }

                        override fun onError(error: String) {
                            // 识别失败也要删除临时PCM文件
                            try {
                                val pcmFile = File(pcmFilePath)
                                if (pcmFile.exists() && pcmFile.delete()) {
                                    Log.d(TAG, "已删除临时PCM文件: $pcmFilePath")
                                } else {
                                    Log.w(TAG, "删除临时PCM文件失败: $pcmFilePath")
                                }
                            } catch (e: Exception) {
                                Log.w(TAG, "删除临时PCM文件异常: ${e.message}")
                            }

                            callback.onError(error)
                        }
                    }
                )
            }

        } catch (e: Exception) {
            Log.e(TAG, "MP4文件识别异常", e)
            callback.onError("MP4文件识别异常: ${e.message}")
        }
    }

    /**
     * 获取配置状态
     */
    fun isConfigured(): Boolean = currentConfig != null
    
    /**
     * 清理配置
     */
    fun clearConfig() {
        currentConfig = null
        Log.i(TAG, "腾讯云ASR配置已清理")
    }
}
