package com.xunhe.aishoucang.lib

import android.content.Context
import android.content.SharedPreferences

/**
 * SharedPreferences辅助类，用于封装本地键值对存储操作
 */
class SharedPreferencesHelper private constructor(private val context: Context) {
    companion object {

        // 常用键名，与Flutter端保持一致
        const val KEY_AUTH_TOKEN = "auth_token"
        const val KEY_USER_ID = "user_id"
        const val KEY_USER_PHONE = "user_phone"

        // 悬浮窗位置键名
        const val KEY_FLOATING_WINDOW_X = "floating_window_x"
        const val KEY_FLOATING_WINDOW_Y = "floating_window_y"

        @Volatile
        private var instance: SharedPreferencesHelper? = null

        fun getInstance(context: Context): SharedPreferencesHelper {
            return instance ?: synchronized(this) {
                instance ?: SharedPreferencesHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    // 获取SharedPreferences实例
    private fun getPrefs(): SharedPreferences {
        // 使用应用包名作为SharedPreferences文件名
        val prefName = context.packageName
        return context.getSharedPreferences(prefName, Context.MODE_PRIVATE)
    }

    // 设置认证令牌
    fun setAuthToken(token: String) {
        getPrefs().edit().putString(KEY_AUTH_TOKEN, token).apply()
    }

    // 获取认证令牌
    fun getAuthToken(): String {
        return getPrefs().getString(KEY_AUTH_TOKEN, "") ?: ""
    }

    // 设置用户ID
    fun setUserId(userId: String) {
        android.util.Log.d("SharedPreferencesHelper", "设置用户ID: $userId")
        getPrefs().edit().putString(KEY_USER_ID, userId).apply()
    }

    // 获取用户ID
    fun getUserId(): String {
        val userId = getPrefs().getString(KEY_USER_ID, "") ?: ""
        android.util.Log.d("SharedPreferencesHelper", "获取用户ID: $userId")
        return userId
    }

    // 设置用户手机号
    fun setUserPhone(phone: String) {
        getPrefs().edit().putString(KEY_USER_PHONE, phone).apply()
    }

    // 获取用户手机号
    fun getUserPhone(): String {
        return getPrefs().getString(KEY_USER_PHONE, "") ?: ""
    }

    // 清除所有用户数据（登出操作）
    fun clearUserData() {
        getPrefs().edit()
            .remove(KEY_AUTH_TOKEN)
            .remove(KEY_USER_ID)
            .remove(KEY_USER_PHONE)
            .apply()
    }

    // 通用方法：存储字符串值，可以被暂露给Flutter端调用
    fun putString(key: String, value: String) {
        getPrefs().edit().putString(key, value).apply()
    }

    // 通用方法：获取字符串值
    fun getString(key: String, defaultValue: String = ""): String {
        return getPrefs().getString(key, defaultValue) ?: defaultValue
    }

    // 通用方法：存储布尔值
    fun putBoolean(key: String, value: Boolean) {
        getPrefs().edit().putBoolean(key, value).apply()
    }

    // 通用方法：获取布尔值
    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return getPrefs().getBoolean(key, defaultValue)
    }

    // 保存悬浮窗X坐标
    fun setFloatingWindowX(x: Int) {
        getPrefs().edit().putInt(KEY_FLOATING_WINDOW_X, x).apply()
    }

    // 获取悬浮窗X坐标
    fun getFloatingWindowX(defaultValue: Int = 0): Int {
        return getPrefs().getInt(KEY_FLOATING_WINDOW_X, defaultValue)
    }

    // 保存悬浮窗Y坐标
    fun setFloatingWindowY(y: Int) {
        getPrefs().edit().putInt(KEY_FLOATING_WINDOW_Y, y).apply()
    }

    // 获取悬浮窗Y坐标
    fun getFloatingWindowY(defaultValue: Int = 0): Int {
        return getPrefs().getInt(KEY_FLOATING_WINDOW_Y, defaultValue)
    }

    // 保存悬浮窗位置
    fun saveFloatingWindowPosition(x: Int, y: Int) {
        getPrefs().edit()
            .putInt(KEY_FLOATING_WINDOW_X, x)
            .putInt(KEY_FLOATING_WINDOW_Y, y)
            .apply()
    }

    // 获取所有SharedPreferences中的键值对
    fun getAllPrefs(): Map<String, Any?> {
        val result = mutableMapOf<String, Any?>()
        val prefs = getPrefs()

        // 获取所有键
        val allKeys = prefs.all.keys

        // 遍历所有键，获取对应的值
        for (key in allKeys) {
            when {
                key == KEY_AUTH_TOKEN -> result[key] = "***TOKEN***" // 不显示实际token值
                else -> result[key] = prefs.all[key]
            }
        }

        return result
    }
}
