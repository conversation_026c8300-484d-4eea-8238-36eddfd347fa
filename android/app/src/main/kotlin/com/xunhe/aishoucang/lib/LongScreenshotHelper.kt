package com.xunhe.aishoucang.lib

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityNodeInfo
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.core.view.GestureDetectorCompat
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.helpers.CustomToastHelper
import java.io.File

/**
 * 长截图助手
 * 用于实现长截图功能，包括滚动捕获、图像拼接等
 */
class LongScreenshotHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "LongScreenshotHelper"
        const val REQUEST_LONG_SCREENSHOT_PERMISSION = 1002

        @Volatile
        private var instance: LongScreenshotHelper? = null

        // 保存最后一次选区信息
        private var lastSelectionRect: RectF? = null

        fun getInstance(context: Context): LongScreenshotHelper {
            return instance ?: synchronized(this) {
                instance ?: LongScreenshotHelper(context.applicationContext).also { instance = it }
            }
        }

        /**
         * 处理截图权限结果
         */
        fun handleLongScreenshotPermissionResult(context: Context, requestCode: Int, resultCode: Int, data: Intent?) {
            if (requestCode == REQUEST_LONG_SCREENSHOT_PERMISSION && resultCode == android.app.Activity.RESULT_OK && data != null) {
                // 启动前台服务进行长截图
                try {
                    // 显示提示
                    Toast.makeText(context, "正在准备长截图，请稍候...", Toast.LENGTH_SHORT).show()

                    // 启动长截图服务
                    LongScreenshotService.startLongScreenshotService(context, resultCode, data)

                    Log.d(TAG, "已启动长截图服务")
                } catch (e: Exception) {
                    Log.e(TAG, "启动长截图服务失败", e)
                    Toast.makeText(context, "启动长截图服务失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var overlayParams: WindowManager.LayoutParams? = null
    private var isOverlayShowing = false
    private var progressBar: ProgressBar? = null
    private var statusTextView: TextView? = null
    private var gestureDetector: GestureDetectorCompat? = null
    private var floatingWindowHelper: FloatingWindowHelper? = null

    // 取消按钮窗口
    private var cancelButtonView: View? = null
    private var cancelButtonParams: WindowManager.LayoutParams? = null
    private var isCancelButtonShowing = false

    // 保存悬浮窗位置信息
    private var floatingWindowX: Int? = null
    private var floatingWindowY: Int? = null

    // SharedPreferences辅助类
    private var prefsHelper: SharedPreferencesHelper? = null

    // 保存当前活动窗口的根节点，用于后续滚动操作
    private var savedRootNode: AccessibilityNodeInfo? = null

    // 保存开始长截图时的前台Activity名称
    private var startForegroundActivity: String = ""

    // 前台Activity变化监听器
    private val foregroundActivityChangeListener: (String) -> Unit = { newActivity ->
        Log.d(TAG, "前台Activity变化: $newActivity, 开始时的Activity: $startForegroundActivity")

        // 如果已经开始长截图，且前台Activity发生变化，则检查是否需要退出长截图
        if (isOverlayShowing && startForegroundActivity.isNotEmpty() && newActivity != startForegroundActivity) {
            // 检查新的Activity是否属于当前应用
            val currentAppPackage = context.packageName
            val newActivityPackage = newActivity.split("/")[0]

            // 只有当新的Activity不属于当前应用时才退出截图
            if (newActivityPackage != currentAppPackage) {
                Log.d(TAG, "前台Activity已变化到其他应用($newActivityPackage)，退出长截图")

                // 发送取消广播
                val intent = Intent(LongScreenshotService.ACTION_CANCEL_LONG_SCREENSHOT)
                context.sendBroadcast(intent)

                // 隐藏进度界面
                hideLongScreenshotProgressOverlay()

                // 确保悬浮窗被显示
                if (floatingWindowHelper?.isFloatingWindowShowing() == false) {
                    // 尝试从内存变量获取位置
                    if (floatingWindowX != null && floatingWindowY != null) {
                        Log.d(TAG, "从内存变量恢复悬浮窗位置: x=$floatingWindowX, y=$floatingWindowY")
                        floatingWindowHelper?.showFloatingWindowAtPosition(null, floatingWindowX, floatingWindowY)
                    } else {
                        // 尝试从SharedPreferences获取位置
                        val x = prefsHelper?.getFloatingWindowX()
                        val y = prefsHelper?.getFloatingWindowY()

                        if (x != null && y != null && (x != 0 || y != 0)) {
                            Log.d(TAG, "从SharedPreferences恢复悬浮窗位置: x=$x, y=$y")
                            floatingWindowHelper?.showFloatingWindowAtPosition(null, x, y)
                        } else {
                            // 如果没有保存的位置，则正常显示悬浮窗
                            Log.d(TAG, "没有保存的位置，正常显示悬浮窗")
                            floatingWindowHelper?.showFloatingWindow(null)
                        }
                    }
                }

                // 显示自定义Toast
                CustomToastHelper.showLongToast(context, "截长图暂停")
            } else {
                Log.d(TAG, "前台Activity变化但仍在当前应用内，继续长截图")
            }
        }
    }

    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        prefsHelper = SharedPreferencesHelper.getInstance(context)
    }

    /**
     * 开始长截图流程
     */
    fun startLongScreenshot() {
        Log.d(TAG, "开始长截图流程")

        // 获取FloatingWindowHelper实例
        floatingWindowHelper = FloatingWindowHelper.getInstance(context)

        // 获取并保存悬浮窗位置，然后隐藏悬浮窗
        if (floatingWindowHelper?.isFloatingWindowShowing() == true) {
            // 先获取悬浮窗位置
            val position = floatingWindowHelper?.getFloatingWindowPosition()
            floatingWindowX = position?.first
            floatingWindowY = position?.second

            // 同时保存到内存变量和SharedPreferences
            if (floatingWindowX != null && floatingWindowY != null) {
                prefsHelper?.saveFloatingWindowPosition(floatingWindowX!!, floatingWindowY!!)
                Log.d(TAG, "保存悬浮窗位置到SharedPreferences: x=$floatingWindowX, y=$floatingWindowY")
            } else {
                Log.d(TAG, "无法获取悬浮窗位置，使用默认位置")
            }

            // 然后隐藏悬浮窗
            floatingWindowHelper?.hideFloatingWindow()
        }

        // 获取并保存当前活动窗口的根节点，用于后续滚动操作
        val accessibilityHelper = AccessibilityHelper.getInstance(context)

        // 确保无障碍服务已启用
        if (!accessibilityHelper.isAccessibilityServiceEnabled()) {
            Log.e(TAG, "无障碍服务未启用，无法获取根节点")
            Toast.makeText(context, "长截图需要开启无障碍服务，请在设置中开启", Toast.LENGTH_LONG).show()
            accessibilityHelper.openAccessibilitySettings(context)
            return
        }

        // 缓存当前前台Activity名称
        startForegroundActivity = accessibilityHelper.getCurrentForegroundActivity() ?: ""
        Log.d(TAG, "缓存当前前台Activity名称: $startForegroundActivity")

        // 注册前台Activity变化监听器
        accessibilityHelper.setOnForegroundActivityChangeListener(foregroundActivityChangeListener)
        Log.d(TAG, "已注册前台Activity变化监听器")

        // 获取根节点
        savedRootNode = accessibilityHelper.getRootNode()

        if (savedRootNode != null) {
            // 打印根节点详细信息
            val nodeInfo = savedRootNode!!
            Log.d(TAG, "成功获取并保存当前活动窗口的根节点")
            Log.d(TAG, "根节点信息: packageName=${nodeInfo.packageName}, className=${nodeInfo.className}")
            Log.d(TAG, "根节点属性: childCount=${nodeInfo.childCount}, isScrollable=${nodeInfo.isScrollable}")

            // 查找可滚动的子节点
            findScrollableNodes(nodeInfo, 0)
        } else {
            Log.e(TAG, "无法获取当前活动窗口的根节点")
            Toast.makeText(context, "无法获取当前窗口信息，长截图可能无法正常工作", Toast.LENGTH_LONG).show()
        }

        // 无障碍服务检查已在上面完成，这里不需要重复检查

        // 请求截图权限
        requestLongScreenshotPermission()
    }

    /**
     * 请求长截图权限
     */
    private fun requestLongScreenshotPermission() {
        try {
            // 检查是否有已保存的权限结果
            val mediaProjectionSingleton = MediaProjectionSingleton.getInstance()
            if (mediaProjectionSingleton.hasScreenshotPermission()) {
                // 使用已保存的权限结果
                Log.d(TAG, "使用已保存的权限结果进行长截图")

                // 直接启动长截图服务
                LongScreenshotService.startLongScreenshotServiceWithSavedPermission(context)
                return
            }

            // 如果没有有效的权限结果，则请求新的权限
            Toast.makeText(context, "请在通知栏中点击允许截图", Toast.LENGTH_LONG).show()

            // 创建一个新的透明Activity来请求截图权限
            val intent = Intent(context, LongScreenshotPermissionActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)

            Log.d(TAG, "已启动长截图权限请求Activity，等待用户授权")
        } catch (e: Exception) {
            Log.e(TAG, "请求长截图权限失败", e)
            Toast.makeText(context, "请求长截图权限失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 显示长截图进度界面
     * 修改：不再显示进度界面，只记录状态
     */
    fun showLongScreenshotProgressOverlay() {
        Log.d(TAG, "显示长截图进度界面 - 已禁用，不再显示进度界面")

        // 只设置状态，不创建实际界面
        isOverlayShowing = true
    }

    /**
     * 隐藏长截图进度界面
     */
    fun hideLongScreenshotProgressOverlay() {
        Log.d(TAG, "隐藏长截图进度界面")

        // 移除前台Activity变化监听器
        if (startForegroundActivity.isNotEmpty()) {
            Log.d(TAG, "移除前台Activity变化监听器")
            val accessibilityHelper = AccessibilityHelper.getInstance(context)
            accessibilityHelper.setOnForegroundActivityChangeListener(null)

            // 清空缓存的前台Activity名称
            startForegroundActivity = ""
        }

        if (overlayView != null && isOverlayShowing) {
            try {
                // 发送取消广播，确保长截图服务也能收到取消信号
                val intent = Intent(LongScreenshotService.ACTION_CANCEL_LONG_SCREENSHOT)
                context.sendBroadcast(intent)

                // 应用退出动画
                try {
                    val animation = AnimationUtils.loadAnimation(context, R.anim.menu_fade_out)
                    animation.setAnimationListener(object : android.view.animation.Animation.AnimationListener {
                        override fun onAnimationStart(animation: android.view.animation.Animation?) {}

                        override fun onAnimationEnd(animation: android.view.animation.Animation?) {
                            // 动画结束后移除视图
                            try {
                                // 移除进度界面
                                windowManager?.removeView(overlayView)
                                overlayView = null
                                isOverlayShowing = false

                                // 移除取消按钮
                                if (cancelButtonView != null) {
                                    try {
                                        windowManager?.removeView(cancelButtonView)
                                        cancelButtonView = null
                                        isCancelButtonShowing = false
                                    } catch (e: Exception) {
                                        Log.e(TAG, "移除取消按钮失败", e)
                                    }
                                }

                                Log.d(TAG, "长截图进度界面已隐藏")

                                // 显示悬浮窗，恢复到原位置
                                if (floatingWindowHelper?.isFloatingWindowShowing() == false) {
                                    // 尝试从内存变量获取位置
                                    if (floatingWindowX != null && floatingWindowY != null) {
                                        Log.d(TAG, "从内存变量恢复悬浮窗位置: x=$floatingWindowX, y=$floatingWindowY")
                                        floatingWindowHelper?.showFloatingWindowAtPosition(null, floatingWindowX, floatingWindowY)
                                    } else {
                                        // 尝试从SharedPreferences获取位置
                                        val x = prefsHelper?.getFloatingWindowX()
                                        val y = prefsHelper?.getFloatingWindowY()

                                        if (x != null && y != null && (x != 0 || y != 0)) {
                                            Log.d(TAG, "从SharedPreferences恢复悬浮窗位置: x=$x, y=$y")
                                            floatingWindowHelper?.showFloatingWindowAtPosition(null, x, y)
                                        } else {
                                            // 如果没有保存的位置，则正常显示悬浮窗
                                            Log.d(TAG, "没有保存的位置，正常显示悬浮窗")
                                            floatingWindowHelper?.showFloatingWindow(null)
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "移除长截图进度界面失败", e)
                            }
                        }

                        override fun onAnimationRepeat(animation: android.view.animation.Animation?) {}
                    })

                    overlayView?.startAnimation(animation)
                } catch (e: Exception) {
                    // 如果动画失败，直接移除视图
                    Log.e(TAG, "长截图进度界面退出动画执行失败，直接移除", e)
                    // 移除进度界面
                    windowManager?.removeView(overlayView)
                    overlayView = null
                    isOverlayShowing = false

                    // 移除取消按钮
                    if (cancelButtonView != null) {
                        try {
                            windowManager?.removeView(cancelButtonView)
                            cancelButtonView = null
                            isCancelButtonShowing = false
                        } catch (e: Exception) {
                            Log.e(TAG, "移除取消按钮失败", e)
                        }
                    }

                    // 显示悬浮窗
                    if (floatingWindowHelper?.isFloatingWindowShowing() == false) {
                        floatingWindowHelper?.showFloatingWindow(null)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "隐藏长截图进度界面失败", e)

                // 确保即使出现异常，也能尝试移除视图
                try {
                    // 移除进度界面
                    windowManager?.removeView(overlayView)
                    overlayView = null
                    isOverlayShowing = false

                    // 移除取消按钮
                    if (cancelButtonView != null) {
                        try {
                            windowManager?.removeView(cancelButtonView)
                            cancelButtonView = null
                            isCancelButtonShowing = false
                        } catch (e: Exception) {
                            Log.e(TAG, "移除取消按钮失败", e)
                        }
                    }

                    // 显示悬浮窗
                    if (floatingWindowHelper?.isFloatingWindowShowing() == false) {
                        floatingWindowHelper?.showFloatingWindow(null)
                    }
                } catch (e2: Exception) {
                    Log.e(TAG, "紧急移除长截图进度界面失败", e2)
                }
            }
        } else {
            Log.d(TAG, "长截图进度界面为空或未显示，无需隐藏")
        }
    }

    /**
     * 创建长截图进度界面
     * 修改：不再创建实际界面，只记录状态
     */
    private fun createLongScreenshotProgressOverlay() {
        Log.d(TAG, "创建长截图进度界面 - 已禁用，不再创建实际界面")

        // 只设置状态，不创建实际界面
        isOverlayShowing = true

        // 调用取消按钮函数以保持状态一致性
        createAndShowCancelButton()
    }

    /**
     * 更新进度
     */
    fun updateProgress(progress: Int, status: String) {
        Handler(Looper.getMainLooper()).post {
            progressBar?.progress = progress
            statusTextView?.text = status
        }
    }

    /**
     * 创建并显示取消按钮
     * 修改：不再创建和显示取消按钮
     */
    private fun createAndShowCancelButton() {
        Log.d(TAG, "创建取消按钮 - 已禁用，不再显示取消按钮")

        // 只设置状态，不创建实际按钮
        isCancelButtonShowing = true
    }

    /**
     * 检查长截图进度界面是否正在显示
     */
    fun isProgressOverlayVisible(): Boolean {
        return isOverlayShowing
    }

    /**
     * 获取保存的根节点
     */
    fun getSavedRootNode(): AccessibilityNodeInfo? {
        return savedRootNode
    }

    /**
     * 递归查找可滚动的节点
     */
    private fun findScrollableNodes(node: AccessibilityNodeInfo?, depth: Int) {
        if (node == null) return

        // 限制递归深度，避免栈溢出
        if (depth > 5) return

        // 检查当前节点是否可滚动
        if (node.isScrollable) {
            Log.d(TAG, "找到可滚动节点: depth=$depth, packageName=${node.packageName}, className=${node.className}")
            // 保存第一个找到的可滚动节点
            if (savedRootNode == null || !savedRootNode!!.isScrollable) {
                Log.d(TAG, "更新保存的根节点为可滚动节点")
                savedRootNode = node
            }
        }

        // 递归查找子节点
        for (i in 0 until node.childCount) {
            try {
                val childNode = node.getChild(i)
                if (childNode != null) {
                    findScrollableNodes(childNode, depth + 1)
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取子节点失败", e)
            }
        }
    }
}
