package com.xunhe.aishoucang.helpers.hooks

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.helpers.BilibiliAppItemHandler
import com.xunhe.aishoucang.lib.AccessibilityHelper

/**
 * B站应用面板显示前的钩子实现
 */
class BilibiliBeforePanelShow : BeforePanelShowHook {
    companion object {
        private const val TAG = "BilibiliBeforePanelShow"
        private const val PACKAGE_BILIBILI = "tv.danmaku.bili"
    }

    /**
     * 判断当前应用是否是B站
     */
    override fun isApplicable(packageName: String): Boolean {
        return packageName.startsWith(PACKAGE_BILIBILI)
    }

    /**
     * 执行B站特定的预处理逻辑
     * 主要是提前获取作者名称
     */
    override fun execute(context: Context, rootNode: AccessibilityNodeInfo?) {
        if (rootNode == null) return

        Log.d(TAG, "当前应用是B站，尝试提前获取作者名称")

        // 获取无障碍服务实例
        val service = com.xunhe.aishoucang.lib.AccessibilityHelper.AppAccessibilityService.getInstance()
        if (service == null) {
            Log.e(TAG, "无障碍服务未运行")
            return
        }

        // 调用BilibiliAppItemHandler中的方法提前获取作者名称
        val authorName = BilibiliAppItemHandler.extractAuthorNameFromUI(service as AccessibilityService)
        Log.d(TAG, "从UI获取的作者名称: $authorName")

        // 将作者名称保存到BilibiliAppItemHandler中供后续使用
        authorName?.let {
            BilibiliAppItemHandler.setAuthorNameCache(it)
            Log.d(TAG, "已缓存作者名称: $it")
        }
    }
}
