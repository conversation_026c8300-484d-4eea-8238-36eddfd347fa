package com.xunhe.aishoucang.lib

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import com.xunhe.aishoucang.MainActivity
import com.xunhe.aishoucang.R

/**
 * 返回按钮悬浮窗助手
 * 用于在用户跳转到其他应用后显示一个返回按钮，点击后可以快速返回到AI收藏应用
 */
class ReturnButtonHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "ReturnButtonHelper"
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "return_button_channel"

        @Volatile
        private var instance: ReturnButtonHelper? = null

        fun getInstance(context: Context): ReturnButtonHelper {
            return instance ?: synchronized(this) {
                instance ?: ReturnButtonHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    private var isShowing = false

    /**
     * 显示返回按钮悬浮窗
     */
    fun show() {
        try {
            val intent = Intent(context, ReturnButtonService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            isShowing = true
            Log.d(TAG, "显示返回按钮悬浮窗")
        } catch (e: Exception) {
            Log.e(TAG, "显示返回按钮悬浮窗时出错", e)
        }
    }

    /**
     * 隐藏返回按钮悬浮窗
     */
    fun hide() {
        try {
            context.stopService(Intent(context, ReturnButtonService::class.java))
            isShowing = false
            Log.d(TAG, "隐藏返回按钮悬浮窗")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏返回按钮悬浮窗时出错", e)
        }
    }

    /**
     * 检查返回按钮悬浮窗是否正在显示
     */
    fun isShowing(): Boolean = isShowing

    /**
     * 返回按钮悬浮窗服务
     */
    class ReturnButtonService : Service() {
        private var windowManager: WindowManager? = null
        private var floatingView: View? = null
        private var params: WindowManager.LayoutParams? = null
        private var initialX = 0
        private var initialY = 0
        private var initialTouchX = 0f
        private var initialTouchY = 0f

        override fun onBind(intent: Intent?): IBinder? = null

        override fun onCreate() {
            super.onCreate()
            windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) createNotificationChannel()
            startForeground(NOTIFICATION_ID, createNotification("返回按钮服务正在运行"))
        }

        override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
            // 确保每次启动服务时都调用startForeground
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForeground(NOTIFICATION_ID, createNotification("返回按钮服务正在运行"))
            }

            if (floatingView == null) createFloatingButton()
            return START_STICKY
        }

        /**
         * 创建通知渠道（Android 8.0+）
         */
        private fun createNotificationChannel() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = NotificationChannel(
                    CHANNEL_ID,
                    "返回按钮服务",
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "显示返回AI收藏应用的按钮"
                    setShowBadge(false)
                }

                val notificationManager = getSystemService(NotificationManager::class.java)
                notificationManager.createNotificationChannel(channel)
            }
        }

        /**
         * 创建前台服务通知
         */
        private fun createNotification(contentText: String): Notification {
            val builder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Notification.Builder(this, CHANNEL_ID)
            } else {
                @Suppress("DEPRECATION")
                Notification.Builder(this)
            }

            return builder
                .setContentTitle("爱收藏")
                .setContentText(contentText)
                .setSmallIcon(android.R.drawable.ic_menu_revert)
                .build()
        }

        /**
         * 创建悬浮按钮
         */
        private fun createFloatingButton() {
            try {
                val inflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
                floatingView = inflater.inflate(R.layout.return_button_layout, null)

                // 设置布局参数
                params = WindowManager.LayoutParams().apply {
                    width = WindowManager.LayoutParams.WRAP_CONTENT
                    height = WindowManager.LayoutParams.WRAP_CONTENT
                    type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                    } else {
                        WindowManager.LayoutParams.TYPE_PHONE
                    }
                    flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                    format = PixelFormat.TRANSLUCENT
                    gravity = Gravity.TOP or Gravity.START
                }

                // 获取屏幕尺寸
                val displayMetrics = resources.displayMetrics

                // 先测量按钮宽度
                val returnButton = floatingView?.findViewById<LinearLayout>(R.id.return_button)
                returnButton?.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
                val buttonWidth = returnButton?.measuredWidth ?: 160

                // 设置初始位置（底部中间）
                params?.x = (displayMetrics.widthPixels / 2) - (buttonWidth / 2)
                params?.y = (displayMetrics.heightPixels * 0.9).toInt()

                // 在正确位置添加视图
                windowManager?.addView(floatingView, params)

                // 设置拖动监听
                setupDragListener()
            } catch (e: Exception) {
                Log.e(TAG, "创建返回按钮悬浮窗失败", e)
            }
        }

        /**
         * 设置拖动监听器
         */
        private fun setupDragListener() {
            // 获取按钮
            val returnButton = floatingView?.findViewById<LinearLayout>(R.id.return_button)

            // 用于记录触摸状态
            var isDragging = false
            var startX = 0f
            var startY = 0f
            var lastTouchTime = 0L
            val CLICK_TIMEOUT = 200L // 点击判定的时间阈值（毫秒）
            val MOVE_THRESHOLD = 10 // 移动判定的距离阈值（像素）

            returnButton?.setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        // 记录初始位置
                        initialX = params?.x ?: 0
                        initialY = params?.y ?: 0
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        startX = event.rawX
                        startY = event.rawY
                        lastTouchTime = System.currentTimeMillis()
                        isDragging = false
                        true
                    }

                    MotionEvent.ACTION_MOVE -> {
                        val deltaX = (event.rawX - initialTouchX).toInt()
                        val deltaY = (event.rawY - initialTouchY).toInt()

                        // 计算移动距离
                        val distance = Math.sqrt(
                            Math.pow((event.rawX - startX).toDouble(), 2.0) +
                                    Math.pow((event.rawY - startY).toDouble(), 2.0)
                        ).toFloat()

                        // 如果移动距离超过阈值，则认为是拖动
                        if (distance > MOVE_THRESHOLD) {
                            isDragging = true
                            params?.x = initialX + deltaX
                            params?.y = initialY + deltaY
                            windowManager?.updateViewLayout(floatingView, params)
                        }
                        true
                    }

                    MotionEvent.ACTION_UP -> {
                        // 如果没有拖动且触摸时间很短，则认为是点击
                        val touchDuration = System.currentTimeMillis() - lastTouchTime
                        if (!isDragging && touchDuration < CLICK_TIMEOUT) {
                            // 执行返回操作
                            returnToApp()
                        }
                        false
                    }

                    else -> false
                }
            }
        }

        /**
         * 返回到AI收藏应用
         */
        private fun returnToApp() {
            try {
                // 创建返回到主应用的Intent
                val intent = Intent(applicationContext, MainActivity::class.java).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                }
                startActivity(intent)

                // 返回后隐藏悬浮窗
                hide()

                Log.d(TAG, "已返回到AI收藏应用")
            } catch (e: Exception) {
                Log.e(TAG, "返回到AI收藏应用时出错", e)
            }
        }

        /**
         * 隐藏悬浮窗
         */
        private fun hide() {
            try {
                stopSelf()
            } catch (e: Exception) {
                Log.e(TAG, "隐藏悬浮窗时出错", e)
            }
        }

        override fun onDestroy() {
            super.onDestroy()
            Log.d(TAG, "返回按钮服务即将销毁")

            // Android 14+ 需要立即停止前台服务状态，避免超时异常
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14+
                try {
                    stopForeground(STOP_FOREGROUND_REMOVE) // 立即移除通知并停止前台服务状态
                    Log.d(TAG, "Android 14+ 已立即停止前台服务状态")
                } catch (e: Exception) {
                    Log.e(TAG, "Android 14+ 停止前台服务状态失败", e)
                }
            } else {
                // Android 13 及以下的标准处理
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        stopForeground(true) // 移除通知并停止前台服务状态
                        Log.d(TAG, "已停止前台服务状态")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "停止前台服务状态失败", e)
                }
            }

            // 清理悬浮窗视图
            try {
                if (floatingView != null) {
                    windowManager?.removeView(floatingView)
                    floatingView = null
                    Log.d(TAG, "返回按钮悬浮窗视图已移除")
                }
            } catch (e: Exception) {
                Log.e(TAG, "移除返回按钮悬浮窗视图失败", e)
            }

            Log.d(TAG, "返回按钮服务已销毁")
        }
    }
}
