package com.xunhe.aishoucang.helpers.hooks

import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.lib.AccessibilityHelper
import com.xunhe.aishoucang.helpers.SharePanelHelper

/**
 * 微信应用面板显示前的钩子实现
 */
class WechatBeforePanelShow : BeforePanelShowHook {
    companion object {
        private const val TAG = "WechatBeforePanelShow"
        private const val PACKAGE_WECHAT = "com.tencent.mm"
    }

    /**
     * 判断当前应用是否是微信
     */
    override fun isApplicable(packageName: String): Boolean {
        return packageName.startsWith(PACKAGE_WECHAT)
    }

    /**
     * 执行微信特定的预处理逻辑
     */
    override fun execute(context: Context, rootNode: AccessibilityNodeInfo?) {
        if (rootNode == null) return

        Log.d(TAG, "当前应用是微信，执行微信特定的预处理逻辑")

        // 设置自动处理为false，禁用自动显示分享面板
        // SharePanelHelper.setAuto(false)
        Log.d(TAG, "已禁用微信的自动处理功能")

        // 打印当前界面信息
        val packageName = rootNode.packageName?.toString() ?: "未知"
        val className = rootNode.className?.toString() ?: "未知"
        Log.d(TAG, "当前微信界面: $packageName/$className")
    }
}
