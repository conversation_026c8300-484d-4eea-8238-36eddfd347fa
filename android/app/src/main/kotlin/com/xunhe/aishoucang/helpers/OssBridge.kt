package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * OSS桥接类
 * 用于处理从Flutter调用的OSS相关请求
 */
object OssBridge {
    private const val TAG = "OssBridge"
    
    /**
     * 处理方法调用
     * 
     * @param context 上下文
     * @param call Flutter方法调用
     * @param result 结果回调
     */
    fun handleMethodCall(context: Context, call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "uploadFileToOss" -> uploadFileToOss(context, call, result)
            else -> result.notImplemented()
        }
    }
    
    /**
     * 处理文件上传到OSS请求
     */
    private fun uploadFileToOss(context: Context, call: MethodCall, result: MethodChannel.Result) {
        val filePath = call.argument<String>("filePath")
        val objectKey = call.argument<String>("objectKey")
        
        if (filePath.isNullOrEmpty()) {
            result.error("INVALID_FILE_PATH", "文件路径不能为空", null)
            return
        }
        
        GlobalScope.launch {
            try {
                // 获取OSS管理器实例
                val ossManager = OssManager.getInstance(context)
                
                // 上传文件
                val ossUrl = ossManager.uploadFile(filePath, objectKey)
                
                // 在主线程中返回结果
                MainScope().launch {
                    result.success(mapOf(
                        "success" to true,
                        "ossUrl" to ossUrl
                    ))
                }
            } catch (e: Exception) {
                Log.e(TAG, "上传文件到OSS失败", e)
                
                // 在主线程中返回错误结果
                MainScope().launch {
                    result.success(mapOf(
                        "success" to false,
                        "error" to e.message
                    ))
                }
            }
        }
    }
}
