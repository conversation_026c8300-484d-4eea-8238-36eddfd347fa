package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.lib.AccessibilityHelper


/**
 * 拼多多应用处理器
 */
class PinDuoDuoAppHandler : AppHandler {
    companion object {
        private const val TAG = "PinDuoDuoAppHandler"
    }

    override fun handle(context: Context) {
        Log.d(TAG, "正在处理拼多多应用")

        // 获取无障碍服务实例
        val helper = AccessibilityHelper.getInstance(context)
        val service = AccessibilityHelper.AppAccessibilityService.getInstance()

        if (service == null) {
            Log.e(TAG, "无障碍服务未运行，无法继续操作")
            CustomToastHelper.showToast(context, "无障碍服务未运行，请检查权限设置")
            return
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.e(TAG, "无法获取当前活动窗口的根节点")
            CustomToastHelper.showToast(context, "无法获取窗口信息，请重试")
            return
        }

        try {
            // 首先获取商品标题信息
            val productTitle = getProductTitle(service)
            if (productTitle != null) {
                // 将标题信息存储到临时数据中，供ItemHandler使用
                SharePanelHelper.setTempData("pinduoduo_product_title", productTitle)
                Log.d(TAG, "获取到商品标题: $productTitle")
            } else {
                Log.w(TAG, "未能获取到商品标题")
            }
            // 找到desc属性为分享的节点并点击
            val shareNodes = helper.findNodesByDescription("分享")
            if (shareNodes.isNullOrEmpty()) {
                Log.e(TAG, "未找到分享按钮，无法继续操作")
                rootNode.recycle()
                CustomToastHelper.showToast(context, "拼多多暂时只支持商品详情页，其他场景暂不支持哦~")
                return
            }

            // 点击分享按钮
            val shareNode = shareNodes[0]
            val clickResult = shareNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            Log.d(TAG, "点击分享按钮结果: $clickResult")
            shareNode.recycle()

            if (!clickResult) {
                Log.e(TAG, "点击分享按钮失败")
                rootNode.recycle()
                CustomToastHelper.showToast(context, "点击分享按钮失败，请重试")
                return
            }

            // 主线程等待2秒后查找复制链接按钮
            try {
                Thread.sleep(1000)

                // 查找复制链接按钮
                val copyNodes = service.findNodesByContent("复制链接")
                if (copyNodes != null && copyNodes.isNotEmpty()) {
                    val copyNode = copyNodes[0]
                    Log.d(TAG, "找到复制链接按钮")

                    // 获取父节点并点击
                    val parentNode = copyNode.parent
                    val copyClickResult = if (parentNode != null) {
                        Log.d(TAG, "点击复制链接按钮的父节点")
                        val result = parentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        parentNode.recycle()
                        result
                    } else {
                        Log.d(TAG, "父节点为空，直接点击复制链接按钮")
                        copyNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    }

                    Log.d(TAG, "点击复制链接按钮结果: $copyClickResult")

                    if (copyClickResult) {
                        // 设置当前内容类型为拼多多商品
                        SharePanelHelper.setCurrentContentType(ContentTypeConstants.PINDUODUO_TYPE_PRODUCT)
                        // 通知内容已准备好
                        SharePanelHelper.notifyContentReady()
                        Log.d(TAG, "已复制链接，设置业务类型为拼多多商品，通知内容准备完毕")
                    }

                    copyNode.recycle()
                } else {
                    Log.e(TAG, "未找到复制链接按钮")
                    CustomToastHelper.showToast(context, "未找到复制链接选项，请手动操作")
                }
            } catch (e: InterruptedException) {
                Log.e(TAG, "等待过程中被中断: ${e.message}", e)
                CustomToastHelper.showToast(context, "操作被中断，请重试")
            } catch (e: Exception) {
                Log.e(TAG, "查找复制链接按钮时出错: ${e.message}", e)
                CustomToastHelper.showToast(context, "查找复制链接时出错，请重试")
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理拼多多应用时出错: ${e.message}", e)
            CustomToastHelper.showToast(context, "处理拼多多应用时出错，请重试")
        } finally {
            // 确保根节点资源被回收
            try {
                rootNode.recycle()
            } catch (e: Exception) {
                Log.e(TAG, "回收根节点资源时出错: ${e.message}")
            }
        }
    }

    /**
     * 获取拼多多商品标题
     *
     * @param service 无障碍服务实例
     * @return 商品标题，如果获取失败则返回null
     */
    private fun getProductTitle(service: AccessibilityHelper.AppAccessibilityService): String? {
        try {
            val rootNode = service.rootInActiveWindow ?: return null

            // 查找id为com.xunmeng.pinduoduo:id/tv_title的节点
            val titleNodes = service.findNodesByViewId(rootNode, "com.xunmeng.pinduoduo:id/tv_title")

            if (titleNodes.isNotEmpty()) {
                val titleNode = titleNodes[0]

                // 优先获取contentDescription（desc属性）
                val title = titleNode.contentDescription?.toString()
                    ?: titleNode.text?.toString() // 如果desc为空，则获取text属性

                Log.d(TAG, "找到标题节点，contentDescription: ${titleNode.contentDescription}, text: ${titleNode.text}")

                // 回收节点资源
                titleNodes.forEach { it.recycle() }

                return title
            } else {
                Log.w(TAG, "未找到id为com.xunmeng.pinduoduo:id/tv_title的节点")
                return null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取商品标题时出错: ${e.message}", e)
            return null
        }
    }
}
