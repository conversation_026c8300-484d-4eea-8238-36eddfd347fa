package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.iflytek.sparkchain.core.asr.ASR
import com.iflytek.sparkchain.core.asr.AsrCallbacks
import com.iflytek.sparkchain.core.asr.AudioAttributes
import com.iflytek.sparkchain.core.SparkChain
import com.iflytek.sparkchain.core.SparkChainConfig
import io.flutter.plugin.common.MethodChannel
import java.util.concurrent.ConcurrentHashMap

/**
 * SparkChain 大模型识别助手类
 * 封装SparkChain SDK的初始化和使用逻辑
 */
class SparkChainHelper private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "SparkChainHelper"
        
        @Volatile
        private var instance: SparkChainHelper? = null
        
        fun getInstance(context: Context): SparkChainHelper {
            return instance ?: synchronized(this) {
                instance ?: SparkChainHelper(context.applicationContext).also { instance = it }
            }
        }
    }
    
    // SDK初始化状态
    private var isInitialized = false
    
    // 当前ASR实例
    private var currentAsr: ASR? = null
    
    // 识别会话状态
    private var isRecognizing = false
    
    // 结果回调映射
    private val resultCallbacks = ConcurrentHashMap<String, MethodChannel.Result>()
    
    /**
     * 初始化SparkChain SDK
     */
    fun initializeSDK(
        appId: String,
        apiKey: String,
        apiSecret: String,
        logLevel: Int = 2, // INFO级别
        logPath: String? = null,
        uid: String? = null
    ): Boolean {
        return try {
            if (isInitialized) {
                Log.w(TAG, "SparkChain SDK已经初始化")
                return true
            }
            
            // 构建配置
            val configBuilder = SparkChainConfig.builder()
                .appID(appId)
                .apiKey(apiKey)
                .apiSecret(apiSecret)
                .logLevel(logLevel)
            
            // 设置可选参数
            logPath?.let { configBuilder.logPath(it) }
            uid?.let { configBuilder.uid(it) }
            
            val config = configBuilder
            
            // 初始化SDK
            val ret = SparkChain.getInst().init(context, config)
            
            if (ret == 0) {
                isInitialized = true
                Log.i(TAG, "SparkChain SDK初始化成功")
                true
            } else {
                Log.e(TAG, "SparkChain SDK初始化失败，错误码: $ret")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "SparkChain SDK初始化异常", e)
            false
        }
    }
    
    /**
     * 创建ASR实例
     */
    fun createASR(
        language: String = "zh_cn",
        domain: String = "slm", 
        accent: String = "mandarin"
    ): ASR? {
        return try {
            if (!isInitialized) {
                Log.e(TAG, "SDK未初始化，无法创建ASR实例")
                return null
            }
            
            val asr = ASR(language, domain, accent)
            Log.i(TAG, "ASR实例创建成功")
            asr
        } catch (e: Exception) {
            Log.e(TAG, "创建ASR实例失败", e)
            null
        }
    }
    
    /**
     * 配置ASR参数
     */
    fun configureASR(
        asr: ASR,
        vgap: Int? = null,
        vadEos: Int? = null,
        vinfo: Boolean? = null,
        dwa: String? = null,
        ptt: Boolean? = null,
        smth: Boolean? = null,
        nunum: Boolean? = null,
        rlang: String? = null,
        ln: String? = null
    ) {
        try {
            vgap?.let {
                asr.vgap(it)
                Log.d(TAG, "设置vgap参数: $it")
            }
            vadEos?.let {
                asr.vadEos(it)
                Log.d(TAG, "设置vadEos参数: $it")
            }
            vinfo?.let {
                asr.vinfo(it)
                Log.d(TAG, "设置vinfo参数: $it")
            }
            dwa?.let {
                asr.dwa(it)
                Log.d(TAG, "设置dwa参数: $it")
            }
            ptt?.let {
                asr.ptt(it)
                Log.d(TAG, "设置ptt参数: $it")
            }
            smth?.let {
                asr.smth(it)
                Log.d(TAG, "设置smth参数: $it")
            }
            nunum?.let {
                asr.nunum(it)
                Log.d(TAG, "设置nunum参数: $it")
            }
            rlang?.let {
                asr.rlang(it)
                Log.d(TAG, "设置rlang参数: $it")
            }
            ln?.let {
                asr.ln(it)
                Log.d(TAG, "设置ln参数: $it")
            }

            Log.i(TAG, "ASR参数配置完成")
        } catch (e: Exception) {
            Log.e(TAG, "配置ASR参数失败", e)
        }
    }
    
    /**
     * 注册识别结果回调
     */
    fun registerCallback(asr: ASR, sessionId: String, result: MethodChannel.Result) {
        try {
            resultCallbacks[sessionId] = result
            
            val callbacks = object : AsrCallbacks {
                override fun onResult(asrResult: ASR.ASRResult, usrContext: Any?) {
                    try {
                        val resultText = asrResult.bestMatchText
                        val status = asrResult.status
                        val sid = asrResult.sid

                        val resultMap = mapOf(
                            "success" to true,
                            "text" to resultText,
                            "status" to status,
                            "sid" to sid,
                            "sessionId" to sessionId
                        )

                        // 如果是最终结果，移除回调
                        if (status == 2) {
                            resultCallbacks.remove(sessionId)
                            isRecognizing = false
                        }

                        result.success(resultMap)
                        Log.d(TAG, "识别结果: $resultText, 状态: $status")
                    } catch (e: Exception) {
                        Log.e(TAG, "处理识别结果失败", e)
                    }
                }

                override fun onError(asrError: ASR.ASRError, usrContext: Any?) {
                    try {
                        val errorCode = asrError.code
                        val errorMsg = asrError.errMsg
                        val sid = asrError.sid

                        val errorMap = mapOf(
                            "success" to false,
                            "errorCode" to errorCode,
                            "errorMessage" to errorMsg,
                            "sid" to sid,
                            "sessionId" to sessionId
                        )

                        resultCallbacks.remove(sessionId)
                        isRecognizing = false

                        result.success(errorMap)
                        Log.e(TAG, "识别错误: $errorMsg, 错误码: $errorCode")
                    } catch (e: Exception) {
                        Log.e(TAG, "处理识别错误失败", e)
                    }
                }

                override fun onBeginOfSpeech() {
                    Log.d(TAG, "开始检测到语音")
                }

                override fun onEndOfSpeech() {
                    Log.d(TAG, "语音结束")
                }
            }
            
            asr.registerCallbacks(callbacks)
            Log.i(TAG, "识别回调注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册识别回调失败", e)
        }
    }
    
    /**
     * 启动识别会话
     */
    fun startRecognition(
        asr: ASR,
        sampleRate: Int = 16000,
        encoding: String = "raw",
        channels: Int = 1,
        bitdepth: Int = 16,
        frameSize: Int = 0,
        usrTag: Any? = null
    ): Boolean {
        return try {
            if (isRecognizing) {
                Log.w(TAG, "已有识别会话在进行中")
                return false
            }
            
            val attributes = AudioAttributes().apply {
                setSampleRate(sampleRate)
                setEncoding(encoding)
                setChannels(channels)
                setBitdepth(bitdepth)
                setFrameSize(frameSize)
            }
            
            val ret = asr.start(attributes, usrTag)
            
            if (ret == 0) {
                isRecognizing = true
                currentAsr = asr
                Log.i(TAG, "识别会话启动成功")
                true
            } else {
                Log.e(TAG, "识别会话启动失败，错误码: $ret")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动识别会话异常", e)
            false
        }
    }
    
    /**
     * 发送音频数据
     */
    fun writeAudioData(data: ByteArray): Boolean {
        return try {
            currentAsr?.let { asr ->
                val ret = asr.write(data)
                if (ret == 0) {
                    Log.d(TAG, "音频数据发送成功，大小: ${data.size}")
                    true
                } else {
                    Log.e(TAG, "音频数据发送失败，错误码: $ret")
                    false
                }
            } ?: run {
                Log.e(TAG, "没有活跃的ASR实例")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "发送音频数据异常", e)
            false
        }
    }
    
    /**
     * 停止识别会话
     */
    fun stopRecognition(immediate: Boolean = false): Boolean {
        return try {
            currentAsr?.let { asr ->
                val ret = asr.stop(immediate)
                if (ret == 0) {
                    if (immediate) {
                        isRecognizing = false
                        currentAsr = null
                    }
                    Log.i(TAG, "识别会话停止成功")
                    true
                } else {
                    Log.e(TAG, "识别会话停止失败，错误码: $ret")
                    false
                }
            } ?: run {
                Log.e(TAG, "没有活跃的ASR实例")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止识别会话异常", e)
            false
        }
    }

    /**
     * 识别音频文件
     *
     * @param audioFilePath 音频文件路径
     * @param language 识别语种 (zh_cn: 中文, en_us: 英文)
     * @param domain 应用领域 (slm: 大模型识别, iat: 日常用语)
     * @param accent 方言 (mandarin: 普通话)
     * @param callback 识别结果回调
     */
    fun recognizeAudioFile(
        audioFilePath: String,
        language: String = "zh_cn",
        domain: String = "slm",
        accent: String = "mandarin",
        callback: (success: Boolean, result: String?, error: String?) -> Unit
    ) {
        recognizeAudioFileWithProgress(audioFilePath, language, domain, accent, null, callback)
    }

    /**
     * 识别音频文件（支持进度回调）
     *
     * @param audioFilePath 音频文件路径
     * @param language 识别语种 (zh_cn: 中文, en_us: 英文)
     * @param domain 应用领域 (slm: 大模型识别, iat: 日常用语)
     * @param accent 方言 (mandarin: 普通话)
     * @param progressCallback 进度回调 (progress: 0.0-1.0)
     * @param callback 识别结果回调
     */
    fun recognizeAudioFileWithProgress(
        audioFilePath: String,
        language: String = "zh_cn",
        domain: String = "slm",
        accent: String = "mandarin",
        progressCallback: ((Double) -> Unit)? = null,
        callback: (success: Boolean, result: String?, error: String?) -> Unit
    ) {
        Thread {
            try {
                if (!isInitialized) {
                    callback(false, null, "SDK未初始化")
                    return@Thread
                }

                if (isRecognizing) {
                    callback(false, null, "已有识别会话在进行中")
                    return@Thread
                }

                // 检查文件是否存在
                val audioFile = java.io.File(audioFilePath)
                if (!audioFile.exists()) {
                    callback(false, null, "音频文件不存在: $audioFilePath")
                    return@Thread
                }

                Log.i(TAG, "开始识别音频文件: $audioFilePath")

                // 创建ASR实例
                val asr = createASR(language, domain, accent)
                if (asr == null) {
                    callback(false, null, "创建ASR实例失败")
                    return@Thread
                }

                // 先配置ASR参数（在注册回调之前）
                Log.i(TAG, "配置ASR参数用于文件识别")
                configureASR(
                    asr = asr,
                    vinfo = true,
                    dwa = if (language == "zh_cn") "wpgs" else null,
                    ptt = true,
                    nunum = true,
                    vadEos = 60000,  // 设置为60秒，与示例文件一致
                    vgap = 2000      // 增加语音间隔检测时间到2秒
                )

                var recognitionResult = StringBuilder()
                var hasError = false
                var audioSendProgress = 0.0 // 音频发送进度
                var recognitionStarted = false // 识别是否已开始

                // 注册回调
                val asrCallback = object : AsrCallbacks {
                    override fun onResult(asrResult: ASR.ASRResult?, usrTag: Any?) {
                        asrResult?.let { result ->
                            val status = result.status
                            val text = result.bestMatchText
                            val sid = result.sid

                            Log.d(TAG, "识别结果: status=$status, text=$text, sid=$sid")

                            when (status) {
                                0 -> { // 开始
                                    recognitionStarted = true
                                    // 开始时清空之前的结果
                                    recognitionResult.clear()
                                    if (text.isNotEmpty()) {
                                        recognitionResult.append(text)
                                    }

                                    // 识别开始，进度设为音频发送进度的基础上加10%
                                    progressCallback?.let { callback ->
                                        val currentProgress = (audioSendProgress * 0.8 + 0.1).coerceAtMost(0.9)
                                        Log.d(TAG, "识别开始，更新进度: ${(currentProgress * 100).toInt()}%")
                                        callback(currentProgress)
                                    }
                                }
                                1 -> { // 中间结果
                                    // 中间结果替换之前的内容，不拼接
                                    recognitionResult.clear()
                                    if (text.isNotEmpty()) {
                                        recognitionResult.append(text)
                                    }

                                    // 中间结果，进度设为音频发送进度的基础上加20%
                                    progressCallback?.let { callback ->
                                        val currentProgress = (audioSendProgress * 0.8 + 0.2).coerceAtMost(0.95)
                                        Log.d(TAG, "识别中间结果，更新进度: ${(currentProgress * 100).toInt()}%")
                                        callback(currentProgress)
                                    }
                                }
                                2 -> { // 最终结果
                                    // 最终结果，直接使用当前文本，不拼接
                                    val finalResult = text?.trim() ?: ""

                                    // 识别完成，进度设为100%
                                    progressCallback?.let { callback ->
                                        Log.d(TAG, "识别完成，进度设为100%")
                                        callback(1.0)
                                    }

                                    // 识别完成
                                    isRecognizing = false
                                    currentAsr = null

                                    Log.i(TAG, "音频文件识别完成: $finalResult")
                                    callback(true, finalResult, null)
                                }
                                else -> {
                                    Log.d(TAG, "未知状态: $status")
                                }
                            }
                        }
                    }

                    override fun onError(asrError: ASR.ASRError?, usrTag: Any?) {
                        asrError?.let { error ->
                            val code = error.code
                            val msg = error.errMsg
                            val sid = error.sid

                            hasError = true
                            val errorMsg = "识别错误: code=$code, msg=$msg, sid=$sid"
                            Log.e(TAG, errorMsg)

                            isRecognizing = false
                            currentAsr = null
                            callback(false, null, errorMsg)
                        }
                    }

                    override fun onBeginOfSpeech() {
                        Log.d(TAG, "开始检测到语音")
                    }

                    override fun onEndOfSpeech() {
                        Log.d(TAG, "语音检测结束")
                    }
                }

                asr.registerCallbacks(asrCallback)

                // 启动识别
                val sessionId = "audio_file_${System.currentTimeMillis()}"
                val success = startRecognition(
                    asr = asr,
                    sampleRate = 16000,
                    encoding = "raw",
                    channels = 1,
                    bitdepth = 16,
                    frameSize = 0,
                    usrTag = sessionId
                )

                if (!success) {
                    callback(false, null, "启动识别失败")
                    return@Thread
                }

                // 读取音频文件并发送数据
                try {
                    val fileInputStream = java.io.FileInputStream(audioFile)
                    val buffer = ByteArray(1280) // 每次读取1280字节
                    var bytesRead: Int
                    var totalBytesRead = 0L
                    var frameCount = 0

                    Log.i(TAG, "开始读取音频文件，文件大小: ${audioFile.length()} 字节")

                    while (fileInputStream.read(buffer).also { bytesRead = it } != -1 && isRecognizing && !hasError) {
                        if (bytesRead > 0) {
                            val audioData = if (bytesRead == buffer.size) {
                                buffer
                            } else {
                                buffer.copyOf(bytesRead)
                            }

                            val writeSuccess = writeAudioData(audioData)
                            if (writeSuccess) {
                                totalBytesRead += bytesRead
                                frameCount++

                                // 每100帧打印一次进度
                                if (frameCount % 100 == 0) {
                                    val progressPercent = (totalBytesRead * 100.0 / audioFile.length()).toInt()
                                    Log.d(TAG, "音频发送进度: $progressPercent% (${frameCount}帧, ${totalBytesRead}/${audioFile.length()}字节)")

                                    // 更新音频发送进度
                                    audioSendProgress = totalBytesRead.toDouble() / audioFile.length()

                                    // 如果识别还没开始，只基于音频发送进度回调
                                    if (!recognitionStarted) {
                                        progressCallback?.let { callback ->
                                            // 音频发送阶段占总进度的80%
                                            val currentProgress = audioSendProgress * 0.8
                                            Log.d(TAG, "音频发送阶段，更新进度: ${(currentProgress * 100).toInt()}%")
                                            callback(currentProgress)
                                        }
                                    }
                                }
                            } else {
                                Log.w(TAG, "发送音频数据失败，帧数: $frameCount")
                            }

                            // 模拟实时音频流，每40ms发送一次
                            Thread.sleep(40)
                        }
                    }

                    fileInputStream.close()
                    Log.i(TAG, "音频文件读取完成，总共发送: $frameCount 帧, $totalBytesRead 字节")

                    // 音频发送完毕，更新发送进度为100%
                    audioSendProgress = 1.0

                    // 如果识别还没开始，设置进度为80%（等待识别开始）
                    if (!recognitionStarted) {
                        progressCallback?.let { callback ->
                            Log.d(TAG, "音频发送完毕，等待识别开始，进度设为80%")
                            callback(0.8)
                        }
                    }

                    // 发送完毕后等待识别完成，设置超时机制
                    if (isRecognizing && !hasError) {
                        Log.d(TAG, "等待识别完成...")

                        // 等待识别完成，最多等待10秒
                        var waitTime = 0L
                        val maxWaitTime = 10000L // 10秒
                        val checkInterval = 500L // 每500ms检查一次

                        while (isRecognizing && !hasError && waitTime < maxWaitTime) {
                            Thread.sleep(checkInterval)
                            waitTime += checkInterval

                            // 如果等待时间超过3秒且还没有识别结果，更新进度
                            if (waitTime > 3000L && !recognitionStarted) {
                                progressCallback?.let { callback ->
                                    val waitProgress = 0.8 + (waitTime - 3000L) * 0.1 / (maxWaitTime - 3000L)
                                    Log.d(TAG, "等待识别中，进度: ${(waitProgress * 100).toInt()}%")
                                    callback(waitProgress.coerceAtMost(0.9))
                                }
                            }
                        }

                        // 如果超时或仍在识别，主动停止
                        if (isRecognizing) {
                            if (waitTime >= maxWaitTime) {
                                Log.w(TAG, "识别超时，主动停止")
                            } else {
                                Log.d(TAG, "主动停止识别")
                            }
                            stopRecognition(false) // 等待最终结果
                        }
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "读取音频文件异常", e)
                    isRecognizing = false
                    currentAsr = null
                    callback(false, null, "读取音频文件失败: ${e.message}")
                }

            } catch (e: Exception) {
                Log.e(TAG, "识别音频文件异常", e)
                isRecognizing = false
                currentAsr = null
                callback(false, null, "识别音频文件异常: ${e.message}")
            }
        }.start()
    }
    
    /**
     * 获取SDK初始化状态
     */
    fun isSDKInitialized(): Boolean = isInitialized
    
    /**
     * 获取识别状态
     */
    fun isRecognitionActive(): Boolean = isRecognizing
    
    /**
     * 逆初始化SDK
     */
    fun uninitializeSDK() {
        try {
            if (isRecognizing) {
                stopRecognition(true)
            }
            
            if (isInitialized) {
                SparkChain.getInst().unInit()
                isInitialized = false
                currentAsr = null
                resultCallbacks.clear()
                Log.i(TAG, "SparkChain SDK逆初始化完成")
            }
        } catch (e: Exception) {
            Log.e(TAG, "SparkChain SDK逆初始化异常", e)
        }
    }
}
