package com.xunhe.aishoucang.lib

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.helpers.CustomToastHelper

/**
 * 创建笔记预览器
 * 用于显示剪切板内容的悬浮窗面板
 */
class CreateNotePreviewer private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "CreateNotePreviewer"
        
        @Volatile
        private var instance: CreateNotePreviewer? = null
        
        fun getInstance(context: Context): CreateNotePreviewer {
            return instance ?: synchronized(this) {
                instance ?: CreateNotePreviewer(context.applicationContext).also { instance = it }
            }
        }
    }
    
    private var windowManager: WindowManager? = null
    private var previewView: View? = null
    private var layoutParams: WindowManager.LayoutParams? = null
    private var isShowing = false
    
    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }
    
    /**
     * 显示创建笔记预览窗口
     * 
     * @param clipboardContent 剪切板内容
     * @param onConfirm 点击确定按钮的回调
     * @param onCancel 点击取消按钮的回调
     */
    fun showPreview(
        clipboardContent: String?,
        onConfirm: (() -> Unit)? = null,
        onCancel: (() -> Unit)? = null
    ) {
        if (isShowing) {
            Log.d(TAG, "预览窗口已显示，先隐藏")
            hidePreview()
        }
        
        try {
            Log.d(TAG, "开始显示创建笔记预览窗口")
            
            // 创建布局
            val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            previewView = inflater.inflate(R.layout.create_note_preview_layout, null)
            
            // 设置剪切板内容
            val contentTextView = previewView?.findViewById<TextView>(R.id.note_content_text)
            if (clipboardContent.isNullOrEmpty()) {
                contentTextView?.text = "暂无内容"
            } else {
                contentTextView?.text = clipboardContent
            }
            
            // 设置按钮点击事件
            setupButtonListeners(onConfirm, onCancel)
            
            // 创建窗口参数
            createLayoutParams()
            
            // 添加到窗口管理器
            windowManager?.addView(previewView, layoutParams)
            isShowing = true
            
            Log.d(TAG, "创建笔记预览窗口显示成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "显示创建笔记预览窗口失败", e)
            isShowing = false
        }
    }
    
    /**
     * 隐藏预览窗口
     */
    fun hidePreview() {
        try {
            if (previewView != null && isShowing) {
                Log.d(TAG, "隐藏创建笔记预览窗口")
                windowManager?.removeView(previewView)
                previewView = null
                isShowing = false
                Log.d(TAG, "创建笔记预览窗口隐藏成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "隐藏创建笔记预览窗口失败", e)
        }
    }
    
    /**
     * 检查预览窗口是否正在显示
     */
    fun isShowing(): Boolean {
        return isShowing
    }
    
    /**
     * 设置按钮监听器
     */
    private fun setupButtonListeners(
        onConfirm: (() -> Unit)?,
        onCancel: (() -> Unit)?
    ) {
        // 取消按钮
        previewView?.findViewById<Button>(R.id.cancel_button)?.setOnClickListener {
            Log.d(TAG, "点击取消按钮")
            hidePreview()
            onCancel?.invoke()
        }
        
        // 确定按钮
        previewView?.findViewById<Button>(R.id.confirm_button)?.setOnClickListener {
            Log.d(TAG, "点击确定按钮")
            hidePreview()
            onConfirm?.invoke()
            // 显示简单的toast
            CustomToastHelper.showShortToast(context, "已确认创建笔记")
        }
    }
    
    /**
     * 创建窗口布局参数
     */
    private fun createLayoutParams() {
        layoutParams = WindowManager.LayoutParams().apply {
            // 设置窗口类型
            type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            }
            
            // 设置窗口大小
            width = (context.resources.displayMetrics.widthPixels * 0.85).toInt()
            height = WindowManager.LayoutParams.WRAP_CONTENT
            
            // 设置窗口标志 - 可以获取焦点
            flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
            
            // 设置窗口格式
            format = PixelFormat.TRANSLUCENT
            
            // 设置窗口位置 - 居中显示
            gravity = Gravity.CENTER
        }
    }
}
