package com.xunhe.aishoucang.helpers

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.*
import android.widget.Toast
import com.bumptech.glide.Glide
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.ContentTypeConstants
import com.xunhe.aishoucang.helpers.XiaohongshuAppSourceHelper
import com.xunhe.aishoucang.helpers.XiaohongshuWebViewHelper
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.OssManager
import com.xunhe.aishoucang.helpers.FavoriteItemHandler
import com.xunhe.aishoucang.api.BookMark
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import java.io.File
import java.net.URL
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request

/**
 * 小红书应用收藏项处理器
 */
object XiaohongshuAppItemHandler {
    private const val TAG = "XiaohongshuAppItemHandler"

    // 时间格式化工具
    private val timeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

    // 最大重试次数
    private const val MAX_RETRY_COUNT = 2

    // 重试计数器映射
    private val retryCount = mutableMapOf<String, Int>()

    /**
     * 用于保存提取的小红书笔记数据
     */
    private class ExtractedData {
        var avatarUrl: String? = null
        var noteInfo: XiaohongshuAppSourceHelper.NoteInfo? = null
        var shareInfo: XiaohongshuAppSourceHelper.ShareInfo? = null
        var schemeURL: String? = null

        // 检查是否所有数据都已准备就绪
        fun isComplete(): Boolean {
            return noteInfo != null
        }

        // 处理完成后的操作
        fun processResult(context: Context, favoriteItem: SharePanelItem?, startTime: Long) {
            Log.d(TAG, "所有数据已准备就绪，开始处理结果")
            Log.i(TAG, "【时间日志】开始处理结果: ${FavoriteItemHandler.getElapsedTimeLog(startTime)}")

            // 使用协程在后台线程处理
            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                val username = shareInfo?.username
                val title = noteInfo?.title
                val description = noteInfo?.description
                val imageUrl = noteInfo?.imageUrl

                val authorInfo = if (username != null) "作者: $username 的" else ""

                Log.d(TAG, "提取结果汇总:")
                Log.d(TAG, "- 用户名: ${username ?: "未知"}")
                Log.d(TAG, "- 标题: ${title ?: "未知"}")
                Log.d(TAG, "- 描述: ${description ?: "未知"}")
                Log.d(TAG, "- 图片URL: ${imageUrl ?: "未知"}")
                Log.d(TAG, "- 头像URL: ${avatarUrl ?: "未找到"}")
                Log.d(TAG, "- 自定义URL: ${schemeURL ?: "未获取"}")

                // 直接使用原始URL，不上传到OSS
                val avatarUrlToUse = avatarUrl
                val coverUrlToUse = imageUrl

                // 获取平台类型
                val platformType = SharePanelHelper.getCurrentPlatformType(schemeURL)

                // 在主线程中调用BookMark.addBookMark
                Log.i(TAG, "【时间日志】准备调用BookMark.addBookMark: ${FavoriteItemHandler.getElapsedTimeLog(startTime)}")
                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                    BookMark.addBookMark(
                        context,
                        influencer_name = username,
                        influencer_avatar = avatarUrlToUse,
                        cover = coverUrlToUse,
                        title = title,
                        desc = description,
                        parent_id = favoriteItem?.id ?: "",
                        scheme_url = schemeURL ?: "",
                        platform_type = platformType,
                        callback = { success, errorMessage ->
                            Handler(Looper.getMainLooper()).post {
                                // 隐藏悬浮窗上的加载动画
                                val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
                                floatingWindowHelper.hideLoading()
                                Log.i(TAG, "【时间日志】收藏完成，隐藏加载动画: ${FavoriteItemHandler.getElapsedTimeLog(startTime)}")

                                if (success) {
                                    CustomToastHelper.showShortToast(context, "主人，收藏成功啦~")
                                    Log.i(TAG, "【时间日志】收藏成功: ${FavoriteItemHandler.getElapsedTimeLog(startTime)}")
                                } else {
                                    CustomToastHelper.showShortToast(context, "收藏失败，请重试")
                                    Log.i(TAG, "【时间日志】收藏失败: ${FavoriteItemHandler.getElapsedTimeLog(startTime)}, 错误: $errorMessage")
                                }
                            }
                        }
                    )
                }
            }
        }
    }

    /**
     * 添加书签到收藏夹
     */
    private fun addBookMark(
        context: Context,
        influencer_name: String?,
        influencer_avatar: String?,
        cover: String?,
        title: String?,
        desc: String?,
        parent_id: String,
        scheme_url: String
    ) {
        // 获取平台类型
        val platformType = SharePanelHelper.getCurrentPlatformType(scheme_url)

        // 直接在主线程中调用BookMark.addBookMark，不进行OSS上传
        CoroutineScope(Dispatchers.Main).launch {
            // 添加回调来显示成功或失败的Toast消息
            BookMark.addBookMark(
                context,
                influencer_name = influencer_name,
                influencer_avatar = influencer_avatar, // 直接使用原始头像URL
                cover = cover, // 直接使用原始封面URL
                title = title,
                desc = desc,
                parent_id = parent_id,
                scheme_url = scheme_url,
                platform_type = platformType,
                callback = { success, errorMessage ->
                    Handler(Looper.getMainLooper()).post {
                        // 隐藏悬浮窗上的加载动画
                        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
                        floatingWindowHelper.hideLoading()

                        if (success) {
                            val authorInfo = if (influencer_name != null) "作者: $influencer_name 的" else ""
                            val message = "已保存${authorInfo}小红书笔记"
                            CustomToastHelper.showShortToast(context, "收藏成功")
                        } else {
                            CustomToastHelper.showShortToast(context, "保存失败: $errorMessage")
                        }
                    }
                }
            )
        }
    }

    /**
     * 处理小红书应用的收藏项
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     * @param startTime 收藏开始时间（毫秒）
     */
    fun handle(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?,
        startTime: Long = System.currentTimeMillis()
    ) {
        Log.i(TAG, "【时间日志】小红书处理器开始: ${getElapsedTimeLog(startTime)}")
        val contentType = SharePanelHelper.getCurrentContentType()

        when (contentType) {
            ContentTypeConstants.XIAOHONGSHU_TYPE_NOTE -> {
                SaveCommonNote(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            ContentTypeConstants.XIAOHONGSHU_TYPE_VIDEO_NOTE -> {
                SaveCommonNote(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            ContentTypeConstants.XIAOHONGSHU_TYPE_PROFILE -> {
                CustomToastHelper.showToast(context, "已保存小红书用户主页到「${favoriteItem?.name}」")
            }
            ContentTypeConstants.XIAOHONGSHU_TYPE_GOODS -> {
                SaveGoods(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            ContentTypeConstants.XIAOHONGSHU_TYPE_COLLECTION -> {
                CustomToastHelper.showToast(context, "已保存小红书合集到「${favoriteItem?.name}」")
            }
            else -> {
                CustomToastHelper.showToast(context, "已保存小红书内容到「${favoriteItem?.name}」")
            }
        }
    }

    /**
     * 小红书商品的收藏
     */
    fun SaveGoods(context: Context, appPackage: String, clipboardContent: String?, favoriteItem: SharePanelItem?, startTime: Long) {
        Log.i(TAG, "【时间日志】开始处理小红书商品: ${getElapsedTimeLog(startTime)}")
        Log.i(TAG, "小红书剪切板内容: $clipboardContent")

        val extractedLink = extractXiaohongshuLink(clipboardContent)

        if (extractedLink != null) {
            Log.d(TAG, "开始处理小红书商品链接: $extractedLink")

            // 显示加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
            floatingWindowHelper.showLoading()

            Log.i(TAG, "【时间日志】提取链接成功: ${getElapsedTimeLog(startTime)}")

            // 使用WebViewHtmlExtractor执行业务特定的JavaScript
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context,
                extractedLink,
                "XiaoHongshuGoods"
            ) { result, error ->
                if (error != null) {
                    // 隐藏加载动画
                    floatingWindowHelper.hideLoading()

                    // 显示错误消息
                    CustomToastHelper.showShortToast(context, "获取商品信息失败，请重试")
                    Log.e(TAG, "提取商品信息失败: $error")
                    Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                    return@executeBusinessJavaScript
                }

                if (result != null) {
                    try {
                        Log.i(TAG, "【时间日志】商品数据提取成功: ${getElapsedTimeLog(startTime)}")

                        // 解析JSON结果
                        val jsonObject = org.json.JSONObject(result)

                        // 检查是否有错误
                        if (jsonObject.has("error")) {
                            val errorMessage = jsonObject.getString("error")
                            Log.e(TAG, "提取商品数据时JavaScript报错: $errorMessage")

                            // 隐藏加载动画
                            floatingWindowHelper.hideLoading()

                            // 显示错误消息
                            CustomToastHelper.showShortToast(context, "获取商品信息失败，请重试")
                            Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                            return@executeBusinessJavaScript
                        }

                        // 提取数据
                        val title = if (jsonObject.has("title")) jsonObject.getString("title") else null
                        val price = if (jsonObject.has("price")) jsonObject.getString("price") else null
                        val coverImage = if (jsonObject.has("coverImage")) jsonObject.getString("coverImage") else null

                        // 提取schemeURL
                        val schemeURL = if (jsonObject.has("schemeURL")) jsonObject.getString("schemeURL") else null

                        // 提取店铺信息
                        var shopName: String? = null
                        var shopAvatar: String? = null
                        if (jsonObject.has("shop") && !jsonObject.isNull("shop")) {
                            val shopObject = jsonObject.getJSONObject("shop")
                            if (shopObject.has("name")) {
                                shopName = shopObject.getString("name")
                            }
                            if (shopObject.has("avatar")) {
                                shopAvatar = shopObject.getString("avatar")
                            }
                        }

                        Log.d(TAG, "提取结果汇总:")
                        Log.d(TAG, "- 标题: ${title ?: "未知"}")
                        Log.d(TAG, "- 价格: ${price ?: "未知"}")
                        Log.d(TAG, "- 封面图: ${coverImage ?: "未知"}")
                        Log.d(TAG, "- 店铺名称: ${shopName ?: "未知"}")
                        Log.d(TAG, "- 店铺头像: ${shopAvatar ?: "未知"}")
                        Log.d(TAG, "- SchemeURL: ${schemeURL ?: "未知"}")

                        // 获取平台类型
                        val platformType = SharePanelHelper.getCurrentPlatformType(schemeURL)

                        // 直接调用BookMark.addBookMark，不进行OSS上传
                        BookMark.addBookMark(
                            context = context,
                            influencer_name = shopName,
                            influencer_avatar = shopAvatar, // 直接使用原始头像URL
                            cover = coverImage, // 直接使用原始封面URL
                            title = title,
                            desc = price,
                            parent_id = favoriteItem?.id ?: "",
                            scheme_url = schemeURL ?: "",
                            platform_type = platformType,
                            callback = { success, errorMessage ->
                                // 隐藏悬浮窗上的加载动画
                                val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
                                floatingWindowHelper.hideLoading()

                                if (success) {
                                    CustomToastHelper.showShortToast(context, "收藏成功")
                                } else {
                                    CustomToastHelper.showShortToast(context, "收藏失败，请重试")
                                }
                            }
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "解析商品数据失败: ${e.message}", e)

                        // 隐藏加载动画
                        floatingWindowHelper.hideLoading()

                        // 显示错误消息
                        CustomToastHelper.showShortToast(context, "获取商品信息失败，请重试")
                        Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                    }
                } else {
                    // 隐藏加载动画
                    floatingWindowHelper.hideLoading()

                    // 显示错误消息
                    CustomToastHelper.showShortToast(context, "获取商品信息失败，请重试")
                    Log.e(TAG, "提取商品信息失败: 结果为空")
                    Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                }
            }
        } else {
            Log.e(TAG, "未能从剪贴板内容中提取到小红书链接")
            CustomToastHelper.showShortToast(context, "提取链接失败，请重试")
        }
    }

    /**
     * 小红书普通图文的收藏
     */
    fun SaveCommonNote(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?,
        startTime: Long
    ) {
        Log.i(TAG, "【时间日志】开始处理小红书普通图文: ${getElapsedTimeLog(startTime)}")
        val extractedLink = extractXiaohongshuLink(clipboardContent)

        if (extractedLink != null) {
            Log.d(TAG, "开始处理小红书链接: $extractedLink")

            // 创建数据实例
            val extractedData = ExtractedData()

            Log.i(TAG, "【时间日志】提取链接成功: ${getElapsedTimeLog(startTime)}")

            // 1. 提取分享信息
            val shareInfo = XiaohongshuAppSourceHelper.extractXiaohongshuShareInfo(clipboardContent)
            extractedData.shareInfo = shareInfo
            Log.i(TAG, "【时间日志】提取分享信息完成: ${getElapsedTimeLog(startTime)}")

            if (shareInfo != null) {
                // 2. 使用WebViewHelper获取头像URL和SchemeURL
                Log.i(TAG, "【时间日志】开始加载WebView获取资源: ${getElapsedTimeLog(startTime)}")
                XiaohongshuWebViewHelper.loadUrlAndMonitorResources(
                    context,
                    extractedLink
                ) { avatarUrl, schemeUrl, noteImageUrl, webViewTitle, webViewDescription ->
                    Log.i(TAG, "【时间日志】WebView资源获取完成: ${getElapsedTimeLog(startTime)}")
                    extractedData.avatarUrl = avatarUrl
                    extractedData.schemeURL = schemeUrl

                    // 使用WebView中已获取的信息创建NoteInfo对象
                    if (avatarUrl != null) {
                        // 使用WebView中获取的标题和描述
                        val title = webViewTitle ?: run {
                            // 如果WebView中没有获取到标题，尝试从剪贴板内容中提取
                            val titlePattern = """【小红书】([^【】]+)""".toRegex()
                            val titleMatch = titlePattern.find(clipboardContent ?: "")
                            titleMatch?.groupValues?.getOrNull(1)?.trim()
                        }

                        val description = webViewDescription ?: run {
                            // 如果WebView中没有获取到描述，尝试从剪贴板内容中提取
                            val descPattern = """【小红书】[^【】]+(.+)""".toRegex()
                            val descMatch = descPattern.find(clipboardContent ?: "")
                            descMatch?.groupValues?.getOrNull(1)?.trim()
                        }

                        // 创建笔记信息对象
                        val noteInfo = XiaohongshuAppSourceHelper.NoteInfo(
                            title = title,
                            description = description,
                            imageUrl = noteImageUrl, // 使用WebView中获取的第一张笔记图片作为封面
                            finalUrl = shareInfo.link // 使用分享信息中的链接作为finalUrl
                        )
                        extractedData.noteInfo = noteInfo

                        Log.i(TAG, "【时间日志】使用onPageFinished中获取的信息: ${getElapsedTimeLog(startTime)}")
                        if (extractedData.isComplete()) {
                            Log.i(TAG, "【时间日志】所有数据准备就绪，开始处理结果: ${getElapsedTimeLog(startTime)}")
                            extractedData.processResult(context, favoriteItem, startTime)
                        }
                    } else {
                        Log.e(TAG, "WebView未能获取到必要信息")
                        CustomToastHelper.showShortToast(context, "处理小红书链接出错")
                        // 隐藏悬浮窗上的加载动画
                        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
                        floatingWindowHelper.hideLoading()
                        Log.i(TAG, "【时间日志】处理失败，结束: ${getElapsedTimeLog(startTime)}")
                    }
                }
            } else {
                Log.e(TAG, "未能从剪贴板内容中提取到小红书链接")
                CustomToastHelper.showShortToast(context, "提取链接失败，请重试")
            }
        } else {
            Log.e(TAG, "未能从剪贴板内容中提取到小红书链接")
            CustomToastHelper.showShortToast(context, "提取链接失败，请重试")
        }
    }

    /**
     * 从剪贴板内容中提取小红书链接
     * 支持多种小红书分享格式
     *
     * @param content 剪贴板内容
     * @return 提取的链接，如果未找到则返回null
     */
    private fun extractXiaohongshuLink(content: String?): String? {
        return XiaohongshuAppSourceHelper.extractXiaohongshuLink(content)
    }

    /**
     * 获取从开始时间到现在的耗时日志
     *
     * @param startTime 开始时间（毫秒）
     * @return 格式化的耗时日志
     */
    private fun getElapsedTimeLog(startTime: Long): String {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - startTime
        val currentTimeStr = timeFormat.format(Date(currentTime))
        return "当前时间: $currentTimeStr, 耗时: ${elapsedTime}ms"
    }
}