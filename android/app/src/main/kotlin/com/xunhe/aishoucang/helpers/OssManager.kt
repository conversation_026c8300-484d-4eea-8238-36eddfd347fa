package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.alibaba.sdk.android.oss.*
import com.alibaba.sdk.android.oss.common.*
import com.alibaba.sdk.android.oss.model.*
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback
import com.alibaba.sdk.android.oss.internal.OSSAsyncTask
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONObject
import java.io.File
import java.util.UUID
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * OSS管理类
 * 用于封装阿里云OSS SDK的操作
 */
class OssManager private constructor(private val context: Context) {
    companion object {
        private const val TAG = "OssManager"
        private const val TIMEOUT_SECONDS = 60L

        @Volatile
        private var instance: OssManager? = null

        fun getInstance(context: Context): OssManager {
            return instance ?: synchronized(this) {
                instance ?: OssManager(context.applicationContext).also { instance = it }
            }
        }
    }

    // OkHttp客户端，用于网络请求
    private val client = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .build()

    // OSS客户端
    private var ossClient: OSS? = null

    // STS凭证
    private var stsCredentials: STSCredentials? = null

    // STS凭证过期时间
    private var stsExpiration: Long = 0

    /**
     * 初始化OSS客户端
     * 如果已经初始化且凭证未过期，则直接返回
     */
    private suspend fun initOssClient(): OSS {
        // 检查是否已初始化且凭证未过期
        val currentClient = ossClient
        if (currentClient != null && stsCredentials != null && System.currentTimeMillis() < stsExpiration) {
            Log.d(TAG, "OSS客户端已初始化且凭证未过期，直接使用")
            return currentClient
        }

        // 获取STS临时凭证
        val credentials = getStsCredentials()

        // 创建OSS客户端配置
        val conf = ClientConfiguration()
        conf.connectionTimeout = 15 * 1000 // 连接超时，默认15秒
        conf.socketTimeout = 15 * 1000 // socket超时，默认15秒
        conf.maxConcurrentRequest = 5 // 最大并发请求数，默认5个
        conf.maxErrorRetry = 2 // 失败后最大重试次数，默认2次

        // 创建OSS客户端
        val endpoint = OssConfig.getOssEndpoint(context)
        val region = OssConfig.getOssRegion(context)

        // 使用STS凭证创建OSS客户端
        val credentialProvider = OSSStsTokenCredentialProvider(
            credentials.accessKeyId,
            credentials.accessKeySecret,
            credentials.securityToken
        )

        val newOssClient = OSSClient(context, endpoint, credentialProvider, conf)
        // 设置区域
        newOssClient.setRegion(region)
        ossClient = newOssClient

        Log.d(TAG, "OSS客户端初始化成功")
        return newOssClient
    }

    /**
     * 获取STS临时凭证
     */
    private suspend fun getStsCredentials(): STSCredentials = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始获取STS临时凭证")

            // 获取认证Token
            val token = SharedPreferencesHelper.getInstance(context).getAuthToken()
            if (token.isEmpty()) {
                Log.w(TAG, "未找到认证Token，可能无法正常获取STS凭证")
            }

            // 获取STS授权接口地址
            val stsAuthUrl = OssConfig.getOssStsAuthUrl(context)
            Log.d(TAG, "STS授权接口地址: $stsAuthUrl")

            // 创建请求
            val requestBuilder = Request.Builder()
                .url(stsAuthUrl)
                .get()

            // 添加认证头
            if (token.isNotEmpty()) {
                requestBuilder.addHeader("Authorization", "Bearer $token")
                Log.d(TAG, "已添加认证头: Bearer ${token.take(10)}...")
            }

            // 执行请求
            val request = requestBuilder.build()

            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    throw Exception("获取STS凭证失败: ${response.code} - ${response.message}")
                }

                // 解析响应
                val responseBody = response.body?.string() ?: throw Exception("响应体为空")
                Log.d(TAG, "STS授权响应: $responseBody")

                val jsonObject = JSONObject(responseBody)

                // 检查响应状态
                val code = jsonObject.optInt("code", -1)
                if (code != 0) {
                    val message = jsonObject.optString("message", "未知错误")
                    throw Exception("获取STS凭证失败: $message")
                }

                // 解析数据
                val data = jsonObject.getJSONObject("data")

                // 获取credentials对象
                val credentialsObj = data.getJSONObject("credentials")

                // 解析expiration字符串为时间戳
                val expirationStr = credentialsObj.getString("expiration")
                val expiration = try {
                    // 尝试解析ISO 8601格式的日期时间字符串
                    val formatter = java.time.format.DateTimeFormatter.ISO_DATE_TIME
                    val dateTime = java.time.ZonedDateTime.parse(expirationStr, formatter)
                    dateTime.toInstant().toEpochMilli()
                } catch (e: Exception) {
                    Log.e(TAG, "解析expiration日期失败: $expirationStr", e)
                    // 如果解析失败，设置为当前时间加一小时
                    System.currentTimeMillis() + 3600000
                }

                val credentials = STSCredentials(
                    accessKeyId = credentialsObj.getString("access_key_id"),
                    accessKeySecret = credentialsObj.getString("access_key_secret"),
                    securityToken = credentialsObj.getString("security_token"),
                    expiration = expiration
                )

                // 如果返回了region和bucket，更新配置
                if (data.has("region") && data.has("bucket")) {
                    val region = data.getString("region")
                    val bucket = data.getString("bucket")
                    Log.d(TAG, "从STS响应更新OSS配置: region=$region, bucket=$bucket")
                    // 调用OssConfig更新配置
                    OssConfig.updateOssConfig(region, bucket)
                }

                // 保存凭证和过期时间
                stsCredentials = credentials
                stsExpiration = credentials.expiration

                Log.d(TAG, "STS凭证获取成功，过期时间: ${credentials.expiration}")

                return@use credentials
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取STS凭证失败: ${e.message}", e)
            throw e
        }
    }

    /**
     * 上传文件到OSS
     *
     * @param filePath 本地文件路径
     * @param objectKey OSS对象键，如果为null则自动生成
     * @return OSS文件URL
     */
    suspend fun uploadFile(filePath: String, objectKey: String? = null): String = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始上传文件: $filePath")

            val file = File(filePath)
            if (!file.exists()) {
                throw Exception("文件不存在: $filePath")
            }

            // 初始化OSS客户端
            val ossClient = initOssClient()

            // 生成OSS对象键
            val finalObjectKey = objectKey ?: generateObjectKey(file)
            Log.d(TAG, "OSS对象键: $finalObjectKey")

            // 创建上传请求
            val bucket = OssConfig.getOssBucket(context)
            val put = PutObjectRequest(bucket, finalObjectKey, filePath)

            // 执行上传
            val result = suspendCancellableCoroutine<PutObjectResult> { continuation ->
                val task = ossClient.asyncPutObject(put, object : OSSCompletedCallback<PutObjectRequest, PutObjectResult> {
                    override fun onSuccess(request: PutObjectRequest, result: PutObjectResult) {
                        Log.d(TAG, "文件上传成功: ${result.eTag}")
                        continuation.resume(result)
                    }

                    override fun onFailure(request: PutObjectRequest, clientException: ClientException?, serviceException: ServiceException?) {
                        val errorMsg = when {
                            clientException != null -> "客户端异常: ${clientException.message}"
                            serviceException != null -> "服务端异常: ${serviceException.errorCode} - ${serviceException.message}"
                            else -> "未知异常"
                        }
                        Log.e(TAG, "文件上传失败: $errorMsg")

                        val exception = clientException ?: serviceException ?: Exception("上传失败")
                        continuation.resumeWithException(exception)
                    }
                })

                continuation.invokeOnCancellation {
                    task.cancel()
                }
            }

            // 构建文件URL
            val endpoint = OssConfig.getOssEndpoint(context)
            // 正确的OSS URL格式：https://[bucket].[endpoint-domain]/[objectKey]
            // 从endpoint中提取域名部分（去掉https://）
            val endpointDomain = endpoint.replace("https://", "")
            val fileUrl = "https://$bucket.$endpointDomain/$finalObjectKey"
            Log.d(TAG, "文件上传成功，URL: $fileUrl")

            return@withContext fileUrl
        } catch (e: Exception) {
            Log.e(TAG, "上传文件失败: ${e.message}", e)
            throw e
        }
    }

    /**
     * 从内存数据上传到OSS
     *
     * @param data 要上传的字节数组
     * @param objectKey OSS对象键，如果为null则自动生成
     * @param extension 文件扩展名，例如"jpg"、"png"等，用于生成对象键
     * @return OSS文件URL
     */
    suspend fun uploadData(data: ByteArray, objectKey: String? = null, extension: String = "jpg"): String = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始上传内存数据: ${data.size} 字节")

            // 初始化OSS客户端
            val ossClient = initOssClient()

            // 生成OSS对象键
            val finalObjectKey = objectKey ?: generateObjectKey(extension)
            Log.d(TAG, "OSS对象键: $finalObjectKey")

            // 创建上传请求
            val bucket = OssConfig.getOssBucket(context)
            val put = PutObjectRequest(bucket, finalObjectKey, data)

            // 执行上传
            val result = suspendCancellableCoroutine<PutObjectResult> { continuation ->
                val task = ossClient.asyncPutObject(put, object : OSSCompletedCallback<PutObjectRequest, PutObjectResult> {
                    override fun onSuccess(request: PutObjectRequest, result: PutObjectResult) {
                        Log.d(TAG, "数据上传成功: ${result.eTag}")
                        continuation.resume(result)
                    }

                    override fun onFailure(request: PutObjectRequest, clientException: ClientException?, serviceException: ServiceException?) {
                        val errorMsg = when {
                            clientException != null -> "客户端异常: ${clientException.message}"
                            serviceException != null -> "服务端异常: ${serviceException.errorCode} - ${serviceException.message}"
                            else -> "未知异常"
                        }
                        Log.e(TAG, "数据上传失败: $errorMsg")

                        val exception = clientException ?: serviceException ?: Exception("上传失败")
                        continuation.resumeWithException(exception)
                    }
                })

                continuation.invokeOnCancellation {
                    task.cancel()
                }
            }

            // 构建文件URL
            val endpoint = OssConfig.getOssEndpoint(context)
            // 正确的OSS URL格式：https://[bucket].[endpoint-domain]/[objectKey]
            // 从endpoint中提取域名部分（去掉https://）
            val endpointDomain = endpoint.replace("https://", "")
            val fileUrl = "https://$bucket.$endpointDomain/$finalObjectKey"
            Log.d(TAG, "数据上传成功，URL: $fileUrl")

            return@withContext fileUrl
        } catch (e: Exception) {
            Log.e(TAG, "上传数据失败: ${e.message}", e)
            throw e
        }
    }

    /**
     * 上传文件到OSS（同步方法）
     * 注意：此方法会阻塞调用线程，不推荐在主线程中使用
     *
     * @param filePath 本地文件路径
     * @param objectKey OSS对象键，如果为null则自动生成
     * @return OSS文件URL
     */
    @Throws(Exception::class)
    fun uploadFileSync(filePath: String, objectKey: String? = null): String {
        try {
            Log.d(TAG, "开始同步上传文件: $filePath")

            val file = File(filePath)
            if (!file.exists()) {
                throw Exception("文件不存在: $filePath")
            }

            // 获取STS临时凭证（同步方式）
            val credentials = getStsCredentialsSync()

            // 创建OSS客户端配置
            val conf = ClientConfiguration()
            conf.connectionTimeout = 15 * 1000 // 连接超时，默认15秒
            conf.socketTimeout = 15 * 1000 // socket超时，默认15秒
            conf.maxConcurrentRequest = 5 // 最大并发请求数，默认5个
            conf.maxErrorRetry = 2 // 失败后最大重试次数，默认2次

            // 创建OSS客户端
            val endpoint = OssConfig.getOssEndpoint(context)
            val region = OssConfig.getOssRegion(context)
            val bucket = OssConfig.getOssBucket(context)

            // 使用STS凭证创建OSS客户端
            val credentialProvider = OSSStsTokenCredentialProvider(
                credentials.accessKeyId,
                credentials.accessKeySecret,
                credentials.securityToken
            )

            val ossClient = OSSClient(context, endpoint, credentialProvider, conf)
            // 设置区域
            ossClient.setRegion(region)

            // 生成OSS对象键
            val finalObjectKey = objectKey ?: generateObjectKey(file)
            Log.d(TAG, "OSS对象键: $finalObjectKey")

            // 创建上传请求
            val put = PutObjectRequest(bucket, finalObjectKey, filePath)

            // 执行同步上传
            val result = ossClient.putObject(put)
            Log.d(TAG, "文件同步上传成功: ${result.eTag}")

            // 构建文件URL
            // 正确的OSS URL格式：https://[bucket].[endpoint-domain]/[objectKey]
            // 从endpoint中提取域名部分（去掉https://）
            val endpointDomain = endpoint.replace("https://", "")
            val fileUrl = "https://$bucket.$endpointDomain/$finalObjectKey"
            Log.d(TAG, "文件上传成功，URL: $fileUrl")

            return fileUrl
        } catch (e: Exception) {
            Log.e(TAG, "同步上传文件失败: ${e.message}", e)
            throw e
        }
    }

    /**
     * 获取STS临时凭证（同步方法）
     * 注意：此方法会阻塞调用线程，不推荐在主线程中使用
     */
    @Throws(Exception::class)
    private fun getStsCredentialsSync(): STSCredentials {
        try {
            Log.d(TAG, "开始同步获取STS临时凭证")

            // 检查是否已有有效凭证
            val currentCredentials = stsCredentials
            if (currentCredentials != null && System.currentTimeMillis() < stsExpiration) {
                Log.d(TAG, "使用缓存的STS凭证，过期时间: $stsExpiration")
                return currentCredentials
            }

            // 获取认证Token
            val token = SharedPreferencesHelper.getInstance(context).getAuthToken()
            if (token.isEmpty()) {
                Log.w(TAG, "未找到认证Token，可能无法正常获取STS凭证")
            }

            // 获取STS授权接口地址
            val stsAuthUrl = OssConfig.getOssStsAuthUrl(context)
            Log.d(TAG, "STS授权接口地址: $stsAuthUrl")

            // 创建请求
            val requestBuilder = Request.Builder()
                .url(stsAuthUrl)
                .get()

            // 添加认证头
            if (token.isNotEmpty()) {
                requestBuilder.addHeader("Authorization", "Bearer $token")
                Log.d(TAG, "已添加认证头: Bearer ${token.take(10)}...")
            }

            // 执行请求
            val request = requestBuilder.build()

            val credentials = client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    throw Exception("获取STS凭证失败: ${response.code} - ${response.message}")
                }

                // 解析响应
                val responseBody = response.body?.string() ?: throw Exception("响应体为空")
                Log.d(TAG, "STS授权响应: $responseBody")

                val jsonObject = JSONObject(responseBody)

                // 检查响应状态
                val code = jsonObject.optInt("code", -1)
                if (code != 0) {
                    val message = jsonObject.optString("message", "未知错误")
                    throw Exception("获取STS凭证失败: $message")
                }

                // 解析数据
                val data = jsonObject.getJSONObject("data")

                // 获取credentials对象
                val credentialsObj = data.getJSONObject("credentials")

                // 解析expiration字符串为时间戳
                val expirationStr = credentialsObj.getString("expiration")
                val expiration = try {
                    // 尝试解析ISO 8601格式的日期时间字符串
                    val formatter = java.time.format.DateTimeFormatter.ISO_DATE_TIME
                    val dateTime = java.time.ZonedDateTime.parse(expirationStr, formatter)
                    dateTime.toInstant().toEpochMilli()
                } catch (e: Exception) {
                    Log.e(TAG, "解析expiration日期失败: $expirationStr", e)
                    // 如果解析失败，设置为当前时间加一小时
                    System.currentTimeMillis() + 3600000
                }

                val creds = STSCredentials(
                    accessKeyId = credentialsObj.getString("access_key_id"),
                    accessKeySecret = credentialsObj.getString("access_key_secret"),
                    securityToken = credentialsObj.getString("security_token"),
                    expiration = expiration
                )

                // 如果返回了region和bucket，更新配置
                if (data.has("region") && data.has("bucket")) {
                    val region = data.getString("region")
                    val bucket = data.getString("bucket")
                    Log.d(TAG, "从STS响应更新OSS配置: region=$region, bucket=$bucket")
                    // 调用OssConfig更新配置
                    OssConfig.updateOssConfig(region, bucket)
                }

                // 保存凭证和过期时间
                stsCredentials = creds
                stsExpiration = creds.expiration

                Log.d(TAG, "STS凭证获取成功，过期时间: ${creds.expiration}")

                creds
            }

            return credentials
        } catch (e: Exception) {
            Log.e(TAG, "获取STS凭证失败: ${e.message}", e)
            throw e
        }
    }

    /**
     * 生成OSS对象键
     */
    private fun generateObjectKey(file: File): String {
        val extension = file.extension.takeIf { it.isNotEmpty() }?.let { ".$it" } ?: ""
        return generateObjectKey(extension.removePrefix("."))
    }

    /**
     * 生成OSS对象键
     */
    private fun generateObjectKey(extension: String): String {
        val uuid = UUID.randomUUID().toString()
        val timestamp = System.currentTimeMillis()
        val ext = if (extension.isNotEmpty()) ".$extension" else ""

        return "uploads/$timestamp-$uuid$ext"
    }

    /**
     * STS凭证数据类
     */
    data class STSCredentials(
        val accessKeyId: String,
        val accessKeySecret: String,
        val securityToken: String,
        val expiration: Long
    )
}
