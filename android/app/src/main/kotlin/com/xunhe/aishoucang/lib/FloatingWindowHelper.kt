// 文件：FloatingWindowHelper.kt
// 功能：负责管理和展示悬浮窗，包括前台服务、内容更新、拖动操作、与无障碍服务交互等

package com.xunhe.aishoucang.lib

import android.accessibilityservice.AccessibilityService
import android.app.ActivityManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.*
import android.view.accessibility.AccessibilityNodeInfo
import android.view.GestureDetector
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import com.xunhe.aishoucang.lib.MenuHelper
import android.widget.*
import androidx.core.app.NotificationCompat
import com.xunhe.aishoucang.MainActivity
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import com.xunhe.aishoucang.lib.CustomNotificationPanel

class FloatingWindowHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "FloatingWindowHelper"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "clipboard_monitor_channel"
        private const val ACTION_UPDATE_CONTENT = "com.xunhe.aishoucang.ACTION_UPDATE_CONTENT"
        private const val ACTION_GET_POSITION = "com.xunhe.aishoucang.ACTION_GET_POSITION"
        private const val ACTION_SHOW_LOADING = "com.xunhe.aishoucang.ACTION_SHOW_LOADING"
        private const val ACTION_HIDE_LOADING = "com.xunhe.aishoucang.ACTION_HIDE_LOADING"
        private const val LOADING_TIMEOUT_MS = 15000L // 15秒超时

        @Volatile
        private var instance: FloatingWindowHelper? = null

        fun getInstance(context: Context): FloatingWindowHelper {
            return instance ?: synchronized(this) {
                instance ?: FloatingWindowHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    private var isShowing = false

    // 全局超时保护机制
    private var loadingTimeoutHandler: Handler? = null
    private var loadingTimeoutRunnable: Runnable? = null

    fun show() {
        try {
            val intent = Intent(context, FloatingWindowService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            isShowing = true
            Log.d(TAG, "显示悬浮窗")
        } catch (e: Exception) {
            Log.e(TAG, "显示悬浮窗时出错", e)
        }
    }

    fun hide() {
        try {
            stopFloatingWindowService()
            isShowing = false
            Log.d(TAG, "隐藏悬浮窗")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏悬浮窗时出错", e)
        }
    }

    fun showFloatingWindow(content: String?) {
        try {
            val intent = Intent(context, FloatingWindowService::class.java).apply {
                content?.let { putExtra("clipboard_content", it) }
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            isShowing = true
            Log.d(TAG, "显示悬浮窗")
        } catch (e: Exception) {
            Log.e(TAG, "显示悬浮窗时出错", e)
        }
    }

    fun hideFloatingWindow() {
        try {
            stopFloatingWindowService()
            isShowing = false
            Log.d(TAG, "隐藏悬浮窗")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏悬浮窗时出错", e)
        }
    }

    fun isFloatingWindowShowing(): Boolean {
        // 检查服务是否真的在运行
        val isServiceRunning = isServiceRunning(FloatingWindowService::class.java)

        // 如果内存状态与实际服务状态不一致，更新内存状态
        if (isShowing != isServiceRunning) {
            Log.d(TAG, "悬浮窗状态不一致，内存状态: $isShowing, 服务状态: $isServiceRunning，更新为: $isServiceRunning")
            isShowing = isServiceRunning
        }

        return isShowing
    }

    /**
     * 检查指定服务是否正在运行
     */
    private fun isServiceRunning(serviceClass: Class<*>): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)

            for (service in runningServices) {
                if (serviceClass.name == service.service.className) {
                    return true
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "检查服务运行状态失败", e)
            false
        }
    }

    /**
     * 更新悬浮窗显示状态
     * 用于服务销毁时同步状态
     */
    fun updateShowingState(showing: Boolean) {
        isShowing = showing
        Log.d(TAG, "更新悬浮窗状态: isShowing=$isShowing")
    }

    /**
     * 停止悬浮窗服务
     * Android 14+ 需要特殊处理以避免前台服务超时异常
     */
    private fun stopFloatingWindowService() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14+
            // Android 14+ 需要预停止机制避免服务超时
            stopServiceWithPreparation()
        } else {
            // Android 13 及以下直接停止服务
            context.stopService(Intent(context, FloatingWindowService::class.java))
        }
    }

    /**
     * Android 14+ 的预停止机制
     */
    private fun stopServiceWithPreparation() {
        try {
            // 先通知服务准备停止，让服务提前开始清理
            val prepareIntent = Intent(context, FloatingWindowService::class.java).apply {
                action = "ACTION_PREPARE_STOP"
            }
            context.startService(prepareIntent)

            // 进一步减少延迟时间，更快停止服务
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    context.stopService(Intent(context, FloatingWindowService::class.java))
                    Log.d(TAG, "Android 14+ 服务停止指令已发送")
                } catch (e: Exception) {
                    Log.e(TAG, "发送停止服务指令失败", e)
                    // 如果停止失败，尝试强制停止
                    try {
                        val serviceIntent = Intent(context, FloatingWindowService::class.java)
                        serviceIntent.action = "ACTION_FORCE_STOP"
                        context.startService(serviceIntent)
                    } catch (ex: Exception) {
                        Log.e(TAG, "强制停止服务也失败", ex)
                    }
                }
            }, 30) // 进一步减少到30ms延迟

            Log.d(TAG, "Android 14+ 使用预停止机制")
        } catch (e: Exception) {
            Log.e(TAG, "预停止机制失败，尝试直接停止", e)
            // 如果预停止失败，回退到直接停止
            try {
                context.stopService(Intent(context, FloatingWindowService::class.java))
            } catch (ex: Exception) {
                Log.e(TAG, "直接停止服务也失败", ex)
            }
        }
    }

    // 获取悬浮窗位置
    fun getFloatingWindowPosition(): Pair<Int, Int>? {
        try {
            // 从SharedPreferences获取保存的位置
            val prefsHelper = SharedPreferencesHelper.getInstance(context)
            val x = prefsHelper.getFloatingWindowX()
            val y = prefsHelper.getFloatingWindowY()

            // 如果位置有效，则返回
            if (x != 0 || y != 0) {
                Log.d(TAG, "从SharedPreferences获取悬浮窗位置: x=$x, y=$y")
                return Pair(x, y)
            }

            // 如果没有保存的位置，则尝试从服务获取当前位置
            val intent = Intent(context, FloatingWindowService::class.java).apply {
                action = ACTION_GET_POSITION
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }

            // 返回服务中的位置（可能为null）
            val position = FloatingWindowService.lastPosition
            Log.d(TAG, "从服务获取悬浮窗位置: $position")
            return position
        } catch (e: Exception) {
            Log.e(TAG, "获取悬浮窗位置失败", e)
            return null
        }
    }

    // 在指定位置显示悬浮窗
    fun showFloatingWindowAtPosition(content: String?, x: Int?, y: Int?) {
        try {
            val intent = Intent(context, FloatingWindowService::class.java).apply {
                content?.let { putExtra("clipboard_content", it) }
                x?.let { putExtra("position_x", it) }
                y?.let { putExtra("position_y", it) }
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            isShowing = true
            Log.d(TAG, "在指定位置(x=$x, y=$y)显示悬浮窗")
        } catch (e: Exception) {
            Log.e(TAG, "在指定位置显示悬浮窗时出错", e)
        }
    }

    fun updateFloatingWindowContent(content: String?) {
        try {
            val safeContent = content ?: ""
            val intent = Intent(context, FloatingWindowService::class.java).apply {
                action = ACTION_UPDATE_CONTENT
                putExtra("clipboard_content", safeContent)
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            Log.d(TAG, "更新悬浮窗内容")
        } catch (e: Exception) {
            Log.e(TAG, "更新悬浮窗内容时出错", e)
        }
    }

    // 显示悬浮窗上的加载动画
    fun showLoading() {
        try {
            val intent = Intent(context, FloatingWindowService::class.java).apply {
                action = ACTION_SHOW_LOADING
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            Log.d(TAG, "显示悬浮窗加载动画")

            // 设置全局超时保护
            setupLoadingTimeout()
        } catch (e: Exception) {
            Log.e(TAG, "显示悬浮窗加载动画时出错", e)
        }
    }

    // 隐藏悬浮窗上的加载动画
    fun hideLoading() {
        try {
            val intent = Intent(context, FloatingWindowService::class.java).apply {
                action = ACTION_HIDE_LOADING
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            Log.d(TAG, "隐藏悬浮窗加载动画")

            // 清除全局超时保护
            clearLoadingTimeout()
        } catch (e: Exception) {
            Log.e(TAG, "隐藏悬浮窗加载动画时出错", e)
        }
    }

    /**
     * 设置全局超时保护机制
     * 在指定时间后强制隐藏loading并提示用户
     */
    private fun setupLoadingTimeout() {
        // 清除之前的超时任务（如果有的话）
        clearLoadingTimeout()

        loadingTimeoutHandler = Handler(Looper.getMainLooper())
        loadingTimeoutRunnable = Runnable {
            Log.w(TAG, "收藏操作超时，强制隐藏loading")
            try {
                // 强制隐藏loading（不再调用clearLoadingTimeout避免递归）
                val intent = Intent(context, FloatingWindowService::class.java).apply {
                    action = ACTION_HIDE_LOADING
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }

                // 显示超时提示
                CustomToastHelper.showToast(context, "收藏超时，请稍后再试")

                // 清除超时任务
                loadingTimeoutHandler = null
                loadingTimeoutRunnable = null
            } catch (e: Exception) {
                Log.e(TAG, "超时处理时出错: ${e.message}", e)
            }
        }
        loadingTimeoutHandler?.postDelayed(loadingTimeoutRunnable!!, LOADING_TIMEOUT_MS)
        Log.d(TAG, "已设置${LOADING_TIMEOUT_MS}ms超时保护")
    }

    /**
     * 清除超时保护机制
     */
    private fun clearLoadingTimeout() {
        loadingTimeoutRunnable?.let {
            loadingTimeoutHandler?.removeCallbacks(it)
            Log.d(TAG, "已清除超时保护")
        }
        loadingTimeoutHandler = null
        loadingTimeoutRunnable = null
    }

    class FloatingWindowService : Service() {
        companion object {
            // 保存最后一次悬浮窗的位置
            var lastPosition: Pair<Int, Int>? = null
        }
        private var windowManager: WindowManager? = null
        private var floatingView: View? = null
        private var params: WindowManager.LayoutParams? = null
        private var initialX = 0
        private var initialY = 0
        private var initialTouchX = 0f
        private var initialTouchY = 0f

        // 下拉菜单相关变量
        private var menuHelper: MenuHelper? = null

        // 用于监听全局触摸事件的透明覆盖层
        private var touchOverlayView: View? = null
        private var touchOverlayParams: WindowManager.LayoutParams? = null

        private var clipboardContent: String = "无剪贴板内容"

        override fun onBind(intent: Intent?): IBinder? = null

        override fun onCreate() {
            super.onCreate()
            windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) createNotificationChannel()
            startForeground(NOTIFICATION_ID, createNotification("剪贴板监听服务正在运行"))
        }

        override fun onDestroy() {
            super.onDestroy()

            Log.d(TAG, "FloatingWindowService onDestroy 开始")

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14+
                // Android 14+ 需要立即停止前台服务状态，这是最关键的步骤
                try {
                    stopForeground(STOP_FOREGROUND_REMOVE) // 立即移除通知并停止前台服务状态
                    Log.d(TAG, "Android 14+ 已立即停止前台服务状态")
                } catch (e: Exception) {
                    Log.e(TAG, "Android 14+ 停止前台服务状态失败", e)
                }

                // 立即执行快速清理，避免超时
                performFastCleanup()
            } else {
                // Android 13 及以下的标准处理
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        stopForeground(true) // 移除通知并停止前台服务状态
                        Log.d(TAG, "已停止前台服务状态")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "停止前台服务状态失败", e)
                }

                // Android 13 及以下使用标准清理
                performStandardCleanup()
            }

            // 通知FloatingWindowHelper更新状态
            try {
                val helper = FloatingWindowHelper.getInstance(applicationContext)
                helper.updateShowingState(false)
            } catch (e: Exception) {
                Log.e(TAG, "更新FloatingWindowHelper状态失败", e)
            }

            Log.d(TAG, "FloatingWindowService onDestroy 完成")
        }

        /**
         * Android 14+ 快速清理，避免前台服务超时
         */
        private fun performFastCleanup() {
            val startTime = System.currentTimeMillis()
            try {
                Log.d(TAG, "开始Android 14+ 快速清理")

                // 1. 立即移除悬浮窗视图（最重要的清理操作）
                floatingView?.let { view ->
                    try {
                        windowManager?.removeView(view)
                        Log.d(TAG, "悬浮窗视图已移除")
                    } catch (e: Exception) {
                        Log.e(TAG, "移除悬浮窗视图失败", e)
                    }
                    floatingView = null
                }

                // 2. 立即清空菜单引用（不调用hideMenu避免耗时操作）
                menuHelper?.let {
                    try {
                        menuHelper = null
                        Log.d(TAG, "菜单引用已清空")
                    } catch (e: Exception) {
                        Log.e(TAG, "清空菜单引用失败", e)
                        menuHelper = null
                    }
                }

                // 3. 同步保存位置信息（避免异步操作）
                params?.let { layoutParams ->
                    try {
                        val prefsHelper = SharedPreferencesHelper.getInstance(applicationContext)
                        prefsHelper.saveFloatingWindowPosition(layoutParams.x, layoutParams.y)
                        Log.d(TAG, "快速保存悬浮窗位置: x=${layoutParams.x}, y=${layoutParams.y}")
                    } catch (e: Exception) {
                        Log.e(TAG, "快速保存位置失败", e)
                    }
                }

                // 4. 立即清空所有引用
                params = null
                windowManager = null

                val duration = System.currentTimeMillis() - startTime
                Log.d(TAG, "Android 14+ 快速清理完成，耗时: ${duration}ms")
            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                Log.e(TAG, "快速清理失败，耗时: ${duration}ms", e)
            }
        }

        /**
         * Android 13 及以下标准清理
         */
        private fun performStandardCleanup() {
            try {
                // 清理触摸覆盖层
                removeTouchOverlay()

                // 清理菜单视图
                menuHelper?.let { helper ->
                    try {
                        helper.hideMenu()
                    } catch (e: Exception) {
                        Log.e(TAG, "移除菜单视图失败", e)
                    }
                    menuHelper = null
                }

                // 保存悬浮窗位置到SharedPreferences
                params?.let { layoutParams ->
                    val prefsHelper = SharedPreferencesHelper.getInstance(applicationContext)
                    prefsHelper.saveFloatingWindowPosition(layoutParams.x, layoutParams.y)
                    Log.d(TAG, "服务销毁前保存悬浮窗位置: x=${layoutParams.x}, y=${layoutParams.y}")
                }

                // 清理悬浮窗视图
                floatingView?.let { view ->
                    try {
                        windowManager?.removeView(view)
                    } catch (e: Exception) {
                        Log.e(TAG, "移除悬浮窗视图失败", e)
                    }
                    floatingView = null
                }

                Log.d(TAG, "标准清理完成")
            } catch (e: Exception) {
                Log.e(TAG, "标准清理失败", e)
            }
        }

        /**
         * 预停止清理，在收到停止信号时提前进行部分清理
         */
        private fun prepareForStop() {
            val startTime = System.currentTimeMillis()
            try {
                Log.d(TAG, "开始预停止清理")

                // Android 14+ 立即停止前台服务状态（最关键的步骤）
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                        // Android 14+ 使用更强制的停止方式
                        stopForeground(STOP_FOREGROUND_REMOVE)
                        Log.d(TAG, "Android 14+ 预停止时已立即停止前台服务状态")
                    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        stopForeground(true)
                        Log.d(TAG, "预停止时已停止前台服务状态")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "预停止时停止前台服务状态失败", e)
                }

                // 提前保存位置信息
                params?.let { layoutParams ->
                    try {
                        val prefsHelper = SharedPreferencesHelper.getInstance(applicationContext)
                        prefsHelper.saveFloatingWindowPosition(layoutParams.x, layoutParams.y)
                        Log.d(TAG, "预停止时保存悬浮窗位置: x=${layoutParams.x}, y=${layoutParams.y}")
                    } catch (e: Exception) {
                        Log.e(TAG, "预停止时保存位置失败", e)
                    }
                }

                // 对于Android 14+，直接清空菜单引用而不调用hideMenu
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    menuHelper?.let {
                        try {
                            menuHelper = null
                            Log.d(TAG, "Android 14+ 预停止时直接清空菜单引用")
                        } catch (e: Exception) {
                            Log.e(TAG, "预停止时清空菜单引用失败", e)
                            menuHelper = null
                        }
                    }
                } else {
                    // Android 13及以下才调用hideMenu
                    menuHelper?.let { helper ->
                        try {
                            helper.hideMenu()
                            Log.d(TAG, "预停止时隐藏菜单")
                        } catch (e: Exception) {
                            Log.e(TAG, "预停止时隐藏菜单失败", e)
                        }
                    }
                }

                val duration = System.currentTimeMillis() - startTime
                Log.d(TAG, "预停止清理完成，耗时: ${duration}ms")
            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                Log.e(TAG, "预停止清理失败，耗时: ${duration}ms", e)
            }
        }

        /**
         * 以下方法已不再使用，因为我们现在使用全屏菜单容器代替了触摸覆盖层
         * 保留方法签名以避免编译错误，但内部实现已清空
         */
        private fun createTouchOverlay() {
            // 不再需要实现，使用全屏菜单容器代替
            Log.d(TAG, "createTouchOverlay方法已弃用")
        }

        /**
         * 以下方法已不再使用，因为我们现在使用全屏菜单容器代替了触摸覆盖层
         * 保留方法签名以避免编译错误，但内部实现已清空
         */
        private fun removeTouchOverlay() {
            // 不再需要实现，使用全屏菜单容器代替
            Log.d(TAG, "removeTouchOverlay方法已弃用")
        }

        private fun createNotificationChannel() {
            val channel = NotificationChannel(
                CHANNEL_ID, "剪贴板监听服务", NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用于保持剪贴板监听服务在后台运行"
                setShowBadge(false)
            }
            val nm = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            nm.createNotificationChannel(channel)
        }

        private fun createNotification(content: String): Notification {
            // 创建PendingIntent，点击通知时打开MainActivity
            val pendingIntent = PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_IMMUTABLE
            )

            return NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("收藏助手")
                .setContentText(content)
                .setSmallIcon(android.R.drawable.ic_menu_save)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MIN)
                .setVisibility(NotificationCompat.VISIBILITY_SECRET)
                .setCategory(Notification.CATEGORY_SERVICE)
                .setOngoing(true)
                .build()
        }

        override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14+
                handleStartCommandForAndroid14Plus(intent, flags, startId)
            } else {
                handleStartCommandStandard(intent, flags, startId)
            }
        }

        /**
         * Android 14+ 的 onStartCommand 处理，包含异常保护
         */
        private fun handleStartCommandForAndroid14Plus(intent: Intent?, flags: Int, startId: Int): Int {
            try {
                // 确保每次启动服务时都调用startForeground
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    startForeground(NOTIFICATION_ID, createNotification("剪贴板监听服务正在运行"))
                }

                when (intent?.action) {
                    "ACTION_PREPARE_STOP" -> {
                        // 预停止信号，开始预清理
                        Log.d(TAG, "收到预停止信号，开始预清理")
                        prepareForStop()
                        return START_NOT_STICKY // 不要重启服务
                    }
                    "ACTION_FORCE_STOP" -> {
                        // 强制停止信号，立即停止服务
                        Log.d(TAG, "收到强制停止信号，立即停止服务")
                        try {
                            // 立即停止前台服务状态
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                stopForeground(true)
                            }
                            // 立即执行快速清理
                            performFastCleanup()
                            // 立即停止服务
                            stopSelf()
                        } catch (e: Exception) {
                            Log.e(TAG, "强制停止服务时出错", e)
                        }
                        return START_NOT_STICKY
                    }
                    else -> handleCommonActions(intent)
                }

                createFloatingWindowIfNeeded(intent)
                return START_STICKY
            } catch (e: Exception) {
                Log.e(TAG, "Android 14+ onStartCommand执行失败", e)
                return handleStartCommandError()
            }
        }

        /**
         * Android 13 及以下的标准 onStartCommand 处理
         */
        private fun handleStartCommandStandard(intent: Intent?, flags: Int, startId: Int): Int {
            // 确保每次启动服务时都调用startForeground
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForeground(NOTIFICATION_ID, createNotification("剪贴板监听服务正在运行"))
            }

            handleCommonActions(intent)
            createFloatingWindowIfNeeded(intent)
            return START_STICKY
        }

        /**
         * 处理通用的 Intent 动作
         */
        private fun handleCommonActions(intent: Intent?) {
            when (intent?.action) {
                ACTION_UPDATE_CONTENT -> intent.getStringExtra("clipboard_content")?.let {
                    updateClipboardContent(it)
                }
                ACTION_GET_POSITION -> {
                    // 保存当前位置到静态变量和SharedPreferences
                    params?.let { layoutParams ->
                        val x = layoutParams.x
                        val y = layoutParams.y

                        // 保存到静态变量
                        lastPosition = Pair(x, y)

                        // 保存到SharedPreferences
                        val prefsHelper = SharedPreferencesHelper.getInstance(applicationContext)
                        prefsHelper.saveFloatingWindowPosition(x, y)

                        Log.d(TAG, "保存悬浮窗位置到SharedPreferences: x=$x, y=$y")
                    }
                }
                ACTION_SHOW_LOADING -> showLoadingAnimation()
                ACTION_HIDE_LOADING -> hideLoadingAnimation()
                else -> intent?.getStringExtra("clipboard_content")?.let {
                    clipboardContent = it
                }
            }
        }

        /**
         * 根据需要创建悬浮窗
         */
        private fun createFloatingWindowIfNeeded(intent: Intent?) {
            if (floatingView == null) {
                // 获取指定位置（如果有）
                val positionX = intent?.getIntExtra("position_x", Int.MIN_VALUE)
                val positionY = intent?.getIntExtra("position_y", Int.MIN_VALUE)

                // 创建悬浮窗，并设置位置（如果指定了位置）
                createFloatingWindow(
                    if (positionX != Int.MIN_VALUE) positionX else null,
                    if (positionY != Int.MIN_VALUE) positionY else null
                )
            }
        }

        /**
         * 处理启动命令错误
         */
        private fun handleStartCommandError(): Int {
            // 即使出错也要确保前台服务正常运行
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                try {
                    startForeground(NOTIFICATION_ID, createNotification("服务运行中"))
                } catch (ex: Exception) {
                    Log.e(TAG, "启动前台服务失败", ex)
                }
            }
            return START_NOT_STICKY // 出错时不重启
        }

        private fun createFloatingWindow(positionX: Int? = null, positionY: Int? = null) {
            try {
                val inflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
                floatingView = inflater.inflate(R.layout.floating_window_layout, null)

                // 设置布局参数
                params = WindowManager.LayoutParams().apply {
                    width = WindowManager.LayoutParams.WRAP_CONTENT
                    height = WindowManager.LayoutParams.WRAP_CONTENT
                    type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                    } else {
                        WindowManager.LayoutParams.TYPE_PHONE
                    }
                    flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                    format = PixelFormat.TRANSLUCENT
                }

                // 设置初始位置，优先级：1.传入的位置 2.SharedPreferences中的位置 3.默认位置(0,0)
                val prefsHelper = SharedPreferencesHelper.getInstance(applicationContext)
                params?.x = positionX ?: prefsHelper.getFloatingWindowX()
                params?.y = positionY ?: prefsHelper.getFloatingWindowY()

                Log.d(TAG, "创建悬浮窗，位置: x=${params?.x}, y=${params?.y}")

                setupDragListener()
                windowManager?.addView(floatingView, params)

                // 更新最后位置
                lastPosition = Pair(params?.x ?: 0, params?.y ?: 0)
            } catch (e: Exception) {
                Log.e(TAG, "创建悬浮窗失败", e)
            }
        }

        private fun updateClipboardContent(content: String) {
            clipboardContent = content
            // 已简化悬浮窗，不再显示剪贴板内容
        }

        /**
         * 显示加载动画
         */
        private fun showLoadingAnimation() {
            try {
                if (floatingView == null) {
                    Log.e(TAG, "悬浮窗视图为空，无法显示加载动画")
                    return
                }

                // 获取加载进度条视图
                val loadingProgress = floatingView?.findViewById<ProgressBar>(R.id.loading_progress)
                if (loadingProgress == null) {
                    Log.e(TAG, "找不到加载进度条视图")
                    return
                }

                // 获取收藏按钮视图
                val bookmarkButton = floatingView?.findViewById<ImageButton>(R.id.find_and_click_button)

                // 在主线程中更新UI
                Handler(Looper.getMainLooper()).post {
                    // 隐藏收藏按钮
                    bookmarkButton?.visibility = View.INVISIBLE
                    // 显示加载进度条
                    loadingProgress.visibility = View.VISIBLE
                }

                Log.d(TAG, "显示悬浮窗加载动画")
            } catch (e: Exception) {
                Log.e(TAG, "显示加载动画失败", e)
            }
        }

        /**
         * 隐藏加载动画
         */
        private fun hideLoadingAnimation() {
            try {
                if (floatingView == null) {
                    Log.e(TAG, "悬浮窗视图为空，无法隐藏加载动画")
                    return
                }

                // 获取加载进度条视图
                val loadingProgress = floatingView?.findViewById<ProgressBar>(R.id.loading_progress)
                if (loadingProgress == null) {
                    Log.e(TAG, "找不到加载进度条视图")
                    return
                }

                // 获取收藏按钮视图
                val bookmarkButton = floatingView?.findViewById<ImageButton>(R.id.find_and_click_button)

                // 在主线程中更新UI
                Handler(Looper.getMainLooper()).post {
                    // 显示收藏按钮
                    bookmarkButton?.visibility = View.VISIBLE
                    // 隐藏加载进度条
                    loadingProgress.visibility = View.GONE
                }

                Log.d(TAG, "隐藏悬浮窗加载动画")
            } catch (e: Exception) {
                Log.e(TAG, "隐藏加载动画失败", e)
            }
        }

        /**
         * 显示下拉菜单
         */
        private fun showFloatingMenu() {
            // 初始化菜单助手
            if (menuHelper == null) {
                menuHelper = MenuHelper.getInstance(applicationContext)
            }

            // 显示菜单
            menuHelper?.showMenu(params, floatingView) { menuItemId ->
                when (menuItemId) {
                    R.id.menu_item_share -> {
                        // 截图收藏功能已在MenuHelper中实现
                    }
                    R.id.menu_item_long_screenshot -> {
                        // 截长图功能已在MenuHelper中实现
                    }
                    1001 -> { // 关闭悬浮窗菜单项的ID
                        // 关闭悬浮窗
                        stopSelf()
                        // 通知Flutter悬浮窗已关闭
                        val intent = Intent("com.xunhe.aishoucang.FLOATING_WINDOW_CLOSED")
                        applicationContext.sendBroadcast(intent)

                        // 长按关闭悬浮窗后开启自定义通知栏
                        try {
                            val customNotificationPanel = CustomNotificationPanel.getInstance(applicationContext)
                            // 设置开关状态为关闭状态
                            customNotificationPanel.switchState = false
                            customNotificationPanel.showPanel()
                            Log.d(TAG, "悬浮窗关闭后已开启自定义通知栏，开关状态设为false")
                        } catch (e: Exception) {
                            Log.e(TAG, "开启自定义通知栏失败", e)
                        }

                        Log.d(TAG, "用户点击菜单中的关闭按钮关闭悬浮窗")
                    }
                }
            }
        }

        /**
         * 隐藏下拉菜单
         */
        private fun hideFloatingMenu() {
            menuHelper?.hideMenu()
        }

        private fun setupDragListener() {
            // 获取按钮
            val iconButton = floatingView?.findViewById<ImageButton>(R.id.find_and_click_button)

            // 用于记录触摸状态
            var isDragging = false
            var startX = 0f
            var startY = 0f
            var lastTouchTime = 0L

            // 长按检测相关变量
            var longPressHandler: Handler? = null
            val LONG_PRESS_TIMEOUT = 500L // 长按判定的时间阈值（毫秒）
            val longPressRunnable = Runnable {
                // 长按操作 - 显示菜单
                if (!isDragging) {
                    Log.d(TAG, "检测到长按，显示菜单")
                    showFloatingMenu()
                }
            }

            val CLICK_TIMEOUT = 200L // 点击判定的时间阈值（毫秒）
            val MOVE_THRESHOLD = 10 // 移动判定的距离阈值（像素）

            // 设置收藏按钮的触摸事件
            iconButton?.setOnTouchListener { view, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        // 记录初始状态
                        isDragging = false
                        initialX = params?.x ?: 0
                        initialY = params?.y ?: 0
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        startX = event.rawX
                        startY = event.rawY
                        lastTouchTime = System.currentTimeMillis()

                        // 创建长按定时器
                        longPressHandler = Handler(Looper.getMainLooper())
                        longPressHandler?.postDelayed(longPressRunnable, LONG_PRESS_TIMEOUT)

                        true
                    }

                    MotionEvent.ACTION_MOVE -> {
                        val deltaX = (event.rawX - initialTouchX).toInt()
                        val deltaY = (event.rawY - initialTouchY).toInt()

                        // 计算移动距离
                        val distance = Math.sqrt(
                            Math.pow((event.rawX - startX).toDouble(), 2.0) +
                                    Math.pow((event.rawY - startY).toDouble(), 2.0)
                        ).toFloat()

                        // 如果移动距离超过阈值，则认为是拖动
                        if (distance > MOVE_THRESHOLD) {
                            isDragging = true

                            // 取消长按定时器
                            longPressHandler?.removeCallbacks(longPressRunnable)
                            longPressHandler = null

                            // 如果菜单正在显示，则隐藏
                            if (menuHelper?.isMenuVisible() == true) {
                                hideFloatingMenu()
                            }

                            // 更新位置
                            params?.x = initialX + deltaX
                            params?.y = initialY + deltaY
                            windowManager?.updateViewLayout(floatingView, params)

                            // 保存位置到静态变量
                            lastPosition = Pair(params?.x ?: 0, params?.y ?: 0)

                            // 每10次移动保存一次位置到SharedPreferences，避免频繁IO操作
                            if (Math.random() < 0.1) {
                                val prefsHelper = SharedPreferencesHelper.getInstance(applicationContext)
                                prefsHelper.saveFloatingWindowPosition(params?.x ?: 0, params?.y ?: 0)
                                Log.d(TAG, "拖动时保存悬浮窗位置: x=${params?.x}, y=${params?.y}")
                            }
                        }
                        true
                    }

                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                        // 取消长按定时器
                        longPressHandler?.removeCallbacks(longPressRunnable)
                        longPressHandler = null

                        // 如果没有拖动且触摸时间很短，则认为是点击
                        val touchDuration = System.currentTimeMillis() - lastTouchTime
                        if (!isDragging && touchDuration < CLICK_TIMEOUT) {
                            // 单击操作 - 执行收藏
                            findAndClickElement()
                        }
                        false
                    }

                    else -> false
                }
            }

            // 设置悬浮窗容器的触摸事件，用于检测外部点击
            floatingView?.setOnTouchListener { _, event ->
                if (event.action == MotionEvent.ACTION_OUTSIDE && menuHelper?.isMenuVisible() == true) {
                    hideFloatingMenu()
                    return@setOnTouchListener true
                }
                false
            }
        }


        fun showSharePanel() {
            // 获取当前应用包名
            val helper = AccessibilityHelper.getInstance(applicationContext)
            val rootNode = helper.getRootNode()
            val currentPackage = rootNode?.packageName?.toString()

            // 判断当前应用是否是支持的应用
            val handler = com.xunhe.aishoucang.helpers.AppHandlerFactory.getHandlerByPackage(currentPackage)
            val isUnknownApp = handler is com.xunhe.aishoucang.helpers.UnknownAppHandler

            Log.d(TAG, "当前应用包名: $currentPackage, 是否为未知应用: $isUnknownApp")

            // 调用SharePanelHelper显示分享面板，传递unSupport参数
            com.xunhe.aishoucang.helpers.SharePanelHelper.showSharePanel(applicationContext, windowManager, isUnknownApp)
        }

        fun hideSharePanel() {
            // 调用SharePanelHelper隐藏分享面板
            com.xunhe.aishoucang.helpers.SharePanelHelper.hideSharePanel(applicationContext, windowManager)
        }





        /** 使用无障碍功能查找并点击指定元素 */
        fun findAndClickElement() {
            val helper = AccessibilityHelper.getInstance(applicationContext)

            // 1. 检查无障碍服务是否启用
            if (!helper.isAccessibilityServiceEnabled()) {
                Toast.makeText(applicationContext, "无障碍服务未运行，无法执行操作", Toast.LENGTH_SHORT).show()
                Log.e("ElementClicker", "无障碍服务未启用")
                return
            }

            // 2. 获取当前界面信息
            val rootNode = helper.getRootNode()
            if (rootNode == null) {
                Toast.makeText(applicationContext, "无法获取当前界面信息", Toast.LENGTH_SHORT).show()
                Log.e("ElementClicker", "rootInActiveWindow 为 null")
                return
            }

            // 3. 显示分享面板
            showSharePanel()
        }
    }
}