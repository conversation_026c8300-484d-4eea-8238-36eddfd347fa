package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.lib.AccessibilityHelper

/**
 * 淘宝应用处理器
 * 参考XiaohongshuAppHandler的设计模式，使用if-else分发不同业务场景
 */
class TaobaoAppHandler : AppHandler {
    companion object {
        private const val TAG = "TaobaoAppHandler"
    }

    override fun handle(context: Context) {
        Log.d(TAG, "正在处理淘宝应用")

        // 获取无障碍服务实例
        val helper = AccessibilityHelper.getInstance(context)
        val service = AccessibilityHelper.AppAccessibilityService.getInstance()

        if (service == null) {
            Log.e(TAG, "无障碍服务未运行，无法继续操作")
            CustomToastHelper.showToast(context, "无障碍服务未运行，请检查权限设置")
            return
        }

        // 业务场景分发 - 只支持商品详情页
        if (isTaobaoProductPage(context)) {
            // 商品详情页
            Log.d(TAG, "检测到淘宝商品详情页")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.TAOBAO_TYPE_PRODUCT)
            handleProductPage(context)
        } else {
            // 未知类型或不支持的页面
            Log.d(TAG, "淘宝当前页面暂不支持")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.TYPE_UNKNOWN)
            CustomToastHelper.showToast(context, "淘宝当前页面暂不支持，请在商品详情页使用")
        }
    }

    /**
     * 判断是否为淘宝商品详情页
     * 通过检查页面是否同时存在三个特定的resource-id节点来判断：
     * 1. com.taobao.taobao:id/tt_detail_nav_bar_share_icon (分享图标)
     * 2. com.taobao.taobao:id/tt_detail_cart_item (购物车按钮)
     * 3. com.taobao.taobao:id/uik_public_menu_action_message (消息按钮)
     */
    private fun isTaobaoProductPage(context: Context): Boolean {
        val helper = AccessibilityHelper.getInstance(context)

        try {
            // 检查三个关键的resource-id是否同时存在
            val hasShareIcon = helper.IsExistElementById("com.taobao.taobao:id/tt_detail_nav_bar_share_icon")
            val hasCartItem = helper.IsExistElementById("com.taobao.taobao:id/tt_detail_cart_item")
            val hasMessageAction = helper.IsExistElementById("com.taobao.taobao:id/uik_public_menu_action_icon")

            Log.d(TAG, "淘宝商品详情页元素检查:")
            Log.d(TAG, "  分享图标 (tt_detail_nav_bar_share_icon): $hasShareIcon")
            Log.d(TAG, "  购物车按钮 (tt_detail_cart_item): $hasCartItem")
            Log.d(TAG, "  消息按钮 (uik_public_menu_action_icon): $hasMessageAction")

            // 只有三个元素都存在时，才判断为商品详情页
            val isProductPage = hasShareIcon && hasCartItem
            Log.d(TAG, "是否为淘宝商品详情页: $isProductPage")

            return isProductPage
        } catch (e: Exception) {
            Log.e(TAG, "判断是否为淘宝商品详情页时出错: ${e.message}", e)
            return false
        }
    }



    /**
     * 处理商品详情页
     */
    private fun handleProductPage(context: Context) {
        Log.d(TAG, "开始处理淘宝商品详情页")

        val helper = AccessibilityHelper.getInstance(context)
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.e(TAG, "无法获取当前活动窗口的根节点")
            CustomToastHelper.showToast(context, "无法获取窗口信息，请重试")
            return
        }

        try {
            val messageActionNodes = rootNode.findAccessibilityNodeInfosByViewId("com.taobao.taobao:id/uik_public_menu_action_icon")
            var clickResult = false

            if (messageActionNodes != null && messageActionNodes.isNotEmpty()) {
                val messageNode = messageActionNodes[0]
                Log.d(TAG, "找到消息按钮节点，准备点击其父节点")

                // 点击父节点的父节点
                val parentNode = messageNode.parent?.parent
                if (parentNode != null) {
                    Log.d(TAG, "点击消息按钮的父节点")
                    clickResult = parentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    parentNode.recycle()
                } else {
                    CustomToastHelper.showToast(context, "未找到复制链接按钮，请稍后再试")
                    Log.e(TAG, "父节点为空，无法执行点击操作")
                    return
                }

                // 回收节点资源
                messageActionNodes.forEach { it.recycle() }
            } else {
                Log.e(TAG, "未找到消息按钮节点")
            }

            Log.d(TAG, "点击分享按钮结果: $clickResult")

            if (!clickResult) {
                Log.e(TAG, "点击分享按钮失败")
                CustomToastHelper.showToast(context, "点击分享按钮失败，请重试")
                return
            }

            // 等待分享面板出现
            Thread.sleep(1000)

            // 查找并点击复制链接按钮
            handleCopyLinkAction(service, context)
        } catch (e: Exception) {
            Log.e(TAG, "处理淘宝商品详情页时出错: ${e.message}", e)
            CustomToastHelper.showToast(context, "处理商品详情页时出错，请重试")
        } finally {
            try {
                rootNode.recycle()
            } catch (e: Exception) {
                Log.e(TAG, "回收根节点资源时出错: ${e.message}")
            }
        }
    }



    /**
     * 处理复制链接操作
     * 查找所有id为com.taobao.taobao:id/uik_public_menu_item_title的节点，
     * 然后过滤出text属性为"复制链接"的节点并点击它的父节点
     */
    private fun handleCopyLinkAction(service: AccessibilityHelper.AppAccessibilityService, context: Context) {
        try {
            val rootNode = service.rootInActiveWindow
            if (rootNode == null) {
                Log.e(TAG, "无法获取根节点")
                CustomToastHelper.showToast(context, "无法获取窗口信息，请重试")
                return
            }

            // 使用Android原生方法查找所有id为uik_public_menu_item_title的节点
            val titleNodes = rootNode.findAccessibilityNodeInfosByViewId("com.taobao.taobao:id/uik_public_menu_item_title")
            Log.d(TAG, "找到 ${titleNodes?.size ?: 0} 个 uik_public_menu_item_title 节点")

            var copyClickResult = false
            var foundCopyLink = false

            if (titleNodes != null && titleNodes.isNotEmpty()) {
                for (titleNode in titleNodes) {
                    try {
                        val nodeText = titleNode.text?.toString()
                        Log.d(TAG, "检查节点文本: '$nodeText'")

                        if (nodeText == "复制链接") {
                            Log.d(TAG, "找到文本为'复制链接'的节点")
                            foundCopyLink = true

                            // 点击父节点
                            val parentNode = titleNode.parent
                            if (parentNode != null) {
                                Log.d(TAG, "点击复制链接节点的父节点")
                                copyClickResult = parentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                                parentNode.recycle()
                            } else {
                                Log.d(TAG, "父节点为空，直接点击复制链接节点")
                                copyClickResult = titleNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                            }

                            Log.d(TAG, "点击复制链接按钮结果: $copyClickResult")
                            break
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "处理单个节点时出错: ${e.message}", e)
                    } finally {
                        titleNode.recycle()
                    }
                }
            } else {
                Log.e(TAG, "未找到任何 uik_public_menu_item_title 节点")
            }

            if (foundCopyLink) {
                if (copyClickResult) {
                    // 通知内容已准备好
                    SharePanelHelper.notifyContentReady()
                    Log.d(TAG, "已复制链接，通知内容准备完毕")
                } else {
                    CustomToastHelper.showToast(context, "复制链接失败，请重试")
                }
            } else {
                Log.e(TAG, "未找到文本为'复制链接'的节点")
                CustomToastHelper.showToast(context, "未找到复制链接选项，请手动操作")
            }

            rootNode.recycle()
        } catch (e: Exception) {
            Log.e(TAG, "处理复制链接操作时出错: ${e.message}", e)
            CustomToastHelper.showToast(context, "复制链接时出错，请重试")
        }
    }

    /**
     * 获取商品标题
     *
     * @param service 无障碍服务实例
     * @return 商品标题，如果获取失败则返回null
     */
    private fun getProductTitle(service: AccessibilityHelper.AppAccessibilityService): String? {
        try {
            val rootNode = service.rootInActiveWindow ?: return null

            // 尝试多种可能的标题选择器
            val titleSelectors = listOf(
                "商品标题",
                "标题",
                "商品名称"
            )

            for (selector in titleSelectors) {
                val titleNodes = service.findNodesByContent(selector)
                if (titleNodes != null && titleNodes.isNotEmpty()) {
                    for (node in titleNodes) {
                        val titleText = node.text?.toString()
                        if (!titleText.isNullOrBlank() && titleText.length > 5) {
                            Log.d(TAG, "通过选择器 '$selector' 获取到商品标题: $titleText")
                            titleNodes.forEach { it.recycle() }
                            return titleText
                        }
                    }
                    titleNodes.forEach { it.recycle() }
                }
            }

            // 如果通过内容查找失败，尝试通过遍历节点查找
            return findTitleByTraversal(rootNode)

        } catch (e: Exception) {
            Log.e(TAG, "获取商品标题时出错: ${e.message}", e)
            return null
        }
    }

    /**
     * 通过遍历节点查找标题
     *
     * @param rootNode 根节点
     * @return 商品标题，如果获取失败则返回null
     */
    private fun findTitleByTraversal(rootNode: AccessibilityNodeInfo): String? {
        try {
            // 递归遍历所有节点，查找可能的标题文本
            return traverseForTitle(rootNode)
        } catch (e: Exception) {
            Log.e(TAG, "遍历节点查找标题时出错: ${e.message}", e)
            return null
        }
    }

    /**
     * 递归遍历节点查找标题
     *
     * @param node 当前节点
     * @return 找到的标题文本，如果没找到则返回null
     */
    private fun traverseForTitle(node: AccessibilityNodeInfo): String? {
        try {
            // 检查当前节点的文本
            val nodeText = node.text?.toString()
            if (!nodeText.isNullOrBlank() && nodeText.length > 10 && nodeText.length < 100) {
                // 过滤掉一些明显不是标题的文本
                if (!nodeText.contains("分享") &&
                    !nodeText.contains("收藏") &&
                    !nodeText.contains("购买") &&
                    !nodeText.contains("加入购物车") &&
                    !nodeText.contains("立即购买") &&
                    !nodeText.contains("￥") &&
                    !nodeText.contains("价格") &&
                    !nodeText.contains("元")) {
                    Log.d(TAG, "通过遍历找到可能的商品标题: $nodeText")
                    return nodeText
                }
            }

            // 递归遍历子节点
            for (i in 0 until node.childCount) {
                val childNode = node.getChild(i)
                if (childNode != null) {
                    val result = traverseForTitle(childNode)
                    if (result != null) {
                        childNode.recycle()
                        return result
                    }
                    childNode.recycle()
                }
            }

            return null
        } catch (e: Exception) {
            Log.e(TAG, "递归遍历节点时出错: ${e.message}", e)
            return null
        }
    }
}
