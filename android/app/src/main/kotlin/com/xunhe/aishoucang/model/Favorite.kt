package com.xunhe.aishoucang.model

import java.util.Date

/**
 * 收藏夹模型类
 *
 * 对应服务器端的Favorite结构
 */
data class Favorite(
    /**
     * MongoDB 文档ID
     */
    val id: String? = null,
    
    /**
     * 用户ID
     */
    val userId: String,
    
    /**
     * 收藏夹名称
     */
    val name: String,
    
    /**
     * 封面图片URL
     */
    val cover: String,
    
    /**
     * 创建时间
     */
    val createTime: Date,
    
    /**
     * 最后更新时间
     */
    val updateTime: Date
) {
    /**
     * 转换为JSON格式的Map
     */
    fun toMap(): Map<String, Any> {
        val map = mutableMapOf<String, Any>(
            "user_id" to userId,
            "name" to name,
            "cover" to cover,
            "create_time" to createTime.time,
            "update_time" to updateTime.time
        )
        
        // 只有在id不为空时才添加到map中
        id?.let { map["_id"] = it }
        
        return map
    }
    
    companion object {
        /**
         * 从JSON Map创建Favorite对象
         */
        fun fromMap(map: Map<String, Any>): Favorite {
            return Favorite(
                id = map["_id"] as? String,
                userId = map["user_id"] as String,
                name = map["name"] as String,
                cover = map["cover"] as String,
                createTime = Date((map["create_time"] as Number).toLong()),
                updateTime = Date((map["update_time"] as Number).toLong())
            )
        }
    }
}
