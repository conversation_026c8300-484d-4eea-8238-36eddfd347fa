package com.xunhe.aishoucang.api

import android.content.Context
import com.xunhe.aishoucang.lib.RequestHelper
import com.xunhe.aishoucang.model.Favorite
import com.xunhe.aishoucang.helpers.ConfigHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone

/**
 * 收藏夹相关API
 */
class FavoriteApi(private val context: Context) {
    
    private val requestHelper = RequestHelper.getInstance(context)
    
    /**
     * 获取收藏夹列表
     * 
     * @param userId 用户ID
     * @param page 页码，默认为1
     * @param pageSize 每页数量，默认为12
     * @return 收藏夹列表结果
     */
    suspend fun getFavoriteList(userId: String, page: Int = 1, pageSize: Int = 12): Result<List<Favorite>> = withContext(Dispatchers.IO) {
        try {
            // 构建请求参数，包含分页信息
            val params = mapOf(
                "user_id" to userId,
                "page" to page.toString(),
                "page_size" to pageSize.toString()
            )
            
            // 使用ConfigHelper属性访问API基地址
            val apiBaseUrl = ConfigHelper.getString("api_base_url")
            
            // 发送GET请求
            val response = requestHelper.get("$apiBaseUrl/favorites/list", params)
            
            when (response) {
                is RequestHelper.ApiResult.Success -> {
                    // 解析JSON响应
                    val jsonResponse = JSONObject(response.data)
                    
                    // 检查API响应状态
                    if (jsonResponse.optInt("code", -1) == 0) {
                        // 获取data对象
                        val dataObject = jsonResponse.optJSONObject("data")
                        if (dataObject != null) {
                            // 从 data.favorites 获取收藏夹数组
                            val favoritesArray = dataObject.optJSONArray("favorites") ?: JSONArray()
                            val favorites = mutableListOf<Favorite>()
                            
                            // 解析每个收藏夹对象
                            for (i in 0 until favoritesArray.length()) {
                                val favoriteJson = favoritesArray.getJSONObject(i)
                                favorites.add(parseFavorite(favoriteJson))
                            }
                            
                            // 打印日志便于调试
                            println("FavoriteApi: 解析到${favorites.size}个收藏夹")
                            
                            Result.success(favorites)
                        } else {
                            // data对象为空
                            Result.success(emptyList())
                        }
                    } else {
                        // API返回错误
                        val message = jsonResponse.optString("message", "未知错误")
                        Result.failure(Exception("API错误: $message"))
                    }
                }
                is RequestHelper.ApiResult.Error -> {
                    Result.failure(Exception("HTTP错误: ${response.code}, ${response.message}"))
                }
                is RequestHelper.ApiResult.Exception -> {
                    Result.failure(response.throwable)
                }
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 解析收藏夹JSON对象
     */
    private fun parseFavorite(json: JSONObject): Favorite {
        // 创建ISO 8601日期格式解析器
        val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
        dateFormat.timeZone = TimeZone.getTimeZone("UTC")
        
        // 解析时间字符串
        val createTimeStr = json.optString("create_time", "")
        val updateTimeStr = json.optString("update_time", "")
        
        val createTime = if (createTimeStr.isNotEmpty()) {
            try {
                dateFormat.parse(createTimeStr) ?: Date()
            } catch (e: Exception) {
                Date()
            }
        } else {
            Date()
        }
        
        val updateTime = if (updateTimeStr.isNotEmpty()) {
            try {
                dateFormat.parse(updateTimeStr) ?: Date()
            } catch (e: Exception) {
                Date()
            }
        } else {
            Date()
        }
        
        return Favorite(
            id = json.optString("id"),  // 使用"id"而非"_id"
            userId = json.optString("user_id", ""),  // 使用optString避免空值异常
            name = json.optString("name", ""),
            cover = json.optString("cover", ""),
            createTime = createTime,
            updateTime = updateTime
        )
    }
}
