package com.xunhe.aishoucang.helpers

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import okhttp3.*
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.TimeUnit
import java.security.MessageDigest

/**
 * 网络资源转OSS链接工具类
 * 用于将网络资源下载到本地，然后上传到OSS，最后返回OSS链接
 */
object SourceToOssLinkHelper {
    private const val TAG = "SourceToOssLinkHelper"
    private const val TIMEOUT_SECONDS = 60L
    private const val BUFFER_SIZE = 4096
    private const val UPLOAD_API_ENDPOINT = "/upload/file" // 上传接口地址

    // 主线程Handler，用于回调到主线程
    private val mainHandler = Handler(Looper.getMainLooper())

    // 下载结果回调接口
    interface DownloadCallback {
        fun onSuccess(file: File)
        fun onFailure(error: Exception)
    }

    // OSS上传结果回调接口
    interface OssUploadCallback {
        fun onSuccess(ossUrl: String)
        fun onFailure(error: Exception)
    }

    // 资源转换结果回调接口
    interface ConvertCallback {
        fun onSuccess(ossUrl: String)
        fun onFailure(error: Exception)
    }

    // OkHttp客户端，用于网络请求
    private val client = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .build()

    /**
     * 将网络资源链接转换为OSS链接（同步方法）
     * 注意：此方法会阻塞调用线程，不推荐在主线程中使用
     *
     * @param context 上下文
     * @param sourceUrl 源网络资源链接
     * @return OSS链接，如果失败则抛出异常
     */
    @Throws(IOException::class)
    fun convertToOssLink(context: Context, sourceUrl: String): String {
        // 使用同步方式下载文件
        val downloadedFile = downloadFile(context, sourceUrl)
        try {
            // 使用同步方式上传文件
            return uploadToOssSync(context, downloadedFile)
        } finally {
            // 删除缓存文件
            if (downloadedFile.exists()) {
                val deleted = downloadedFile.delete()
                Log.d(TAG, "删除缓存文件 ${downloadedFile.absolutePath}: ${if (deleted) "成功" else "失败"}")
            }
        }
    }

    /**
     * 将本地文件上传到OSS并返回OSS链接（同步方法）
     * 注意：此方法会阻塞调用线程，不推荐在主线程中使用
     *
     * @param context 上下文
     * @param filePath 本地文件路径
     * @param compress 是否压缩图片，默认为true
     * @return OSS链接，如果失败则抛出异常
     */
    @Throws(IOException::class)
    fun uploadLocalFileToOss(context: Context, filePath: String, compress: Boolean = true): String {
        val file = File(filePath)
        if (!file.exists()) {
            throw IOException("文件不存在: $filePath")
        }

        Log.d(TAG, "开始上传本地文件: $filePath, 文件大小: ${file.length()} 字节")

        // 根据compress参数决定是否压缩
        val fileToUpload: File
        var compressedFilePath: String? = null

        if (compress && filePath.endsWith(".jpg", ignoreCase = true) ||
            filePath.endsWith(".jpeg", ignoreCase = true) ||
            filePath.endsWith(".png", ignoreCase = true)) {
            // 压缩图片
            Log.d(TAG, "压缩图片...")
            val imageCompressHelper = com.xunhe.aishoucang.lib.ImageCompressHelper.getInstance(context)
            compressedFilePath = imageCompressHelper.compressImage(filePath, 85)
            fileToUpload = File(compressedFilePath)
            Log.d(TAG, "图片压缩完成，压缩后路径: $compressedFilePath, 压缩后大小: ${fileToUpload.length()} 字节")
        } else {
            // 不压缩，直接使用原文件
            Log.d(TAG, "跳过压缩，直接上传原文件")
            fileToUpload = file
        }

        try {
            // 使用同步方式上传文件
            return uploadToOssSync(context, fileToUpload)
        } finally {
            // 如果创建了压缩文件，删除它
            if (compressedFilePath != null && compressedFilePath != filePath) {
                try {
                    val compressedFile = File(compressedFilePath)
                    if (compressedFile.exists()) {
                        val deleted = compressedFile.delete()
                        Log.d(TAG, "删除压缩后的文件: ${if (deleted) "成功" else "失败"}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "删除压缩后的文件失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 将网络资源链接转换为OSS链接（异步方法）
     *
     * @param context 上下文
     * @param sourceUrl 源网络资源链接
     * @param callback 回调接口，返回OSS链接或异常
     */
    fun convertToOssLinkAsync(context: Context, sourceUrl: String, callback: ConvertCallback) {
        Log.d(TAG, "开始异步处理资源: $sourceUrl")

        // 1. 异步下载资源到缓存目录
        downloadFileAsync(context, sourceUrl, object : DownloadCallback {
            override fun onSuccess(downloadedFile: File) {
                Log.d(TAG, "下载完成: ${downloadedFile.absolutePath}, 文件大小: ${downloadedFile.length()} 字节")

                // 2. 异步上传资源到OSS
                uploadToOssAsync(context, downloadedFile, object : OssUploadCallback {
                    override fun onSuccess(ossUrl: String) {
                        Log.d(TAG, "上传完成，获取到OSS链接: $ossUrl")

                        // 3. 删除缓存文件
                        if (downloadedFile.exists()) {
                            val deleted = downloadedFile.delete()
                            Log.d(TAG, "删除缓存文件 ${downloadedFile.absolutePath}: ${if (deleted) "成功" else "失败"}")
                        }

                        // 4. 回调成功结果
                        mainHandler.post {
                            callback.onSuccess(ossUrl)
                        }
                    }

                    override fun onFailure(error: Exception) {
                        Log.e(TAG, "上传到OSS失败: ${error.message}", error)

                        // 删除缓存文件
                        if (downloadedFile.exists()) {
                            downloadedFile.delete()
                        }

                        // 回调失败结果
                        mainHandler.post {
                            callback.onFailure(error)
                        }
                    }
                })
            }

            override fun onFailure(error: Exception) {
                Log.e(TAG, "下载资源失败: ${error.message}", error)

                // 回调失败结果
                mainHandler.post {
                    callback.onFailure(error)
                }
            }
        })
    }

    /**
     * 将本地文件上传到OSS并返回OSS链接（异步方法）
     *
     * @param context 上下文
     * @param filePath 本地文件路径
     * @param compress 是否压缩图片，默认为true
     * @param callback 回调接口，返回OSS链接或异常
     */
    fun uploadLocalFileToOssAsync(context: Context, filePath: String, compress: Boolean = true, callback: OssUploadCallback) {
        // 在后台线程处理文件压缩和上传
        Thread {
            try {
                val file = File(filePath)
                if (!file.exists()) {
                    mainHandler.post {
                        callback.onFailure(IOException("文件不存在: $filePath"))
                    }
                    return@Thread
                }

                Log.d(TAG, "开始上传本地文件: $filePath, 文件大小: ${file.length()} 字节")

                // 根据compress参数决定是否压缩
                val fileToUpload: File
                var compressedFilePath: String? = null

                if (compress && filePath.endsWith(".jpg", ignoreCase = true) ||
                    filePath.endsWith(".jpeg", ignoreCase = true) ||
                    filePath.endsWith(".png", ignoreCase = true)) {
                    // 压缩图片
                    Log.d(TAG, "压缩图片...")
                    val imageCompressHelper = com.xunhe.aishoucang.lib.ImageCompressHelper.getInstance(context)
                    compressedFilePath = imageCompressHelper.compressImage(filePath, 85)
                    fileToUpload = File(compressedFilePath)
                    Log.d(TAG, "图片压缩完成，压缩后路径: $compressedFilePath, 压缩后大小: ${fileToUpload.length()} 字节")
                } else {
                    // 不压缩，直接使用原文件
                    Log.d(TAG, "跳过压缩，直接上传原文件")
                    fileToUpload = file
                }

                // 异步上传到OSS
                uploadToOssAsync(context, fileToUpload, object : OssUploadCallback {
                    override fun onSuccess(ossUrl: String) {
                        // 如果创建了压缩文件，删除它
                        if (compressedFilePath != null && compressedFilePath != filePath) {
                            try {
                                val compressedFile = File(compressedFilePath)
                                if (compressedFile.exists()) {
                                    val deleted = compressedFile.delete()
                                    Log.d(TAG, "删除压缩后的文件: ${if (deleted) "成功" else "失败"}")
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "删除压缩后的文件失败: ${e.message}")
                            }
                        }

                        // 回调成功结果
                        mainHandler.post {
                            callback.onSuccess(ossUrl)
                        }
                    }

                    override fun onFailure(error: Exception) {
                        // 如果创建了压缩文件，删除它
                        if (compressedFilePath != null && compressedFilePath != filePath) {
                            try {
                                val compressedFile = File(compressedFilePath)
                                if (compressedFile.exists()) {
                                    compressedFile.delete()
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "删除压缩后的文件失败: ${e.message}")
                            }
                        }

                        // 回调失败结果
                        mainHandler.post {
                            callback.onFailure(error)
                        }
                    }
                })
            } catch (e: Exception) {
                Log.e(TAG, "处理本地文件时出错: ${e.message}", e)
                mainHandler.post {
                    callback.onFailure(e)
                }
            }
        }.start()
    }

    /**
     * 下载文件到缓存目录
     *
     * @param context 上下文
     * @param url 文件URL
     * @return 下载的文件
     */
    @Throws(IOException::class)
    private fun downloadFile(context: Context, url: String): File {
        // 创建请求
        val request = Request.Builder().url(url).build()
        // 记录开始时间
        val startTime = System.currentTimeMillis()
        client.newCall(request).execute().use { response ->
            // 计算并打印响应时间
            val responseTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "网络请求响应时间: ${responseTime}ms, URL: $url")
            if (!response.isSuccessful) {
                throw IOException("下载失败: {response.code}")
            }

            // 计算url的md5
            val md5Name = md5(url)

            // 根据Content-Type决定扩展名
            val contentType = response.header("Content-Type") ?: ""
            val ext = contentTypeToExtension(contentType)

            // 最终文件名: md5 + 扩展名
            val fileName = "$md5Name$ext"
            Log.d(TAG, "下载文件，使用临时文件名: $fileName, Content-Type: $contentType")

            // 获取缓存目录
            val cacheDir = context.cacheDir
            val outputFile = File(cacheDir, fileName)

            response.body?.let { body ->
                outputFile.outputStream().use { fileOut ->
                    body.byteStream().use { inputStream ->
                        val buffer = ByteArray(BUFFER_SIZE)
                        var bytesRead: Int
                        var totalBytesRead: Long = 0

                        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                            fileOut.write(buffer, 0, bytesRead)
                            totalBytesRead += bytesRead

                            // 记录下载进度
                            if (totalBytesRead % (1024 * 100) == 0L) { // 每100KB记录一次
                                Log.d(TAG, "已下载: $totalBytesRead 字节")
                            }
                        }

                        fileOut.flush()
                    }
                }
            } ?: throw IOException("响应体为空")

            return outputFile
        }
    }

    /**
     * 下载文件到缓存目录（异步方法）
     *
     * @param context 上下文
     * @param url 文件URL
     * @param callback 回调接口，返回下载的文件或异常
     */
    private fun downloadFileAsync(context: Context, url: String, callback: DownloadCallback) {
        // 创建请求
        val request = Request.Builder().url(url).build()
        // 记录开始时间
        val startTime = System.currentTimeMillis()

        // 使用OkHttp的异步API
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "下载失败: ${e.message}", e)
                mainHandler.post {
                    callback.onFailure(e)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                var outputFile: File? = null

                try {
                    // 计算并打印响应时间
                    val responseTime = System.currentTimeMillis() - startTime
                    Log.d(TAG, "网络请求响应时间: ${responseTime}ms, URL: $url")

                    if (!response.isSuccessful) {
                        throw IOException("下载失败: ${response.code}")
                    }

                    // 计算url的md5
                    val md5Name = md5(url)

                    // 根据Content-Type决定扩展名
                    val contentType = response.header("Content-Type") ?: ""
                    val ext = contentTypeToExtension(contentType)

                    // 最终文件名: md5 + 扩展名
                    val fileName = "$md5Name$ext"
                    Log.d(TAG, "下载文件，使用临时文件名: $fileName, Content-Type: $contentType")

                    // 获取缓存目录
                    val cacheDir = context.cacheDir
                    outputFile = File(cacheDir, fileName)

                    response.body?.let { body ->
                        outputFile.outputStream().use { fileOut ->
                            body.byteStream().use { inputStream ->
                                val buffer = ByteArray(BUFFER_SIZE)
                                var bytesRead: Int
                                var totalBytesRead: Long = 0

                                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                                    fileOut.write(buffer, 0, bytesRead)
                                    totalBytesRead += bytesRead

                                    // 记录下载进度
                                    if (totalBytesRead % (1024 * 100) == 0L) { // 每100KB记录一次
                                        Log.d(TAG, "已下载: $totalBytesRead 字节")
                                    }
                                }

                                fileOut.flush()
                            }
                        }

                        Log.d(TAG, "下载完成: ${outputFile.absolutePath}, 文件大小: ${outputFile.length()} 字节")
                        mainHandler.post {
                            callback.onSuccess(outputFile)
                        }
                    } ?: throw IOException("响应体为空")

                } catch (e: Exception) {
                    Log.e(TAG, "处理下载响应时出错: ${e.message}", e)
                    // 如果处理过程中出错，删除可能创建的文件
                    if (outputFile != null && outputFile.exists()) {
                        outputFile.delete()
                    }
                    mainHandler.post {
                        callback.onFailure(e)
                    }
                } finally {
                    // 确保关闭响应
                    response.close()
                }
            }
        })
    }

    // Content-Type到扩展名的简单映射
    private fun contentTypeToExtension(contentType: String): String {
        return when (contentType.substringBefore(';').trim().lowercase()) {
            "image/jpeg", "image/jpg" -> ".jpg"
            "image/png" -> ".png"
            "image/gif" -> ".gif"
            "image/webp" -> ".webp"
            "image/bmp" -> ".bmp"
            "image/svg+xml" -> ".svg"
            "application/pdf" -> ".pdf"
            "text/plain" -> ".txt"
            "text/html" -> ".html"
            "application/zip" -> ".zip"
            "application/json" -> ".json"
            "audio/mpeg" -> ".mp3"
            "audio/wav" -> ".wav"
            "video/mp4" -> ".mp4"
            else -> "" // 未知类型不加扩展名
        }
    }

    // 计算字符串的md5
    private fun md5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray())
        return digest.joinToString("") { "%02x".format(it) }
    }

    /**
     * 上传文件到OSS（同步方法）
     *
     * @param context 上下文
     * @param file 要上传的文件
     * @return OSS链接
     */
    @Throws(IOException::class)
    private fun uploadToOssSync(context: Context, file: File): String {
        try {
            Log.d(TAG, "使用OssManager同步上传文件: ${file.absolutePath}")

            // 获取OssManager实例
            val ossManager = OssManager.getInstance(context)

            // 调用OssManager的uploadFileSync方法同步上传文件
            val ossUrl = ossManager.uploadFileSync(file.absolutePath)

            Log.d(TAG, "OssManager上传成功，OSS链接: $ossUrl")
            return ossUrl
        } catch (e: Exception) {
            Log.e(TAG, "OssManager上传失败: ${e.message}", e)
            throw IOException("上传文件失败: ${e.message}", e)
        }
    }

    /**
     * 上传文件到OSS（异步方法）
     *
     * @param context 上下文
     * @param file 要上传的文件
     * @param callback 回调接口，返回OSS链接或异常
     */
    private fun uploadToOssAsync(context: Context, file: File, callback: OssUploadCallback) {
        // 在后台线程处理上传
        Thread {
            try {
                Log.d(TAG, "使用OssManager异步上传文件: ${file.absolutePath}")

                // 获取OssManager实例
                val ossManager = OssManager.getInstance(context)

                // 调用OssManager的uploadFileSync方法上传文件（在后台线程中执行）
                val ossUrl = ossManager.uploadFileSync(file.absolutePath)

                Log.d(TAG, "OssManager上传成功，OSS链接: $ossUrl")
                mainHandler.post {
                    callback.onSuccess(ossUrl)
                }
            } catch (e: Exception) {
                Log.e(TAG, "OssManager上传失败: ${e.message}", e)
                mainHandler.post {
                    callback.onFailure(e)
                }
            }
        }.start()
    }
}