package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.widget.Toast


/**
 * 快手应用处理器
 */
class KuaishouAppHandler : AppHandler {
    companion object {
        private const val TAG = "<PERSON><PERSON>houAppHandler"
    }

    override fun handle(context: Context) {
        Toast.makeText(context, "正在处理快手应用", Toast.LENGTH_SHORT)
            .show()
        Log.d(TAG, "正在处理快手应用")

        // 暂时直接通知内容准备完成
        SharePanelHelper.notifyContentReady()

        // TODO: 实现快手特定的操作逻辑
    }
}
