package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import org.json.JSONObject
import java.io.IOException

/**
 * 配置工具类
 * 根据环境变量APP_ENV加载不同的配置文件:
 * - 优先尝试加载assets/config.{APP_ENV}.json
 * - 如果找不到环境特定的配置文件，则使用assets/config.json作为兜底
 * 使用动态方法访问配置，无需硬编码键名
 */
object ConfigHelper {
    private const val TAG = "ConfigHelper"
    private const val DEFAULT_CONFIG_FILE = "config.json"

    // 环境变量名
    private const val ENV_APP_ENV = "APP_ENV"

    // 环境类型
    private const val ENV_DEVELOP = "develop"
    private const val ENV_PRE = "pre"
    private const val ENV_PRD = "prd"

    // 配置项缓存
    private var configCache: JSONObject? = null

    // 默认值 - 仅在配置文件无法加载时使用
    private val defaultValues = mutableMapOf<String, Any>(
        "api_base_url" to "https://api.xunhewenhua.com" // 默认使用生产环境API地址作为兜底
    )

    /**
     * 初始化配置
     * 应在应用启动时调用
     */
    @JvmStatic
    fun init(context: Context) {
        try {
            // 获取当前环境
            val appEnv = System.getenv(ENV_APP_ENV) ?: ENV_DEVELOP
            Log.d(TAG, "当前运行环境: $appEnv")

            // 同步加载配置
            val json = readConfigFile(context)
            configCache = JSONObject(json)

            // 打印读取到的所有配置项，便于调试
            val keys = configCache?.keys()
            if (keys != null) {
                Log.d(TAG, "已读取的配置项:")
                while (keys.hasNext()) {
                    val key = keys.next()
                    val value = configCache?.opt(key)
                    Log.d(TAG, "  $key = $value")
                }
            }

            // 打印API基址，这是最常用的配置项
            val apiBaseUrl = getString("api_base_url")
            Log.d(TAG, "当前API基址: $apiBaseUrl")

            Log.d(TAG, "配置初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "配置初始化失败: ${e.message}", e)
        }
    }

    /**
     * 获取任意配置项
     *
     * @param key 配置键名
     * @param defaultValue 默认值(可选)
     */
    @JvmStatic
    fun <T> getValue(key: String, defaultValue: T? = null): T? {
        @Suppress("UNCHECKED_CAST")
        val value = configCache?.opt(key) ?: defaultValues[key] ?: defaultValue
        return value as? T
    }

    /**
     * 获取字符串配置项
     * 更方便的访问字符串配置
     *
     * @param key 配置键名
     * @param defaultValue 默认值(可选)
     */
    @JvmStatic
    fun getString(key: String, defaultValue: String = ""): String {
        return getValue(key, defaultValue) ?: defaultValue
    }

    /**
     * 获取整数配置项
     *
     * @param key 配置键名
     * @param defaultValue 默认值(可选)
     */
    @JvmStatic
    fun getInt(key: String, defaultValue: Int = 0): Int {
        return getValue(key, defaultValue) ?: defaultValue
    }

    /**
     * 获取布尔配置项
     *
     * @param key 配置键名
     * @param defaultValue 默认值(可选)
     */
    @JvmStatic
    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return getValue(key, defaultValue) ?: defaultValue
    }

    /**
     * 读取配置文件内容
     * 尝试根据环境变量加载对应的配置文件，如果失败则使用默认的config.json
     */
    @Throws(IOException::class)
    private fun readConfigFile(context: Context): String {
        // 获取环境变量
        val appEnv = System.getenv(ENV_APP_ENV)

        // 如果有环境变量，尝试加载对应的配置文件
        if (!appEnv.isNullOrEmpty()) {
            val envConfigFile = "config.$appEnv.json"
            try {
                // 尝试打开环境特定的配置文件
                val inputStream = context.assets.open(envConfigFile)
                val configContent = inputStream.bufferedReader().use { it.readText() }
                Log.d(TAG, "成功加载环境配置文件: $envConfigFile")
                return configContent
            } catch (e: IOException) {
                // 如果找不到环境特定的配置文件，记录日志
                Log.w(TAG, "未找到环境配置文件 $envConfigFile，将使用默认配置文件")
            }
        }

        // 使用默认配置文件
        Log.d(TAG, "使用默认配置文件: $DEFAULT_CONFIG_FILE")
        val inputStream = context.assets.open(DEFAULT_CONFIG_FILE)
        return inputStream.bufferedReader().use { it.readText() }
    }

    /**
     * 获取所有配置项
     * 便于Flutter桥接使用
     */
    @JvmStatic
    fun getAllConfig(): Map<String, Any?> {
        val result = mutableMapOf<String, Any?>()

        // 添加默认值
        result.putAll(defaultValues)

        // 添加从配置文件读取的值，覆盖默认值
        configCache?.let { json ->
            val keys = json.keys()
            while (keys.hasNext()) {
                val key = keys.next()
                result[key] = json.opt(key)
            }
        }

        return result
    }
}