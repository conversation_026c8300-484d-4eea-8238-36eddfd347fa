package com.xunhe.aishoucang.views.share_panel

import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.api.NoteItem

/**
 * 笔记面板适配器
 */
class NotePanelAdapter(
    private val context: Context,
    initialNotes: MutableList<NoteItem>,
    private val onNoteClick: (NoteItem) -> Unit
) : RecyclerView.Adapter<NotePanelAdapter.NoteViewHolder>() {

    // 使用自己的列表副本，避免引用问题
    private val notes: MutableList<NoteItem> = mutableListOf<NoteItem>().apply {
        addAll(initialNotes)
    }

    companion object {
        private const val TAG = "NotePanelAdapter"
        private const val COLUMNS = 4 // 每行显示4个笔记
    }

    class NoteViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val frameLayout: FrameLayout = itemView.findViewById<View>(R.id.note_background).parent as FrameLayout
        val backgroundView: View = itemView.findViewById<View>(R.id.note_background)
        val coverImageView: ImageView = itemView.findViewById<ImageView>(R.id.note_cover)
        val textView: TextView = itemView.findViewById<TextView>(R.id.note_text)
        val rootLayout: LinearLayout = itemView as LinearLayout
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NoteViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.note_panel_item, parent, false)
        return NoteViewHolder(view)
    }

    override fun onBindViewHolder(holder: NoteViewHolder, position: Int) {
        val note = notes[position]

        // 设置笔记标题
        holder.textView.text = note.title
        holder.textView.setTextColor(Color.WHITE)

        // 设置封面或背景
        if (!note.cover.isNullOrEmpty()) {
            // 有封面，加载封面图片
            holder.coverImageView.visibility = View.VISIBLE
            Glide.with(context)
                .load(note.cover)
                .apply(RequestOptions()
                    .centerCrop()
                    .placeholder(R.drawable.note_item_background)
                    .error(R.drawable.note_item_background))
                .into(holder.coverImageView)
            
            // 隐藏背景色，让封面图片完全显示
            holder.backgroundView.setBackgroundColor(Color.TRANSPARENT)
        } else {
            // 没有封面，使用默认背景
            holder.coverImageView.visibility = View.GONE
            holder.backgroundView.setBackgroundColor(Color.parseColor("#F6F6F6"))
        }

        // 设置点击事件
        holder.rootLayout.setOnClickListener {
            onNoteClick(note)
        }
    }

    override fun getItemCount(): Int {
        return notes.size
    }

    /**
     * 更新笔记列表
     */
    fun updateNotes(newNotes: List<NoteItem>) {
        Log.d(TAG, "updateNotes called with ${newNotes.size} notes")
        notes.clear()
        notes.addAll(newNotes)
        notifyDataSetChanged()
        Log.d(TAG, "适配器更新完成，当前数量: ${notes.size}")
    }

    /**
     * 添加笔记
     */
    fun addNotes(newNotes: List<NoteItem>) {
        val startPosition = notes.size
        notes.addAll(newNotes)
        notifyItemRangeInserted(startPosition, newNotes.size)
    }

    /**
     * 清空笔记列表
     */
    fun clearNotes() {
        val size = notes.size
        notes.clear()
        notifyItemRangeRemoved(0, size)
    }
}
