package com.xunhe.aishoucang.helpers.hooks

import android.content.Context
import android.util.Log
import android.view.WindowManager
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.helpers.SharePanelHelper

/**
 * 小红书应用面板显示前的钩子实现
 */
class XiaoHongshuBeforePanelShow : BeforePanelShowHook {
    companion object {
        private const val TAG = "XiaoHongshuBeforePanelShow"
        private const val PACKAGE_XIAOHONGSHU = "com.xingin.xhs"
    }

    /**
     * 判断当前应用是否是小红书
     */
    override fun isApplicable(packageName: String): Boolean {
        return packageName.startsWith(PACKAGE_XIAOHONGSHU)
    }

    /**
     * 执行小红书特定的预处理逻辑
     * 主要是设置面板不可获取焦点，使无障碍服务能操作后台应用
     */
    override fun execute(context: Context, rootNode: AccessibilityNodeInfo?) {
        if (rootNode == null) return

        Log.d(TAG, "当前应用是小红书，执行特定的预处理逻辑")

        // 设置面板不可获取焦点，使无障碍服务能操作后台应用
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        SharePanelHelper.setPanelCannotFocus(windowManager)
        
        // 打印当前界面信息
        val packageName = rootNode.packageName?.toString() ?: "未知"
        val className = rootNode.className?.toString() ?: "未知"
        Log.d(TAG, "当前小红书界面: $packageName/$className")
    }
}
