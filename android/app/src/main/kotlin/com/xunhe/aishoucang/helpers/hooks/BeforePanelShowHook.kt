package com.xunhe.aishoucang.helpers.hooks

import android.content.Context
import android.view.accessibility.AccessibilityNodeInfo

/**
 * 面板显示前的钩子接口
 * 用于在显示分享面板前执行特定应用的预处理逻辑
 */
interface BeforePanelShowHook {
    /**
     * 判断当前应用是否适用于此钩子
     * 
     * @param packageName 当前应用包名
     * @return 是否适用
     */
    fun isApplicable(packageName: String): Boolean
    
    /**
     * 执行面板显示前的处理逻辑
     * 
     * @param context 上下文
     * @param rootNode 当前活动窗口的根节点
     */
    fun execute(context: Context, rootNode: AccessibilityNodeInfo?)
}
