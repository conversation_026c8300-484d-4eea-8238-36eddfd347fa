package com.xunhe.aishoucang.lib

import android.graphics.Bitmap
import android.graphics.Color
import android.util.Log
import android.widget.Toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * 长截图服务结果处理
 * 包含图像相似度计算、保存和上传的方法
 */
internal fun calculateSimilarity(bitmap1: Bitmap, bitmap2: Bitmap): Double {
    try {
        if (bitmap1.width != bitmap2.width || bitmap1.height != bitmap2.height) {
            return 0.0
        }

        var samePixels = 0
        val totalPixels = bitmap1.width * bitmap1.height

        for (y in 0 until bitmap1.height) {
            for (x in 0 until bitmap1.width) {
                val pixel1 = bitmap1.getPixel(x, y)
                val pixel2 = bitmap2.getPixel(x, y)

                // 计算颜色差异
                val rDiff = Math.abs(Color.red(pixel1) - Color.red(pixel2))
                val gDiff = Math.abs(Color.green(pixel1) - Color.green(pixel2))
                val bDiff = Math.abs(Color.blue(pixel1) - Color.blue(pixel2))

                // 如果颜色差异小于阈值，认为是相同像素
                if (rDiff + gDiff + bDiff < 30) {
                    samePixels++
                }
            }
        }

        return samePixels.toDouble() / totalPixels.toDouble()
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "计算相似度失败", e)
        return 0.0
    }
}

/**
 * 保存Bitmap到文件
 */
internal fun saveBitmapToFile(service: LongScreenshotService, bitmap: Bitmap): File? {
    try {
        // 创建临时文件
        val fileName = "long_screenshot_${System.currentTimeMillis()}.jpg"
        val file = File(service.cacheDir, fileName)

        // 保存Bitmap到文件，使用100%质量以保持最佳图像质量
        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)
            out.flush()
        }

        return file
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "保存图像失败", e)
        return null
    }
}

/**
 * 处理长截图结果
 */
internal fun processLongScreenshotResult(service: LongScreenshotService, filePath: String) {
    GlobalScope.launch {
        try {
            // 更新进度
            service.longScreenshotHelper?.updateProgress(85, "正在处理长截图...")

            // 直接上传原始图片到OSS，使用OssManager
            val ossManager = com.xunhe.aishoucang.helpers.OssManager.getInstance(service)
            val ossUrl = ossManager.uploadFile(filePath)
            Log.i(LongScreenshotService.TAG, "长截图上传成功，OSS链接: $ossUrl")

            // 更新进度
            service.longScreenshotHelper?.updateProgress(90, "正在生成封面...")

            // 生成压缩版的封面图片
            val imageCompressHelper = ImageCompressHelper.getInstance(service)
            // 使用较低的质量和较小的尺寸来生成封面
            val coverFilePath = imageCompressHelper.compressImage(filePath, quality = 60, maxSize = 800)
            Log.d(LongScreenshotService.TAG, "封面图片生成完成，路径: $coverFilePath")

            // 更新进度
            service.longScreenshotHelper?.updateProgress(95, "正在上传封面...")

            // 上传封面图片到OSS，使用已创建的OssManager实例
            val coverOssUrl = ossManager.uploadFile(coverFilePath)
            Log.i(LongScreenshotService.TAG, "封面图片上传成功，OSS链接: $coverOssUrl")

            // 显示输入对话框
            withContext(Dispatchers.Main) {
                // 更新进度
                service.longScreenshotHelper?.updateProgress(100, "长截图完成")

                // 立即隐藏暂停按钮，不使用动画
                service.pauseButton?.hideImmediately()

                // 显示书签输入对话框，传递原始图片URL和封面URL
                val bookmarkInputDialog = BookmarkInputDialog(service)
                bookmarkInputDialog.showWithCover(ossUrl, coverOssUrl) {
                    // 成功添加书签后的回调
                    Toast.makeText(service, "已添加到收藏夹", Toast.LENGTH_SHORT).show()
                    service.stopLongScreenshot()
                }
            }

            // 删除临时文件
            try {
                // 删除原始文件
                val originalFile = File(filePath)
                if (originalFile.exists()) {
                    val deleted = originalFile.delete()
                    Log.d(LongScreenshotService.TAG, "删除原始文件: ${if (deleted) "成功" else "失败"}")
                }

                // 删除封面文件（如果与原始文件不同）
                if (coverFilePath != filePath) {
                    val coverFile = File(coverFilePath)
                    if (coverFile.exists()) {
                        val deleted = coverFile.delete()
                        Log.d(LongScreenshotService.TAG, "删除封面文件: ${if (deleted) "成功" else "失败"}")
                    }
                }
            } catch (e: Exception) {
                Log.e(LongScreenshotService.TAG, "删除临时文件失败: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e(LongScreenshotService.TAG, "处理长截图结果失败", e)

            withContext(Dispatchers.Main) {
                Toast.makeText(service, "处理长截图失败: ${e.message}", Toast.LENGTH_SHORT).show()
                service.stopLongScreenshot()
            }
        }
    }
}
