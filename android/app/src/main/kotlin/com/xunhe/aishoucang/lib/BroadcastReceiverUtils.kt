package com.xunhe.aishoucang.lib

import android.content.BroadcastReceiver
import android.content.Context
import android.content.IntentFilter
import android.os.Build
import android.util.Log

/**
 * 广播接收器工具类
 * 用于处理Android 13+的广播接收器注册兼容性问题
 */
object BroadcastReceiverUtils {
    private const val TAG = "BroadcastReceiverUtils"

    /**
     * 安全注册广播接收器
     * 自动处理Android 13+的RECEIVER_EXPORTED/RECEIVER_NOT_EXPORTED要求
     * 
     * @param context 上下文
     * @param receiver 广播接收器
     * @param filter 意图过滤器
     * @param exported 是否导出（true=RECEIVER_EXPORTED, false=RECEIVER_NOT_EXPORTED）
     * @param logTag 日志标签，用于错误日志
     * @return 是否注册成功
     */
    fun safeRegisterReceiver(
        context: Context,
        receiver: BroadcastReceiver,
        filter: IntentFilter,
        exported: Boolean = false,
        logTag: String = TAG
    ): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ 需要明确指定导出标志
                val flag = if (exported) {
                    Context.RECEIVER_EXPORTED
                } else {
                    Context.RECEIVER_NOT_EXPORTED
                }
                context.registerReceiver(receiver, filter, flag)
                Log.d(logTag, "使用新API成功注册广播接收器 (exported=$exported)")
            } else {
                // Android 13以下使用旧API
                context.registerReceiver(receiver, filter)
                Log.d(logTag, "使用旧API成功注册广播接收器")
            }
            true
        } catch (e: Exception) {
            Log.e(logTag, "注册广播接收器失败", e)
            
            // 如果新API失败，尝试回退到旧API（可能是targetSdkVersion < 33的情况）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                try {
                    context.registerReceiver(receiver, filter)
                    Log.w(logTag, "新API失败，回退到旧API成功注册广播接收器")
                    true
                } catch (e2: Exception) {
                    Log.e(logTag, "回退到旧API也失败", e2)
                    false
                }
            } else {
                false
            }
        }
    }

    /**
     * 安全取消注册广播接收器
     * 
     * @param context 上下文
     * @param receiver 广播接收器
     * @param logTag 日志标签，用于错误日志
     * @return 是否取消注册成功
     */
    fun safeUnregisterReceiver(
        context: Context,
        receiver: BroadcastReceiver,
        logTag: String = TAG
    ): Boolean {
        return try {
            context.unregisterReceiver(receiver)
            Log.d(logTag, "成功取消注册广播接收器")
            true
        } catch (e: Exception) {
            Log.e(logTag, "取消注册广播接收器失败", e)
            false
        }
    }

    /**
     * 注册应用内部广播接收器（不导出）
     * 这是最常用的情况，用于应用内部通信
     * 
     * @param context 上下文
     * @param receiver 广播接收器
     * @param filter 意图过滤器
     * @param logTag 日志标签
     * @return 是否注册成功
     */
    fun registerInternalReceiver(
        context: Context,
        receiver: BroadcastReceiver,
        filter: IntentFilter,
        logTag: String = TAG
    ): Boolean {
        return safeRegisterReceiver(context, receiver, filter, exported = false, logTag)
    }

    /**
     * 注册导出的广播接收器（可被其他应用访问）
     * 谨慎使用，只有在需要接收系统或其他应用广播时才使用
     * 
     * @param context 上下文
     * @param receiver 广播接收器
     * @param filter 意图过滤器
     * @param logTag 日志标签
     * @return 是否注册成功
     */
    fun registerExportedReceiver(
        context: Context,
        receiver: BroadcastReceiver,
        filter: IntentFilter,
        logTag: String = TAG
    ): Boolean {
        return safeRegisterReceiver(context, receiver, filter, exported = true, logTag)
    }
}
