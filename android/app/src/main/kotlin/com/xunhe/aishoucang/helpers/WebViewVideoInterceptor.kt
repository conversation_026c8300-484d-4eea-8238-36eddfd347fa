package com.xunhe.aishoucang.helpers

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import java.lang.ref.WeakReference
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.CountDownLatch

/**
 * WebView视频资源拦截器
 * 用于拦截视频资源和非HTTP(S) scheme URL跳转，不执行JavaScript
 */
object WebViewVideoInterceptor {
    private const val TAG = "WebViewVideoInterceptor"

    // 共享线程池，避免每次调用都创建新的线程池
    private val sharedExecutor = Executors.newSingleThreadExecutor()

    // 超时时间常量 - 10秒
    private const val TIMEOUT_MS = 10000L

    // WebView销毁延迟时间
    private const val WEBVIEW_DESTROY_DELAY_MS = 100L

    /**
     * 拦截结果数据类
     */
    data class InterceptResult(
        val videoUrl: String? = null,
        val contentType: String? = null
    )

    /**
     * 安全地销毁WebView，最大程度减少销毁后的活动
     *
     * @param webView 要销毁的WebView
     * @param handler 主线程Handler
     * @param onDestroyed 销毁完成后的回调
     */
    private fun safeDestroyWebView(webView: WebView?, handler: Handler, onDestroyed: () -> Unit) {
        if (webView == null) {
            onDestroyed()
            return
        }

        try {
            // 使用弱引用避免内存泄漏
            val weakWebView = WeakReference(webView)

            // 在主线程中执行清理操作
            handler.post {
                try {
                    val view = weakWebView.get()
                    if (view != null) {
                        // 停止加载
                        view.stopLoading()

                        // 加载空白页面
                        view.loadUrl("about:blank")

                        // 清除WebView状态
                        view.clearHistory()
                        view.clearCache(true)
                        view.clearFormData()
                        view.clearSslPreferences()
                        view.clearMatches()

                        // 短暂延迟后再销毁
                        handler.postDelayed({
                            try {
                                val delayedView = weakWebView.get()
                                if (delayedView != null) {
                                    // 从父视图中移除
                                    val parent = delayedView.parent
                                    if (parent != null) {
                                        try {
                                            (parent as ViewGroup).removeView(delayedView)
                                        } catch (e: Exception) {
                                            Log.w(TAG, "从父视图移除WebView时出错: ${e.message}")
                                        }
                                    }

                                    // 最后销毁
                                    delayedView.destroy()
                                    Log.d(TAG, "WebView已安全销毁")
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "销毁WebView时出错: ${e.message}", e)
                            } finally {
                                // 确保回调一定会执行
                                onDestroyed()
                            }
                        }, WEBVIEW_DESTROY_DELAY_MS)
                    } else {
                        // WebView已经被回收，直接执行回调
                        onDestroyed()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "准备销毁WebView时出错: ${e.message}", e)
                    onDestroyed()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "销毁WebView过程中出错: ${e.message}", e)
            onDestroyed()
        }
    }

    /**
     * 检查content-type是否为视频类型
     *
     * @param contentType HTTP响应的content-type
     * @return 是否为视频类型
     */
    private fun isVideoContentType(contentType: String?): Boolean {
        if (contentType == null) return false
        return contentType.lowercase().contains("video")
    }



    /**
     * 使用WebView拦截视频资源和scheme URL跳转
     *
     * @param context 上下文
     * @param url 要加载的URL
     * @param callback 回调函数，返回拦截结果和可能的错误信息
     */
    fun interceptVideoResources(
        context: Context,
        url: String,
        callback: (result: InterceptResult?, error: String?) -> Unit
    ) {
        // 使用原子布尔值跟踪是否已完成
        val isCompleted = AtomicBoolean(false)

        // 使用CountDownLatch等待拦截完成
        val latch = CountDownLatch(1)

        // 主线程Handler
        val mainHandler = Handler(Looper.getMainLooper())

        // 超时处理
        var timeoutRunnable: Runnable? = null

        // 在共享线程池中执行WebView操作
        sharedExecutor.execute {
            var webView: WebView? = null

            try {
                // 设置超时处理
                timeoutRunnable = Runnable {
                    if (!isCompleted.getAndSet(true)) {
                        Log.e(TAG, "视频资源拦截超时")
                        latch.countDown() // 释放等待的线程

                        // 安全销毁WebView
                        safeDestroyWebView(webView, mainHandler) {
                            webView = null
                            callback(null, "拦截超时，请稍后重试")
                        }
                    }
                }

                // 在主线程中创建和配置WebView
                mainHandler.post {
                    try {
                        // 创建WebView
                        webView = WebView(context)

                        // 配置WebView设置 - 不启用JavaScript
                        val settings = webView?.settings
                        settings?.javaScriptEnabled = false
                        settings?.domStorageEnabled = false
                        settings?.allowFileAccess = true
                        settings?.allowContentAccess = true
                        settings?.loadsImagesAutomatically = false // 不加载图片以提高性能

                        // 设置WebViewClient进行资源拦截
                        webView?.webViewClient = object : WebViewClient() {
                            override fun shouldOverrideUrlLoading(
                                view: WebView,
                                request: WebResourceRequest
                            ): Boolean {
                                val scheme = request.url.scheme
                                val urlString = request.url.toString()

                                Log.d(TAG, "URL跳转: $urlString")

                                // 拦截非HTTP(S)开头的跳转，但不终止搜寻
                                if (scheme == null || (!scheme.startsWith("http"))) {
                                    Log.d(TAG, "拦截非HTTP(S) scheme URL（不终止搜寻）: $urlString")
                                    return true // 返回true表示WebView不处理这个URL，但继续搜寻视频资源
                                }

                                // 允许HTTP(S)重定向
                                Log.d(TAG, "允许重定向: $urlString")
                                return false // 返回false让WebView处理http和https协议
                            }

                            override fun shouldInterceptRequest(
                                view: WebView,
                                request: WebResourceRequest
                            ): WebResourceResponse? {
                                try {
                                    val urlString = request.url.toString()
                                    Log.d(TAG, "检查资源请求: $urlString")

                                    // 发起网络请求获取响应头，检查每一个资源的content-type
                                    val connection = java.net.URL(urlString).openConnection()
                                    connection.connectTimeout = 5000
                                    connection.readTimeout = 5000
                                    connection.setRequestProperty("User-Agent", com.xunhe.aishoucang.helpers.UserAgentGenerator.generateUserAgentForUrl(urlString))

                                    val contentType = connection.contentType
                                    Log.d(TAG, "资源Content-Type: $contentType")

                                    // 检查是否为视频资源
                                    if (isVideoContentType(contentType)) {
                                        Log.d(TAG, "拦截到视频资源: $urlString, Content-Type: $contentType")

                                        if (!isCompleted.getAndSet(true)) {
                                            // 取消超时任务
                                            timeoutRunnable?.let { mainHandler.removeCallbacks(it) }

                                            // 立即销毁WebView并返回结果
                                            safeDestroyWebView(webView, mainHandler) {
                                                webView = null
                                                val result = InterceptResult(
                                                    videoUrl = urlString,
                                                    contentType = contentType
                                                )
                                                callback(result, null)
                                            }

                                            // 释放等待的线程
                                            latch.countDown()
                                        }

                                        // 返回空响应，阻止继续加载
                                        return WebResourceResponse("text/plain", "utf-8", null)
                                    }
                                } catch (e: Exception) {
                                    Log.w(TAG, "检查资源请求时出错: ${e.message}")
                                }

                                return null // 继续正常处理其他资源
                            }

                            override fun onPageFinished(view: WebView?, url: String?) {
                                super.onPageFinished(view, url)
                                Log.d(TAG, "页面加载完成: $url")
                                // 页面加载完成但没有拦截到视频资源，继续等待或超时
                            }

                            override fun onReceivedError(
                                view: WebView?,
                                errorCode: Int,
                                description: String?,
                                failingUrl: String?
                            ) {
                                super.onReceivedError(view, errorCode, description, failingUrl)
                                Log.w(TAG, "WebView加载错误: $description, URL: $failingUrl")
                            }
                        }

                        // 加载URL
                        webView?.loadUrl(url)

                        // 设置超时
                        mainHandler.postDelayed(timeoutRunnable!!, TIMEOUT_MS)
                    } catch (e: Exception) {
                        Log.e(TAG, "WebView创建或配置失败: ${e.message}", e)
                        if (!isCompleted.getAndSet(true)) {
                            latch.countDown() // 释放等待的线程
                            callback(null, "网络连接异常，请检查网络后重试")
                        }
                    }
                }
                // 等待拦截完成或超时
                try {
                    latch.await() // 阻塞等待拦截完成
                } catch (e: InterruptedException) {
                    Log.e(TAG, "等待拦截被中断: ${e.message}", e)
                    if (!isCompleted.getAndSet(true)) {
                        // 取消超时任务
                        timeoutRunnable?.let { mainHandler.removeCallbacks(it) }

                        // 安全销毁WebView
                        mainHandler.post {
                            safeDestroyWebView(webView, mainHandler) {
                                webView = null
                                callback(null, "拦截中断，请重试")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "拦截过程中出错: ${e.message}", e)
                if (!isCompleted.getAndSet(true)) {
                    // 取消超时任务
                    timeoutRunnable?.let { mainHandler.removeCallbacks(it) }

                    // 安全销毁WebView
                    mainHandler.post {
                        safeDestroyWebView(webView, mainHandler) {
                            webView = null
                            callback(null, "处理过程中出现异常，请重试")
                        }
                    }

                    // 释放等待的线程
                    latch.countDown()
                }
            }
        }
    }
}
