package com.xunhe.aishoucang.lib

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View

/**
 * 截图选择视图
 * 用于在截图操作界面中实现可选中区域的功能
 */
class ScreenshotSelectionView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "ScreenshotSelectionView"
        private const val HANDLE_SIZE_DP = 30f // 调整手柄的大小（触摸区域）
        private const val EDGE_THRESHOLD_DP = 40f // 边缘检测阈值
    }

    // 绘制相关
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    }
    private val path = Path()

    // 选择区域
    private var selectionRect = RectF()
    private var startX = 0f
    private var startY = 0f
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 调整模式
    private var isSelecting = false
    private var isInitialized = false
    private var adjustMode = AdjustMode.NONE

    // 密度比例，用于dp到px的转换
    private val density = context.resources.displayMetrics.density
    private val handleSize = HANDLE_SIZE_DP * density
    private val edgeThreshold = EDGE_THRESHOLD_DP * density

    // 调整模式枚举
    private enum class AdjustMode {
        NONE,       // 无操作
        MOVE,       // 移动整个选区
        TOP_LEFT,   // 调整左上角
        TOP_RIGHT,  // 调整右上角
        BOTTOM_LEFT,// 调整左下角
        BOTTOM_RIGHT,// 调整右下角
        TOP,        // 调整上边
        BOTTOM,     // 调整下边
        LEFT,       // 调整左边
        RIGHT       // 调整右边
    }

    init {
        // 设置为可绘制
        setWillNotDraw(false)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // 只在第一次初始化默认选区
        if (!isInitialized) {
            // 设置默认选区为屏幕宽度，高度为屏幕高度的1/3，位于屏幕中央
            val defaultWidth = w.toFloat()
            val defaultHeight = h / 3f

            selectionRect.left = 0f
            selectionRect.top = (h - defaultHeight) / 2
            selectionRect.right = defaultWidth
            selectionRect.bottom = selectionRect.top + defaultHeight

            isInitialized = true
            invalidate()
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 创建离屏缓冲区
        val sc = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)

        // 绘制半透明背景
        paint.color = Color.parseColor("#80000000")
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)

        // 清除选择区域（使其透明）
        if (selectionRect.width() > 0 && selectionRect.height() > 0) {
            canvas.drawRect(selectionRect, clearPaint)

            // 绘制选择区域的边框
            paint.color = Color.WHITE
            paint.style = Paint.Style.STROKE
            paint.strokeWidth = 2f
            canvas.drawRect(selectionRect, paint)

            // 绘制四个角的边框
            val cornerSize = 20f * density

            // 左上角
            canvas.drawLine(selectionRect.left, selectionRect.top, selectionRect.left + cornerSize, selectionRect.top, paint)
            canvas.drawLine(selectionRect.left, selectionRect.top, selectionRect.left, selectionRect.top + cornerSize, paint)

            // 右上角
            canvas.drawLine(selectionRect.right - cornerSize, selectionRect.top, selectionRect.right, selectionRect.top, paint)
            canvas.drawLine(selectionRect.right, selectionRect.top, selectionRect.right, selectionRect.top + cornerSize, paint)

            // 左下角
            canvas.drawLine(selectionRect.left, selectionRect.bottom - cornerSize, selectionRect.left, selectionRect.bottom, paint)
            canvas.drawLine(selectionRect.left, selectionRect.bottom, selectionRect.left + cornerSize, selectionRect.bottom, paint)

            // 右下角
            canvas.drawLine(selectionRect.right - cornerSize, selectionRect.bottom, selectionRect.right, selectionRect.bottom, paint)
            canvas.drawLine(selectionRect.right, selectionRect.bottom - cornerSize, selectionRect.right, selectionRect.bottom, paint)

            paint.style = Paint.Style.FILL
        }

        canvas.restoreToCount(sc)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 确定调整模式
                adjustMode = detectAdjustMode(x, y)
                Log.d(TAG, "触摸开始，调整模式: $adjustMode")
                lastTouchX = x
                lastTouchY = y
                return true
            }
            MotionEvent.ACTION_MOVE -> {
                // 根据调整模式更新选区
                val dx = x - lastTouchX
                val dy = y - lastTouchY

                when (adjustMode) {
                    AdjustMode.MOVE -> moveSelection(dx, dy)
                    AdjustMode.TOP_LEFT -> adjustTopLeft(dx, dy)
                    AdjustMode.TOP_RIGHT -> adjustTopRight(dx, dy)
                    AdjustMode.BOTTOM_LEFT -> adjustBottomLeft(dx, dy)
                    AdjustMode.BOTTOM_RIGHT -> adjustBottomRight(dx, dy)
                    AdjustMode.TOP -> adjustTop(dy)
                    AdjustMode.BOTTOM -> adjustBottom(dy)
                    AdjustMode.LEFT -> adjustLeft(dx)
                    AdjustMode.RIGHT -> adjustRight(dx)
                    AdjustMode.NONE -> {
                        // 如果没有检测到调整模式，则默认为移动整个选区
                        moveSelection(dx, dy)
                        adjustMode = AdjustMode.MOVE
                    }
                }

                lastTouchX = x
                lastTouchY = y
                invalidate()
                return true
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                Log.d(TAG, "触摸结束，选区: $selectionRect")
                // 保持当前调整模式，不重置为NONE，这样下次触摸时会重新检测
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    /**
     * 检测调整模式
     */
    private fun detectAdjustMode(x: Float, y: Float): AdjustMode {
        // 计算选区的25%宽度和高度作为角落区域的大小
        val cornerWidthThreshold = selectionRect.width() * 0.25f
        val cornerHeightThreshold = selectionRect.height() * 0.25f

        // 确保角落区域不会太小
        val minCornerSize = 40f * density
        val cornerWidth = maxOf(cornerWidthThreshold, minCornerSize)
        val cornerHeight = maxOf(cornerHeightThreshold, minCornerSize)

        // 检查是否在四个角上
        val inLeftRange = x >= selectionRect.left - edgeThreshold && x <= selectionRect.left + cornerWidth
        val inRightRange = x >= selectionRect.right - cornerWidth && x <= selectionRect.right + edgeThreshold
        val inTopRange = y >= selectionRect.top - edgeThreshold && y <= selectionRect.top + cornerHeight
        val inBottomRange = y >= selectionRect.bottom - cornerHeight && y <= selectionRect.bottom + edgeThreshold

        // 检查四个角
        if (inLeftRange && inTopRange) return AdjustMode.TOP_LEFT
        if (inRightRange && inTopRange) return AdjustMode.TOP_RIGHT
        if (inLeftRange && inBottomRange) return AdjustMode.BOTTOM_LEFT
        if (inRightRange && inBottomRange) return AdjustMode.BOTTOM_RIGHT

        // 检查四条边
        val onLeft = Math.abs(x - selectionRect.left) < edgeThreshold && y > selectionRect.top + cornerHeight && y < selectionRect.bottom - cornerHeight
        val onRight = Math.abs(x - selectionRect.right) < edgeThreshold && y > selectionRect.top + cornerHeight && y < selectionRect.bottom - cornerHeight
        val onTop = Math.abs(y - selectionRect.top) < edgeThreshold && x > selectionRect.left + cornerWidth && x < selectionRect.right - cornerWidth
        val onBottom = Math.abs(y - selectionRect.bottom) < edgeThreshold && x > selectionRect.left + cornerWidth && x < selectionRect.right - cornerWidth

        if (onTop) return AdjustMode.TOP
        if (onBottom) return AdjustMode.BOTTOM
        if (onLeft) return AdjustMode.LEFT
        if (onRight) return AdjustMode.RIGHT

        // 检查是否在选区内
        if (selectionRect.contains(x, y)) {
            // 在选区内，移动整个选区
            return AdjustMode.MOVE
        }

        // 如果不在任何特殊区域，保持当前模式
        return AdjustMode.NONE
    }

    /**
     * 移动整个选区
     */
    private fun moveSelection(dx: Float, dy: Float) {
        // 确保选区不会移出屏幕
        val newLeft = selectionRect.left + dx
        val newTop = selectionRect.top + dy
        val newRight = selectionRect.right + dx
        val newBottom = selectionRect.bottom + dy

        if (newLeft >= 0 && newRight <= width) {
            selectionRect.left = newLeft
            selectionRect.right = newRight
        }

        if (newTop >= 0 && newBottom <= height) {
            selectionRect.top = newTop
            selectionRect.bottom = newBottom
        }
    }

    /**
     * 调整左上角
     */
    private fun adjustTopLeft(dx: Float, dy: Float) {
        val newLeft = selectionRect.left + dx
        val newTop = selectionRect.top + dy

        if (newLeft < selectionRect.right - handleSize) {
            selectionRect.left = newLeft.coerceAtLeast(0f)
        }

        if (newTop < selectionRect.bottom - handleSize) {
            selectionRect.top = newTop.coerceAtLeast(0f)
        }
    }

    /**
     * 调整右上角
     */
    private fun adjustTopRight(dx: Float, dy: Float) {
        val newRight = selectionRect.right + dx
        val newTop = selectionRect.top + dy

        if (newRight > selectionRect.left + handleSize) {
            selectionRect.right = newRight.coerceAtMost(width.toFloat())
        }

        if (newTop < selectionRect.bottom - handleSize) {
            selectionRect.top = newTop.coerceAtLeast(0f)
        }
    }

    /**
     * 调整左下角
     */
    private fun adjustBottomLeft(dx: Float, dy: Float) {
        val newLeft = selectionRect.left + dx
        val newBottom = selectionRect.bottom + dy

        if (newLeft < selectionRect.right - handleSize) {
            selectionRect.left = newLeft.coerceAtLeast(0f)
        }

        if (newBottom > selectionRect.top + handleSize) {
            selectionRect.bottom = newBottom.coerceAtMost(height.toFloat())
        }
    }

    /**
     * 调整右下角
     */
    private fun adjustBottomRight(dx: Float, dy: Float) {
        val newRight = selectionRect.right + dx
        val newBottom = selectionRect.bottom + dy

        if (newRight > selectionRect.left + handleSize) {
            selectionRect.right = newRight.coerceAtMost(width.toFloat())
        }

        if (newBottom > selectionRect.top + handleSize) {
            selectionRect.bottom = newBottom.coerceAtMost(height.toFloat())
        }
    }

    /**
     * 调整上边
     */
    private fun adjustTop(dy: Float) {
        val newTop = selectionRect.top + dy
        if (newTop < selectionRect.bottom - handleSize) {
            selectionRect.top = newTop.coerceAtLeast(0f)
        }
    }

    /**
     * 调整下边
     */
    private fun adjustBottom(dy: Float) {
        val newBottom = selectionRect.bottom + dy
        if (newBottom > selectionRect.top + handleSize) {
            selectionRect.bottom = newBottom.coerceAtMost(height.toFloat())
        }
    }

    /**
     * 调整左边
     */
    private fun adjustLeft(dx: Float) {
        val newLeft = selectionRect.left + dx
        // 确保选区宽度不小于最小值
        if (newLeft < selectionRect.right - handleSize) {
            selectionRect.left = newLeft.coerceAtLeast(0f)
        }
    }

    /**
     * 调整右边
     */
    private fun adjustRight(dx: Float) {
        val newRight = selectionRect.right + dx
        // 确保选区宽度不小于最小值
        if (newRight > selectionRect.left + handleSize) {
            selectionRect.right = newRight.coerceAtMost(width.toFloat())
        }
    }

    /**
     * 获取当前选择区域
     */
    fun getSelectionRect(): RectF {
        return RectF(selectionRect)
    }

    /**
     * 重置选择区域
     */
    fun resetSelection() {
        selectionRect.setEmpty()
        isInitialized = false
        invalidate()
    }
}
