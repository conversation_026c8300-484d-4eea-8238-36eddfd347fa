package com.xunhe.aishoucang.helpers

/**
 * 内容类型常量定义
 * 用于标识不同应用内的具体内容类型
 */
object ContentTypeConstants {
    /** 未知类型 */
    const val TYPE_UNKNOWN = 0

    /** 未知应用类型 */
    const val TYPE_UNKNOWN_APP = -1

    /** 抖音内容类型 */
    const val DOUYIN_TYPE_IMAGE_TEXT = 1001        // 图文
    const val DOUYIN_TYPE_SHORT_VIDEO = 1002       // 短视频
    const val DOUYIN_TYPE_LONG_VIDEO = 1003        // 长视频
    const val DOUYIN_TYPE_PROFILE = 1004           // 个人主页
    const val DOUYIN_TYPE_LIVE = 1005              // 直播
    const val DOUYIN_TYPE_TOPIC = 1006             // 话题
    const val DOUYIN_TYPE_GOODS = 1007             // 商品

    /** 快手内容类型 */
    const val KUAISHOU_TYPE_IMAGE_TEXT = 2001      // 图文
    const val KUAISHOU_TYPE_SHORT_VIDEO = 2002     // 短视频
    const val KUAISHOU_TYPE_PROFILE = 2003         // 个人主页
    const val KUAISHOU_TYPE_LIVE = 2004            // 直播

    /** 小红书内容类型 */
    const val XIAOHONGSHU_TYPE_NOTE = 3001         // 普通笔记
    const val XIAOHONGSHU_TYPE_VIDEO_NOTE = 3002   // 视频笔记
    const val XIAOHONGSHU_TYPE_PROFILE = 3003      // 个人主页
    const val XIAOHONGSHU_TYPE_GOODS = 3004        // 话题
    const val XIAOHONGSHU_TYPE_COLLECTION = 3005   // 合集

    /** B站内容类型 */
    const val BILIBILI_TYPE_VIDEO = 4001           // 视频
    const val BILIBILI_TYPE_ARTICLE = 4002         // 专栏文章
    const val BILIBILI_TYPE_LIVE = 4003            // 直播
    const val BILIBILI_TYPE_PROFILE = 4004         // UP主主页
    const val BILIBILI_TYPE_SERIES = 4005          // 合集/系列

    /** 微信内容类型 */
    const val WECHAT_TYPE_ARTICLE = 5001           // 公众号文章
    const val WECHAT_TYPE_MINI_PROGRAM = 5002      // 小程序
    const val WECHAT_TYPE_MOMENT = 5003            // 朋友圈
    const val WECHAT_TYPE_OFFICIAL_ACCOUNT = 5004  // 公众号

    /** 美团内容类型 */
    const val MEITUAN_TYPE_RESTAURANT = 6001       // 餐厅
    const val MEITUAN_TYPE_HOTEL = 6002            // 酒店
    const val MEITUAN_TYPE_MOVIE = 6003            // 电影
    const val MEITUAN_TYPE_SHOP = 6004             // 商店

    /** 豆瓣内容类型 */
    const val DOUBAN_TYPE_MOVIE = 7001             // 电影
    const val DOUBAN_TYPE_BOOK = 7002              // 图书
    const val DOUBAN_TYPE_MUSIC = 7003             // 音乐
    const val DOUBAN_TYPE_GROUP = 7004             // 小组
    const val DOUBAN_TYPE_NOTE = 7005              // 动态
    const val DOUBAN_TYPE_BOKE = 7006              // 播客

    /** 拼多多内容类型 */
    const val PINDUODUO_TYPE_PRODUCT = 8001        // 商品
    const val PINDUODUO_TYPE_SHOP = 8002           // 店铺
    const val PINDUODUO_TYPE_ACTIVITY = 8003       // 活动
    const val PINDUODUO_TYPE_LIVE = 8004           // 直播

    /** 淘宝内容类型 */
    const val TAOBAO_TYPE_PRODUCT = 9001           // 商品

    /** 京东内容类型 */
    const val JINGDONG_TYPE_PRODUCT = 10001        // 商品
    const val JINGDONG_TYPE_SHOP = 10002           // 店铺
    const val JINGDONG_TYPE_ACTIVITY = 10003       // 活动
    const val JINGDONG_TYPE_LIVE = 10004           // 直播
}