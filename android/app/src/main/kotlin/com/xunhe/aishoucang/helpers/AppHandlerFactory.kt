package com.xunhe.aishoucang.helpers

// 导入所有应用处理器类
import com.xunhe.aishoucang.helpers.DouyinAppHandler
import com.xunhe.aishoucang.helpers.KuaishouAppHandler
import com.xunhe.aishoucang.helpers.XiaohongshuAppHandler
import com.xunhe.aishoucang.helpers.BilibiliAppHandler
import com.xunhe.aishoucang.helpers.WechatAppHandler
import com.xunhe.aishoucang.helpers.MeituanAppHandler
import com.xunhe.aishoucang.helpers.DoubanAppHandler
import com.xunhe.aishoucang.helpers.PinDuoDuoAppHandler
import com.xunhe.aishoucang.helpers.TaobaoAppHandler
import com.xunhe.aishoucang.helpers.JingdongAppHandler
import com.xunhe.aishoucang.helpers.UnknownAppHandler

/**
 * 应用处理器工厂
 * 根据应用类型创建对应的处理器实例
 */
object AppHandlerFactory {
    /**
     * 根据包名获取应用处理器
     *
     * @param packageName 应用包名
     * @return 对应的应用处理器实例
     */
    fun getHandlerByPackage(packageName: String?): AppHandler {
        val safePackage = packageName ?: ""
        return when {
            safePackage.startsWith("com.ss.android.ugc.aweme") -> DouyinAppHandler()
            safePackage.startsWith("com.kuaishou.nebula") ||
                    safePackage.startsWith("com.smile.gifmaker") -> KuaishouAppHandler()
            safePackage.startsWith("com.xingin.xhs") -> XiaohongshuAppHandler()
            safePackage.startsWith("tv.danmaku.bili") -> BilibiliAppHandler()
            safePackage.startsWith("com.tencent.mm") -> WechatAppHandler()
            safePackage.startsWith("com.sankuai.meituan") -> MeituanAppHandler()
            safePackage.startsWith("com.douban.frodo") -> DoubanAppHandler()
            safePackage.startsWith("com.xunmeng.pinduoduo") -> PinDuoDuoAppHandler()
            safePackage.startsWith("com.taobao.taobao") -> TaobaoAppHandler()
            safePackage.startsWith("com.jingdong.app.mall") -> JingdongAppHandler()
            else -> UnknownAppHandler()
        }
    }
}
