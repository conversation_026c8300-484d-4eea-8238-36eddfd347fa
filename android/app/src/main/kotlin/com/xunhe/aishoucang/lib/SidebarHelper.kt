package com.xunhe.aishoucang.lib

import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

class SidebarHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "SidebarHelper"
        
        @Volatile
        private var instance: SidebarHelper? = null

        fun getInstance(context: Context): SidebarHelper {
            return instance ?: synchronized(this) {
                instance ?: SidebarHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    private var isShowing = false

    fun show() {
        try {
            val intent = Intent(context, SidebarService::class.java)
            context.startService(intent)
            isShowing = true
            Log.d(TAG, "显示侧边栏")
        } catch (e: Exception) {
            Log.e(TAG, "显示侧边栏时出错", e)
        }
    }

    fun hide() {
        try {
            stopSidebarService()
            isShowing = false
            Log.d(TAG, "隐藏侧边栏")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏侧边栏时出错", e)
        }
    }

    fun showSidebarAtPosition(x: Int?, y: Int?) {
        try {
            val intent = Intent(context, SidebarService::class.java).apply {
                x?.let { putExtra("position_x", it) }
                y?.let { putExtra("position_y", it) }
            }
            context.startService(intent)
            isShowing = true
            Log.d(TAG, "在指定位置(x=$x, y=$y)显示侧边栏")
        } catch (e: Exception) {
            Log.e(TAG, "在指定位置显示侧边栏时出错", e)
        }
    }

    fun isSidebarShowing(): Boolean {
        return isShowing
    }

    fun getSidebarPosition(): Pair<Int, Int>? {
        return try {
            val position = SidebarService.lastPosition
            Log.d(TAG, "从服务获取侧边栏位置: $position")
            position
        } catch (e: Exception) {
            Log.e(TAG, "获取侧边栏位置失败", e)
            null
        }
    }

    private fun stopSidebarService() {
        try {
            val intent = Intent(context, SidebarService::class.java)
            context.stopService(intent)
            Log.d(TAG, "侧边栏服务已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止侧边栏服务失败", e)
        }
    }
}
