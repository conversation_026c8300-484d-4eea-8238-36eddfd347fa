package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.xunhe.aishoucang.lib.AccessibilityHelper
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.ContentTypeConstants


/**
 * B站应用处理器
 */
class BilibiliAppHandler : AppHandler {
    companion object {
        private const val TAG = "BilibiliAppHandler"
        private const val MAX_REFRESH_ATTEMPTS = 3
    }

    override fun handle(context: Context) {
        Log.d(TAG, "正在处理B站应用")

        // 获取AccessibilityHelper实例
        val helper = AccessibilityHelper.getInstance(context)

        // 检查当前界面类型并处理
        if (isVideoPage(helper)) {
            // 设置内容类型为B站视频
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.BILIBILI_TYPE_VIDEO)
            handleVideoPage(context)
        } else {
            // 未找到分享按钮，当前界面不支持收藏
            Log.e(TAG, "当前B站界面不支持收藏，未找到分享按钮")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.TYPE_UNKNOWN)
            CustomToastHelper.showToast(context, "B站暂时只支持收藏视频，请到视频详情页收藏")
        }
    }

    /**
     * 判断当前是否是视频页面
     *
     * @param helper AccessibilityHelper实例
     * @return 是否是视频页面
     */
    private fun isVideoPage(helper: AccessibilityHelper): Boolean {
        return helper.IsExistElementById("tv.danmaku.bili:id/frame_share")
    }

    /**
     * 判断当前是否是短视频页面
     *
     * @param helper AccessibilityHelper实例
     * @return 是否是短视频页面
     */
    private fun isShortVideoPage(helper: AccessibilityHelper): Boolean {
        return helper.IsExistElementById("tv.danmaku.bili:id/share_layout")
    }

    /**
     * 处理视频页面
     *
     * @param context 上下文
     */
    private fun handleVideoPage(context: Context) {
        val helper = AccessibilityHelper.getInstance(context)

        // 1. 点击分享按钮
        val shareButtonResult = helper.findAndClickElementById("tv.danmaku.bili:id/frame_share")
        if (shareButtonResult) {
            Log.d(TAG, "成功点击B站分享按钮")

            // 2. 等待分享菜单出现
            Thread.sleep(500)

            // 3. 找到文本为"复制链接"的节点
            val service = AccessibilityHelper.AppAccessibilityService.getInstance()
            if (service != null) {
                val rootNode = service.rootInActiveWindow
                if (rootNode != null) {
                    // 查找包含"复制链接"文本的节点
                    val copyNodes = service.findNodesByContent("复制链接")

                    if (copyNodes != null && copyNodes.isNotEmpty()) {
                        Log.d(TAG, "找到复制链接节点，数量: ${copyNodes.size}")

                        // 获取第一个匹配节点
                        val copyNode = copyNodes[0]

                        // 4. 获取父节点并点击
                        val parentNode = copyNode.parent
                        if (parentNode != null) {
                            val clickResult = parentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                            Log.d(TAG, "点击复制链接父节点结果: $clickResult")

                            // 通知内容已准备好
                            SharePanelHelper.notifyContentReady()

                            // 释放资源
                            parentNode.recycle()
                        } else {
                            Log.e(TAG, "无法获取复制链接节点的父节点")
                        }

                        // 释放资源
                        copyNode.recycle()
                    } else {
                        Log.e(TAG, "未找到复制链接节点")
                        CustomToastHelper.showToast(context, "当前内容无法收藏，未找到复制链接选项")
                    }

                    // 释放资源
                    rootNode.recycle()
                } else {
                    Log.e(TAG, "无法获取当前活动窗口")
                    CustomToastHelper.showToast(context, "当前内容无法收藏，无法获取窗口信息")
                }
            } else {
                Log.e(TAG, "无障碍服务未运行")
                CustomToastHelper.showToast(context, "无障碍服务未运行，请检查权限设置")
            }
        } else {
            Log.e(TAG, "未找到B站分享按钮")
            CustomToastHelper.showToast(context, "当前内容无法收藏，未找到分享按钮")
        }
    }
}
