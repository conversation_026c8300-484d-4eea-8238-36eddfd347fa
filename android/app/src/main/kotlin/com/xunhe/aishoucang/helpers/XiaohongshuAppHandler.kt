package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.lib.AccessibilityHelper
import android.widget.Toast
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.ContentTypeConstants
import com.xunhe.aishoucang.lib.AccessibilityHelper.AppAccessibilityService
import com.xunhe.aishoucang.helpers.CustomToastHelper
import android.view.accessibility.AccessibilityNodeInfo
import java.util.regex.Pattern

/**
 * 小红书应用处理器
 */
class XiaohongshuAppHandler : AppHandler {
    companion object {
        private const val TAG = "XiaohongshuAppHandler"

        // 评论按钮描述的正则表达式模式 - 匹配包含"评论"的描述
        private val COMMENT_BUTTON_PATTERN = Pattern.compile(".*评论.*")

        // 分享按钮描述的正则表达式模式 - 匹配包含"分享"的描述
        private val SHARE_BUTTON_PATTERN = Pattern.compile("分享.*")
    }

    /**
     * 判断节点是否在屏幕可见区域内
     *
     * @param node 要检查的节点
     * @return 是否在屏幕可见区域内
     */
    private fun isNodeVisibleOnScreen(node: AccessibilityNodeInfo): Boolean {
        try {
            val rect = android.graphics.Rect()
            node.getBoundsInScreen(rect)

            // 获取屏幕尺寸
            val displayMetrics = android.content.res.Resources.getSystem().displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels

            // 检查节点是否在屏幕范围内
            val isVisible = rect.left >= 0 &&
                           rect.top >= 0 &&
                           rect.right <= screenWidth &&
                           rect.bottom <= screenHeight &&
                           rect.width() > 0 &&
                           rect.height() > 0

            Log.d(TAG, "节点可见性检查 - 节点边界: $rect, 屏幕尺寸: ${screenWidth}x${screenHeight}, 可见: $isVisible")
            return isVisible
        } catch (e: Exception) {
            Log.e(TAG, "检查节点可见性时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 查找匹配正则表达式的描述节点
     *
     * @param service 无障碍服务实例
     * @param pattern 正则表达式模式
     * @return 匹配的节点列表
     */
    private fun findNodesByDescriptionPattern(service: AccessibilityHelper.AppAccessibilityService, pattern: Pattern): List<AccessibilityNodeInfo> {
        val rootNode = service.rootInActiveWindow ?: return emptyList()
        val result = mutableListOf<AccessibilityNodeInfo>()

        try {
            findNodesByDescriptionPatternRecursive(rootNode, pattern, result)
        } catch (e: Exception) {
            Log.e(TAG, "查找匹配正则表达式的描述节点时出错: ${e.message}", e)
        }

        return result
    }

    /**
     * 递归查找匹配正则表达式的描述节点
     * 优化：找到一个匹配节点后就停止搜索，提高性能
     */
    private fun findNodesByDescriptionPatternRecursive(
        node: AccessibilityNodeInfo,
        pattern: Pattern,
        result: MutableList<AccessibilityNodeInfo>
    ) {
        try {
            // 如果已经找到一个节点，提前结束递归
            if (result.isNotEmpty()) {
                return
            }

            // 检查当前节点的描述内容是否匹配正则表达式
            val nodeDesc = node.contentDescription?.toString()
            if (nodeDesc != null && pattern.matcher(nodeDesc).matches()) {
                Log.d(TAG, "找到匹配正则的描述节点: $nodeDesc")
                result.add(AccessibilityNodeInfo.obtain(node))
                return // 找到一个就返回
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                // 如果已经找到节点，提前结束递归
                if (result.isNotEmpty()) {
                    break
                }

                node.getChild(i)?.let { child ->
                    findNodesByDescriptionPatternRecursive(child, pattern, result)
                    child.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "递归查找匹配正则表达式的描述节点时出错", e)
        }
    }

    /**
     * 查找第一个匹配正则表达式的描述节点
     *
     * @param service 无障碍服务实例
     * @param pattern 正则表达式模式
     * @return 第一个匹配的节点，如果没有找到则返回null
     */
    private fun findFirstNodeByDescriptionPattern(service: AccessibilityHelper.AppAccessibilityService, pattern: Pattern): AccessibilityNodeInfo? {
        val rootNode = service.rootInActiveWindow ?: return null

        try {
            return findFirstNodeByDescriptionPatternRecursive(rootNode, pattern)
        } catch (e: Exception) {
            Log.e(TAG, "查找第一个匹配正则表达式的描述节点时出错: ${e.message}", e)
        }

        return null
    }

    /**
     * 递归查找第一个匹配正则表达式的描述节点
     */
    private fun findFirstNodeByDescriptionPatternRecursive(
        node: AccessibilityNodeInfo,
        pattern: Pattern
    ): AccessibilityNodeInfo? {
        try {
            // 检查当前节点的描述内容是否匹配正则表达式
            val nodeDesc = node.contentDescription?.toString()
            if (nodeDesc != null && pattern.matcher(nodeDesc).matches()) {
                Log.d(TAG, "找到第一个匹配正则的描述节点: $nodeDesc")
                return AccessibilityNodeInfo.obtain(node)
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    val result = findFirstNodeByDescriptionPatternRecursive(child, pattern)
                    if (result != null) {
                        child.recycle()
                        return result
                    }
                    child.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "递归查找第一个匹配正则表达式的描述节点时出错", e)
        }

        return null
    }

    // 保存找到的分享按钮，避免重复查找
    private var cachedShareButton: AccessibilityNodeInfo? = null

    /**
     * 同时查找评论和分享按钮
     *
     * 在一次遍历中同时查找两种按钮，提高性能
     * 同时缓存找到的分享按钮，避免后续重复查找
     *
     * @param service 无障碍服务实例
     * @return Pair<Boolean, Boolean> 第一个Boolean表示是否找到评论按钮，第二个Boolean表示是否找到分享按钮
     */
    private fun findCommentAndShareButtons(service: AccessibilityHelper.AppAccessibilityService): Pair<Boolean, Boolean> {
        val rootNode = service.rootInActiveWindow ?: return Pair(false, false)

        // 使用可变引用类型来存储结果
        val result = BooleanArray(2) // [0]表示评论按钮, [1]表示分享按钮

        // 清除之前缓存的分享按钮
        cachedShareButton?.recycle()
        cachedShareButton = null

        try {
            findCommentAndShareButtonsRecursive(rootNode, COMMENT_BUTTON_PATTERN, SHARE_BUTTON_PATTERN, result)
        } catch (e: Exception) {
            Log.e(TAG, "同时查找评论和分享按钮时出错: ${e.message}", e)
        } finally {
            try {
                rootNode.recycle()
            } catch (e: Exception) {
                Log.e(TAG, "回收根节点资源时出错: ${e.message}")
            }
        }

        return Pair(result[0], result[1])
    }

    /**
     * 递归同时查找评论和分享按钮
     *
     * 一旦同时找到两种按钮，就停止递归
     * 同时缓存找到的分享按钮，避免后续重复查找
     *
     * @param node 当前节点
     * @param commentPattern 评论按钮的正则表达式模式
     * @param sharePattern 分享按钮的正则表达式模式
     * @param result 结果数组，[0]表示是否找到评论按钮，[1]表示是否找到分享按钮
     */
    private fun findCommentAndShareButtonsRecursive(
        node: AccessibilityNodeInfo,
        commentPattern: Pattern,
        sharePattern: Pattern,
        result: BooleanArray
    ) {
        // 如果已经同时找到评论和分享按钮，提前结束递归
        if (result[0] && result[1]) {
            return
        }

        try {
            // 检查当前节点的描述内容
            val nodeDesc = node.contentDescription?.toString()
            if (nodeDesc != null) {
                // 检查是否为评论按钮
                if (!result[0] && commentPattern.matcher(nodeDesc).matches()) {
                    Log.d(TAG, "找到评论按钮: $nodeDesc")
                    result[0] = true
                }

                // 检查是否为分享按钮
                if (!result[1] && sharePattern.matcher(nodeDesc).matches()) {
                    // 判断按钮是否在屏幕上可见
                    if (isNodeVisibleOnScreen(node)) {
                        Log.d(TAG, "找到可见的分享按钮: $nodeDesc")
                        result[1] = true

                        // 缓存找到的分享按钮，避免后续重复查找
                        if (cachedShareButton == null) {
                            cachedShareButton = AccessibilityNodeInfo.obtain(node)
                            val rect = android.graphics.Rect()
                            node.getBoundsInScreen(rect)
                            Log.d(TAG, "已缓存分享按钮，位置: $rect")
                        }
                    } else {
                        Log.d(TAG, "找到分享按钮但不在屏幕可见区域: $nodeDesc")
                    }
                }

                // 如果已经同时找到评论和分享按钮，提前结束递归
                if (result[0] && result[1]) {
                    return
                }
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                // 如果已经同时找到评论和分享按钮，提前结束递归
                if (result[0] && result[1]) {
                    break
                }

                node.getChild(i)?.let { child ->
                    findCommentAndShareButtonsRecursive(child, commentPattern, sharePattern, result)
                    child.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "递归同时查找评论和分享按钮时出错", e)
        }
    }

    /**
     * 判断是否为小红书短视频页面
     *
     * 通过检查页面是否同时存在评论和分享按钮来判断
     * 使用正则表达式匹配，支持各种数字格式
     * 优化性能：在一次遍历中同时查找评论和分享按钮
     */
    private fun isXiaohongshuShortVideoPage(context: Context): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        // 在一次遍历中同时查找评论和分享按钮
        val (hasCommentButton, hasShareButton) = findCommentAndShareButtons(service)

        Log.d(TAG, "是否找到评论按钮: $hasCommentButton")
        Log.d(TAG, "是否找到分享按钮: $hasShareButton")

        // 同时存在评论按钮和分享按钮，才判断为短视频页面
        val isShortVideoPage = hasCommentButton && hasShareButton
        Log.d(TAG, "是否为小红书短视频页面: $isShortVideoPage")

        return isShortVideoPage
    }

    fun specialClick(context: Context): Boolean {
      // 尝试直接点击text为”复制链接“的节点
      val helper = AccessibilityHelper.getInstance(context)
      val service = AccessibilityHelper.AppAccessibilityService.getInstance()

      if (service != null) {
          // 先尝试通过text内容查找"复制链接"节点
          val textNodes = service.findNodesByContent("复制链接")
          if (textNodes != null && textNodes.isNotEmpty()) {
              Log.d(TAG, "直接找到复制链接节点，尝试点击")
              val copyNode = textNodes[0]
              val parentNode = copyNode.parent
              val grandParentNode = parentNode?.parent

              if (grandParentNode != null && grandParentNode.isClickable) {
                  // 直接点击节点
                  val clickResult = grandParentNode.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
                  Log.d(TAG, "直接点击复制链接节点结果: $clickResult")

                  if (clickResult) {
                      // 点击成功，通知内容已准备好
                      SharePanelHelper.notifyContentReady()

                      // 释放资源
                      grandParentNode.recycle()
                      parentNode.recycle()
                      copyNode.recycle()

                      // 清理其余节点资源
                      for (i in 1 until textNodes.size) {
                          textNodes[i].recycle()
                      }

                      return true // 成功处理，返回true
                  }

                  // 点击失败，释放资源
                  grandParentNode.recycle()
              }

              // 释放资源
              parentNode?.recycle()
              copyNode.recycle()

              // 清理其余节点资源
              for (i in 1 until textNodes.size) {
                  textNodes[i].recycle()
              }
          }
      }
      return false // 未找到或点击失败，返回false
    }

    override fun handle(context: Context) {
      // 尝试直接点击text为”复制链接“的节点
      val helper = AccessibilityHelper.getInstance(context)
      val service = AccessibilityHelper.AppAccessibilityService.getInstance()

        if (helper.IsExistElementById("com.xingin.xhs:id/moreOperateIV")) {
            // 普通图文笔记
            // 清理缓存的分享按钮，因为不是短视频页面
            cleanupCachedShareButton()
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.XIAOHONGSHU_TYPE_NOTE)
            handleCommonArticle(context)
        } else if (isXiaohongshuShortVideoPage(context)) {
            // 短视频 - 使用新的判断逻辑
            Log.d(TAG, "检测到小红书短视频页面")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.XIAOHONGSHU_TYPE_VIDEO_NOTE)
            handleCommonVideo(context)
        } else if (helper.IsExistElementById("com.xingin.xhs:id/video_detail_layout")) {
            // 视频笔记
            // 清理缓存的分享按钮，因为不是短视频页面
            cleanupCachedShareButton()
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.XIAOHONGSHU_TYPE_VIDEO_NOTE)
            handleCommonArticle(context)
        } else if (isXiaohongshuGoodsPage(context)) {
            // 商品页 - 使用新的判断逻辑
            Log.d(TAG, "检测到小红书商品页面")
            // 清理缓存的分享按钮，因为不是短视频页面
            cleanupCachedShareButton()
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.XIAOHONGSHU_TYPE_GOODS)
            handleGoods(context)
        } else {
            // 未知类型
            // 清理缓存的分享按钮，因为不是短视频页面
            cleanupCachedShareButton()
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.TYPE_UNKNOWN)
            CustomToastHelper.showToast(context, "小红书当前页面暂不支持")
        }
    }

    /**
     * 判断当前页面是否为小红书商品页面
     *
     * 通过检查页面是否存在"加入购物车"或"立即购买"按钮来判断
     *
     * @param context 上下文
     * @return 是否为商品页面
     */
    private fun isXiaohongshuGoodsPage(context: Context): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        try {
            // 查找文本为"加入购物车"的节点
            val cartNodes = service.findNodesByContent("加入购物车")
            if (cartNodes != null && cartNodes.isNotEmpty()) {
                Log.d(TAG, "找到'加入购物车'按钮，判断为商品页面")
                // 释放资源
                cartNodes.forEach { it.recycle() }
                return true
            }

            // 查找文本为"立即购买"的节点
            val buyNodes = service.findNodesByContent("立即购买")
            if (buyNodes != null && buyNodes.isNotEmpty()) {
                Log.d(TAG, "找到'立即购买'按钮，判断为商品页面")
                // 释放资源
                buyNodes.forEach { it.recycle() }
                return true
            }

            // 如果都没找到，则不是商品页面
            Log.d(TAG, "未找到'加入购物车'或'立即购买'按钮，判断不是商品页面")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "判断是否为小红书商品页面时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 清理缓存的分享按钮
     */
    private fun cleanupCachedShareButton() {
        try {
            cachedShareButton?.recycle()
            cachedShareButton = null
        } catch (e: Exception) {
            Log.e(TAG, "清理缓存的分享按钮时出错: ${e.message}")
        }
    }

    // 普通图文收藏
    fun handleCommonArticle(context: Context) {
        val helper = AccessibilityHelper.getInstance(context)
        val result = helper.findAndClickElementById("com.xingin.xhs:id/moreOperateIV")
        if (result) {
            Log.d(TAG, "成功点击小红书分享按钮")
        } else {
            Log.d(TAG, "未找到小红书分享按钮")
        }

        Thread.sleep(500)

        // 刷新节点层次结构，确保获取最新的分享菜单状态
        val service = AppAccessibilityService.getInstance()

        val nodes = helper.findNodesByDescription("复制链接")
        if (nodes != null && nodes.isNotEmpty()) {
            val parentNode = nodes[0]
            val firstChild = parentNode.getChild(0)
            firstChild.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
            SharePanelHelper.notifyContentReady()
            // 释放资源
            parentNode.recycle()
        } else {
            Log.d(TAG, "未找到复制链接按钮")
        }
    }

    // 商品收藏
    fun handleGoods(context: Context) {
        val helper = AccessibilityHelper.getInstance(context)
        val service = AppAccessibilityService.getInstance() ?: return
        var rootNode = service.rootInActiveWindow ?: return
        var scrollResult = false

        // 1. 点击text为”分享商品“的节点
        val shareNodes = service.findNodesByContent("分享商品")
        var clickResult = false

        if (shareNodes != null && shareNodes.isNotEmpty()) {
            Log.d(TAG, "找到'分享商品'按钮")
            val shareNode = shareNodes[0]

            // 检查节点是否可点击
            if (shareNode.isClickable) {
                // 直接点击节点
                clickResult = shareNode.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
                Log.d(TAG, "直接点击'分享商品'按钮结果: $clickResult")
            } else {
                // 如果节点本身不可点击，尝试点击其父节点
                val parent = shareNode.parent
                if (parent != null && parent.isClickable) {
                    clickResult = parent.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
                    Log.d(TAG, "点击'分享商品'按钮的父节点结果: $clickResult")
                    parent.recycle()
                }
            }

            // 释放资源
            shareNode.recycle()
            // 释放其余节点资源
            for (i in 1 until shareNodes.size) {
                shareNodes[i].recycle()
            }
        } else {
            CustomToastHelper.showShortToast(context, "当前内容无法收藏，未找到分享商品按钮")
        }

        // 等待1秒让界面更新（增加等待时间与AutoJS脚本一致）
        Thread.sleep(1000)

        // 找到类名符合正则：.*RecyclerView.*的可滚动节点，并通过bounds取离屏幕底部近的节点
        val scrollableNodes = findScrollableRecyclerViewNodes(service)
        if (scrollableNodes.isNotEmpty()) {
            // 找到离屏幕底部最近的节点
            val bottomMostNode = findBottomMostNode(scrollableNodes)

            // 检查节点是否可滚动
            if (bottomMostNode.isScrollable) {
                Log.d(TAG, "找到可滚动的RecyclerView节点，尝试使用无障碍API直接滚动")
                // 使用无障碍API直接滚动节点
                scrollResult = bottomMostNode.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_SCROLL_FORWARD)
            } else {
                Log.d(TAG, "找到的RecyclerView节点不可滚动")
            }

            // 释放资源
            bottomMostNode.recycle()
        } else {
            Log.d(TAG, "未找到可滚动的RecyclerView节点")
        }

        // 每500毫秒查询一次节点，最多查询5秒
        Thread {
            try {
                val maxAttempts = 10 // 最多尝试10次，总共5秒
                var attempts = 0
                var success = false
                
                while (attempts < maxAttempts && !success) {
                    Log.d(TAG, "第${attempts + 1}次尝试查询节点...")
                    // 尝试执行specialClick
                    success = specialClick(context)
                    
                    if (success) {
                        Log.d(TAG, "成功找到并点击节点")
                        break
                    } else {
                        attempts++
                        if (attempts < maxAttempts) {
                            Log.d(TAG, "未找到节点，500毫秒后重试")
                            Thread.sleep(500)
                        } else {
                            Log.d(TAG, "达到最大尝试次数，放弃查询")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "轮询执行 specialClick 时出错: ${e.message}", e)
            }
        }.start()

        // 释放根节点资源
        rootNode.recycle()
    }

    // 处理复制链接节点的辅助方法
    private fun handleCopyNode(copyNode: android.view.accessibility.AccessibilityNodeInfo, helper: AccessibilityHelper) {
        val firstChild = copyNode.parent.parent
        firstChild.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
        firstChild.recycle()
        copyNode.recycle()
    }

    /**
     * 查找类名符合正则：.*RecyclerView.*的可滚动节点
     *
     * @param service 无障碍服务实例
     * @return 匹配的可滚动节点列表
     */
    private fun findScrollableRecyclerViewNodes(service: AccessibilityHelper.AppAccessibilityService): List<AccessibilityNodeInfo> {
        val rootNode = service.rootInActiveWindow ?: return emptyList()
        val result = mutableListOf<AccessibilityNodeInfo>()
        val recyclerViewPattern = Pattern.compile(".*RecyclerView.*")

        try {
            // 最多查找3个符合条件的节点，避免过度遍历
            findScrollableRecyclerViewNodesRecursive(rootNode, recyclerViewPattern, result, 3)
            Log.d(TAG, "找到 ${result.size} 个类名匹配 RecyclerView 的可滚动节点")
        } catch (e: Exception) {
            Log.e(TAG, "查找RecyclerView节点时出错: ${e.message}", e)
        }

        return result
    }

    /**
     * 递归查找类名符合正则的可滚动节点
     *
     * @param node 当前节点
     * @param pattern 类名正则表达式模式
     * @param result 结果列表
     * @param maxCount 最大查找数量
     */
    private fun findScrollableRecyclerViewNodesRecursive(
        node: AccessibilityNodeInfo,
        pattern: Pattern,
        result: MutableList<AccessibilityNodeInfo>,
        maxCount: Int
    ) {
        // 如果已经找到足够数量的节点，提前结束递归
        if (result.size >= maxCount) {
            return
        }

        try {
            // 检查当前节点的类名是否匹配正则表达式
            val className = node.className?.toString()
            if (className != null && pattern.matcher(className).matches()) {
                Log.d(TAG, "找到类名匹配的节点: $className, 可滚动: ${node.isScrollable}")

                // 只添加可滚动的节点
                if (node.isScrollable) {
                    result.add(AccessibilityNodeInfo.obtain(node))
                }
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                // 如果已经找到足够数量的节点，提前结束递归
                if (result.size >= maxCount) {
                    break
                }

                node.getChild(i)?.let { child ->
                    findScrollableRecyclerViewNodesRecursive(child, pattern, result, maxCount)
                    child.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "递归查找RecyclerView节点时出错", e)
        }
    }

    /**
     * 找到离屏幕底部最近的节点
     *
     * @param nodes 节点列表
     * @return 离屏幕底部最近的节点
     */
    private fun findBottomMostNode(nodes: List<AccessibilityNodeInfo>): AccessibilityNodeInfo {
        if (nodes.isEmpty()) {
            throw IllegalArgumentException("节点列表不能为空")
        }

        // 使用节点的bounds.bottom值来确定哪个节点离屏幕底部最近
        var bottomMostNode = nodes[0]
        var maxBottom = 0

        for (node in nodes) {
            val rect = android.graphics.Rect()
            node.getBoundsInScreen(rect)
            Log.d(TAG, "节点bounds: $rect")

            if (rect.bottom > maxBottom) {
                maxBottom = rect.bottom
                // 回收之前的节点
                if (bottomMostNode != node) {
                    bottomMostNode.recycle()
                    bottomMostNode = AccessibilityNodeInfo.obtain(node)
                }
            } else if (node != bottomMostNode) {
                // 回收不需要的节点
                node.recycle()
            }
        }

        Log.d(TAG, "选择的最底部节点bounds.bottom: $maxBottom")
        return bottomMostNode
    }

    /**
     * 打印节点及其子节点的详细信息
     *
     * 递归打印节点树，显示每个节点的类名、文本、描述、边界等信息
     * 这对于调试界面结构非常有用
     *
     * @param node 要打印的节点
     * @param depth 当前递归深度，用于缩进显示
     * @param maxDepth 最大递归深度，防止过深递归
     * @param maxChildrenPerNode 每个节点最多打印的子节点数量
     */
    private fun printNodeChildren(
        node: AccessibilityNodeInfo,
        depth: Int = 0,
        maxDepth: Int = 3,
        maxChildrenPerNode: Int = 10
    ) {
        if (depth > maxDepth) {
            Log.d(TAG, "${getIndent(depth)}... (达到最大递归深度)")
            return
        }

        try {
            val rect = android.graphics.Rect()
            node.getBoundsInScreen(rect)

            // 构建节点信息字符串
            val nodeInfo = StringBuilder()
            nodeInfo.append("${getIndent(depth)}节点[${depth}]: ")
            nodeInfo.append("类名=${node.className}, ")
            nodeInfo.append("文本=${node.text}, ")
            nodeInfo.append("描述=${node.contentDescription}, ")
            nodeInfo.append("边界=$rect, ")
            nodeInfo.append("可点击=${node.isClickable}, ")
            nodeInfo.append("可滚动=${node.isScrollable}, ")
            nodeInfo.append("子节点数=${node.childCount}")

            // 打印节点信息
            Log.d(TAG, nodeInfo.toString())

            // 打印子节点信息
            val childCount = node.childCount.coerceAtMost(maxChildrenPerNode)
            if (childCount > 0) {
                Log.d(TAG, "${getIndent(depth)}子节点列表(${childCount}/${node.childCount}):")

                for (i in 0 until childCount) {
                    node.getChild(i)?.let { child ->
                        printNodeChildren(child, depth + 1, maxDepth, maxChildrenPerNode)
                        child.recycle() // 回收子节点资源
                    }
                }

                if (childCount < node.childCount) {
                    Log.d(TAG, "${getIndent(depth)}... (还有${node.childCount - childCount}个子节点未显示)")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "${getIndent(depth)}打印节点信息时出错: ${e.message}", e)
        }
    }

    /**
     * 获取缩进字符串，用于打印节点树时的层次显示
     *
     * @param depth 缩进深度
     * @return 缩进字符串
     */
    private fun getIndent(depth: Int): String {
        return "  ".repeat(depth)
    }

    /**
     * 强制刷新无障碍节点层次结构
     * 通过临时禁用和重新启用无障碍服务来强制刷新节点
     *
     * @return 是否成功刷新节点层次结构
     */
    private fun forceRefreshAccessibilityNodeHierarchy(): Boolean {
        val service = AppAccessibilityService.getInstance() ?: return false

        try {
            // 1. 完全释放旧的根节点
            service.rootInActiveWindow?.recycle()

            // 2. 强制GC（可选）
            System.gc()

            // 3. 等待系统处理
            Thread.sleep(100)

            // 4. 获取全新的根节点
            val newRoot = service.rootInActiveWindow

            return newRoot != null
        } catch (e: Exception) {
            Log.e(TAG, "强制刷新节点层次结构失败", e)
            return false
        }
    }

    // 短视频收藏
    fun handleCommonVideo(context: Context) {
        val helper = AccessibilityHelper.getInstance(context)
        val service = AppAccessibilityService.getInstance() ?: return
        val rootNode = service.rootInActiveWindow ?: return

        try {
            // 使用缓存的分享按钮，避免重复查找
            if (cachedShareButton == null) {
                Log.e(TAG, "缓存的分享按钮为空，这不应该发生，因为前面已经验证了页面类型")
                // 作为备份方案，重新查找分享按钮
                val shareButtons = findNodesByDescriptionPattern(service, SHARE_BUTTON_PATTERN)

                if (shareButtons.isEmpty()) {
                    Log.e(TAG, "未找到分享按钮，无法继续操作")
                    rootNode.recycle()
                    return
                }

                Log.d(TAG, "找到的分享按钮数量: ${shareButtons.size}")
                cachedShareButton = AccessibilityNodeInfo.obtain(shareButtons[0])

                // 回收临时资源
                shareButtons.forEach { it.recycle() }
            }

            // 记录所选按钮信息
            val rect = android.graphics.Rect()
            cachedShareButton?.getBoundsInScreen(rect)
            Log.d(TAG, "使用缓存的分享按钮 - 可点击: ${cachedShareButton?.isClickable}, 位置: $rect")

            // 点击分享按钮
            val clickResult = cachedShareButton?.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK) ?: false
            Log.d(TAG, "点击分享按钮结果: $clickResult")

            Thread.sleep(500)  // 给足够时间让菜单出现

            // 点击复制链接
            val copyNodes = helper.findNodesByDescription("复制链接") ?: emptyList()
            if (copyNodes.isEmpty()) {
                Log.e(TAG, "未找到复制链接按钮，无法继续操作")
                rootNode.recycle()
                return
            }

            val copyNode = copyNodes[0]
            val copyChildNode = copyNode.getChild(0)
            copyChildNode.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
            SharePanelHelper.notifyContentReady()

            // 资源回收
            copyNode.recycle()

        } catch (e: Exception) {
            Log.e(TAG, "处理小红书短视频时出错: ${e.message}", e)
        } finally {
            // 确保根节点资源被回收
            try {
                rootNode.recycle()
            } catch (e: Exception) {
                Log.e(TAG, "回收根节点资源时出错: ${e.message}")
            }

            // 不在这里回收缓存的分享按钮，因为它可能在其他地方被使用
            // 它会在下一次查找时被回收
        }
    }
}
