package com.xunhe.aishoucang.helpers

/**
 * 平台类型识别工具类
 * 用于根据应用包名或scheme_url识别平台类型
 */
object PlatformTypeHelper {

    /**
     * 根据应用包名获取平台类型
     *
     * @param packageName 应用包名
     * @return 平台类型的拼音小写，如果无法识别则返回null
     */
    fun getPlatformTypeByPackage(packageName: String?): String? {
        val safePackage = packageName ?: ""
        return when {
            safePackage.startsWith("com.ss.android.ugc.aweme") -> "douyin"
            safePackage.startsWith("com.kuaishou.nebula") ||
                    safePackage.startsWith("com.smile.gifmaker") -> "kuaishou"
            safePackage.startsWith("com.xingin.xhs") -> "xiaohongshu"
            safePackage.startsWith("tv.danmaku.bili") -> "bilibili"
            safePackage.startsWith("com.tencent.mm") -> "wechat"
            safePackage.startsWith("com.sankuai.meituan") -> "meituan"
            safePackage.startsWith("com.douban.frodo") -> "douban"
            safePackage.startsWith("com.xunmeng.pinduoduo") -> "pinduoduo"
            safePackage.startsWith("com.taobao.taobao") -> "taobao"
            safePackage.startsWith("com.jingdong.app.mall") -> "jingdong"
            else -> null
        }
    }

    /**
     * 根据scheme_url获取平台类型
     *
     * @param schemeUrl 原生跳转链接
     * @return 平台类型的拼音小写，如果无法识别则返回null
     */
    fun getPlatformTypeBySchemeUrl(schemeUrl: String?): String? {
        if (schemeUrl.isNullOrEmpty()) {
            return null
        }

        return when {
            // 小红书
            schemeUrl.startsWith("xhsdiscover://") -> "xiaohongshu"
            // B站
            schemeUrl.startsWith("bilibili://") -> "bilibili"
            // 抖音
            schemeUrl.startsWith("snssdk1128://") -> "douyin"
            // 微信 (支持原生协议和公众号链接)
            schemeUrl.startsWith("weixin://") || isWechatPublicAccountUrl(schemeUrl) -> "wechat"
            // 豆瓣 (通过HTTP链接判断)
            schemeUrl.contains("douban.com") -> "douban"
            // 快手
            schemeUrl.startsWith("kwai://") || schemeUrl.startsWith("kuaishou://") -> "kuaishou"
            // 美团
            schemeUrl.startsWith("imeituan://") -> "meituan"
            // 拼多多
            schemeUrl.startsWith("pinduoduo://") || schemeUrl.contains("yangkeduo.com") -> "pinduoduo"
            // 淘宝
            schemeUrl.startsWith("taobao://") || schemeUrl.contains("taobao.com") -> "taobao"
            // 京东
            schemeUrl.startsWith("openapp.jdmobile://") || schemeUrl.contains("jd.com") -> "jingdong"
            else -> null
        }
    }

    /**
     * 判断是否为微信公众号链接
     *
     * @param url 待检查的URL
     * @return true如果是微信公众号链接，否则返回false
     */
    private fun isWechatPublicAccountUrl(url: String): Boolean {
        return try {
            val uri = android.net.Uri.parse(url)
            uri.host == "mp.weixin.qq.com"
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取平台类型，优先使用scheme_url，如果无法识别则使用包名
     *
     * @param schemeUrl 原生跳转链接
     * @param packageName 应用包名
     * @return 平台类型的拼音小写，如果都无法识别则返回null
     */
    fun getPlatformType(schemeUrl: String?, packageName: String?): String? {
        // 优先使用scheme_url识别
        getPlatformTypeBySchemeUrl(schemeUrl)?.let { return it }

        // 如果scheme_url无法识别，则使用包名识别
        return getPlatformTypeByPackage(packageName)
    }
}
