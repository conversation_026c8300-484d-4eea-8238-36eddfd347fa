package com.xunhe.aishoucang.lib

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.widget.RemoteViews
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.MainActivity

/**
 * 自定义通知面板助手类
 * 在通知栏显示一个包含APP图标、悬浮窗按钮和开关按钮的自定义面板
 */
class CustomNotificationPanel private constructor(private val context: Context) {

    companion object {
        private const val TAG = "CustomNotificationPanel"
        private const val CHANNEL_ID = "custom_panel_channel"
        private const val CHANNEL_NAME = "自定义面板"
        private const val NOTIFICATION_ID = 1001

        // 广播Action
        private const val ACTION_TOGGLE_FLOATING_WINDOW = "com.xunhe.aishoucang.TOGGLE_FLOATING_WINDOW"
        private const val ACTION_TOGGLE_SWITCH = "com.xunhe.aishoucang.TOGGLE_SWITCH"

        @Volatile
        private var instance: CustomNotificationPanel? = null

        fun getInstance(context: Context): CustomNotificationPanel {
            return instance ?: synchronized(this) {
                instance ?: CustomNotificationPanel(context.applicationContext).also { instance = it }
            }
        }
    }

    private var isShowing = false
    internal var switchState = false

    /**
     * 显示自定义通知面板
     */
    fun showPanel() {
        try {
            Log.d(TAG, "开始显示自定义通知面板")

            // 检查通知权限
            if (!checkNotificationPermission()) {
                Log.w(TAG, "通知权限未开启，引导用户开启权限")
                showNotificationPermissionDialog()
                return
            }

            createNotificationChannel()
            val notification = createCustomNotification()
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            notificationManager.notify(NOTIFICATION_ID, notification)
            isShowing = true
            Log.d(TAG, "自定义通知面板已显示，通知ID: $NOTIFICATION_ID")
        } catch (e: Exception) {
            Log.e(TAG, "显示自定义通知面板失败", e)
        }
    }

    /**
     * 检查通知权限
     */
    private fun checkNotificationPermission(): Boolean {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 需要检查通知权限
            val hasPermission = notificationManager.areNotificationsEnabled()
            Log.d(TAG, "Android 13+ 通知权限检查结果: $hasPermission")
            hasPermission
        } else {
            // Android 13以下默认有通知权限
            Log.d(TAG, "Android版本低于13，默认有通知权限")
            true
        }
    }

    /**
     * 显示通知权限对话框
     */
    private fun showNotificationPermissionDialog() {
        try {
            // 显示Toast提示用户
            Toast.makeText(context, "需要开启通知权限才能显示自定义通知面板，即将跳转到设置", Toast.LENGTH_LONG).show()

            // 立即跳转到设置页面，不需要延迟
            openNotificationSettings()

        } catch (e: Exception) {
            Log.e(TAG, "显示通知权限对话框失败", e)
        }
    }

    /**
     * 打开通知权限设置页面
     */
    private fun openNotificationSettings() {
        try {
            val intent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ 可以直接跳转到应用的通知设置页面
                Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                    putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
            } else {
                // Android 8.0以下跳转到应用详情页面
                Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.parse("package:${context.packageName}")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
            }

            context.startActivity(intent)
            Log.d(TAG, "已跳转到通知权限设置页面")
        } catch (e: Exception) {
            Log.e(TAG, "打开通知权限设置页面失败", e)
            // 如果跳转失败，尝试跳转到系统设置主页
            try {
                val fallbackIntent = Intent(Settings.ACTION_SETTINGS).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(fallbackIntent)
            } catch (ex: Exception) {
                Log.e(TAG, "跳转到系统设置也失败", ex)
            }
        }
    }

    /**
     * 隐藏自定义通知面板
     */
    fun hidePanel() {
        try {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(NOTIFICATION_ID)
            isShowing = false
            Log.d(TAG, "自定义通知面板已隐藏")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏自定义通知面板失败", e)
        }
    }

    /**
     * 切换开关状态并更新通知
     */
    fun toggleSwitch() {
        switchState = !switchState
        if (isShowing) {
            showPanel() // 重新显示以更新状态
        }
        Log.d(TAG, "开关状态已切换为: $switchState")
    }

    /**
     * 获取当前开关状态
     */
    fun getSwitchState(): Boolean {
        return switchState
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "自定义面板通知"
                enableLights(false)
                enableVibration(false)
                setShowBadge(false)
            }

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建自定义通知
     */
    private fun createCustomNotification(): Notification {
        Log.d(TAG, "开始创建自定义通知")

        // 使用完整的自定义布局
        val notification = createFullCustomNotification()
        Log.d(TAG, "完整自定义通知创建完成")
        return notification
    }

    /**
     * 创建简单通知（用于测试）
     */
    private fun createSimpleNotification(): Notification {
        // 创建点击通知的PendingIntent
        val mainIntent = Intent(context, MainActivity::class.java)
        val mainPendingIntent = PendingIntent.getActivity(
            context,
            0,
            mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle("自定义通知面板")
            .setContentText("左边APP图标，右边悬浮窗和开关按钮")
            .setContentIntent(mainPendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }

    /**
     * 创建完整的自定义通知
     */
    private fun createFullCustomNotification(): Notification {
        Log.d(TAG, "开始创建完整自定义通知")

        // 创建自定义布局
        val remoteViews = RemoteViews(context.packageName, R.layout.custom_notification_panel)
        Log.d(TAG, "RemoteViews创建成功")

        // 设置APP图标
        remoteViews.setImageViewResource(R.id.app_icon, R.mipmap.ic_launcher)
        Log.d(TAG, "APP图标设置完成")

        // 设置开关状态
        val switchIcon = if (switchState) R.drawable.ic_switch_on else R.drawable.ic_switch_off
        remoteViews.setImageViewResource(R.id.switch_button, switchIcon)
        Log.d(TAG, "开关图标设置完成，状态: $switchState")

        // 创建开关按钮的PendingIntent（直接控制悬浮窗）
        val switchIntent = Intent(ACTION_TOGGLE_FLOATING_WINDOW).apply {
            setPackage(context.packageName)
        }
        val switchPendingIntent = PendingIntent.getBroadcast(
            context,
            0,
            switchIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        remoteViews.setOnClickPendingIntent(R.id.switch_button, switchPendingIntent)

        // 创建点击通知的PendingIntent（与switch按钮相同的行为）
        val mainIntent = Intent(ACTION_TOGGLE_FLOATING_WINDOW).apply {
            setPackage(context.packageName)
        }
        val mainPendingIntent = PendingIntent.getBroadcast(
            context,
            1, // 使用不同的requestCode避免冲突
            mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setCustomContentView(remoteViews)
            .setContentIntent(mainPendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()

        Log.d(TAG, "自定义通知创建完成")
        return notification
    }

    /**
     * 广播接收器，处理通知面板按钮点击事件
     */
    class NotificationPanelReceiver : android.content.BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (context == null || intent == null) return

            when (intent.action) {
                ACTION_TOGGLE_FLOATING_WINDOW -> {
                    Log.d(TAG, "点击开关按钮，切换悬浮窗")
                    try {
                        val panel = getInstance(context)
                        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

                        // 切换悬浮窗状态
                        if (floatingWindowHelper.isFloatingWindowShowing()) {
                            floatingWindowHelper.hide()
                            panel.switchState = false
                            Log.d(TAG, "悬浮窗已隐藏，开关状态设为false")
                        } else {
                            floatingWindowHelper.show()
                            panel.switchState = true
                            Log.d(TAG, "悬浮窗已显示，开关状态设为true")
                        }

                        // 更新通知显示
                        if (panel.isShowing) {
                            panel.showPanel()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "切换悬浮窗失败", e)
                    }
                }
                ACTION_TOGGLE_SWITCH -> {
                    Log.d(TAG, "点击开关按钮（备用）")
                    try {
                        val panel = getInstance(context)
                        panel.toggleSwitch()
                    } catch (e: Exception) {
                        Log.e(TAG, "切换开关失败", e)
                    }
                }
            }
        }
    }
}
