package com.xunhe.aishoucang.helpers

import android.content.Context
import android.graphics.Rect
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.xunhe.aishoucang.lib.AccessibilityHelper
import java.util.regex.Pattern

/**
 * 微信应用处理器
 */
class WechatAppHandler : AppHandler {
    companion object {
        private const val TAG = "WechatAppHandler"
    }

    override fun handle(context: Context) {
        Log.d(TAG, "正在处理微信应用")
        SharePanelHelper.setCurrentContentType(ContentTypeConstants.WECHAT_TYPE_ARTICLE)
        SharePanelHelper.notifyContentReady()
        // val helper = AccessibilityHelper.getInstance(context)

        // // 检查是否是公众号文章页面
        // if (isArticlePage(helper)) {
        //     // 设置当前内容类型为微信公众号文章
        //     SharePanelHelper.setCurrentContentType(ContentTypeConstants.WECHAT_TYPE_ARTICLE)
        //     handleArticle(context)
        // } else {
        //     // 未知类型
        //     SharePanelHelper.setCurrentContentType(ContentTypeConstants.TYPE_UNKNOWN)
        //     Log.d(TAG, "微信当前页面暂不支持")
        //     CustomToastHelper.showToast(context, "微信当前页面暂不支持")
        // }
    }

    /**
     * 判断当前是否是公众号文章页面
     * 通过查找四个特定节点来判断：点赞、分享、在看和评论节点
     *
     * @param helper AccessibilityHelper实例
     * @return 是否是公众号文章页面
     */
    private fun isArticlePage(helper: AccessibilityHelper): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false
        val rootNode = service.rootInActiveWindow ?: return false

        // 定义四个正则表达式模式
        val likePattern = Pattern.compile("赞\\s*\\d*") // 匹配"赞"后跟可选的空格和数字
        val sharePattern = Pattern.compile("分享\\s*\\d*") // 匹配"分享"后跟可选的空格和数字
        val watchingPattern = Pattern.compile("在看\\s*\\d*") // 匹配"在看"后跟可选的空格和数字
        val commentPattern = Pattern.compile("\\*留言\\*") // 匹配"留言 写留言"

        // 用于记录找到的节点数量
        var foundCount = 0

        try {
            // 创建一个列表来存储所有节点，以便倒序遍历
            val allNodes = mutableListOf<AccessibilityNodeInfo>()
            collectAllNodes(rootNode, allNodes)
            Log.d(TAG, "找到所有节点数量: ${allNodes.size}")
            // 倒序遍历所有节点
            for (i in allNodes.size - 1 downTo 0) {
                val node = allNodes[i]
                val nodeText = node.text?.toString() ?: continue

                // 检查节点文本是否匹配任一模式
                when {
                    likePattern.matcher(nodeText).matches() -> {
                        Log.d(TAG, "找到点赞节点: $nodeText")
                        foundCount++
                    }
                    sharePattern.matcher(nodeText).matches() -> {
                        Log.d(TAG, "找到分享节点: $nodeText")
                        foundCount++
                    }
                    watchingPattern.matcher(nodeText).matches() -> {
                        Log.d(TAG, "找到在看节点: $nodeText")
                        foundCount++
                    }
                    commentPattern.matcher(nodeText).matches() -> {
                        Log.d(TAG, "找到评论节点: $nodeText")
                        foundCount++
                    }
                }

                // 如果找到了所有四个节点，提前返回
                if (foundCount >= 3) {
                    Log.d(TAG, "找到所有四个特征节点，确认为公众号文章页面")
                    return true
                }
            }

            // 释放所有节点资源
            for (node in allNodes) {
                try {
                    node.recycle()
                } catch (e: Exception) {
                    // 忽略回收异常
                }
            }

            Log.d(TAG, "只找到 $foundCount 个特征节点，不是公众号文章页面")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "判断公众号文章页面时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 收集所有节点到列表中，用于倒序遍历
     */
    private fun collectAllNodes(node: AccessibilityNodeInfo, result: MutableList<AccessibilityNodeInfo>) {
        try {
            // 添加当前节点到结果列表
            result.add(AccessibilityNodeInfo.obtain(node))

            // 递归收集子节点
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    collectAllNodes(child, result)
                    child.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "收集节点时出错", e)
        }
    }

    /**
     * 检查元素是否存在
     *
     * @param helper AccessibilityHelper实例
     * @param elementId 元素ID
     * @return 元素是否存在
     */
    private fun isElementExist(helper: AccessibilityHelper, elementId: String): Boolean {
        return helper.IsExistElementById(elementId)
    }

    /**
     * 查找并点击元素
     *
     * @param helper AccessibilityHelper实例
     * @param elementId 元素ID
     * @param elementName 元素名称（用于日志）
     * @return 是否成功点击
     */
    private fun findAndClickElement(helper: AccessibilityHelper, elementId: String, elementName: String): Boolean {
        val result = helper.findAndClickElementById(elementId)
        if (result) {
            Log.d(TAG, "成功点击$elementName")
        } else {
            Log.d(TAG, "未找到$elementName")
        }
        return result
    }

    /**
     * 查找匹配正则表达式的文本节点
     *
     * @param service 无障碍服务实例
     * @param pattern 正则表达式模式
     * @return 匹配的节点列表
     */
    private fun findNodesByTextPattern(service: AccessibilityHelper.AppAccessibilityService, pattern: Pattern): List<AccessibilityNodeInfo> {
        val rootNode = service.rootInActiveWindow ?: return emptyList()
        val result = mutableListOf<AccessibilityNodeInfo>()

        try {
            findNodesByTextPatternRecursive(rootNode, pattern, result)
        } catch (e: Exception) {
            Log.e(TAG, "查找匹配正则表达式的节点时出错: ${e.message}", e)
        }

        return result
    }

    /**
     * 递归查找匹配正则表达式的文本节点
     */
    private fun findNodesByTextPatternRecursive(
        node: AccessibilityNodeInfo,
        pattern: Pattern,
        result: MutableList<AccessibilityNodeInfo>
    ) {
        try {
            // 检查当前节点的文本内容是否匹配正则表达式
            val nodeText = node.text?.toString()
            if (nodeText != null && pattern.matcher(nodeText).matches()) {
                Log.d(TAG, "找到匹配正则的节点: $nodeText")
                result.add(AccessibilityNodeInfo.obtain(node))
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    findNodesByTextPatternRecursive(child, pattern, result)
                    child.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "递归查找匹配正则表达式的节点时出错", e)
        }
    }

    /**
     * 处理公众号文章页面
     *
     * @param context 上下文
     */
    private fun handleArticle(context: Context) {
        val helper = AccessibilityHelper.getInstance(context)
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return

        // 获取根节点
        val rootNode = service.rootInActiveWindow ?: return

        try {
            // 1. 查找并点击分享按钮（匹配"分享 数字"格式）
            val sharePattern = Pattern.compile("分享\\s+\\d+(\\.\\d+)?(万)?")
            val shareNodes = findNodesByTextPattern(service, sharePattern)

            if (shareNodes.isNotEmpty()) {
                Log.d(TAG, "找到分享按钮，数量: ${shareNodes.size}")

                // 点击第一个匹配的分享按钮
                val shareNode = shareNodes[0]
                val clickResult = shareNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                Log.d(TAG, "点击分享按钮结果: $clickResult")

                if (clickResult) {
                    // 等待分享菜单出现
                    Thread.sleep(500)

                    // 2. 获取更新后的根节点
                    val updatedRoot = service.rootInActiveWindow ?: return

                    // 3. 查找类名匹配RecyclerView的节点
                    val recyclerViewNodes = findRecyclerViewNodes(service)

                    if (recyclerViewNodes.isNotEmpty()) {
                        // 找到离屏幕底部最近的节点
                        val bottomMostNode = findBottomMostNode(recyclerViewNodes)

                        if (bottomMostNode != null) {
                            Log.d(TAG, "找到最底部的RecyclerView节点")

                            // 执行滚动操作
                            val scrollResult = bottomMostNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD)
                            Log.d(TAG, "滚动操作结果: $scrollResult")

                            // 释放滚动节点资源
                            bottomMostNode.recycle()

                            // 等待滚动完成
                            Thread.sleep(500)
                        } else {
                            Log.d(TAG, "未找到最底部的RecyclerView节点")
                        }
                    } else {
                        Log.d(TAG, "未找到匹配RecyclerView的节点")
                    }

                    // 释放资源
                    updatedRoot.recycle()

                    // 启动子线程，每500毫秒查找一次"复制链接"按钮，最多查找5秒
                    Thread {
                        try {
                            val maxAttempts = 10 // 最多尝试10次，总共5秒
                            var attempts = 0
                            var success = false

                            while (attempts < maxAttempts && !success) {
                                Log.d(TAG, "第${attempts + 1}次尝试查找'复制链接'按钮...")
                                // 尝试查找并点击"复制链接"按钮
                                success = findAndClickCopyLinkButton(context)

                                if (success) {
                                    Log.d(TAG, "成功找到并点击'复制链接'按钮")
                                    break
                                } else {
                                    attempts++
                                    if (attempts < maxAttempts) {
                                        Log.d(TAG, "未找到'复制链接'按钮，500毫秒后重试")
                                        Thread.sleep(500)
                                    } else {
                                        Log.d(TAG, "达到最大尝试次数，放弃查找")
                                        // 如果达到最大尝试次数仍未找到，通知内容已准备好，避免界面一直处于加载状态
                                        SharePanelHelper.notifyContentReady()
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "轮询查找'复制链接'按钮时出错: ${e.message}", e)
                            // 出错时也要通知内容已准备好，避免界面一直处于加载状态
                            SharePanelHelper.notifyContentReady()
                        }
                    }.start()
                }

                // 释放分享按钮节点资源
                shareNode.recycle()
            } else {
                Log.d(TAG, "未找到分享按钮")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理微信文章时出错: ${e.message}", e)
            // 出错时通知内容已准备好，避免界面一直处于加载状态
            SharePanelHelper.notifyContentReady()
        } finally {
            // 释放根节点资源
            try {
                rootNode.recycle()
            } catch (e: Exception) {
                Log.e(TAG, "回收根节点资源时出错: ${e.message}")
            }
        }
    }

    /**
     * 查找类名符合正则：.*RecyclerView.*的节点
     *
     * @param service 无障碍服务实例
     * @return 匹配的节点列表
     */
    private fun findRecyclerViewNodes(service: AccessibilityHelper.AppAccessibilityService): List<AccessibilityNodeInfo> {
        val rootNode = service.rootInActiveWindow ?: return emptyList()
        val result = mutableListOf<AccessibilityNodeInfo>()
        val recyclerViewPattern = Pattern.compile(".*RecyclerView.*")

        try {
            findRecyclerViewNodesRecursive(rootNode, recyclerViewPattern, result)
            Log.d(TAG, "找到 ${result.size} 个类名匹配 RecyclerView 的节点")
        } catch (e: Exception) {
            Log.e(TAG, "查找RecyclerView节点时出错: ${e.message}", e)
        }

        return result
    }

    /**
     * 递归查找类名符合正则的节点
     *
     * @param node 当前节点
     * @param pattern 类名正则表达式模式
     * @param result 结果列表
     */
    private fun findRecyclerViewNodesRecursive(
        node: AccessibilityNodeInfo,
        pattern: Pattern,
        result: MutableList<AccessibilityNodeInfo>
    ) {
        try {
            // 检查当前节点的类名是否匹配正则表达式
            val className = node.className?.toString()
            if (className != null && pattern.matcher(className).matches()) {
                Log.d(TAG, "找到类名匹配的节点: $className")
                result.add(AccessibilityNodeInfo.obtain(node))
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    findRecyclerViewNodesRecursive(child, pattern, result)
                    child.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "递归查找RecyclerView节点时出错", e)
        }
    }

    /**
     * 找到离屏幕底部最近的节点
     *
     * @param nodes 节点列表
     * @return 离屏幕底部最近的节点，如果列表为空则返回null
     */
    private fun findBottomMostNode(nodes: List<AccessibilityNodeInfo>): AccessibilityNodeInfo? {
        if (nodes.isEmpty()) {
            return null
        }

        // 使用节点的bounds.bottom值来确定哪个节点离屏幕底部最近
        var bottomMostNode = nodes[0]
        var maxBottom = 0

        for (node in nodes) {
            val rect = Rect()
            node.getBoundsInScreen(rect)
            Log.d(TAG, "节点bounds: $rect")

            if (rect.bottom > maxBottom) {
                maxBottom = rect.bottom
                // 回收之前的节点
                if (bottomMostNode != node) {
                    bottomMostNode.recycle()
                    bottomMostNode = AccessibilityNodeInfo.obtain(node)
                }
            } else if (node != bottomMostNode) {
                // 回收不需要的节点
                node.recycle()
            }
        }

        Log.d(TAG, "选择的最底部节点bounds.bottom: $maxBottom")
        return bottomMostNode
    }

    /**
     * 查找并点击"复制链接"按钮
     *
     * @param context 上下文
     * @return 是否成功找到并点击按钮
     */
    private fun findAndClickCopyLinkButton(context: Context): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        try {
            // 查找文本为"复制链接"的节点
            val copyLinkNodes = service.findNodesByContent("复制链接")
            if (copyLinkNodes != null && copyLinkNodes.isNotEmpty()) {
                Log.d(TAG, "找到'复制链接'节点，数量: ${copyLinkNodes.size}")

                // 获取第一个匹配的节点
                val copyLinkNode = copyLinkNodes[0]

                // 尝试获取父节点并点击
                val parentNode = copyLinkNode.parent
                if (parentNode != null && parentNode.isClickable) {
                    // 点击父节点
                    val clickResult = parentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    Log.d(TAG, "点击'复制链接'父节点结果: $clickResult")

                    // 释放资源
                    parentNode.recycle()
                    copyLinkNode.recycle()

                    // 清理其余节点资源
                    for (i in 1 until copyLinkNodes.size) {
                        copyLinkNodes[i].recycle()
                    }

                    // 如果点击成功，通知内容已准备好
                    if (clickResult) {
                        SharePanelHelper.notifyContentReady()
                        return true
                    }
                } else {
                    // 如果父节点不可点击，尝试直接点击节点本身
                    val clickResult = copyLinkNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    Log.d(TAG, "直接点击'复制链接'节点结果: $clickResult")

                    // 释放资源
                    parentNode?.recycle()
                    copyLinkNode.recycle()

                    // 清理其余节点资源
                    for (i in 1 until copyLinkNodes.size) {
                        copyLinkNodes[i].recycle()
                    }

                    // 如果点击成功，通知内容已准备好
                    if (clickResult) {
                        SharePanelHelper.notifyContentReady()
                        return true
                    }
                }
            } else {
                Log.d(TAG, "未找到'复制链接'节点")
            }

            return false
        } catch (e: Exception) {
            Log.e(TAG, "查找并点击'复制链接'按钮时出错: ${e.message}", e)
            return false
        }
    }
}
