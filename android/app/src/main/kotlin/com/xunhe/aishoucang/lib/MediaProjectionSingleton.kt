package com.xunhe.aishoucang.lib

import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log

/**
 * MediaProjection单例类
 * 用于保存截图权限结果，避免重复请求截图权限
 */
class MediaProjectionSingleton private constructor() {
    companion object {
        private const val TAG = "MediaProjectionSingleton"

        @Volatile
        private var instance: MediaProjectionSingleton? = null

        fun getInstance(): MediaProjectionSingleton {
            return instance ?: synchronized(this) {
                instance ?: MediaProjectionSingleton().also { instance = it }
            }
        }
    }

    // 保存权限结果
    private var resultCode: Int = 0
    private var resultData: Intent? = null
    private var hasPermission: Boolean = false

    // 检查是否有截图权限
    fun hasScreenshotPermission(): Boolean {
        return hasPermission && resultData != null
    }

    // 保存权限结果
    fun savePermissionResult(resultCode: Int, data: Intent?) {
        if (data != null) {
            this.resultCode = resultCode
            this.resultData = data
            this.hasPermission = true
            Log.d(TAG, "已保存截图权限结果")
        }
    }

    // 获取权限结果码
    fun getResultCode(): Int {
        return resultCode
    }

    // 获取权限结果数据
    fun getResultData(): Intent? {
        return resultData
    }

    // 从保存的权限结果创建新的MediaProjection对象
    fun createMediaProjection(context: Context): MediaProjection? {
        if (!hasScreenshotPermission()) {
            Log.e(TAG, "没有截图权限，无法创建MediaProjection对象")
            return null
        }

        try {
            val projectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            val projection = projectionManager.getMediaProjection(resultCode, resultData!!)

            if (projection != null) {
                // Android 14+需要注册回调
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    val callback = object : MediaProjection.Callback() {
                        override fun onStop() {
                            Log.d(TAG, "MediaProjection已停止，清除权限")
                            clearPermission()
                        }
                    }
                    projection.registerCallback(callback, Handler(Looper.getMainLooper()))
                    Log.d(TAG, "已为MediaProjection注册回调（Android 14+）")
                }

                Log.d(TAG, "已创建新的MediaProjection对象")
                return projection
            } else {
                Log.e(TAG, "无法创建MediaProjection对象")
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建MediaProjection对象失败: ${e.message}", e)
            // 如果创建失败，可能是权限已过期
            hasPermission = false
        }

        return null
    }

    // 清除权限
    fun clearPermission() {
        resultCode = 0
        resultData = null
        hasPermission = false
        Log.d(TAG, "已清除截图权限")
    }
}
