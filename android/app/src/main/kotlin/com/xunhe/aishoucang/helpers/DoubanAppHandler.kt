package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.view.WindowManager
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.lib.AccessibilityHelper

/**
 * 豆瓣应用处理器
 */
class DoubanAppHandler : AppHandler {
    companion object {
        private const val TAG = "DoubanAppHandler"

        // 豆瓣状态页面相关节点ID
        private const val SOCIAL_ACTION_BAR_ID = "com.douban.frodo:id/social_action_bar"
        private const val ICON_GIFT_ID = "com.douban.frodo:id/icon_gift"
        private const val ICON_COLLECT_ID = "com.douban.frodo:id/icon_collect"
        private const val ICON_RESHARE_ID = "com.douban.frodo:id/icon_reshare"
        private const val ICON_REACT_ID = "com.douban.frodo:id/icon_react"
        private const val SHARE_BUTTON_ID = "com.douban.frodo:id/share"
        // 豆瓣电影页面相关节点ID
        private const val TITLE_ID = "com.douban.frodo:id/title"
        // 重试间隔时间(毫秒)
        private const val RETRY_INTERVAL_MS = 500L
    }

    /**
     * 判断当前页面是否为豆瓣状态页面
     *
     * 通过检查页面是否同时存在以下节点来判断：
     * 1. 是否存在id为com.douban.frodo:id/social_action_bar的节点
     * 2. 是否存在id为com.douban.frodo:id/icon_gift的节点
     * 3. 是否存在id为com.douban.frodo:id/icon_collect的节点
     * 4. 是否存在id为com.douban.frodo:id/icon_reshare的节点
     * 5. 是否存在id为com.douban.frodo:id/icon_react的节点
     *
     * @param context 上下文
     * @return 是否为豆瓣状态页面
     */
    private fun isDoubanStatusPage(context: Context): Boolean {
        // 获取AccessibilityHelper实例
        val helper = AccessibilityHelper.getInstance(context)

        // 刷新节点层次结构，确保获取最新状态
        helper.refreshAccessibilityNodeHierarchy()

        // 检查是否存在社交操作栏
        val hasSocialActionBar = helper.IsExistElementById(SOCIAL_ACTION_BAR_ID)
        Log.d(TAG, "是否存在社交操作栏: $hasSocialActionBar")

        // 如果没有社交操作栏，则不是状态页面
        if (!hasSocialActionBar) {
            return false
        }

        // 检查是否存在礼物图标
        val hasGiftIcon = helper.IsExistElementById(ICON_GIFT_ID)
        Log.d(TAG, "是否存在礼物图标: $hasGiftIcon")

        // 检查是否存在收藏图标
        val hasCollectIcon = helper.IsExistElementById(ICON_COLLECT_ID)
        Log.d(TAG, "是否存在收藏图标: $hasCollectIcon")

        // 检查是否存在转发图标
        val hasReshareIcon = helper.IsExistElementById(ICON_RESHARE_ID)
        Log.d(TAG, "是否存在转发图标: $hasReshareIcon")

        // 检查是否存在反应图标
        val hasReactIcon = helper.IsExistElementById(ICON_REACT_ID)
        Log.d(TAG, "是否存在反应图标: $hasReactIcon")

        // 判断是否为状态页面：需要社交操作栏和其他四个图标同时存在
        val isStatusPage = hasSocialActionBar && hasGiftIcon && hasCollectIcon && hasReshareIcon && hasReactIcon

        Log.d(TAG, "是否为豆瓣状态页面: $isStatusPage")
        return isStatusPage
    }

    /**
     * 判断当前页面是否为豆瓣播客页面
     *
     * 判断逻辑：
     * 1. 检查是否存在text属性为"立即收听"的节点
     * 2. 检查是否存在text属性为"加入播放列表"的节点
     * 只有同时存在这两个节点才判定为豆瓣播客页面
     *
     * @param context 上下文
     * @return 是否为豆瓣播客页面
     */
    private fun isDoubanBoKePage(context: Context): Boolean {
        Log.d(TAG, "检查是否为豆瓣播客页面")

        // 获取AccessibilityHelper实例
        val helper = AccessibilityHelper.getInstance(context)

        // 刷新节点层次结构，确保获取最新状态
        helper.refreshAccessibilityNodeHierarchy()

        // 获取无障碍服务实例
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        try {
            // 查找文本为"立即收听"的节点
            val listenNowNodes = service.findNodesByContent("立即收听")
            val hasListenNowButton = listenNowNodes != null && listenNowNodes.isNotEmpty()
            Log.d(TAG, "是否存在'立即收听'按钮: $hasListenNowButton")

            // 如果没有"立即收听"按钮，则不是播客页面
            if (!hasListenNowButton) {
                return false
            }

            // 释放"立即收听"节点资源
            listenNowNodes?.forEach { it.recycle() }

            // 判断是否为播客页面：需要两个按钮同时存在
            val isBoKePage = hasListenNowButton

            Log.d(TAG, "是否为豆瓣播客页面: $isBoKePage")
            return isBoKePage
        } catch (e: Exception) {
            Log.e(TAG, "判断播客页面时出错: ${e.message}", e)
            return false
        }
    }


    /**
     * 判断当前页面是否为豆瓣电影页面
     *
     * 判断逻辑：
     * 检查是否存在id为"com.douban.frodo:id/title"的节点，且其text属性是"电影"
     *
     * @param context 上下文
     * @return 是否为豆瓣电影页面
     */
    private fun isDoubanSupportPage(context: Context): Boolean {
        // 获取AccessibilityHelper实例
        val helper = AccessibilityHelper.getInstance(context)

        // 刷新节点层次结构，确保获取最新状态
        helper.refreshAccessibilityNodeHierarchy()

        // 检查是否存在标题节点
        val hasTitleNode = helper.IsExistElementById(TITLE_ID)
        Log.d(TAG, "是否存在标题节点: $hasTitleNode")

        // 如果不存在标题节点，则不是电影页面
        if (!hasTitleNode) {
            return false
        }

        // 获取无障碍服务实例
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        // 获取根节点
        val rootNode = service.rootInActiveWindow ?: return false

        try {
            // 查找标题节点
            val titleNodes = service.findNodesByViewId(rootNode, TITLE_ID)

            // 检查标题节点的文本是否为"电影"
            for (node in titleNodes) {
                val titleText = node.text?.toString()
                Log.d(TAG, "标题节点文本: $titleText")

                if (titleText == "电影" || titleText == "电视" || titleText == "图书" || titleText == "唱片" || titleText == "同城" || titleText == "播客") {
                    Log.d(TAG, "确认为${titleText}页面")
                    return true
                }

                if (!titleText.isNullOrEmpty() && titleText.length > 0) {
                  return true
                }

                // 释放节点资源
                node.recycle()
            }
        } catch (e: Exception) {
            Log.e(TAG, "判断电影页面时出错: ${e.message}", e)
        }

        Log.d(TAG, "不是豆瓣电影页面")
        return false
    }

    /**
     * 处理豆瓣状态页面
     *
     * 实现逻辑：
     * 1. 点击分享按钮 (com.douban.frodo:id/share)
     * 2. 等待500毫秒让分享菜单显示
     * 3. 查找文本为"复制链接"的节点
     * 4. 获取其父节点并点击
     * 5. 通知内容已准备好
     *
     * @param context 上下文
     */
    private fun handleStatusPage(context: Context) {
        Log.d(TAG, "处理豆瓣状态页面")

        // 设置当前内容类型为豆瓣动态
        SharePanelHelper.setCurrentContentType(ContentTypeConstants.DOUBAN_TYPE_NOTE)

        // 点击分享按钮并复制链接
        if (clickShareButtonAndCopyLink(context)) {
            Log.d(TAG, "成功处理豆瓣状态页面")
        } else {
            Log.e(TAG, "处理豆瓣状态页面失败")
            CustomToastHelper.showToast(context, "无法获取豆瓣分享链接")

            // 隐藏收藏面板
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            SharePanelHelper.hideSharePanel(context, windowManager, false)
        }
    }

    /**
     * 点击分享按钮并复制链接
     *
     * @param context 上下文
     * @return 是否成功处理
     */
    private fun clickShareButtonAndCopyLink(context: Context): Boolean {
        try {
            // 获取AccessibilityHelper实例
            val helper = AccessibilityHelper.getInstance(context)

            // 1. 点击分享按钮
            Log.d(TAG, "尝试点击分享按钮")
            val clickResult = helper.findAndClickElementById(SHARE_BUTTON_ID)

            if (!clickResult) {
                Log.e(TAG, "点击分享按钮失败")
                return false
            }

            // 2. 等待500毫秒让分享菜单显示
            Thread.sleep(RETRY_INTERVAL_MS)

            // 刷新节点层次结构，确保获取最新的分享菜单状态
            helper.refreshAccessibilityNodeHierarchy()

            // 3. 查找并点击"复制链接"选项
            val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false
            return findAndClickCopyLinkButton(service)
        } catch (e: Exception) {
            Log.e(TAG, "点击分享按钮并复制链接时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 查找并点击"复制链接"按钮
     *
     * @param service 无障碍服务实例
     * @return 是否成功找到并点击按钮
     */
    private fun findAndClickCopyLinkButton(service: AccessibilityHelper.AppAccessibilityService): Boolean {
        try {
            // 查找文本为"复制链接"的节点
            val copyLinkNodes = service.findNodesByContent("复制链接")
            if (copyLinkNodes == null || copyLinkNodes.isEmpty()) {
                Log.e(TAG, "未找到'复制链接'节点")
                return false
            }

            Log.d(TAG, "找到'复制链接'节点，数量: ${copyLinkNodes.size}")

            // 获取第一个匹配的节点
            val copyLinkNode = copyLinkNodes[0]

            // 尝试获取父节点并点击
            val parentNode = copyLinkNode.parent
            if (parentNode != null && parentNode.isClickable) {
                // 点击父节点
                val clickResult = parentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                Log.d(TAG, "点击'复制链接'父节点结果: $clickResult")

                // 释放资源
                parentNode.recycle()
                copyLinkNode.recycle()

                // 清理其余节点资源
                for (i in 1 until copyLinkNodes.size) {
                    copyLinkNodes[i].recycle()
                }

                // 如果点击成功，通知内容已准备好
                if (clickResult) {
                    SharePanelHelper.notifyContentReady()
                    return true
                }
            } else {
                // 如果父节点不可点击，尝试直接点击节点本身
                val clickResult = copyLinkNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                Log.d(TAG, "直接点击'复制链接'节点结果: $clickResult")

                // 释放资源
                parentNode?.recycle()
                copyLinkNode.recycle()

                // 清理其余节点资源
                for (i in 1 until copyLinkNodes.size) {
                    copyLinkNodes[i].recycle()
                }

                // 如果点击成功，通知内容已准备好
                if (clickResult) {
                    SharePanelHelper.notifyContentReady()
                    return true
                }
            }

            Log.e(TAG, "点击'复制链接'按钮失败")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "查找并点击'复制链接'按钮时出错: ${e.message}", e)
            return false
        }
    }

    override fun handle(context: Context) {
        Log.d(TAG, "正在处理豆瓣应用")

        // 判断当前页面类型
        if (isDoubanStatusPage(context)) {
            // 豆瓣状态页面
            handleStatusPage(context)
        } else if (isDoubanSupportPage(context)) {
            // 豆瓣电影、电视、图书、音乐、同城页面
            handleStatusPage(context)
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.DOUBAN_TYPE_MOVIE)
        } else if (isDoubanBoKePage(context)) {
            // 豆瓣广播页面
            handleStatusPage(context)
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.DOUBAN_TYPE_BOKE)
        } else {
            // 未知页面类型
            Log.d(TAG, "未识别的豆瓣页面类型")

            // 设置当前内容类型为未知类型
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.TYPE_UNKNOWN)

            // 显示提示信息
            CustomToastHelper.showToast(context, "豆瓣当前页面暂不支持收藏")

            // 隐藏收藏面板
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            SharePanelHelper.hideSharePanel(context, windowManager, false)
        }
    }
}
