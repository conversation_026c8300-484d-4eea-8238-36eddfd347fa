package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.lib.FFmpegHelper

/**
 * 视频URL处理器
 * 负责视频URL的过滤、去重和批量下载处理
 */
object VideoUrlProcessor {
    private const val TAG = "VideoUrlProcessor"

    /**
     * 处理并下载视频URL列表
     * 包含过滤null值、去重、批量下载等功能
     *
     * @param context 上下文
     * @param videoUrls 原始视频URL列表
     * @param onProgress 进度回调，参数为步骤名称和进度百分比
     * @param onComplete 完成回调，参数为是否成功、处理的视频数量、错误信息
     */
    fun processAndDownloadVideos(
        context: Context,
        videoUrls: List<String>,
        onProgress: ((String, Float) -> Unit)? = null,
        onComplete: ((Boolean, Int, String?) -> Unit)? = null
    ) {
        Log.d(TAG, "开始处理视频URL列表，原始数量: ${videoUrls.size}")

        try {
            // 1. 过滤null和空字符串
            val filteredUrls = filterValidUrls(videoUrls)
            Log.d(TAG, "过滤后的URL数量: ${filteredUrls.size}")

            // 2. 去重
            val uniqueUrls = removeDuplicateUrls(filteredUrls)
            Log.d(TAG, "去重后的URL数量: ${uniqueUrls.size}")

            if (uniqueUrls.isEmpty()) {
                Log.w(TAG, "没有有效的视频URL需要处理")
                onComplete?.invoke(true, 0, null)
                return
            }

            // 3. 批量下载视频
            downloadVideosSequentially(
                context = context,
                videoUrls = uniqueUrls,
                onProgress = onProgress,
                onComplete = onComplete
            )

        } catch (e: Exception) {
            Log.e(TAG, "处理视频URL时发生异常", e)
            onComplete?.invoke(false, 0, "处理视频URL时发生异常: ${e.message}")
        }
    }

    /**
     * 过滤有效的URL
     * 去除null、空字符串和无效的URL
     *
     * @param urls 原始URL列表
     * @return 过滤后的有效URL列表
     */
    private fun filterValidUrls(urls: List<String>): List<String> {
        return urls.filter { url ->
            !url.isNullOrBlank() && isValidUrl(url)
        }.also { filteredUrls ->
            Log.d(TAG, "URL过滤结果:")
            Log.d(TAG, "  原始数量: ${urls.size}")
            Log.d(TAG, "  过滤后数量: ${filteredUrls.size}")
            filteredUrls.forEachIndexed { index, url ->
                Log.d(TAG, "  有效URL ${index + 1}: $url")
            }
        }
    }

    /**
     * 检查URL是否有效
     *
     * @param url 要检查的URL
     * @return 是否为有效URL
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            url.startsWith("http://", ignoreCase = true) || 
            url.startsWith("https://", ignoreCase = true)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 去除重复的URL
     *
     * @param urls URL列表
     * @return 去重后的URL列表
     */
    private fun removeDuplicateUrls(urls: List<String>): List<String> {
        val uniqueUrls = urls.distinct()
        
        Log.d(TAG, "URL去重结果:")
        Log.d(TAG, "  去重前数量: ${urls.size}")
        Log.d(TAG, "  去重后数量: ${uniqueUrls.size}")
        
        if (urls.size > uniqueUrls.size) {
            Log.d(TAG, "  发现并移除了 ${urls.size - uniqueUrls.size} 个重复URL")
        }
        
        uniqueUrls.forEachIndexed { index, url ->
            Log.d(TAG, "  最终URL ${index + 1}: $url")
        }
        
        return uniqueUrls
    }

    /**
     * 顺序下载视频列表
     * 逐个下载视频，避免并发下载造成的资源竞争
     *
     * @param context 上下文
     * @param videoUrls 要下载的视频URL列表
     * @param onProgress 进度回调
     * @param onComplete 完成回调
     */
    private fun downloadVideosSequentially(
        context: Context,
        videoUrls: List<String>,
        onProgress: ((String, Float) -> Unit)? = null,
        onComplete: ((Boolean, Int, String?) -> Unit)? = null
    ) {
        Log.d(TAG, "开始顺序下载 ${videoUrls.size} 个视频")

        val ffmpegHelper = FFmpegHelper.getInstance(context)
        var successCount = 0
        var currentIndex = 0
        val totalCount = videoUrls.size

        fun downloadNext() {
            if (currentIndex >= totalCount) {
                // 所有视频处理完成
                Log.d(TAG, "所有视频下载完成，成功: $successCount, 总数: $totalCount")
                onComplete?.invoke(true, successCount, null)
                return
            }

            val currentUrl = videoUrls[currentIndex]
            val progressPrefix = "下载视频 ${currentIndex + 1}/$totalCount"

            Log.d(TAG, "$progressPrefix: $currentUrl")

            // 为每个视频生成随机User-Agent和合适的Referer
            val randomUserAgent = UserAgentGenerator.generateUserAgentForUrl(currentUrl)
            val smartReferer = UserAgentGenerator.getRefererForUrl(currentUrl)
            Log.d(TAG, "$progressPrefix 使用User-Agent: $randomUserAgent")
            Log.d(TAG, "$progressPrefix 使用Referer: $smartReferer")

            // 使用FFmpegHelper的一体化方法下载并提取文案
            ffmpegHelper.downloadVideoAndExtractText(
                url = currentUrl,
                userAgent = randomUserAgent,
                referer = smartReferer,
                useChinese = true,
                progressCallback = { step, progress ->
                    // 计算总体进度
                    val baseProgress = (currentIndex.toFloat() / totalCount) * 100f
                    val currentVideoProgress = (progress / totalCount)
                    val totalProgress = baseProgress + currentVideoProgress
                    
                    onProgress?.invoke(step, totalProgress)
                },
                callback = { success, result, error ->
                    if (success) {
                        successCount++
                        Log.d(TAG, "$progressPrefix 成功")
                        if (result != null) {
                            Log.d(TAG, "提取结果: $result")
                        }
                    } else {
                        Log.e(TAG, "$progressPrefix 失败: $error")
                    }

                    // 继续下载下一个视频
                    currentIndex++

                    // 如果是最后一个视频，且成功处理了至少一个视频，则完成解析文案步骤
                    if (currentIndex >= totalCount && successCount > 0) {
                        Log.d(TAG, "所有视频处理完成，语音识别也已完成，进入最终步骤")
                        // 通过进度回调通知完成解析文案步骤
                        onProgress?.invoke("语音识别", 100f)
                    }

                    downloadNext()
                }
            )
        }

        // 开始下载第一个视频
        downloadNext()
    }
}
