package com.xunhe.aishoucang.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.xunhe.aishoucang.helpers.WechatHelper

/**
 * 微信回调Activity
 *
 * 用于接收微信SDK的回调，包括登录、分享等操作的结果
 * 必须在包名下的wxapi目录中，且名称必须为WXEntryActivity
 */
class WXEntryActivity : Activity(), IWXAPIEventHandler {
    private val TAG = "WXEntryActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 必须调用此方法，否则不会回调onReq和onResp
        try {
            WechatHelper.getInstance(this).wxApi.handleIntent(intent, this)
        } catch (e: Exception) {
            Log.e(TAG, "处理微信回调时出错: ${e.message}", e)
            finish()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)

        // 必须调用此方法，否则不会回调onReq和onResp
        try {
            WechatHelper.getInstance(this).wxApi.handleIntent(intent, this)
        } catch (e: Exception) {
            Log.e(TAG, "处理微信回调时出错: ${e.message}", e)
            finish()
        }
    }

    /**
     * 微信发送请求到第三方应用时，会回调到该方法
     */
    override fun onReq(req: BaseReq) {
        Log.d(TAG, "收到微信请求: ${req.type}")

        when (req.type) {
            ConstantsAPI.COMMAND_GETMESSAGE_FROM_WX -> {
                // 微信请求获取消息
                Log.d(TAG, "微信请求获取消息")
            }
            ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX -> {
                // 微信请求显示消息
                Log.d(TAG, "微信请求显示消息")
            }
            else -> {
                Log.d(TAG, "未知的微信请求类型: ${req.type}")
            }
        }

        finish()
    }

    /**
     * 第三方应用发送到微信的请求处理后的响应结果，会回调到该方法
     */
    override fun onResp(resp: BaseResp) {
        Log.d(TAG, "收到微信响应: type=${resp.type}, errCode=${resp.errCode}")

        when (resp.type) {
            ConstantsAPI.COMMAND_SENDAUTH -> {
                // 处理登录授权回调
                handleAuthResponse(resp as SendAuth.Resp)
            }
            ConstantsAPI.COMMAND_SENDMESSAGE_TO_WX -> {
                // 处理分享回调
                handleShareResponse(resp)
            }
            else -> {
                Log.d(TAG, "未知的微信响应类型: ${resp.type}")
                WechatHelper.getInstance(this).onResponseReceived(resp)
            }
        }

        finish()
    }

    /**
     * 处理微信登录授权回调
     */
    private fun handleAuthResponse(resp: SendAuth.Resp) {
        // 打印完整的响应对象信息
        Log.d(TAG, "微信授权响应完整内容: errCode=${resp.errCode}, errStr=${resp.errStr}, " +
                "code=${resp.code}, state=${resp.state}, " +
                "url=${resp.url}, lang=${resp.lang}, country=${resp.country}, " +
                "transaction=${resp.transaction}")

        when (resp.errCode) {
            BaseResp.ErrCode.ERR_OK -> {
                // 用户同意授权
                val code = resp.code
                Log.d(TAG, "微信授权成功，授权码: $code")

                // 通知WechatHelper处理授权结果
                WechatHelper.getInstance(this).onAuthSuccess(code)
            }
            BaseResp.ErrCode.ERR_USER_CANCEL -> {
                // 用户取消授权
                Log.d(TAG, "用户取消微信授权")
                WechatHelper.getInstance(this).onAuthCancel()
            }
            BaseResp.ErrCode.ERR_AUTH_DENIED -> {
                // 用户拒绝授权
                Log.d(TAG, "用户拒绝微信授权")
                WechatHelper.getInstance(this).onAuthDenied()
            }
            else -> {
                // 其他错误
                Log.e(TAG, "微信授权失败，错误码: ${resp.errCode}, 错误信息: ${resp.errStr}")
                WechatHelper.getInstance(this).onAuthFail(resp.errCode, resp.errStr)
            }
        }
    }

    /**
     * 处理微信分享回调
     */
    private fun handleShareResponse(resp: BaseResp) {
        when (resp.errCode) {
            BaseResp.ErrCode.ERR_OK -> {
                // 分享成功
                Log.d(TAG, "微信分享成功")
                WechatHelper.getInstance(this).onShareSuccess()
            }
            BaseResp.ErrCode.ERR_USER_CANCEL -> {
                // 用户取消分享
                Log.d(TAG, "用户取消微信分享")
                WechatHelper.getInstance(this).onShareCancel()
            }
            else -> {
                // 分享失败
                Log.e(TAG, "微信分享失败，错误码: ${resp.errCode}, 错误信息: ${resp.errStr}")
                WechatHelper.getInstance(this).onShareFail(resp.errCode, resp.errStr)
            }
        }
    }
}
