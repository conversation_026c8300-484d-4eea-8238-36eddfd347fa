package com.xunhe.aishoucang.helpers

import android.os.Handler
import android.os.Looper
import android.util.Log
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 * 小红书应用资源帮助类
 */
object XiaohongshuAppSourceHelper {
    private const val TAG = "XiaohongshuAppSourceHelper"

    /**
     * 小红书笔记资源信息
     */
    data class NoteInfo(
        val title: String?,
        val description: String?,
        val imageUrl: String?,
        val finalUrl: String
    )

    /**
     * 小红书分享内容信息
     */
    data class ShareInfo(
        val link: String,
        val username: String?
    )

    // 共享OkHttpClient实例，避免每次都创建新的客户端
    private val sharedOkHttpClient by lazy {
        OkHttpClient.Builder()
            .followRedirects(true)
            .followSslRedirects(true)
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build()
    }

    // 共享线程池，避免创建过多线程
    private val networkExecutor = Executors.newFixedThreadPool(2)

    /**
     * 获取小红书笔记信息
     *
     * @param url 小红书笔记URL
     * @param callback 回调函数，返回笔记信息
     */
    fun getNoteInfo(url: String, callback: (NoteInfo?) -> Unit) {
        // 使用共享线程池执行网络请求
        networkExecutor.execute {
            val startTime = System.currentTimeMillis()
            Log.d(TAG, "开始获取笔记信息: $url")

            try {
                // 使用共享OkHttpClient
                val request = Request.Builder().url(url).build()
                val response = sharedOkHttpClient.newCall(request).execute()
                val finalUrl = response.request.url.toString()
                val html = response.body?.string() ?: ""

                Log.d(TAG, "获取HTML内容完成，耗时: ${System.currentTimeMillis() - startTime}ms")

                // 提取标题
                val titlePattern = """<meta\s+(?:property|name)=["'](?:og:title|title)["']\s+content=["']([^"']+)["']""".toRegex()
                val titleMatch = titlePattern.find(html)
                val title = titleMatch?.groupValues?.getOrNull(1)

                // 提取描述
                val descPattern = """<meta\s+(?:property|name)=["'](?:description)["']\s+content=["']([^"']+)["']""".toRegex()
                val descMatch = descPattern.find(html)
                val description = descMatch?.groupValues?.getOrNull(1)

                // 提取图片URL
                val imagePattern = """<meta\s+(?:property|name)=["'](?:og:image)["']\s+content=["']([^"']+)["']""".toRegex()
                val imageMatch = imagePattern.find(html)
                val imageUrl = imageMatch?.groupValues?.getOrNull(1)

                Log.d(TAG, "解析HTML完成，耗时: ${System.currentTimeMillis() - startTime}ms")

                // 在主线程中回调结果
                Handler(Looper.getMainLooper()).post {
                    val noteInfo = NoteInfo(
                        title = title,
                        description = description,
                        imageUrl = imageUrl,
                        finalUrl = finalUrl
                    )
                    Log.d(TAG, "笔记信息获取完成，总耗时: ${System.currentTimeMillis() - startTime}ms")
                    callback(noteInfo)
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取小红书笔记信息出错: ${e.message}, 耗时: ${System.currentTimeMillis() - startTime}ms", e)
                // 在主线程中回调null
                Handler(Looper.getMainLooper()).post {
                    callback(null)
                }
            }
        }
    }

    /**
     * 从内容中提取小红书链接和用户昵称
     * 支持多种小红书分享格式
     *
     * @param content 剪贴板内容
     * @return 提取的链接，如果未找到则返回null
     */
    fun extractXiaohongshuLink(content: String?): String? {
        return extractXiaohongshuShareInfo(content)?.link
    }

    /**
     * 从内容中提取小红书分享信息，包括链接和用户昵称
     * 支持多种小红书分享格式
     *
     * @param content 剪贴板内容
     * @return 提取的分享信息，包括链接和可能的用户昵称，如果未找到则返回null
     */
    fun extractXiaohongshuShareInfo(content: String?): ShareInfo? {
        if (content.isNullOrBlank()) {
            return null
        }

        // 匹配以http或https开头，后面是非中文字符的链接
        // 中文字符的Unicode范围是\u4e00-\u9fa5
        val regex = "https?://[^\u4e00-\u9fa5，]*".toRegex()
        val linkMatch = regex.find(content)

        if (linkMatch == null) {
            return null
        }

        val link = linkMatch.value

        // 提取用户昵称
        // 匹配格式：xxx发布了一篇小红书笔记
        val usernamePattern = """([^0-9\s][^\s]*?)发布了""".toRegex()
        val usernameMatch = usernamePattern.find(content)
        val username = usernameMatch?.groupValues?.getOrNull(1)

        return ShareInfo(link, username)
    }
}