package com.xunhe.aishoucang.lib

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import android.os.Bundle
import android.util.Log

/**
 * 长截图权限请求Activity
 * 用于请求屏幕截图权限
 */
class LongScreenshotPermissionActivity : Activity() {
    companion object {
        private const val TAG = "LongScreenshotPermissionActivity"
        private const val REQUEST_MEDIA_PROJECTION = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置透明主题
        setTheme(android.R.style.Theme_Translucent_NoTitleBar)
        
        // 请求屏幕截图权限
        requestScreenCapturePermission()
    }
    
    /**
     * 请求屏幕截图权限
     */
    private fun requestScreenCapturePermission() {
        try {
            val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            startActivityForResult(
                projectionManager.createScreenCaptureIntent(),
                REQUEST_MEDIA_PROJECTION
            )
        } catch (e: Exception) {
            Log.e(TAG, "请求屏幕截图权限失败", e)
            finish()
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_MEDIA_PROJECTION) {
            if (resultCode == RESULT_OK && data != null) {
                // 处理权限结果
                LongScreenshotHelper.handleLongScreenshotPermissionResult(this, requestCode, resultCode, data)
            } else {
                Log.d(TAG, "用户拒绝了屏幕截图权限")
            }
            
            // 关闭Activity
            finish()
        }
    }
}
