package com.xunhe.aishoucang.lib

import android.content.ContentValues
import android.content.Context
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.util.Log
import com.arthenica.ffmpegkit.FFmpegKit
import com.arthenica.ffmpegkit.FFmpegKitConfig
import com.arthenica.ffmpegkit.FFprobeKit
import com.arthenica.ffmpegkit.ReturnCode
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.util.UUID
import java.util.concurrent.Executors
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import com.xunhe.aishoucang.helpers.SparkChainHelper
import com.xunhe.aishoucang.helpers.ConfigHelper



/**
 * FFmpeg助手类
 * 负责处理视频下载、视频帧提取和OCR相关功能
 */
class FFmpegHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "FFmpegHelper"

        // 单例实例
        @Volatile
        private var instance: FFmpegHelper? = null

        fun getInstance(context: Context): FFmpegHelper {
            return instance ?: synchronized(this) {
                instance ?: FFmpegHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    /**
     * 下载视频（带进度回调）
     * @param url 视频URL
     * @param userAgent 用户代理
     * @param referer 引用页
     * @param outputPath 输出路径，如果为null，则使用默认路径
     * @param saveToGallery 是否将视频保存到相册，默认为true
     * @param progressCallback 进度回调函数，参数为进度百分比 (0-100)
     * @param callback 回调函数，返回下载结果和输出路径
     */
    fun downloadVideoWithProgress(
        url: String,
        userAgent: String? = null,
        referer: String? = null,
        outputPath: String? = null,
        saveToGallery: Boolean = true,
        progressCallback: ((Float) -> Unit)? = null,
        callback: (success: Boolean, outputFilePath: String?, errorMessage: String?) -> Unit
    ) {
        try {
            // 确定输出路径
            val finalOutputPath = outputPath ?: generateDefaultOutputPath()

            // 创建输出目录（如果不存在）
            val outputFile = File(finalOutputPath)
            outputFile.parentFile?.mkdirs()

            // 构建FFmpeg命令
            val command = buildFFmpegCommand(url, userAgent, referer, finalOutputPath)

            Log.d(TAG, "执行FFmpeg命令: $command")

            // 执行FFmpeg命令
            FFmpegKit.executeAsync(command, { session ->
                val returnCode = session.returnCode

                if (ReturnCode.isSuccess(returnCode)) {
                    Log.d(TAG, "视频下载成功: $finalOutputPath")
                    progressCallback?.invoke(100f)

                    if (saveToGallery) {
                        // 保存到相册
                        val galleryPath = saveVideoToGallery(finalOutputPath)
                        // 保存到相册后删除缓存文件
                        val cacheFile = File(finalOutputPath)
                        if (cacheFile.exists() && cacheFile.delete()) {
                            Log.d(TAG, "已删除缓存视频文件: $finalOutputPath")
                        } else {
                            Log.w(TAG, "删除缓存视频文件失败: $finalOutputPath")
                        }
                        callback(true, galleryPath, null)
                    } else {
                        // 直接返回文件路径
                        callback(true, finalOutputPath, null)
                    }
                } else {
                    val errorMessage = session.failStackTrace ?: "未知错误"
                    Log.e(TAG, "视频下载失败: $errorMessage")
                    callback(false, null, errorMessage)
                }
            }, { log ->
                Log.d(TAG, "FFmpeg日志: ${log.message}")
            }, { statistics ->
                // 处理进度更新
                val timeInMicroseconds = statistics.time
                if (timeInMicroseconds > 0) {
                    // 这里可以根据时间估算进度，但FFmpeg的statistics不直接提供百分比
                    // 我们使用一个简单的估算方法
                    val timeInSeconds = timeInMicroseconds.toFloat() / 1000000f
                    val calculatedProgress = timeInSeconds * 10f
                    val estimatedProgress = if (calculatedProgress > 90f) 90f else calculatedProgress // 最多到90%
                    progressCallback?.invoke(estimatedProgress)
                }
                Log.d(TAG, "FFmpeg进度: ${statistics.time}ms")
            })
        } catch (e: Exception) {
            Log.e(TAG, "下载视频时出错", e)
            callback(false, null, e.message)
        }
    }

    /**
     * 下载视频
     * @param url 视频URL
     * @param userAgent 用户代理
     * @param referer 引用页
     * @param outputPath 输出路径，如果为null，则使用默认路径
     * @param saveToGallery 是否将视频保存到相册，默认为true
     * @param callback 回调函数，返回下载结果和输出路径
     */
    fun downloadVideo(
        url: String,
        userAgent: String? = null,
        referer: String? = null,
        outputPath: String? = null,
        saveToGallery: Boolean = true,
        callback: (success: Boolean, outputFilePath: String?, errorMessage: String?) -> Unit
    ) {
        try {
            // 确定输出路径
            val finalOutputPath = outputPath ?: generateDefaultOutputPath()

            // 创建输出目录（如果不存在）
            val outputFile = File(finalOutputPath)
            outputFile.parentFile?.mkdirs()

            // 构建FFmpeg命令
            val command = buildFFmpegCommand(url, userAgent, referer, finalOutputPath)

            Log.d(TAG, "执行FFmpeg命令: $command")

            // 执行FFmpeg命令
            FFmpegKit.executeAsync(command, { session ->
                val returnCode = session.returnCode

                if (ReturnCode.isSuccess(returnCode)) {
                    Log.d(TAG, "视频下载成功: $finalOutputPath")

                    if (saveToGallery) {
                        // 保存到相册
                        val galleryPath = saveVideoToGallery(finalOutputPath)
                        // 保存到相册后删除缓存文件
                        val cacheFile = File(finalOutputPath)
                        if (cacheFile.exists() && cacheFile.delete()) {
                            Log.d(TAG, "已删除缓存视频文件: $finalOutputPath")
                        } else {
                            Log.w(TAG, "删除缓存视频文件失败: $finalOutputPath")
                        }
                        callback(true, galleryPath, null)
                    } else {
                        // 直接返回文件路径
                        callback(true, finalOutputPath, null)
                    }
                } else {
                    val errorMessage = session.failStackTrace ?: "未知错误"
                    Log.e(TAG, "视频下载失败: $errorMessage")
                    callback(false, null, errorMessage)
                }
            }, { log ->
                Log.d(TAG, "FFmpeg日志: ${log.message}")
            }, { statistics ->
                // 可以在这里处理进度更新
                Log.d(TAG, "FFmpeg进度: ${statistics.time}ms")
            })
        } catch (e: Exception) {
            Log.e(TAG, "下载视频时出错", e)
            callback(false, null, e.message)
        }
    }

    /**
     * 构建FFmpeg命令
     */
    private fun buildFFmpegCommand(
        url: String,
        userAgent: String?,
        referer: String?,
        outputPath: String
    ): String {
        val commandBuilder = StringBuilder()

        // 网络优化参数 - 提升下载速度
        commandBuilder.append("-reconnect 1 ")                    // 启用重连
        commandBuilder.append("-reconnect_streamed 1 ")           // 流媒体重连
        commandBuilder.append("-reconnect_delay_max 2 ")          // 最大重连延迟2秒
        commandBuilder.append("-timeout 30000000 ")               // 超时时间30秒（微秒）
        commandBuilder.append("-multiple_requests 1 ")            // 允许多个请求
        commandBuilder.append("-seekable 0 ")                     // 禁用seek以提高流媒体性能

        // 缓冲区优化 - 适度增大缓冲区
        commandBuilder.append("-probesize 10M ")                  // 适度增大探测大小
        commandBuilder.append("-analyzeduration 10M ")            // 适度增大分析时长

        // 性能优化
        commandBuilder.append("-fflags +genpts ")                 // 生成PTS以提高兼容性
        commandBuilder.append("-threads 4 ")                      // 自动选择线程数

        // 添加用户代理
        if (!userAgent.isNullOrEmpty()) {
            commandBuilder.append("-user_agent \"$userAgent\" ")
        }

        // 添加引用页
        if (!referer.isNullOrEmpty()) {
            commandBuilder.append("-headers \"Referer: $referer\" ")
        }

        // 添加输入URL和输出路径
        commandBuilder.append("-i \"$url\" -c copy \"$outputPath\"")

        return commandBuilder.toString()
    }

    /**
     * 生成默认输出路径
     */
    private fun generateDefaultOutputPath(): String {
        val cacheDir = context.externalCacheDir ?: context.cacheDir
        val fileName = "video_${UUID.randomUUID().toString().substring(0, 8)}.mp4"
        return File(cacheDir, fileName).absolutePath
    }

    /**
     * 将视频保存到相册
     * @param sourceFilePath 源文件路径
     * @return 保存到相册的文件路径，如果失败则返回原路径
     */
    private fun saveVideoToGallery(sourceFilePath: String): String {
        val sourceFile = File(sourceFilePath)
        if (!sourceFile.exists()) {
            Log.e(TAG, "源文件不存在: $sourceFilePath")
            return sourceFilePath
        }

        val fileName = "douyin_${System.currentTimeMillis()}.mp4"

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10 及以上使用 MediaStore API
                val contentValues = ContentValues().apply {
                    put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
                    put(MediaStore.MediaColumns.MIME_TYPE, "video/mp4")
                    put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_MOVIES)
                }

                val uri = context.contentResolver.insert(
                    MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                    contentValues
                )
                if (uri != null) {
                    context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                        FileInputStream(sourceFile).use { inputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }
                    Log.d(TAG, "视频已保存到相册: $uri")
                    return uri.toString()
                }
            } else {
                // Android 9 及以下版本
                val moviesDir =
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MOVIES)
                if (!moviesDir.exists()) {
                    moviesDir.mkdirs()
                }

                val destinationFile = File(moviesDir, fileName)
                FileInputStream(sourceFile).use { inputStream ->
                    FileOutputStream(destinationFile).use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }

                // 通知媒体库扫描新文件
                MediaScannerConnection.scanFile(
                    context,
                    arrayOf(destinationFile.absolutePath),
                    arrayOf("video/mp4"),
                    null
                )

                Log.d(TAG, "视频已保存到相册: ${destinationFile.absolutePath}")
                return destinationFile.absolutePath
            }
        } catch (e: IOException) {
            Log.e(TAG, "保存视频到相册失败", e)
        }

        return sourceFilePath
    }

    /**
     * 将本地视频文件转换为音频
     * @param videoPath 本地视频文件路径
     * @param outputPath 输出路径，如果为null，则使用默认路径
     * @param audioCodec 音频编码器，默认为libmp3lame
     * @param audioBitrate 音频比特率，默认为192k
     * @param saveToGallery 是否将音频保存到相册，默认为true
     * @param callback 回调函数，返回转换结果和输出路径
     */
    fun convertVideoToAudio(
        videoPath: String,
        outputPath: String? = null,
        audioCodec: String = "mp3",
        audioBitrate: String = "192k",
        saveToGallery: Boolean = true,
        callback: (success: Boolean, outputFilePath: String?, errorMessage: String?) -> Unit
    ) {
        try {
            // 检查输入视频文件是否存在
            val videoFile = File(videoPath)
            if (!videoFile.exists()) {
                Log.e(TAG, "输入视频文件不存在: $videoPath")
                callback(false, null, "输入视频文件不存在")
                return
            }

            // 确定输出路径
            val initialOutputPath = outputPath ?: generateDefaultAudioOutputPath()

            // 根据编码器调整输出路径和构建命令
            val (command, finalOutputPath) = buildFFmpegVideoToAudioCommandWithPath(videoPath, initialOutputPath, audioCodec, audioBitrate)

            // 创建输出目录（如果不存在）
            val outputFile = File(finalOutputPath)
            outputFile.parentFile?.mkdirs()

            Log.d(TAG, "执行FFmpeg视频转音频命令: $command")

            // 执行FFmpeg命令
            FFmpegKit.executeAsync(command, { session ->
                val returnCode = session.returnCode

                if (ReturnCode.isSuccess(returnCode)) {
                    Log.d(TAG, "视频转音频成功: $finalOutputPath")

                    if (saveToGallery) {
                        // 保存到相册
                        val galleryPath = saveAudioToGallery(finalOutputPath)
                        // 保存到相册后删除缓存文件
                        val cacheFile = File(finalOutputPath)
                        if (cacheFile.exists() && cacheFile.delete()) {
                            Log.d(TAG, "已删除缓存音频文件: $finalOutputPath")
                        } else {
                            Log.w(TAG, "删除缓存音频文件失败: $finalOutputPath")
                        }
                        callback(true, galleryPath, null)
                    } else {
                        // 直接返回文件路径
                        callback(true, finalOutputPath, null)
                    }
                } else {
                    val errorMessage = session.failStackTrace ?: "未知错误"
                    Log.e(TAG, "视频转音频失败: $errorMessage")
                    callback(false, null, errorMessage)
                }
            }, { log ->
                Log.d(TAG, "FFmpeg视频转音频日志: ${log.message}")
            }, { statistics ->
                // 可以在这里处理进度更新
                Log.d(TAG, "FFmpeg视频转音频进度: ${statistics.time}ms")
            })
        } catch (e: Exception) {
            Log.e(TAG, "视频转音频时出错", e)
            callback(false, null, e.message)
        }
    }

    /**
     * 构建FFmpeg视频转音频命令并返回实际输出路径
     */
    private fun buildFFmpegVideoToAudioCommandWithPath(
        videoPath: String,
        outputPath: String,
        audioCodec: String,
        audioBitrate: String
    ): Pair<String, String> {
        // 根据编码器选择合适的FFmpeg命令和输出路径
        return when (audioCodec.lowercase()) {
            "libmp3lame" -> {
                // 使用libmp3lame编码器，输出为MP3格式
                val command = "-i \"$videoPath\" -vn -c:a libmp3lame -b:a $audioBitrate \"$outputPath\""
                Pair(command, outputPath)
            }
            "mp3" -> {
                // 由于当前FFmpeg版本不支持mp3编码器，改用AAC编码器，输出为M4A格式
                Log.w(TAG, "MP3编码器不可用，改用AAC编码器")
                val aacOutputPath = outputPath.replace(".mp3", ".m4a")
                val command = "-i \"$videoPath\" -vn -c:a aac -b:a $audioBitrate \"$aacOutputPath\""
                Pair(command, aacOutputPath)
            }
            "aac" -> {
                // 使用AAC编码器，输出为M4A格式
                val aacOutputPath = outputPath.replace(".mp3", ".m4a")
                val command = "-i \"$videoPath\" -vn -c:a aac -b:a $audioBitrate \"$aacOutputPath\""
                Pair(command, aacOutputPath)
            }
            "copy" -> {
                // 直接复制音频流，不重新编码
                val command = "-i \"$videoPath\" -vn -c:a copy \"$outputPath\""
                Pair(command, outputPath)
            }
            else -> {
                // 默认使用AAC编码器，因为它在Android上有更好的兼容性
                Log.w(TAG, "未知编码器 $audioCodec，使用AAC编码器")
                val aacOutputPath = outputPath.replace(".mp3", ".m4a")
                val command = "-i \"$videoPath\" -vn -c:a aac -b:a $audioBitrate \"$aacOutputPath\""
                Pair(command, aacOutputPath)
            }
        }
    }

    /**
     * 生成默认音频输出路径
     */
    private fun generateDefaultAudioOutputPath(): String {
        val timestamp = System.currentTimeMillis()
        // 默认使用M4A格式，因为AAC编码器输出M4A格式
        val fileName = "audio_$timestamp.m4a"
        val cacheDir = context.cacheDir
        return File(cacheDir, fileName).absolutePath
    }

    /**
     * 保存音频到相册
     */
    private fun saveAudioToGallery(sourceFilePath: String): String {
        try {
            val sourceFile = File(sourceFilePath)
            if (!sourceFile.exists()) {
                Log.e(TAG, "源音频文件不存在: $sourceFilePath")
                return sourceFilePath
            }

            // 根据源文件扩展名确定输出格式
            val sourceExtension = File(sourceFilePath).extension.lowercase()
            val (fileName, mimeType) = when (sourceExtension) {
                "mp3" -> "audio_${System.currentTimeMillis()}.mp3" to "audio/mpeg"
                "m4a", "aac" -> "audio_${System.currentTimeMillis()}.m4a" to "audio/mp4"
                else -> "audio_${System.currentTimeMillis()}.m4a" to "audio/mp4" // 默认使用M4A
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10 及以上版本使用 MediaStore
                val contentValues = ContentValues().apply {
                    put(MediaStore.Audio.Media.DISPLAY_NAME, fileName)
                    put(MediaStore.Audio.Media.MIME_TYPE, mimeType)
                    put(MediaStore.Audio.Media.RELATIVE_PATH, Environment.DIRECTORY_MUSIC)
                }

                val uri = context.contentResolver.insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, contentValues)
                uri?.let { audioUri ->
                    context.contentResolver.openOutputStream(audioUri)?.use { outputStream ->
                        FileInputStream(sourceFile).use { inputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }
                    Log.d(TAG, "音频已保存到相册: $audioUri")
                    return audioUri.toString()
                }
            } else {
                // Android 9 及以下版本
                val musicDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC)
                if (!musicDir.exists()) {
                    musicDir.mkdirs()
                }

                val destinationFile = File(musicDir, fileName)
                FileInputStream(sourceFile).use { inputStream ->
                    FileOutputStream(destinationFile).use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }

                // 通知媒体库扫描新文件
                MediaScannerConnection.scanFile(
                    context,
                    arrayOf(destinationFile.absolutePath),
                    arrayOf(mimeType),
                    null
                )

                Log.d(TAG, "音频已保存到相册: ${destinationFile.absolutePath}")
                return destinationFile.absolutePath
            }
        } catch (e: IOException) {
            Log.e(TAG, "保存音频到相册失败", e)
        }

        return sourceFilePath
    }

    /**
     * 将音频文件转换为PCM格式（用于语音识别）
     * @param audioPath 输入音频文件路径
     * @param outputPath 输出PCM文件路径，如果为null则自动生成
     * @param sampleRate 采样率，默认16000Hz
     * @param channels 声道数，默认1（单声道）
     * @param callback 回调函数，返回转换结果、输出路径和音频时长
     */
    fun convertAudioToPCM(
        audioPath: String,
        outputPath: String? = null,
        sampleRate: Int = 16000,
        channels: Int = 1,
        callback: (success: Boolean, outputFilePath: String?, duration: Double?, errorMessage: String?) -> Unit
    ) {
        try {
            // 检查输入音频文件是否存在
            val audioFile = File(audioPath)
            if (!audioFile.exists()) {
                Log.e(TAG, "输入音频文件不存在: $audioPath")
                callback(false, null, null, "输入音频文件不存在")
                return
            }

            // 确定输出路径
            val finalOutputPath = outputPath ?: generateDefaultPCMOutputPath()

            // 创建输出目录（如果不存在）
            val outputFile = File(finalOutputPath)
            outputFile.parentFile?.mkdirs()

            // 构建FFmpeg命令：转换为16位PCM格式
            val command = "-i \"$audioPath\" -ar $sampleRate -ac $channels -f s16le \"$finalOutputPath\""

            Log.d(TAG, "执行FFmpeg音频转PCM命令: $command")

            // 用于存储完整的FFmpeg日志
            val logBuffer = StringBuilder()

            // 执行FFmpeg命令
            FFmpegKit.executeAsync(command, { session ->
                val returnCode = session.returnCode

                if (ReturnCode.isSuccess(returnCode)) {
                    Log.d(TAG, "音频转PCM成功: $finalOutputPath")

                    // 转换完成后，从完整日志中解析时长
                    val completeLogs = logBuffer.toString()
                    Log.d(TAG, "=== FFmpeg完整日志开始 ===")
                    Log.d(TAG, completeLogs)
                    Log.d(TAG, "=== FFmpeg完整日志结束 ===")

                    val audioDuration = parseDurationFromLogs(completeLogs)

                    if (audioDuration != null) {
                        Log.d(TAG, "从转换日志中获取到音频时长: ${audioDuration}秒")
                        callback(true, finalOutputPath, audioDuration, null)
                    } else {
                        Log.e(TAG, "未能从转换日志中解析到音频时长，转换失败")
                        callback(false, null, null, "未能获取音频时长")
                    }
                } else {
                    val errorMessage = session.failStackTrace ?: "未知错误"
                    Log.e(TAG, "音频转PCM失败: $errorMessage")
                    callback(false, null, null, errorMessage)
                }
            }, { log ->
                // 将日志拼接到内存中，不再逐条处理
                logBuffer.append(log.message).append("\n")
            }, { statistics ->
                // 可以在这里处理进度信息
            })

        } catch (e: Exception) {
            Log.e(TAG, "音频转PCM异常", e)
            callback(false, null, null, "音频转PCM异常: ${e.message}")
        }
    }

    /**
     * 从FFmpeg日志中解析音频时长
     * @param logs 完整的FFmpeg日志
     * @return 解析出的时长（秒），如果解析失败返回null
     */
    private fun parseDurationFromLogs(logs: String): Double? {
        try {
            // 匹配Duration格式，支持跨行：Duration:[\s\S]*?(HH:MM:SS.CC)
            val durationRegex = Regex("Duration:[\\s\\S]*?(\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{2})")
            val matchResult = durationRegex.find(logs)

            if (matchResult != null) {
                val hours = matchResult.groupValues[1].toInt()
                val minutes = matchResult.groupValues[2].toInt()
                val seconds = matchResult.groupValues[3].toInt()
                val centiseconds = matchResult.groupValues[4].toInt()

                val totalSeconds = hours * 3600 + minutes * 60 + seconds + centiseconds / 100.0
                Log.d(TAG, "解析到音频时长: ${totalSeconds}秒 (${hours}:${minutes}:${seconds}.${centiseconds})")
                return totalSeconds
            } else {
                Log.w(TAG, "未在日志中找到Duration信息")
                return null
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析音频时长异常", e)
            return null
        }
    }

    /**
     * 将音频文件转换为PCM格式（支持进度回调）
     * @param audioPath 输入音频文件路径
     * @param outputPath 输出PCM文件路径，如果为null则自动生成
     * @param sampleRate 采样率，默认16000Hz
     * @param channels 声道数，默认1（单声道）
     * @param progressCallback 进度回调函数，参数为进度百分比(0-100)
     * @param callback 回调函数，返回转换结果、输出路径和音频时长
     */
    fun convertAudioToPCMWithProgress(
        audioPath: String,
        outputPath: String? = null,
        sampleRate: Int = 16000,
        channels: Int = 1,
        progressCallback: ((Float) -> Unit)? = null,
        callback: (success: Boolean, outputFilePath: String?, duration: Double?, errorMessage: String?) -> Unit
    ) {
        try {
            // 检查输入音频文件是否存在
            val audioFile = File(audioPath)
            if (!audioFile.exists()) {
                Log.e(TAG, "输入音频文件不存在: $audioPath")
                callback(false, null, null, "输入音频文件不存在")
                return
            }

            // 确定输出路径
            val finalOutputPath = outputPath ?: generateDefaultPCMOutputPath()

            // 创建输出目录（如果不存在）
            val outputFile = File(finalOutputPath)
            outputFile.parentFile?.mkdirs()

            // 构建FFmpeg命令：转换为16位PCM格式
            val command = "-i \"$audioPath\" -ar $sampleRate -ac $channels -f s16le \"$finalOutputPath\""

            Log.d(TAG, "执行FFmpeg音频转PCM命令: $command")

            // 用于存储完整的FFmpeg日志
            val logBuffer = StringBuilder()
            var totalDuration: Double? = null

            // 执行FFmpeg命令
            FFmpegKit.executeAsync(command, { session ->
                val returnCode = session.returnCode

                if (ReturnCode.isSuccess(returnCode)) {
                    Log.d(TAG, "音频转PCM成功: $finalOutputPath")

                    // 转换完成后，从完整日志中解析时长
                    val completeLogs = logBuffer.toString()
                    Log.d(TAG, "=== FFmpeg完整日志开始 ===")
                    Log.d(TAG, completeLogs)
                    Log.d(TAG, "=== FFmpeg完整日志结束 ===")

                    val audioDuration = parseDurationFromLogs(completeLogs)

                    if (audioDuration != null) {
                        Log.d(TAG, "从转换日志中获取到音频时长: ${audioDuration}秒")
                        // 转换完成，进度100%
                        progressCallback?.invoke(100f)
                        callback(true, finalOutputPath, audioDuration, null)
                    } else {
                        Log.e(TAG, "未能从转换日志中解析到音频时长，转换失败")
                        callback(false, null, null, "未能获取音频时长")
                    }
                } else {
                    val errorMessage = session.failStackTrace ?: "未知错误"
                    Log.e(TAG, "音频转PCM失败: $errorMessage")
                    callback(false, null, null, errorMessage)
                }
            }, { log ->
                // 将日志拼接到内存中，同时尝试解析时长信息用于进度计算
                logBuffer.append(log.message).append("\n")

                // 尝试从日志中提取时长信息（用于进度计算）
                if (totalDuration == null) {
                    totalDuration = parseDurationFromLogs(log.message)
                }
            }, { statistics ->
                // 处理进度信息
                if (totalDuration != null && totalDuration!! > 0) {
                    val currentTime = statistics.time / 1000000.0 // 转换为秒
                    val progress = ((currentTime / totalDuration!!) * 100).coerceAtMost(95.0) // 最多95%，留5%给完成处理
                    progressCallback?.invoke(progress.toFloat())
                    Log.d(TAG, "转换音频进度: ${progress.toInt()}% (${currentTime.toInt()}s/${totalDuration!!.toInt()}s)")
                }
            })

        } catch (e: Exception) {
            Log.e(TAG, "音频转PCM异常", e)
            callback(false, null, null, "音频转PCM异常: ${e.message}")
        }
    }

    /**
     * 生成默认的PCM输出路径
     */
    private fun generateDefaultPCMOutputPath(): String {
        val cacheDir = context.cacheDir
        val timestamp = System.currentTimeMillis()
        return File(cacheDir, "audio_${timestamp}.pcm").absolutePath
    }

    /**
     * 取消所有正在执行的FFmpeg任务
     */
    fun cancelAllTasks() {
        FFmpegKit.cancel()
    }



    /**
     * 切割音频文件
     * @param audioPath 音频文件路径
     * @param startTime 开始时间（秒）
     * @param duration 持续时间（秒）
     * @param segmentIndex 片段索引，用于生成文件名
     * @param callback 回调函数，返回切割结果和输出路径
     */
    fun splitAudio(
        audioPath: String,
        startTime: Int,
        duration: Int,
        segmentIndex: Int,
        callback: (success: Boolean, outputFilePath: String?, errorMessage: String?) -> Unit
    ) {
        try {
            // 检查输入音频文件是否存在
            val audioFile = File(audioPath)
            if (!audioFile.exists()) {
                Log.e(TAG, "输入音频文件不存在: $audioPath")
                callback(false, null, "输入音频文件不存在")
                return
            }

            // 生成输出文件路径
            val outputPath = generateAudioSegmentPath(audioPath, segmentIndex)

            // 创建输出目录（如果不存在）
            val outputFile = File(outputPath)
            outputFile.parentFile?.mkdirs()

            // 构建FFmpeg切割命令
            // 检查是否为PCM文件，如果是则需要指定格式参数
            val command = if (audioPath.endsWith(".pcm", ignoreCase = true)) {
                // PCM文件需要指定格式参数：16位，16000Hz采样率，单声道，小端序
                "-f s16le -ar 16000 -ac 1 -i \"$audioPath\" -ss $startTime -t $duration -f s16le \"$outputPath\""
            } else {
                // 其他音频格式使用常规命令
                "-i \"$audioPath\" -ss $startTime -t $duration -c copy \"$outputPath\""
            }

            Log.d(TAG, "执行FFmpeg音频切割命令: $command")

            // 执行FFmpeg命令
            FFmpegKit.executeAsync(command, { session ->
                val returnCode = session.returnCode

                if (ReturnCode.isSuccess(returnCode)) {
                    Log.d(TAG, "音频切割成功: $outputPath")
                    callback(true, outputPath, null)
                } else {
                    val errorMessage = session.failStackTrace ?: "未知错误"
                    Log.e(TAG, "音频切割失败: $errorMessage")
                    callback(false, null, errorMessage)
                }
            }, { log ->
                Log.d(TAG, "FFmpeg音频切割日志: ${log.message}")
            }, { statistics ->
                // 可以在这里处理进度信息
            })

        } catch (e: Exception) {
            Log.e(TAG, "音频切割异常", e)
            callback(false, null, "音频切割异常: ${e.message}")
        }
    }

    /**
     * 生成音频片段文件路径
     */
    private fun generateAudioSegmentPath(originalPath: String, segmentIndex: Int): String {
        val originalFile = File(originalPath)
        val nameWithoutExtension = originalFile.nameWithoutExtension
        val extension = originalFile.extension
        val cacheDir = context.cacheDir
        return File(cacheDir, "${nameWithoutExtension}_segment_${segmentIndex}.${extension}").absolutePath
    }



    /**
     * 将视频转换为每隔500ms一帧的图片序列
     * @param videoPath 视频文件路径
     * @param outputDir 输出目录，如果为null，则使用默认路径
     * @param callback 回调函数，返回处理结果和输出目录
     */
    fun extractFramesFromVideo(
        videoPath: String,
        outputDir: String? = null,
        callback: (success: Boolean, outputDirPath: String?, errorMessage: String?) -> Unit
    ) {
        try {
            // 确定输出目录
            val finalOutputDir = outputDir ?: generateDefaultFramesDir()

            // 创建输出目录（如果不存在）
            val outputDirectory = File(finalOutputDir)
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs()
            }

            // 构建优化的FFmpeg命令 - 每隔500ms提取一帧（每秒2帧）
            val command = buildOptimizedFrameExtractionCommand(videoPath, finalOutputDir)

            Log.d(TAG, "执行FFmpeg帧提取命令: $command")

            // 执行FFmpeg命令
            FFmpegKit.executeAsync(command, { session ->
                val returnCode = session.returnCode

                if (ReturnCode.isSuccess(returnCode)) {
                    Log.d(TAG, "视频帧提取成功: $finalOutputDir")
                    callback(true, finalOutputDir, null)
                } else {
                    val errorMessage = session.failStackTrace ?: "未知错误"
                    Log.e(TAG, "视频帧提取失败: $errorMessage")
                    callback(false, null, errorMessage)
                }
            }, { log ->
                Log.d(TAG, "FFmpeg日志: ${log.message}")
            }, { statistics ->
                // 可以在这里处理进度更新
                Log.d(TAG, "FFmpeg进度: ${statistics.time}ms")
            })
        } catch (e: Exception) {
            Log.e(TAG, "提取视频帧时出错", e)
            callback(false, null, e.message)
        }
    }

    /**
     * 构建优化的帧提取FFmpeg命令
     * 包含多线程处理、硬件加速、内存优化等性能优化
     */
    private fun buildOptimizedFrameExtractionCommand(videoPath: String, outputDir: String): String {
        val commandBuilder = StringBuilder()

        // 1. 获取设备CPU核心数进行动态优化
        val cpuCores = Runtime.getRuntime().availableProcessors()
        val optimalThreads = minOf(cpuCores, 8) // 限制最大线程数为8，避免过度消耗资源
        commandBuilder.append("-threads $optimalThreads ")

        // 2. 输入缓冲区优化
        commandBuilder.append("-probesize 32M ")      // 增大探测缓冲区
        commandBuilder.append("-analyzeduration 5M ") // 适度分析时长

        // 3. 硬件加速尝试（如果可用）
        commandBuilder.append("-hwaccel auto ")       // 尝试自动硬件加速

        // 4. 输入文件
        commandBuilder.append("-i \"$videoPath\" ")

        // 5. 视频滤镜优化 - 每隔500ms提取一帧（每秒2帧）
        // 使用高效的滤镜链，确保输出尺寸为偶数（某些编码器要求）
        commandBuilder.append("-vf \"fps=2,scale=trunc(iw/2)*2:trunc(ih/2)*2\" ")

        // 6. 编码器优化
        commandBuilder.append("-c:v mjpeg ")          // 使用MJPEG编码器，速度快
        commandBuilder.append("-q:v 3 ")             // 质量设置（1-31，3为高质量）
        commandBuilder.append("-pix_fmt yuvj420p ")  // 指定像素格式，提高兼容性

        // 7. 输出格式优化
        commandBuilder.append("-f image2 ")          // 明确指定输出格式
        commandBuilder.append("-y ")                 // 覆盖已存在的文件

        // 8. 禁用不需要的流，节省资源
        commandBuilder.append("-an ")                // 禁用音频处理
        commandBuilder.append("-dn ")                // 禁用数据流处理

        // 9. 内存优化
        commandBuilder.append("-max_muxing_queue_size 1024 ") // 限制混流队列大小

        // 10. 输出文件路径
        commandBuilder.append("\"$outputDir/frame_%04d.jpg\"")

        Log.d(TAG, "优化的帧提取命令: 使用 $optimalThreads 个线程，CPU核心数: $cpuCores")

        return commandBuilder.toString()
    }

    /**
     * 生成默认的帧输出目录
     */
    private fun generateDefaultFramesDir(): String {
        val cacheDir = context.externalCacheDir ?: context.cacheDir
        val dirName = "frames_${UUID.randomUUID().toString().substring(0, 8)}"
        return File(cacheDir, dirName).absolutePath
    }

    /**
     * 下载视频并进行语音识别
     * 完全参考debug页面中"测试进度显示下载"按钮的逻辑
     * @param url 视频URL
     * @param userAgent 用户代理
     * @param referer 引用页
     * @param useChinese 是否使用中文识别（保持接口兼容性，实际语音识别固定使用中文）
     * @param progressCallback 进度回调函数，参数为当前步骤和进度百分比
     * @param callback 回调函数，返回识别结果
     */
    fun downloadVideoAndExtractText(
        url: String,
        userAgent: String? = null,
        referer: String? = null,
        useChinese: Boolean = true,
        progressCallback: ((step: String, progress: Float) -> Unit)? = null,
        callback: (success: Boolean, texts: List<String>?, errorMessage: String?) -> Unit
    ) {
        Log.d(TAG, "开始下载视频并进行语音识别: $url")

        try {
            // 第一步：下载视频（不保存到相册，保留在缓存目录）
            // 完全参考debug页面第565-586行的逻辑
            progressCallback?.invoke("下载视频", 0f)
            // 如果没有提供userAgent，则根据URL生成随机User-Agent
            val finalUserAgent = userAgent ?: com.xunhe.aishoucang.helpers.UserAgentGenerator.generateUserAgentForUrl(url)
            // 如果没有提供referer，则根据URL生成合适的Referer
            val finalReferer = referer ?: com.xunhe.aishoucang.helpers.UserAgentGenerator.getRefererForUrl(url)

            downloadVideoWithProgress(
                url,
                finalUserAgent,
                finalReferer,
                null, // 使用默认缓存路径
                false, // 不保存到相册，保留在缓存目录
                { progress -> progressCallback?.invoke("下载视频", progress) }
            ) { downloadSuccess, videoPath, downloadError ->
                if (downloadSuccess != true) {
                    Log.e(TAG, "视频下载失败：$downloadError")
                    callback(false, null, "视频下载失败：$downloadError")
                    return@downloadVideoWithProgress
                }

                if (videoPath == null || videoPath.isEmpty()) {
                    Log.e(TAG, "视频下载失败：未获取到视频文件路径")
                    callback(false, null, "视频下载失败：未获取到视频文件路径")
                    return@downloadVideoWithProgress
                }

                Log.d(TAG, "视频下载成功，正在转换为PCM格式...")
                progressCallback?.invoke("转换音频", 0f)

                // 第二步：直接转换为PCM格式（同时从转换日志中获取时长）
                // 完全参考debug页面第593-633行的逻辑
                convertAudioToPCMWithProgress(
                    audioPath = videoPath, // 直接使用MP4文件路径
                    sampleRate = 16000,
                    channels = 1,
                    progressCallback = { progress ->
                        progressCallback?.invoke("转换音频", progress)
                    }
                ) { pcmSuccess, pcmPath, videoDurationSeconds, pcmError ->
                    if (pcmSuccess == true) {
                        // 从转换结果中获取视频时长
                        var durationInfo = ""
                        if (videoDurationSeconds != null) {
                            val durationMinutes = (videoDurationSeconds / 60).toString()
                            durationInfo = "视频时长：${durationMinutes}分钟\n"
                            Log.d(TAG, durationInfo)
                        }

                        Log.d(TAG, "${durationInfo}PCM转换成功：$pcmPath\n正在进行语音识别...")
                        progressCallback?.invoke("语音识别", 0f)

                        // 第三步：使用科大讯飞进行语音识别（带进度显示）
                        // 完全参考debug页面第617行和_recognizeAudioWithProgressTest方法
                        recognizeAudioWithProgressTest(pcmPath, videoDurationSeconds, videoPath, progressCallback, callback)

                        // 转换成功后删除临时视频文件
                        // 完全参考debug页面第619-628行的逻辑
                        try {
                            val file = File(videoPath)
                            if (file.exists()) {
                                file.delete()
                                Log.d(TAG, "已删除临时视频文件: $videoPath")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "删除临时视频文件失败: $e")
                        }
                    } else {
                        Log.e(TAG, "PCM转换失败：$pcmError")
                        callback(false, null, "PCM转换失败：$pcmError")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理过程出错：$e")
            callback(false, null, "处理过程出错：$e")
        }
    }

    /**
     * 使用科大讯飞识别PCM文件（支持进度显示测试）
     * 完全参考debug页面的_recognizeAudioWithProgressTest方法（第645-726行）
     * @param pcmPath PCM文件路径
     * @param videoDurationSeconds 视频时长（秒）
     * @param videoPath 原始视频文件路径（用于清理）
     * @param progressCallback 进度回调函数
     * @param callback 回调函数
     */
    private fun recognizeAudioWithProgressTest(
        pcmPath: String?,
        videoDurationSeconds: Double?,
        videoPath: String,
        progressCallback: ((step: String, progress: Float) -> Unit)? = null,
        callback: (success: Boolean, texts: List<String>?, errorMessage: String?) -> Unit
    ) {
        if (pcmPath == null || pcmPath.isEmpty()) {
            Log.e(TAG, "语音识别失败：PCM文件路径为空")
            callback(false, null, "语音识别失败：PCM文件路径为空")
            return
        }

        try {
            // 初始化SparkChain SDK
            // 参考debug页面第656-662行：await SparkChainBridge.initSparkChainFromConfig()
            val sparkChainHelper = SparkChainHelper.getInstance(context)

            // 如果SDK未初始化，尝试初始化
            if (!sparkChainHelper.isSDKInitialized()) {
                Log.d(TAG, "SDK未初始化，正在初始化...")

                // 从配置中读取讯飞参数
                // 完全参考SparkChainBridge.initSparkChainFromConfig()的逻辑（第28-30行）
                val appId = ConfigHelper.getString("xunfei_APPID")
                val apiKey = ConfigHelper.getString("xunfei_APIKey")
                val apiSecret = ConfigHelper.getString("xunfei_APISecret")

                if (appId.isEmpty() || apiKey.isEmpty() || apiSecret.isEmpty()) {
                    val errorMsg = "配置文件中缺少讯飞SDK参数，请检查 xunfei_APPID、xunfei_APIKey、xunfei_APISecret 配置"
                    Log.e(TAG, errorMsg)
                    callback(false, null, errorMsg)
                    return
                }

                // 使用配置文件中的参数初始化SDK
                val initSuccess = sparkChainHelper.initializeSDK(
                    appId = appId,
                    apiKey = apiKey,
                    apiSecret = apiSecret,
                    logLevel = 2, // INFO级别，参考debug页面
                    logPath = null,
                    uid = null
                )

                if (!initSuccess) {
                    Log.e(TAG, "语音识别SDK初始化失败")
                    callback(false, null, "语音识别SDK初始化失败")
                    return
                }
            }

            Log.d(TAG, "SDK初始化成功，开始处理音频...")

            // 使用音频分段处理器进行识别（传递预获取的视频时长）
            // 完全参考AudioSegmentProcessor.processLongAudio的逻辑
            processLongAudioNative(pcmPath, videoDurationSeconds, progressCallback, callback)
        } catch (e: Exception) {
            Log.e(TAG, "语音识别过程出错：$e")
            callback(false, null, "语音识别过程出错：$e")
        }
    }

    /**
     * 原生层实现AudioSegmentProcessor.processLongAudio的完整逻辑
     * 完全参考AudioSegmentProcessor.processLongAudio方法（第29-198行）
     * @param audioPath 音频文件路径
     * @param videoDurationSeconds 视频时长（秒）
     * @param progressCallback 进度回调函数
     * @param callback 回调函数
     */
    private fun processLongAudioNative(
        audioPath: String,
        videoDurationSeconds: Double?,
        progressCallback: ((step: String, progress: Float) -> Unit)? = null,
        callback: (success: Boolean, texts: List<String>?, errorMessage: String?) -> Unit
    ) {
        try {
            // 设置全局进度回调
            globalProgressCallback = progressCallback

            // 检查音频文件是否存在
            // 参考AudioSegmentProcessor第36-43行
            val audioFile = File(audioPath)
            if (!audioFile.exists()) {
                Log.e(TAG, "音频文件不存在: $audioPath")
                callback(false, null, "音频文件不存在: $audioPath")
                return
            }

            // 如果已提供视频时长，则直接使用；否则报错
            // 参考AudioSegmentProcessor第47-56行
            val totalDurationSeconds = if (videoDurationSeconds != null) {
                Log.d(TAG, "使用预获取的视频时长：${videoDurationSeconds.toStringAsFixed(1)}秒")
                videoDurationSeconds
            } else {
                Log.e(TAG, "未提供音频时长，无法进行分段处理")
                callback(false, null, "未提供音频时长，无法进行分段处理")
                return
            }

            // 计算总段数
            // 参考AudioSegmentProcessor第57行
            val segmentDurationSeconds = 60 // 每段60秒
            val totalSegments = (totalDurationSeconds / segmentDurationSeconds).toInt().let {
                if (totalDurationSeconds % segmentDurationSeconds > 0) it + 1 else it
            }

            // 如果音频时长小于等于60秒，直接识别
            // 参考AudioSegmentProcessor第59-68行
            if (totalDurationSeconds <= segmentDurationSeconds) {
                Log.d(TAG, "音频时长不超过1分钟，直接识别...")
                recognizeSingleAudioNative(audioPath) { success, result, error ->
                    // 清理PCM文件
                    cleanupPCMFile(audioPath)

                    if (success) {
                        Log.d(TAG, "🎯 进度测试完成！")
                        Log.d(TAG, "📊 处理统计：共切割 1 段，成功识别 1 段")
                        Log.d(TAG, "📝 识别结果：\n$result")

                        if (!result.isNullOrEmpty()) {
                            callback(true, listOf(result), null)
                        } else {
                            callback(true, emptyList(), null)
                        }
                    } else {
                        Log.e(TAG, "语音识别失败：${error ?: "未知错误"}")
                        callback(false, null, "语音识别失败：${error ?: "未知错误"}")
                    }
                }
                return
            }

            // 开始音频分段切割
            // 参考AudioSegmentProcessor第70-127行
            Log.d(TAG, "=== 开始音频分段切割 ===")
            Log.d(TAG, "音频总时长: ${totalDurationSeconds.toStringAsFixed(1)} 秒")
            Log.d(TAG, "分段时长: $segmentDurationSeconds 秒")
            Log.d(TAG, "总段数: $totalSegments 段")

            performAudioSegmentation(audioPath, totalDurationSeconds, totalSegments, segmentDurationSeconds, callback)

        } catch (e: Exception) {
            Log.e(TAG, "处理音频时发生异常: $e")
            callback(false, null, "处理音频时发生异常: $e")
        }
    }

    /**
     * 执行音频分段处理
     * 参考AudioSegmentProcessor第77-190行
     */
    private fun performAudioSegmentation(
        audioPath: String,
        totalDurationSeconds: Double,
        totalSegments: Int,
        segmentDurationSeconds: Int,
        callback: (success: Boolean, texts: List<String>?, errorMessage: String?) -> Unit
    ) {
        // 切割音频
        // 参考AudioSegmentProcessor第77-127行
        val segmentPaths = mutableListOf<String>()
        var currentSegmentIndex = 0

        // 进度回调：开始切割
        // 参考debug页面第672-681行的onProgress回调
        logProgress(0, totalSegments, "开始切割音频，共需切割为 $totalSegments 段...")

        fun processNextSegment() {
            if (currentSegmentIndex >= totalSegments) {
                // 所有段都切割完成，开始识别
                logProgress(0, totalSegments, "音频切割完成，开始分段识别...")
                performSegmentRecognition(segmentPaths, totalSegments, audioPath, callback)
                return
            }

            val startTime = currentSegmentIndex * segmentDurationSeconds
            val remainingTime = totalDurationSeconds - startTime
            val duration = if (remainingTime > segmentDurationSeconds) {
                segmentDurationSeconds
            } else {
                remainingTime.toInt().let { if (it <= 0) 1 else it } // 确保至少1秒
            }

            // 进度回调：正在切割当前段
            logProgress(currentSegmentIndex + 1, totalSegments, "正在切割第 ${currentSegmentIndex + 1} 段音频（${duration}秒）...")
            Log.d(TAG, "切割第 ${currentSegmentIndex + 1} 段: ${startTime}s - ${startTime + duration}s (时长: ${duration}s)")

            splitAudio(
                audioPath = audioPath,
                startTime = startTime,
                duration = duration,
                segmentIndex = currentSegmentIndex
            ) { success, segmentPath, error ->
                if (!success || segmentPath == null) {
                    Log.e(TAG, "切割第 ${currentSegmentIndex + 1} 段失败: $error")
                    // 清理已创建的片段
                    cleanupSegments(segmentPaths)
                    cleanupPCMFile(audioPath)
                    callback(false, null, "切割第 ${currentSegmentIndex + 1} 段音频失败: $error")
                    return@splitAudio
                }

                segmentPaths.add(segmentPath)
                Log.d(TAG, "切割第 ${currentSegmentIndex + 1} 段成功: $segmentPath")

                currentSegmentIndex++
                processNextSegment() // 递归处理下一段
            }
        }

        processNextSegment()
    }

    /**
     * 执行分段识别
     * 参考AudioSegmentProcessor第133-190行
     */
    private fun performSegmentRecognition(
        segmentPaths: List<String>,
        totalSegments: Int,
        originalAudioPath: String,
        callback: (success: Boolean, texts: List<String>?, errorMessage: String?) -> Unit
    ) {
        Log.d(TAG, "=== 音频切割完成，共生成 ${segmentPaths.size} 个片段 ===")
        Log.d(TAG, "开始分段识别...")

        val recognitionResults = mutableListOf<String>()
        var currentSegmentIndex = 0

        fun recognizeNextSegment() {
            if (currentSegmentIndex >= segmentPaths.size) {
                // 所有段都识别完成
                finishRecognition(recognitionResults, totalSegments, segmentPaths, originalAudioPath, callback)
                return
            }

            val segmentPath = segmentPaths[currentSegmentIndex]

            // 进度回调：正在识别当前段
            // 参考debug页面第672-681行的onProgress回调
            logProgress(currentSegmentIndex + 1, totalSegments, "正在识别第 ${currentSegmentIndex + 1} 段音频...")
            Log.d(TAG, "开始识别第 ${currentSegmentIndex + 1} 段音频: $segmentPath")

            // 使用带进度回调的识别方法
            recognizeSingleAudioWithProgress(segmentPath, currentSegmentIndex, totalSegments) { success, result, error ->
                if (success && !result.isNullOrEmpty()) {
                    recognitionResults.add(result)
                    Log.d(TAG, "第 ${currentSegmentIndex + 1} 段音频识别成功: \"$result\"")
                } else {
                    // 识别失败时记录错误但继续处理其他片段
                    Log.e(TAG, "第 ${currentSegmentIndex + 1} 段音频识别失败: $error")
                }

                currentSegmentIndex++
                recognizeNextSegment() // 递归处理下一段
            }
        }

        recognizeNextSegment()
    }

    /**
     * 完成识别处理
     * 参考AudioSegmentProcessor第171-190行
     */
    private fun finishRecognition(
        recognitionResults: List<String>,
        totalSegments: Int,
        segmentPaths: List<String>,
        originalAudioPath: String,
        callback: (success: Boolean, texts: List<String>?, errorMessage: String?) -> Unit
    ) {
        // 清理临时音频片段
        cleanupSegments(segmentPaths)

        // 清理原始PCM文件
        cleanupPCMFile(originalAudioPath)

        // 打印识别统计信息
        Log.d(TAG, "=== 音频分段识别完成统计 ===")
        Log.d(TAG, "总段数: $totalSegments")
        Log.d(TAG, "成功识别段数: ${recognitionResults.size}")
        Log.d(TAG, "失败段数: ${totalSegments - recognitionResults.size}")

        // 拼接识别结果
        val finalText = recognitionResults.joinToString(" ")
        Log.d(TAG, "最终拼接结果: \"$finalText\"")
        Log.d(TAG, "最终结果长度: ${finalText.length} 字符")

        Log.d(TAG, "🎯 进度测试完成！")
        Log.d(TAG, "📊 处理统计：共切割 $totalSegments 段，成功识别 ${recognitionResults.size} 段")
        Log.d(TAG, "📝 识别结果：\n$finalText")

        if (recognitionResults.isNotEmpty()) {
            callback(true, listOf(finalText), null)
        } else {
            callback(true, emptyList(), null)
        }
    }

    /**
     * 识别单个音频文件（原生层实现）
     * 参考AudioSegmentProcessor._recognizeSingleAudio方法（第200-245行）
     */
    private fun recognizeSingleAudioNative(
        audioPath: String,
        callback: (success: Boolean, result: String?, error: String?) -> Unit
    ) {
        try {
            val sparkChainHelper = SparkChainHelper.getInstance(context)

            // 使用带进度回调的识别方法，支持实时进度更新
            sparkChainHelper.recognizeAudioFileWithProgress(
                audioFilePath = audioPath,
                language = "zh_cn",  // 中文识别
                domain = "slm",      // 大模型识别
                accent = "mandarin", // 普通话
                progressCallback = { progress ->
                    // 对于单个音频文件，直接将进度传递给UI
                    val progressPercent = (progress * 100).toFloat()
                    Log.d(TAG, "🔄 单音频识别进度: ${progressPercent.toInt()}%")

                    // 调用UI进度回调
                    globalProgressCallback?.invoke("语音识别", progressPercent)
                }
            ) { success, result, error ->
                callback(success, result, error)
            }
        } catch (e: Exception) {
            callback(false, null, "识别音频失败: $e")
        }
    }

    /**
     * 识别单个音频文件（带整体进度计算）
     * 参考AudioSegmentProcessor第142-150行的整体进度计算逻辑
     */
    private fun recognizeSingleAudioWithProgress(
        audioPath: String,
        currentSegmentIndex: Int,
        totalSegments: Int,
        callback: (success: Boolean, result: String?, error: String?) -> Unit
    ) {
        try {
            val sparkChainHelper = SparkChainHelper.getInstance(context)

            // 使用带进度回调的识别方法
            sparkChainHelper.recognizeAudioFileWithProgress(
                audioFilePath = audioPath,
                language = "zh_cn",  // 中文识别
                domain = "slm",      // 大模型识别
                accent = "mandarin", // 普通话
                progressCallback = { segmentProgress ->
                    // 计算整体进度
                    // 参考AudioSegmentProcessor第144-149行：
                    // 整体进度 = (已完成段数 + 当前段进度) / 总段数
                    val overallProgress = (currentSegmentIndex + segmentProgress) / totalSegments
                    val progressPercent = (overallProgress * 100).toStringAsFixed(1)

                    Log.d(TAG, "🔄 段进度回调: 第${currentSegmentIndex+1}段进度=${(segmentProgress*100).toStringAsFixed(1)}%, 整体进度=${progressPercent}%")

                    // 整体进度回调
                    // 参考debug页面第683-692行的onOverallProgress回调
                    logOverallProgress(overallProgress)
                }
            ) { success, result, error ->
                callback(success, result, error)
            }
        } catch (e: Exception) {
            callback(false, null, "识别音频失败: $e")
        }
    }

    /**
     * 清理临时音频片段
     * 参考AudioSegmentProcessor._cleanupSegments方法（第274-289行）
     */
    private fun cleanupSegments(segmentPaths: List<String>) {
        Log.d(TAG, "=== 开始清理临时音频片段 ===")
        for (segmentPath in segmentPaths) {
            try {
                val file = File(segmentPath)
                if (file.exists()) {
                    file.delete()
                    Log.d(TAG, "已删除临时音频片段: $segmentPath")
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除音频片段失败: $segmentPath, 错误: $e")
            }
        }
        Log.d(TAG, "=== 临时音频片段清理完成 ===")
    }

    /**
     * 清理PCM文件
     * 参考debug页面第711-720行
     */
    private fun cleanupPCMFile(pcmPath: String) {
        try {
            val file = File(pcmPath)
            if (file.exists()) {
                file.delete()
                Log.d(TAG, "已删除PCM文件: $pcmPath")
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除PCM文件失败: $e")
        }
    }

    /**
     * Double扩展函数，格式化为指定小数位数的字符串
     */
    private fun Double.toStringAsFixed(digits: Int): String {
        return String.format("%.${digits}f", this)
    }

    /**
     * 进度回调日志
     * 参考debug页面第672-681行的onProgress回调
     */
    private fun logProgress(currentSegment: Int, totalSegments: Int, status: String) {
        Log.d(TAG, "📊 进度更新: ($currentSegment/$totalSegments) $status")

        // 这里可以扩展为实际的UI更新回调
        // 由于我们在原生层，暂时只记录日志
        // 如果需要更新UI，可以通过EventChannel发送事件到Flutter层
    }

    // 添加全局进度回调引用
    private var globalProgressCallback: ((step: String, progress: Float) -> Unit)? = null

    /**
     * 整体进度回调日志
     * 参考debug页面第683-692行的onOverallProgress回调
     */
    private fun logOverallProgress(progress: Double) {
        val progressPercent = (progress * 100).toStringAsFixed(1)
        Log.d(TAG, "🎯 进度测试 - 整体识别进度: $progressPercent%")

        // 调用UI进度回调
        globalProgressCallback?.invoke("语音识别", progress.toFloat() * 100f)
    }


    /**
     * 删除目录及其内容
     * @param directory 要删除的目录
     * @return 是否删除成功
     */
    private fun deleteDirectory(directory: File): Boolean {
        if (directory.exists()) {
            val files = directory.listFiles()
            if (files != null) {
                for (file in files) {
                    if (file.isDirectory) {
                        deleteDirectory(file)
                    } else {
                        file.delete()
                    }
                }
            }
            return directory.delete()
        }
        return false
    }

    /**
     * 计算Levenshtein距离
     * 用于测量两个字符串之间的差异
     */
    private fun levenshteinDistance(str1: String, str2: String): Int {
        val m = str1.length
        val n = str2.length

        // 创建距离矩阵
        val dp = Array(m + 1) { IntArray(n + 1) }

        // 初始化第一行和第一列
        for (i in 0..m) {
            dp[i][0] = i
        }
        for (j in 0..n) {
            dp[0][j] = j
        }

        // 填充矩阵
        for (i in 1..m) {
            for (j in 1..n) {
                val cost = if (str1[i - 1] == str2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // 删除
                    dp[i][j - 1] + 1,      // 插入
                    dp[i - 1][j - 1] + cost // 替换
                )
            }
        }

        return dp[m][n]
    }

    /**
     * 清理OCR识别后的重复文本
     * @param texts OCR识别出的文本列表
     * @param similarityThreshold 相似度阈值，默认为0.7，范围0-1，值越大表示要求越相似才认为是重复
     * @return 去重后的文本列表
     */
    fun removeDuplicateTexts(texts: List<String>, similarityThreshold: Double = 0.7): List<String> {
        if (texts.isEmpty()) return emptyList()

        Log.d(TAG, "开始文本去重处理，原始文本数量: ${texts.size}")
        val startTime = System.currentTimeMillis()

        val result = mutableListOf<String>()
        // 预编译正则表达式，避免重复编译
        val watermarkPattern = Regex("@.*?\\d+|抖音号：\\d+|小抖音")

        // 缓存已规范化的文本，避免重复计算
        val normalizedCache = mutableMapOf<String, String>()
        val normalizedResults = mutableListOf<String>()

        // 移除水印，规范化文本用于比较（优化版本）
        fun normalizeText(text: String): String {
            return normalizedCache.getOrPut(text) {
                text.replace(watermarkPattern, "").trim()
            }
        }

        // 计算两个字符串的相似度 (Levenshtein距离的归一化版本)
        fun calculateSimilarity(str1: String, str2: String): Double {
            if (str1 == str2) return 1.0
            if (str1.isEmpty()) return 0.0
            if (str2.isEmpty()) return 0.0

            val distance = levenshteinDistance(str1, str2)
            val maxLength = maxOf(str1.length, str2.length)

            // 归一化距离转换为相似度
            return 1.0 - (distance.toDouble() / maxLength)
        }

        // 第一步：快速过滤和预处理
        val validTexts = mutableListOf<Pair<String, String>>() // (原文, 规范化文本)
        for (text in texts) {
            val normalizedText = normalizeText(text)

            // 如果长度小于一定值，可能是噪音文本，跳过
            if (normalizedText.length < 3) continue

            validTexts.add(text to normalizedText)
        }

        Log.d(TAG, "预处理完成，有效文本数量: ${validTexts.size}")

        // 第二步：使用优化的去重算法
        for ((originalText, normalizedText) in validTexts) {
            // 检查是否与已有结果重复
            var isDuplicate = false

            // 使用已缓存的规范化结果进行比较
            for (i in normalizedResults.indices) {
                val normalizedExisting = normalizedResults[i]
                val similarity = calculateSimilarity(normalizedText, normalizedExisting)

                if (similarity >= similarityThreshold) {
                    isDuplicate = true
                    break
                }
            }

            // 如果不是重复的，添加到结果中
            if (!isDuplicate) {
                result.add(originalText)
                normalizedResults.add(normalizedText)
            }
        }

        val endTime = System.currentTimeMillis()
        val processingTime = endTime - startTime

        Log.d(TAG, "文本去重完成: 原始文本数量 ${texts.size}, 去重后数量 ${result.size}, 处理耗时 ${processingTime}ms")

        return result
    }

    fun removeDuplicateByFixedSubstring(
        rawText: String,
        minLen: Int = 5,
        maxLen: Int = 15
    ): String {
        fun cleanInput(text: String): String {
            return text.replace(Regex("抖音号[:：]?\\d{5,}"), "")
                .replace(Regex("@[\\u4e00-\\u9fa5a-zA-Z0-9_]+"), "")
                .replace(Regex("[^\\u4e00-\\u9fa5a-zA-Z0-9，。！？、“”‘’]"), "")
                .replace(Regex("\\s+"), "")
        }

        fun applySliding(text: String): String {
            var cleaned = text
            for (windowSize in minLen..maxLen) {
                val seen = mutableSetOf<String>()
                val builder = StringBuilder()
                var i = 0
                while (i <= cleaned.length - windowSize) {
                    val sub = cleaned.substring(i, i + windowSize)
                    if (seen.contains(sub)) {
                        i += windowSize
                    } else {
                        seen.add(sub)
                        builder.append(cleaned[i])
                        i += 1
                    }
                }
                if (i < cleaned.length) {
                    builder.append(cleaned.substring(i))
                }
                cleaned = builder.toString()
            }
            return cleaned
        }

        val step1 = applySliding(cleanInput(rawText))
        val step2 = applySliding(step1)
        return step2
    }

    /**
     * 从网页提取视频链接并下载视频提取文字（一体化流程）
     * @param webpageUrl 网页URL
     * @param timeout 提取视频链接的超时时间（毫秒）
     * @param useChinese 是否使用中文OCR
     * @param callback 回调函数，返回识别结果
     */
    fun extractVideoUrlAndExtractText(
        webpageUrl: String,
        timeout: Long = 30000,
        useChinese: Boolean = true,
        callback: (success: Boolean, texts: List<String>?, errorMessage: String?) -> Unit
    ) {
        Log.d(TAG, "开始从网页提取视频链接并提取文字: $webpageUrl")

        // 第一步：从网页提取视频链接
        WebViewHelper.getInstance(context).extractVideoUrl(
            url = webpageUrl,
            timeout = timeout,
            callback = { success: Boolean, resultData: Map<String, List<String>>?, errorMessage: String? ->
                if (!success || resultData == null) {
                    Log.e(TAG, "提取视频链接失败: $errorMessage")
                    callback(false, null, errorMessage ?: "提取视频链接失败")
                    return@extractVideoUrl
                }

                // 获取视频URL
                val videoUrls = resultData["video"]
                if (videoUrls.isNullOrEmpty()) {
                    Log.e(TAG, "未找到视频链接")
                    callback(false, null, "未找到视频链接")
                    return@extractVideoUrl
                }

                val videoUrl = videoUrls.first()
                Log.d(TAG, "成功提取到视频链接: $videoUrl")

                // 第二步：下载视频并提取文字
                val randomUserAgent = com.xunhe.aishoucang.helpers.UserAgentGenerator.generateUserAgentForUrl(videoUrl)
                val smartReferer = com.xunhe.aishoucang.helpers.UserAgentGenerator.getRefererForUrl(videoUrl)
                downloadVideoAndExtractText(
                    url = videoUrl,
                    userAgent = randomUserAgent,
                    referer = smartReferer,
                    useChinese = useChinese
                ) { downloadSuccess, texts, downloadError ->
                    if (downloadSuccess && texts != null) {
                        Log.d(TAG, "成功提取到文字，共${texts.size}条")
                        callback(true, texts, null)
                    } else {
                        Log.e(TAG, "下载视频并提取文字失败: $downloadError")
                        callback(false, null, downloadError ?: "下载视频并提取文字失败")
                    }
                }
            }
        )
    }

    /**
     * 下载视频并通过OCR识别字幕文字
     * @param url 视频URL
     * @param userAgent 用户代理
     * @param referer 引用页
     * @param useChinese 是否使用中文OCR
     * @param progressCallback 进度回调函数，参数为当前步骤和进度百分比
     * @param callback 回调函数，返回识别结果
     */
    fun downloadVideoAndExtractSubtitles(
        url: String,
        userAgent: String? = null,
        referer: String? = null,
        useChinese: Boolean = true,
        progressCallback: ((step: String, progress: Float) -> Unit)? = null,
        callback: (success: Boolean, subtitles: List<String>?, errorMessage: String?) -> Unit
    ) {
        Log.d(TAG, "开始下载视频并通过OCR识别字幕: $url")

        try {
            // 第一步：下载视频（不保存到相册，保留在缓存目录）
            progressCallback?.invoke("下载视频", 0f)
            val finalUserAgent = userAgent ?: com.xunhe.aishoucang.helpers.UserAgentGenerator.generateUserAgentForUrl(url)
            val finalReferer = referer ?: com.xunhe.aishoucang.helpers.UserAgentGenerator.getRefererForUrl(url)

            downloadVideo(
                url = url,
                userAgent = finalUserAgent,
                referer = finalReferer,
                outputPath = null, // 使用默认缓存路径
                saveToGallery = false // 不保存到相册，保留在缓存目录
            ) { downloadSuccess, videoPath, downloadError ->
                if (!downloadSuccess || videoPath.isNullOrEmpty()) {
                    Log.e(TAG, "视频下载失败：$downloadError")
                    callback(false, null, "视频下载失败：$downloadError")
                    return@downloadVideo
                }

                Log.d(TAG, "视频下载成功，开始提取视频帧...")
                progressCallback?.invoke("提取视频帧", 0f)

                // 第二步：提取视频帧
                extractFramesFromVideo(
                    videoPath = videoPath,
                    outputDir = null // 使用默认输出目录
                ) { framesSuccess, framesDir, framesError ->
                    if (!framesSuccess || framesDir.isNullOrEmpty()) {
                        Log.e(TAG, "视频帧提取失败：$framesError")
                        // 清理下载的视频文件
                        try {
                            File(videoPath).delete()
                        } catch (e: Exception) {
                            Log.e(TAG, "删除视频文件失败: $e")
                        }
                        callback(false, null, "视频帧提取失败：$framesError")
                        return@extractFramesFromVideo
                    }

                    Log.d(TAG, "视频帧提取成功，开始OCR识别...")
                    progressCallback?.invoke("OCR识别", 0f)

                    // 第三步：对每一帧进行OCR识别
                    recognizeFramesWithOCR(
                        framesDir = framesDir,
                        useChinese = useChinese,
                        progressCallback = { progress ->
                            progressCallback?.invoke("OCR识别", progress)
                        }
                    ) { ocrSuccess, subtitles, ocrError ->
                        // 清理临时文件
                        try {
                            File(videoPath).delete()
                            File(framesDir).deleteRecursively()
                        } catch (e: Exception) {
                            Log.e(TAG, "清理临时文件失败: $e")
                        }

                        if (ocrSuccess && subtitles != null) {
                            Log.d(TAG, "OCR识别完成，共识别到${subtitles.size}条字幕")
                            callback(true, subtitles, null)
                        } else {
                            Log.e(TAG, "OCR识别失败：$ocrError")
                            callback(false, null, "OCR识别失败：$ocrError")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理过程出错：$e")
            callback(false, null, "处理过程出错：$e")
        }
    }

    /**
     * 对视频帧目录中的所有图片进行OCR识别
     * @param framesDir 视频帧目录
     * @param useChinese 是否使用中文OCR
     * @param progressCallback 进度回调
     * @param callback 回调函数，返回识别结果
     */
    private fun recognizeFramesWithOCR(
        framesDir: String,
        useChinese: Boolean,
        progressCallback: ((progress: Float) -> Unit)? = null,
        callback: (success: Boolean, subtitles: List<String>?, errorMessage: String?) -> Unit
    ) {
        // 切换到主线程执行OCR识别
        android.os.Handler(android.os.Looper.getMainLooper()).post {
            try {
                val framesDirectory = File(framesDir)
                if (!framesDirectory.exists() || !framesDirectory.isDirectory) {
                    callback(false, null, "视频帧目录不存在或不是目录")
                    return@post
                }

                // 获取所有图片文件
                val imageFilesList = framesDirectory.listFiles { file ->
                    file.isFile && file.name.lowercase().endsWith(".jpg")
                }?.toList()?.sortedBy { it.name } ?: emptyList()

                if (imageFilesList.isEmpty()) {
                    callback(false, null, "未找到视频帧图片文件")
                    return@post
                }

                Log.d(TAG, "开始OCR识别，共${imageFilesList.size}张图片")
                val recognizedTexts = mutableListOf<String>()
                var currentIndex = 0

                // 获取PaddleOCR实例
                val paddleOcrHelper = com.xunhe.aishoucang.lib.PaddleOCRHelper(context)

                fun processNextFrame() {
                    if (currentIndex >= imageFilesList.size) {
                        // 所有帧都处理完成
                        Log.d(TAG, "OCR识别完成，共处理${imageFilesList.size}张图片，识别到${recognizedTexts.size}条文字")

                        // 在后台线程进行去重处理，避免阻塞主线程
                        Thread {
                            try {
                                val uniqueTexts = removeDuplicateTexts(recognizedTexts)
                                Log.d(TAG, "去重后剩余${uniqueTexts.size}条字幕")

                                // 回到主线程返回结果
                                android.os.Handler(android.os.Looper.getMainLooper()).post {
                                    callback(true, uniqueTexts, null)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "去重处理出错: $e")
                                android.os.Handler(android.os.Looper.getMainLooper()).post {
                                    callback(false, null, "去重处理出错: $e")
                                }
                            }
                        }.start()
                        return
                    }

                    val imageFile = imageFilesList[currentIndex]
                    val progress = (currentIndex.toFloat() / imageFilesList.size) * 100f
                    progressCallback?.invoke(progress)

                    Log.d(TAG, "正在识别第${currentIndex + 1}/${imageFilesList.size}张图片: ${imageFile.name}")

                    // 使用PaddleOCR识别当前帧
                    paddleOcrHelper.recognizeTextSyncWithDetails(
                        imageUri = imageFile.absolutePath,
                        useChinese = useChinese
                    ) { success: Boolean, text: String?, ocrResults: java.util.ArrayList<com.baidu.paddle.lite.demo.ocr.OcrResultModel>?, error: String? ->
                        Log.d(TAG, "=== 第${currentIndex + 1}张图片OCR识别详细结果 ===")
                        Log.d(TAG, "图片文件: ${imageFile.name}")
                        Log.d(TAG, "识别成功: $success")

                        if (success) {
                            Log.d(TAG, "识别到的完整文字: \"$text\"")

                            // 打印原始OCR结果对象详情并进行置信度过滤
                            if (ocrResults != null && ocrResults.isNotEmpty()) {
                                Log.d(TAG, "原始OCR结果数量: ${ocrResults.size}")
                                for (i in ocrResults.indices) {
                                    val ocrResult = ocrResults[i]
                                    Log.d(TAG, "--- OCR结果块 ${i + 1} ---")
                                    Log.d(TAG, "文字内容: \"${ocrResult.label}\"")
                                    Log.d(TAG, "置信度: ${ocrResult.confidence}")

                                    // 置信度过滤：只保留置信度大于0.9的结果
                                    if (ocrResult.confidence > 0.9f && !ocrResult.label.isNullOrBlank()) {
                                        val cleanText = ocrResult.label.trim()
                                        if (cleanText.isNotEmpty()) {
                                            recognizedTexts.add(cleanText)
                                            Log.d(TAG, "高置信度文字已添加: \"$cleanText\" (置信度: ${ocrResult.confidence})")
                                        }
                                    } else {
                                        Log.d(TAG, "置信度过低或文字为空，跳过: \"${ocrResult.label}\" (置信度: ${ocrResult.confidence})")
                                    }

                                    // 打印坐标点信息
                                    val points = ocrResult.points
                                    if (points != null && points.isNotEmpty()) {
                                        Log.d(TAG, "坐标点数量: ${points.size}")
                                        for (j in points.indices) {
                                            val point = points[j]
                                            Log.d(TAG, "点${j + 1}: (${point.x}, ${point.y})")
                                        }
                                    } else {
                                        Log.d(TAG, "无坐标点信息")
                                    }

                                    // 打印对象的所有属性（通过反射）
                                    try {
                                        val clazz = ocrResult.javaClass
                                        Log.d(TAG, "对象类型: ${clazz.name}")
                                        val fields = clazz.declaredFields
                                        for (field in fields) {
                                            field.setAccessible(true)
                                            val value = field.get(ocrResult)
                                            Log.d(TAG, "属性 ${field.name}: $value (${field.type.simpleName})")
                                        }
                                    } catch (e: Exception) {
                                        Log.e(TAG, "反射获取对象属性失败: $e")
                                    }
                                }
                            } else {
                                Log.d(TAG, "原始OCR结果为空")
                            }

                            // 注意：文字已在上面的置信度过滤中添加到recognizedTexts
                        } else {
                            Log.e(TAG, "第${currentIndex + 1}张图片识别失败: $error")
                        }

                        Log.d(TAG, "=== OCR识别详细结果结束 ===")

                        currentIndex++
                        // 在主线程中继续处理下一张图片
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            processNextFrame()
                        }
                    }
                }

                processNextFrame()

            } catch (e: Exception) {
                Log.e(TAG, "OCR识别过程出错: $e")
                callback(false, null, "OCR识别过程出错: $e")
            }
        }
    }


}
