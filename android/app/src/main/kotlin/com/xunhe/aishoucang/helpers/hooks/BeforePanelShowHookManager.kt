package com.xunhe.aishoucang.helpers.hooks

import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.helpers.hooks.DouyinBeforePanelShow
import com.xunhe.aishoucang.helpers.hooks.BilibiliBeforePanelShow
import com.xunhe.aishoucang.helpers.hooks.WechatBeforePanelShow

/**
 * 面板显示前钩子管理器
 * 负责注册和执行所有钩子
 */
object BeforePanelShowHookManager {
    private const val TAG = "BeforePanelShowHookMgr"

    // 注册的钩子列表
    private val hooks = mutableListOf<BeforePanelShowHook>()

    init {
        // 注册所有钩子
        registerHooks()
    }

    /**
     * 注册所有钩子
     */
    private fun registerHooks() {
        // 注册抖音钩子
        hooks.add(DouyinBeforePanelShow())

        // 注册B站钩子
        hooks.add(BilibiliBeforePanelShow())

        // 注册微信钩子
        hooks.add(WechatBeforePanelShow())

        // 可以在这里注册更多钩子

        Log.d(TAG, "已注册 ${hooks.size} 个钩子")
    }

    /**
     * 执行所有适用的钩子
     *
     * @param context 上下文
     * @param rootNode 当前活动窗口的根节点
     */
    fun executeHooks(context: Context, rootNode: AccessibilityNodeInfo?) {
        if (rootNode == null) {
            Log.w(TAG, "根节点为空，无法执行钩子")
            return
        }

        val packageName = rootNode.packageName?.toString() ?: ""
        if (packageName.isEmpty()) {
            Log.w(TAG, "包名为空，无法执行钩子")
            return
        }

        Log.d(TAG, "当前应用包名: $packageName")

        // 查找并执行所有适用的钩子
        var executedCount = 0
        hooks.forEach { hook ->
            if (hook.isApplicable(packageName)) {
                Log.d(TAG, "执行钩子: ${hook.javaClass.simpleName}")
                hook.execute(context, rootNode)
                executedCount++
            }
        }

        Log.d(TAG, "共执行了 $executedCount 个钩子")
    }
}
