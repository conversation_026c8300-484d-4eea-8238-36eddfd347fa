package com.xunhe.aishoucang.helpers

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.*
import android.view.animation.AnimationUtils
import android.widget.*
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import com.xunhe.aishoucang.views.share_panel.SharePanelManager

/**
 * 创建自定义分享面板UI
 * 用于不支持自动处理的应用
 */
object CustomPanelUICreator {
    private const val TAG = "CustomPanelUICreator"

    /**
     * 创建自定义分享面板UI
     *
     * @param context 上下文
     * @param windowManager 窗口管理器
     * @param sharePanelView 分享面板视图引用
     * @param sharePanelParams 分享面板布局参数引用
     * @param hideSharePanel 隐藏分享面板的方法
     * @param onComplete 完成回调
     */
    fun createPanelCustomUI(
        context: Context,
        windowManager: WindowManager?,
        sharePanelView: View?,
        sharePanelParams: WindowManager.LayoutParams?,
        hideSharePanel: (Context, WindowManager?, Boolean) -> Unit,
        onComplete: () -> Unit = {}
    ): Pair<View?, WindowManager.LayoutParams?> {
        var updatedView = sharePanelView
        var updatedParams = sharePanelParams

        if (updatedView != null) {
            Log.d(TAG, "面板已存在，直接执行回调")
            onComplete()
            return Pair(updatedView, updatedParams)
        }

        Log.d(TAG, "开始创建自定义面板UI")
        try {
            val screenWidth = context.resources.displayMetrics.widthPixels
            val screenHeight = context.resources.displayMetrics.heightPixels

            // 设置面板高度为屏幕高度的65%
            val panelHeight = (screenHeight * 0.65).toInt()
            val panelWidth = screenWidth // 全屏宽度

            val inflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            updatedView = inflater.inflate(R.layout.custom_panel_layout, null)
            Log.d(TAG, "自定义面板视图已创建")

            updatedView?.let { view ->
                // 设置标题文本
                val titleTextView = view.findViewById<TextView>(R.id.custom_title_text_view)
                titleTextView?.text = "手动收藏"

                // 获取剪贴板内容并填充到链接输入框
                val clipboardHelper = ClipboardHelper.getInstance(context)
                val clipText = clipboardHelper.getClipboardText()
                val linkEditText = view.findViewById<EditText>(R.id.link_edit_text)
                linkEditText?.setText(clipText)

                // 设置收藏夹下拉选择器
                val folderSpinner = view.findViewById<Spinner>(R.id.folder_spinner)
                setupFolderSpinner(context, folderSpinner)

                // 设置确认按钮
                val confirmButton = view.findViewById<Button>(R.id.custom_confirm_button)
                confirmButton?.setOnClickListener {
                    // 获取表单数据
                    val link = linkEditText?.text?.toString() ?: ""
                    val title = view.findViewById<EditText>(R.id.title_edit_text)?.text?.toString() ?: ""
                    val description = view.findViewById<EditText>(R.id.description_edit_text)?.text?.toString() ?: ""
                    val selectedFolder = folderSpinner.selectedItem?.toString() ?: ""

                    // 验证必填字段
                    if (link.isEmpty()) {
                        Toast.makeText(context, "请输入链接", Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }

                    if (title.isEmpty()) {
                        Toast.makeText(context, "请输入标题", Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }

                    // 记录表单数据
                    Log.d(TAG, "手动收藏表单数据：")
                    Log.d(TAG, "链接: $link")
                    Log.d(TAG, "标题: $title")
                    Log.d(TAG, "描述: $description")
                    Log.d(TAG, "收藏夹: $selectedFolder")

                    // TODO: 实际保存收藏的逻辑
                    Toast.makeText(context, "收藏成功", Toast.LENGTH_SHORT).show()

                    // 隐藏面板
                    hideSharePanel(context, windowManager, false)
                }

                // 设置取消按钮
                val cancelButton = view.findViewById<Button>(R.id.custom_cancel_button)
                cancelButton?.setOnClickListener {
                    Log.d(TAG, "点击了取消按钮")
                    hideSharePanel(context, windowManager, false)
                }
            }

            updatedParams = WindowManager.LayoutParams().apply {
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else WindowManager.LayoutParams.TYPE_PHONE
                format = PixelFormat.TRANSLUCENT
                flags =
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                            WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS or
                            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                width = panelWidth
                height = panelHeight
                gravity = Gravity.BOTTOM or Gravity.START  // 底部显示，与标准面板一致
                dimAmount = 0f
                windowAnimations = R.style.SharePanelAnimation
            }

            Log.d(TAG, "添加自定义面板到窗口")
            windowManager?.addView(updatedView, updatedParams)
            SharePanelHelper._isSharePanelShowing = true

            // 视图添加后开始动画
            updatedView?.post {
                Log.d(TAG, "视图已添加，准备开始动画")
                try {
                    val animation = AnimationUtils.loadAnimation(context, R.anim.slide_up)
                    updatedView?.startAnimation(animation)
                } catch (e: Exception) {
                    Log.e(TAG, "启动动画失败", e)
                }

                onComplete()
            }

        } catch (e: Exception) {
            Log.e(TAG, "显示自定义面板时出错", e)
            // 确保在主线程中执行回调
            Handler(Looper.getMainLooper()).post {
                Log.d(TAG, "面板创建出错，执行回调")
                onComplete() // 出错时也调用回调以避免阻塞
            }
        }

        return Pair(updatedView, updatedParams)
    }

    /**
     * 设置收藏夹下拉选择器
     *
     * @param context 上下文
     * @param spinner 下拉选择器
     */
    private fun setupFolderSpinner(context: Context, spinner: Spinner) {
        try {
            // 模拟收藏夹数据
            val folders = listOf("默认收藏夹", "重要内容", "学习资料", "工作相关", "娱乐")

            // 创建适配器
            val adapter = ArrayAdapter(
                context,
                android.R.layout.simple_spinner_item,
                folders
            )

            // 设置下拉样式
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

            // 设置适配器
            spinner.adapter = adapter

            // 设置默认选中项
            spinner.setSelection(0)

            // 设置选择监听器
            spinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    val selectedFolder = folders[position]
                    Log.d(TAG, "选择了收藏夹: $selectedFolder")
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {
                    // 不做任何处理
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置收藏夹下拉选择器时出错", e)
        }
    }
}
