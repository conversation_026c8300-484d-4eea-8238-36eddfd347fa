package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.lib.AccessibilityHelper
import com.xunhe.aishoucang.helpers.AppHandlerFactory

/**
 * 创建笔记助手类
 * 负责处理创建笔记时的各个app操作逻辑，但不显示收藏面板
 */
object CreateNoteHelper {
    private const val TAG = "CreateNoteHelper"
    private const val CLICK_INTERVAL = 1000L // 防连点间隔时间（毫秒）

    // 记录处理状态
    private var isProcessing = false
    private var lastClickTime = 0L // 记录上次点击时间，用于防连点



    /**
     * 处理创建笔记操作
     * 只执行各个app的操作逻辑（如点击分享按钮），不显示收藏面板
     *
     * @param context 上下文
     * @param callback 操作完成后的回调，参数为是否成功和错误信息
     */
    fun handleCreateNote(
        context: Context,
        callback: ((<PERSON><PERSON><PERSON>, String?) -> Unit)? = null
    ) {
        Log.d(TAG, "开始处理创建笔记操作")

        // 防连点检查
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime < CLICK_INTERVAL) {
            Log.d(TAG, "点击过于频繁，忽略本次操作")
            callback?.invoke(false, "操作过于频繁，请稍候")
            return
        }
        lastClickTime = currentTime

        // 检查是否正在处理中
        if (isProcessing) {
            Log.d(TAG, "正在处理中，忽略创建笔记操作")
            callback?.invoke(false, "正在处理中，请稍候...")
            return
        }

        // 标记为处理中状态
        isProcessing = true
        
        try {
            // 获取当前前台应用包名
            val currentPackage = getCurrentPackage(context)
            Log.d(TAG, "当前应用包名: $currentPackage")
            
            if (currentPackage.isNullOrEmpty()) {
                Log.w(TAG, "无法获取当前应用包名")
                isProcessing = false
                callback?.invoke(false, "无法识别当前应用")
                return
            }
            
            // 使用工厂根据包名获取应用处理器
            val handler = AppHandlerFactory.getHandlerByPackage(currentPackage)

            try {
                Log.d(TAG, "开始执行应用操作逻辑: $currentPackage")

                // 执行应用特定的操作逻辑（如点击分享按钮）
                handler.handle(context)

                // 操作完成
                isProcessing = false
                callback?.invoke(true, null)
                Log.d(TAG, "创建笔记操作完成")

            } catch (e: Exception) {
                Log.e(TAG, "执行应用操作逻辑失败", e)
                isProcessing = false
                callback?.invoke(false, "操作失败: ${e.message}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理创建笔记操作失败", e)
            isProcessing = false
            callback?.invoke(false, "处理失败: ${e.message}")
        }
    }

    /**
     * 获取当前前台应用包名
     *
     * @param context 上下文
     * @return 当前应用包名，如果获取失败返回null
     */
    private fun getCurrentPackage(context: Context): String? {
        return try {
            val helper = AccessibilityHelper.getInstance(context)
            val rootNode = helper.getRootNode()
            rootNode?.packageName?.toString()
        } catch (e: Exception) {
            Log.e(TAG, "获取当前应用包名失败", e)
            null
        }
    }

    /**
     * 检查是否正在处理中
     *
     * @return 是否正在处理中
     */
    fun isProcessing(): Boolean {
        return isProcessing
    }

    /**
     * 重置处理状态
     * 用于异常情况下的状态重置
     */
    fun resetProcessingState() {
        isProcessing = false
        Log.d(TAG, "重置处理状态")
    }
}
