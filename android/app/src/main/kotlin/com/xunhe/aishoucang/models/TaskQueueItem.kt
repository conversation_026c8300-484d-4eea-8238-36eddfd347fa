package com.xunhe.aishoucang.models

import java.util.Date

/**
 * 任务队列项数据模型
 */
data class TaskQueueItem(
    val id: String,
    val title: String,
    val description: String,
    val status: TaskStatus,
    val progress: Int = 0, // 进度百分比 0-100
    val createdAt: Date,
    val updatedAt: Date,
    val type: TaskType = TaskType.UNKNOWN,
    val platform: String = "", // 平台标识，如 "bilibili", "wechat" 等
    val platformName: String = "" // 平台显示名称，如 "B站", "微信" 等
)

/**
 * 任务状态枚举
 */
enum class TaskStatus(val displayName: String, val color: String) {
    PENDING("等待中", "#FFA500"),
    RUNNING("进行中", "#3D7AF5"),
    COMPLETED("已完成", "#4CAF50"),
    FAILED("失败", "#F44336"),
    CANCELLED("已取消", "#9E9E9E")
}

/**
 * 任务类型枚举
 */
enum class TaskType(val displayName: String) {
    VIDEO_DOWNLOAD("视频下载"),
    TEXT_EXTRACT("文案提取"),
    NOTE_CREATE("笔记创建"),
    CONTENT_PROCESS("内容处理"),
    UNKNOWN("未知")
}
