package com.xunhe.aishoucang.views.share_panel

import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.api.FavoriteApi
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import com.xunhe.aishoucang.model.Favorite
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.absoluteValue

/**
 * 分享面板管理器
 * 负责初始化和管理分享面板的RecyclerView
 */
class SharePanelManager(private val context: Context) {
    private val TAG = "SharePanelManager"
    private val favoriteApi = FavoriteApi(context)
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    // 分享项列表（改为可变列表，支持拖动排序）
    private val shareItems = mutableListOf<SharePanelItem>()

    // 分页相关参数
    private var currentPage = 1
    private var isLoading = false
    private var hasMoreData = true
    private val pageSize = 10

    // 缓存相关参数
    private var swipeRefreshLayout: SwipeRefreshLayout? = null
    private var cachedItems = mutableListOf<SharePanelItem>()
    private var lastUpdateTime = 0L
    private val CACHE_EXPIRATION_TIME = 5 * 60 * 1000 // 缓存过期时间：5分钟

    /**
     * 初始化分享面板RecyclerView
     * @param rootView 包含RecyclerView的根视图
     * @param onItemClick 点击项的回调
     */
    fun setupRecyclerView(rootView: View, onItemClick: (SharePanelItem) -> Unit) {
        val recyclerView: RecyclerView = rootView.findViewById(R.id.share_panel_recycler_view)
        swipeRefreshLayout = rootView.findViewById(R.id.swipe_refresh_layout)

        // 设置网格布局管理器，4列
        val layoutManager = GridLayoutManager(context, 4)
        recyclerView.layoutManager = layoutManager

        // 设置适配器
        val adapter = SharePanelAdapter(context, shareItems, onItemClick)
        recyclerView.adapter = adapter

        // 添加拖动排序支持
        val callback = ShareItemTouchHelperCallback(adapter)
        val touchHelper = ItemTouchHelper(callback)
        touchHelper.attachToRecyclerView(recyclerView)

        // 设置下拉刷新监听器
        swipeRefreshLayout?.setOnRefreshListener {
            Log.d(TAG, "触发下拉刷新")
            // 强制刷新，忽略缓存
            refreshFavorites(recyclerView, onItemClick)
        }

        // 设置下拉刷新的颜色
        swipeRefreshLayout?.setColorSchemeColors(
            Color.parseColor("#1EB9EF"), // 主色调
            Color.parseColor("#4DCBF7"), // 浅色调
            Color.parseColor("#0A9BD0")  // 深色调
        )

        // 添加滚动监听器，实现上拉加载更多
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                // 如果正在加载或没有更多数据，直接返回
                if (isLoading || !hasMoreData) return

                val visibleItemCount = layoutManager.childCount
                val totalItemCount = layoutManager.itemCount
                val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                // 当滚动到底部附近时，加载更多数据
                if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 5
                    && firstVisibleItemPosition >= 0
                    && totalItemCount >= pageSize) {
                    // 加载下一页
                    loadMoreFavorites(recyclerView, onItemClick)
                }
            }
        })

        // 重置分页参数
        resetPagination()

        // 检查是否有可用的缓存数据
        if (isCacheValid()) {
            Log.d(TAG, "使用缓存数据，缓存项数量: ${cachedItems.size}")
            // 使用缓存数据
            shareItems.clear()
            shareItems.addAll(cachedItems)
            adapter.notifyDataSetChanged()
        } else {
            // 没有有效缓存，加载新数据
            Log.d(TAG, "缓存无效或过期，加载新数据")
            loadFavorites(recyclerView, onItemClick)
        }
    }

    /**
     * 添加新的分享渠道
     * @param items 要添加的分享项列表
     */
    fun addShareItems(items: List<SharePanelItem>, recyclerView: RecyclerView) {
        val currentAdapter = recyclerView.adapter as? SharePanelAdapter ?: return
        shareItems.addAll(items)
        currentAdapter.notifyItemRangeInserted(
            shareItems.size - items.size,
            items.size
        )
    }

    /**
     * 重置分页参数
     */
    private fun resetPagination() {
        currentPage = 1
        isLoading = false
        hasMoreData = true
    }

    /**
     * 检查缓存是否有效
     * @return 缓存是否有效
     */
    private fun isCacheValid(): Boolean {
        val currentTime = System.currentTimeMillis()
        val cacheAge = currentTime - lastUpdateTime

        // 缓存有效的条件：缓存不为空且未过期
        return cachedItems.isNotEmpty() && cacheAge < CACHE_EXPIRATION_TIME
    }

    /**
     * 更新缓存
     */
    private fun updateCache() {
        // 清空并重新填充缓存
        cachedItems.clear()
        cachedItems.addAll(shareItems)
        lastUpdateTime = System.currentTimeMillis()
        Log.d(TAG, "更新缓存，缓存项数量: ${cachedItems.size}")
    }

    /**
     * 强制刷新收藏夹列表
     * @param recyclerView RecyclerView实例
     * @param onItemClick 点击项的回调
     */
    private fun refreshFavorites(recyclerView: RecyclerView, onItemClick: (SharePanelItem) -> Unit) {
        // 清空列表并重置分页参数
        shareItems.clear()
        recyclerView.adapter?.notifyDataSetChanged()
        resetPagination()

        // 加载第一页数据
        loadFavoritesPage(recyclerView, onItemClick, currentPage, true)
    }

    /**
     * 刷新RecyclerView
     * 用于外部调用，强制刷新收藏夹列表
     * @param rootView 包含RecyclerView的根视图
     */
    fun refreshRecyclerView(rootView: View) {
        val recyclerView: RecyclerView = rootView.findViewById(R.id.share_panel_recycler_view)
        val adapter = recyclerView.adapter as? SharePanelAdapter ?: return

        // 获取当前的点击回调
        val onItemClick = adapter.getOnItemClickListener()

        // 强制刷新
        refreshFavorites(recyclerView, onItemClick)
    }

    /**
     * 加载收藏夹列表（第一页）
     * @param recyclerView RecyclerView实例
     * @param onItemClick 点击项的回调
     */
    private fun loadFavorites(recyclerView: RecyclerView, onItemClick: (SharePanelItem) -> Unit) {
        // 清空列表并重置分页参数
        shareItems.clear()
        recyclerView.adapter?.notifyDataSetChanged()
        resetPagination()

        // 加载第一页数据
        loadFavoritesPage(recyclerView, onItemClick, currentPage)
    }

    /**
     * 加载更多收藏夹（下一页）
     */
    private fun loadMoreFavorites(recyclerView: RecyclerView, onItemClick: (SharePanelItem) -> Unit) {
        if (isLoading || !hasMoreData) return

        // 增加页码并加载下一页
        currentPage++
        loadFavoritesPage(recyclerView, onItemClick, currentPage)
    }

    /**
     * 加载指定页码的收藏夹列表
     * @param recyclerView RecyclerView实例
     * @param onItemClick 点击项的回调
     * @param page 页码
     * @param isRefreshing 是否是下拉刷新触发的加载
     */
    private fun loadFavoritesPage(
        recyclerView: RecyclerView,
        onItemClick: (SharePanelItem) -> Unit,
        page: Int,
        isRefreshing: Boolean = false
    ) {
        isLoading = true

        coroutineScope.launch {
            try {
                // 获取用户ID
                var userId = SharedPreferencesHelper.getInstance(context).getUserId()
                Log.d(TAG, "获取到的用户ID: '$userId'")

                // 检查SharedPreferences中的所有键值对
                val allPrefs = SharedPreferencesHelper.getInstance(context).getAllPrefs()
                Log.d(TAG, "SharedPreferences中的所有键值对: $allPrefs")

                // 如果用户ID为空，尝试从Flutter的SharedPreferences中获取
                if (userId.isEmpty()) {
                    try {
                        // 尝试从Flutter的SharedPreferences中获取用户ID
                        val flutterPrefs = context.getSharedPreferences(context.packageName, Context.MODE_PRIVATE)
                        val flutterUserId = flutterPrefs.getString("user_id", "")
                        Log.d(TAG, "从Flutter SharedPreferences获取的用户ID: '$flutterUserId'")

                        if (!flutterUserId.isNullOrEmpty()) {
                            // 如果从Flutter获取到了用户ID，则同步到原生端
                            userId = flutterUserId
                            SharedPreferencesHelper.getInstance(context).setUserId(userId)
                            Log.d(TAG, "已将Flutter的用户ID同步到原生端: '$userId'")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "尝试从Flutter获取用户ID时出错", e)
                    }
                }

                if (userId.isEmpty()) {
                    Log.w(TAG, "用户未登录，面板保持为空")
                    isLoading = false
                    // 如果是刷新操作，结束刷新动画
                    if (isRefreshing) {
                        withContext(Dispatchers.Main) {
                            swipeRefreshLayout?.isRefreshing = false
                        }
                    }
                    return@launch
                }

                // 调用API获取收藏夹列表，使用分页参数
                val result = favoriteApi.getFavoriteList(userId, page = page, pageSize = pageSize)

                result.fold(
                    onSuccess = { favorites ->
                        // 将收藏夹列表转换为SharePanelItem
                        val favoriteItems = favorites.map { favorite ->
                            convertFavoriteToShareItem(favorite)
                        }

                        // 在UI线程更新RecyclerView
                        withContext(Dispatchers.Main) {
                            // 判断是否还有更多数据
                            hasMoreData = favoriteItems.size >= pageSize

                            if (favoriteItems.isNotEmpty()) {
                                // 添加收藏夹项到面板
                                val startPosition = shareItems.size
                                addShareItems(favoriteItems, recyclerView)

                                // 如果是第一页，则刷新整个列表，否则只刷新新添加的项
                                if (page == 1) {
                                    recyclerView.adapter?.notifyDataSetChanged()

                                    // 更新缓存（仅当加载第一页成功时）
                                    updateCache()
                                } else {
                                    recyclerView.adapter?.notifyItemRangeInserted(startPosition, favoriteItems.size)
                                }

                                Log.d(TAG, "成功加载第${page}页: ${favoriteItems.size}个收藏夹")
                            } else {
                                // 没有更多数据
                                hasMoreData = false
                                if (page == 1) {
                                    Log.d(TAG, "未找到收藏夹，面板保持为空")
                                    // 清空缓存
                                    cachedItems.clear()
                                    lastUpdateTime = 0
                                } else {
                                    Log.d(TAG, "没有更多收藏夹了")
                                }
                            }

                            // 重置加载状态
                            isLoading = false

                            // 如果是刷新操作，结束刷新动画
                            if (isRefreshing) {
                                swipeRefreshLayout?.isRefreshing = false
                            }
                        }
                    },
                    onFailure = { error ->
                        Log.e(TAG, "加载收藏夹失败: ${error.message}")
                        withContext(Dispatchers.Main) {
                            isLoading = false

                            // 如果是刷新操作，结束刷新动画
                            if (isRefreshing) {
                                swipeRefreshLayout?.isRefreshing = false
                            }
                        }
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "加载收藏夹异常", e)
                withContext(Dispatchers.Main) {
                    isLoading = false

                    // 如果是刷新操作，结束刷新动画
                    if (isRefreshing) {
                        swipeRefreshLayout?.isRefreshing = false
                    }
                }
            }
        }
    }

    /**
     * 将Favorite对象转换为SharePanelItem
     * @param favorite 收藏夹对象
     * @return SharePanelItem对象
     */
    private fun convertFavoriteToShareItem(favorite: Favorite): SharePanelItem {
        // 使用蓝色系列作为收藏夹颜色，与首页风格一致
        val colors = arrayOf(
            "#1EB9EF", // 主色调
            "#4DCBF7", // 浅色调
            "#0A9BD0", // 深色调
            "#33C6F5", // 亮蓝色
            "#0FAAE0"  // 中蓝色
        )

        // 根据收藏夹ID生成一个稳定的颜色索引
        val colorIndex = (favorite.id?.hashCode() ?: 0).absoluteValue % colors.size

        return SharePanelItem(
            id = favorite.id ?: "",
            name = favorite.name,
            iconColor = Color.parseColor(colors[colorIndex]),
            iconDrawable = null,
            isFavorite = true,
            favoriteData = favorite
        )
    }
}
