package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * OSS资源桥接类
 * 用于处理从Flutter调用的OSS资源转换请求
 */
object OssResourceBridge {
    private const val TAG = "OssResourceBridge"
    
    /**
     * 处理资源链接转换请求
     * 
     * @param context 上下文
     * @param call Flutter方法调用
     * @param result 结果回调
     */
    fun handleMethodCall(context: Context, call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "convertToOssLink" -> convertToOssLink(context, call, result)
            else -> result.notImplemented()
        }
    }
    
    /**
     * 处理资源链接转OSS链接请求
     */
    private fun convertToOssLink(context: Context, call: MethodCall, result: MethodChannel.Result) {
        val url = call.argument<String>("url")
        if (url.isNullOrEmpty()) {
            result.error("INVALID_URL", "URL不能为空", null)
            return
        }
        
        GlobalScope.launch {
            try {
                // 调用转换工具类
                val ossLink = SourceToOssLinkHelper.convertToOssLink(context, url)
                
                // 在主线程中返回结果
                MainScope().launch {
                    result.success(mapOf(
                        "success" to true,
                        "ossUrl" to ossLink
                    ))
                }
            } catch (e: Exception) {
                Log.e(TAG, "转换资源链接失败", e)
                
                // 在主线程中返回错误结果
                MainScope().launch {
                    result.success(mapOf(
                        "success" to false,
                        "error" to e.message
                    ))
                }
            }
        }
    }
} 