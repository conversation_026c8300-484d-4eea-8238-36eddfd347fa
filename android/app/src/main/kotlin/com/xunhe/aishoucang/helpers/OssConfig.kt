package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.helpers.ConfigHelper
import org.json.JSONObject

/**
 * OSS配置类
 * 用于存储OSS相关配置
 */
object OssConfig {
    private const val TAG = "OssConfig"

    // OSS配置键名
    private const val KEY_OSS_ENDPOINT = "oss_endpoint"
    private const val KEY_OSS_BUCKET = "oss_bucket"
    private const val KEY_OSS_STS_AUTH_URL = "oss_sts_auth_url"
    private const val KEY_OSS_REGION = "oss_region"

    // 默认值
    private const val DEFAULT_OSS_ENDPOINT = "https://oss-cn-qingdao.aliyuncs.com"
    private const val DEFAULT_OSS_BUCKET = "assets-xunhe"
    private const val DEFAULT_OSS_STS_AUTH_URL = "/oss/sts/token"
    private const val DEFAULT_OSS_REGION = "oss-cn-qingdao"

    /**
     * 获取OSS Endpoint
     * 返回固定的青岛区域Endpoint
     */
    fun getOssEndpoint(context: Context): String {
        return DEFAULT_OSS_ENDPOINT // 直接返回固定值，不使用ConfigHelper中的配置
    }

    /**
     * 获取OSS Bucket
     */
    fun getOssBucket(context: Context): String {
        return DEFAULT_OSS_BUCKET
    }

    /**
     * 获取OSS STS授权接口地址
     * 返回完整的URL，包含API基地址
     */
    fun getOssStsAuthUrl(context: Context): String {
        val apiBaseUrl = ConfigHelper.getString("api_base_url")
        val stsAuthPath = ConfigHelper.getString(KEY_OSS_STS_AUTH_URL, DEFAULT_OSS_STS_AUTH_URL)
        return "$apiBaseUrl$stsAuthPath"
    }

    /**
     * 获取OSS区域
     * 返回固定的青岛区域
     */
    fun getOssRegion(context: Context): String {
        return DEFAULT_OSS_REGION // 直接返回固定值，不使用ConfigHelper中的配置
    }

    /**
     * 日志输出当前OSS配置
     */
    fun logOssConfig(context: Context) {
        Log.d(TAG, "OSS配置:")
        Log.d(TAG, "Endpoint: ${getOssEndpoint(context)}")
        Log.d(TAG, "Bucket: ${getOssBucket(context)}")
        Log.d(TAG, "STS授权URL: ${getOssStsAuthUrl(context)}")
        Log.d(TAG, "区域: ${getOssRegion(context)}")
    }

    /**
     * 更新OSS配置
     * 用于从STS响应中更新bucket（不更新region和endpoint）
     *
     * @param region OSS区域（此参数被忽略，使用固定值）
     * @param bucket OSS存储桶名称
     */
    fun updateOssConfig(region: String, bucket: String) {
        try {
            // 更新内存中的配置
            val configJson = ConfigHelper.getAllConfig().toMutableMap()
            // 不更新region，使用固定值
            // configJson[KEY_OSS_REGION] = region
            configJson[KEY_OSS_BUCKET] = bucket

            // 不更新endpoint，使用固定值
            // val endpoint = "https://$region.aliyuncs.com"
            // configJson[KEY_OSS_ENDPOINT] = endpoint

            // 将更新后的配置应用到ConfigHelper
            // 注意：这里只是更新内存中的配置，不会写入文件
            val jsonObject = JSONObject()
            for ((key, value) in configJson) {
                jsonObject.put(key, value)
            }

            // 这里我们需要反射设置ConfigHelper的configCache字段
            // 由于ConfigHelper的configCache是私有的，我们需要使用反射来更新它
            try {
                val field = ConfigHelper::class.java.getDeclaredField("configCache")
                field.isAccessible = true
                field.set(ConfigHelper, jsonObject)

                Log.d(TAG, "OSS配置已更新: bucket=$bucket, 使用固定endpoint=${DEFAULT_OSS_ENDPOINT}")
            } catch (e: Exception) {
                Log.e(TAG, "更新ConfigHelper配置失败: ${e.message}", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新OSS配置失败: ${e.message}", e)
        }
    }
}
