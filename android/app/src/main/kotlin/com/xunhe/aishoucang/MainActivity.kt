package com.xunhe.aishoucang

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.helpers.ConfigHelper
import com.xunhe.aishoucang.helpers.TaskBridgeHelper
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import com.umeng.commonsdk.UMConfigure
import com.umeng.analytics.MobclickAgent
import com.umeng.analytics.MobclickAgent.EScenarioType

class MainActivity: FlutterActivity() {
    companion object {
        private const val TAG = "MainActivity"

        // 友盟AppKey
        private const val UMENG_APP_KEY = "682ad77dbc47b67d836a0987"

        // 友盟渠道
        private const val UMENG_CHANNEL = "Umeng"
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        Log.d(TAG, "Flutter引擎配置中...")

        // 初始化配置
        ConfigHelper.init(applicationContext)
        Log.d(TAG, "配置已初始化，API基址: ${ConfigHelper.getString("api_base_url")}")

        // 初始化友盟SDK
        initUmengSDK(applicationContext)

        // 记录友盟测试事件， 不要删除！！！
        // MobclickAgent.onEvent(this, "um_test_url", mapOf("url" to data.toString()))

        // 设置Flutter桥接
        FlutterBridge.setup(flutterEngine, applicationContext)

        // 初始化任务桥接
        TaskBridgeHelper.getInstance().initialize(flutterEngine)

        // 启动剪贴板监听
        val clipboardHelper = ClipboardHelper.getInstance(applicationContext)
        clipboardHelper.startMonitor()

        Log.d(TAG, "Flutter引擎配置完成，桥接已设置")
    }

    override fun onDestroy() {
        super.onDestroy()

        // 清除保存的截图权限
        try {
            com.xunhe.aishoucang.lib.MediaProjectionSingleton.getInstance().clearPermission()
            Log.d(TAG, "已清除截图权限")
        } catch (e: Exception) {
            Log.e(TAG, "清除截图权限失败", e)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        // 友盟SDK页面统计，使用LAUNCHER场景类型
        MobclickAgent.onResume(this)
    }

    override fun onPause() {
        super.onPause()
        // 友盟SDK页面统计
        MobclickAgent.onPause(this)
    }

    override fun onStart() {
        super.onStart()
        // 设置统计场景类型
        MobclickAgent.setScenarioType(this, EScenarioType.E_UM_NORMAL)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
    }

    /**
     * 初始化友盟SDK
     *
     * @param context 应用上下文
     */
    private fun initUmengSDK(context: Context) {
        try {
            // 友盟SDK预初始化（合规）
            // 预初始化函数不会采集设备信息，也不会向友盟后台上报数据
            UMConfigure.preInit(context, UMENG_APP_KEY, UMENG_CHANNEL)
            Log.d(TAG, "友盟SDK预初始化完成")

            completeUmengInit(context)
        } catch (e: Exception) {
            Log.e(TAG, "初始化友盟SDK时出错: ${e.message}", e)
        }
    }

    /**
     * 完成友盟SDK的正式初始化
     * 此方法应在用户同意隐私政策后调用
     *
     * @param context 应用上下文
     */
    fun completeUmengInit(context: Context) {
        try {
            // 正式初始化友盟SDK
            UMConfigure.init(
                context,
                UMENG_APP_KEY,
                UMENG_CHANNEL,
                UMConfigure.DEVICE_TYPE_PHONE,
                ""
            )
            // 启用友盟SDK日志功能，便于调试
            UMConfigure.setLogEnabled(true)
            Log.d(TAG, "友盟SDK正式初始化完成，已启用日志功能")
        } catch (e: Exception) {
            Log.e(TAG, "完成友盟SDK初始化时出错: ${e.message}", e)
        }
    }
}
