package com.xunhe.aishoucang.lib

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

/**
 * 图片压缩工具类
 * 用于压缩图片，减小文件大小
 */
class ImageCompressHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "ImageCompressHelper"

        // 默认压缩质量
        private const val DEFAULT_QUALITY = 80

        // 默认最大宽度/高度
        private const val DEFAULT_MAX_SIZE = 1920

        // 文件大小阈值（1MB），超过此大小才进行压缩
        private const val FILE_SIZE_THRESHOLD = 1024 * 1024

        @Volatile
        private var instance: ImageCompressHelper? = null

        fun getInstance(context: Context): ImageCompressHelper {
            return instance ?: synchronized(this) {
                instance ?: ImageCompressHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    /**
     * 压缩图片
     *
     * @param filePath 原图片路径
     * @param quality 压缩质量 (0-100)，默认80
     * @param maxSize 最大宽度/高度，默认1920
     * @return 压缩后的图片路径，如果压缩失败则返回原路径
     */
    fun compressImage(
        filePath: String,
        quality: Int = DEFAULT_QUALITY,
        maxSize: Int = DEFAULT_MAX_SIZE
    ): String {
        val file = File(filePath)

        // 检查文件是否存在
        if (!file.exists()) {
            Log.e(TAG, "文件不存在: $filePath")
            return filePath
        }

        try {
            // 获取图片的原始尺寸
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(filePath, options)

            // 计算采样率
            val width = options.outWidth
            val height = options.outHeight
            val maxDimension = max(width, height)

            // 如果图片尺寸已经小于最大尺寸，则不需要缩小尺寸
            var sampleSize = 1
            if (maxDimension > maxSize) {
                sampleSize = (maxDimension.toFloat() / maxSize.toFloat()).roundToInt()
                sampleSize = max(1, sampleSize)
            }

            // 加载图片
            val decodingOptions = BitmapFactory.Options().apply {
                inSampleSize = sampleSize
                inPreferredConfig = Bitmap.Config.ARGB_8888
            }

            val bitmap = BitmapFactory.decodeFile(filePath, decodingOptions)
                ?: return filePath // 解码失败，返回原路径

            // 创建压缩后的文件
            val originalName = file.name
            val extension = originalName.substringAfterLast('.', "jpg")
            val nameWithoutExtension = originalName.substringBeforeLast('.')
            val compressedName = "${nameWithoutExtension}_compressed.$extension"
            val compressedFile = File(file.parent, compressedName)

            // 压缩并保存图片
            FileOutputStream(compressedFile).use { out ->
                // 使用JPEG格式压缩，质量为指定值
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, out)
                out.flush()
            }

            // 释放Bitmap
            bitmap.recycle()

            // 检查压缩后的文件大小
            val originalSize = file.length()
            val compressedSize = compressedFile.length()

            Log.d(TAG, "图片压缩完成: 原始大小=${originalSize}bytes, 压缩后大小=${compressedSize}bytes, 压缩率=${(compressedSize.toFloat() / originalSize.toFloat() * 100).roundToInt()}%")

            // 如果压缩后的文件比原文件大，则使用原文件
            if (compressedSize >= originalSize) {
                Log.d(TAG, "压缩后文件大小大于或等于原文件，使用原文件")
                compressedFile.delete()
                return filePath
            }

            return compressedFile.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "压缩图片失败: ${e.message}", e)
            return filePath
        }
    }


}
