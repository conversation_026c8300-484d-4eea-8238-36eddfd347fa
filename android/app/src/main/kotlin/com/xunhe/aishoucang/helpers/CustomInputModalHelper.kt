package com.xunhe.aishoucang.helpers

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.AnimationUtils
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView
import com.xunhe.aishoucang.R

/**
 * 自定义输入Modal辅助类
 * 使用悬浮窗来显示输入对话框，支持自定义动画和回调
 */
object CustomInputModalHelper {
    private const val TAG = "CustomInputModalHelper"

    // Modal视图
    private var modalView: View? = null

    // Modal内部容器（实际显示内容的容器）
    private var modalInnerContainer: View? = null

    // 窗口参数
    private var params: WindowManager.LayoutParams? = null

    // 窗口管理器
    private var windowManager: WindowManager? = null

    // 是否正在显示
    private var isShowing = false

    // 用于延迟关闭的Handler
    private val handler = Handler(Looper.getMainLooper())

    // 移除视图的Runnable
    private val removeViewRunnable = Runnable {
        Log.d(TAG, "动画延时结束，执行移除视图")
        hideModalImmediately()
    }

    // 回调接口
    interface InputModalCallback {
        fun onConfirm(inputText: String)
        fun onCancel()
    }

    /**
     * 显示输入Modal
     * @param context 上下文
     * @param title 标题
     * @param hint 输入框提示文本
     * @param cancelText 取消按钮文本
     * @param confirmText 确认按钮文本
     * @param callback 回调接口
     */
    fun showInputModal(
        context: Context,
        title: String = "请输入",
        hint: String = "请输入内容",
        cancelText: String = "取消",
        confirmText: String = "确定",
        callback: InputModalCallback? = null
    ) {
        try {
            // 如果已经在显示，先移除旧的Modal
            if (isShowing) {
                Log.d(TAG, "已有Modal在显示，先移除")
                hideModalImmediately()
            }

            // 移除所有可能的回调
            handler.removeCallbacks(removeViewRunnable)

            // 获取窗口管理器
            if (windowManager == null) {
                windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            }

            // 创建Modal视图
            createModalView(context, title, hint, cancelText, confirmText, callback)

            // 添加到窗口
            windowManager?.addView(modalView, params)
            isShowing = true
            Log.d(TAG, "添加Modal到窗口成功，isShowing = $isShowing")

            // 开始显示动画 - 应用到内部容器
            val animation = AnimationUtils.loadAnimation(context, R.anim.modal_in)
            modalInnerContainer?.startAnimation(animation)

            // 延时让输入框获取焦点（等待动画开始后）
            handler.postDelayed({
                val editText = modalView?.findViewById<EditText>(R.id.modal_input)
                editText?.requestFocus()
                // 显示软键盘
                val inputMethodManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as? android.view.inputmethod.InputMethodManager
                inputMethodManager?.showSoftInput(editText, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
                Log.d(TAG, "输入框已获取焦点并显示软键盘")
            }, 100) // 延时100毫秒

        } catch (e: Exception) {
            Log.e(TAG, "显示Modal时出错", e)
        }
    }

    /**
     * 创建Modal视图
     */
    private fun createModalView(
        context: Context,
        title: String,
        hint: String,
        cancelText: String,
        confirmText: String,
        callback: InputModalCallback?
    ) {
        // 创建视图
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        modalView = inflater.inflate(R.layout.input_modal_layout, null)

        // 获取内部容器引用
        modalInnerContainer = modalView?.findViewById(R.id.modal_container)

        // 设置标题和输入框提示
        modalView?.findViewById<TextView>(R.id.modal_title)?.text = title
        val editText = modalView?.findViewById<EditText>(R.id.modal_input)
        editText?.hint = hint

        // 设置按钮文本
        modalView?.findViewById<TextView>(R.id.btn_cancel)?.text = cancelText
        modalView?.findViewById<TextView>(R.id.btn_confirm)?.text = confirmText

        // 设置取消按钮点击事件
        modalView?.findViewById<TextView>(R.id.btn_cancel)?.setOnClickListener {
            Log.d(TAG, "取消按钮被点击")
            callback?.onCancel()
            hideModalWithAnimation()
        }

        // 设置确认按钮点击事件
        modalView?.findViewById<TextView>(R.id.btn_confirm)?.setOnClickListener {
            Log.d(TAG, "确认按钮被点击")
            val inputText = editText?.text?.toString() ?: ""
            callback?.onConfirm(inputText)
            hideModalWithAnimation()
        }

        // 设置输入框的Enter键监听
        editText?.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)) {
                Log.d(TAG, "输入框Enter键被按下")
                val inputText = editText.text?.toString() ?: ""
                callback?.onConfirm(inputText)
                hideModalWithAnimation()
                true
            } else {
                false
            }
        }

        // 设置背景点击事件（点击背景关闭Modal）
        modalView?.setOnClickListener {
            Log.d(TAG, "背景被点击，关闭Modal")
            callback?.onCancel()
            hideModalWithAnimation()
        }

        // 防止点击Modal内容区域时关闭Modal
        modalInnerContainer?.setOnClickListener {
            // 空实现，阻止事件冒泡
        }

        // 创建窗口参数
        createWindowParams()
    }

    /**
     * 创建窗口参数
     */
    private fun createWindowParams() {
        params = WindowManager.LayoutParams().apply {
            // 设置窗口类型
            type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            }

            // 设置像素格式为半透明，这是关键！
            format = PixelFormat.TRANSLUCENT

            // 设置窗口标志 - 移除FLAG_NOT_FOCUSABLE以允许输入框获取焦点
            flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH

            // 设置窗口大小和位置
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT

            Log.d(TAG, "创建窗口参数完成")
        }
    }

    /**
     * 带动画隐藏Modal
     */
    private fun hideModalWithAnimation() {
        if (!isShowing || modalInnerContainer == null) {
            Log.d(TAG, "Modal未显示或容器为空，直接返回")
            return
        }

        try {
            Log.d(TAG, "开始隐藏动画")
            val animation = AnimationUtils.loadAnimation(modalInnerContainer!!.context, R.anim.modal_out)
            
            // 设置动画监听器
            animation.setAnimationListener(object : android.view.animation.Animation.AnimationListener {
                override fun onAnimationStart(animation: android.view.animation.Animation?) {
                    Log.d(TAG, "隐藏动画开始")
                }

                override fun onAnimationEnd(animation: android.view.animation.Animation?) {
                    Log.d(TAG, "隐藏动画结束，立即移除视图")
                    // 动画结束后立即移除视图，避免闪烁
                    hideModalImmediately()
                }

                override fun onAnimationRepeat(animation: android.view.animation.Animation?) {}
            })

            modalInnerContainer?.startAnimation(animation)
        } catch (e: Exception) {
            Log.e(TAG, "隐藏动画执行失败", e)
            hideModalImmediately()
        }
    }

    /**
     * 立即隐藏Modal（无动画）
     */
    private fun hideModalImmediately() {
        try {
            if (isShowing && modalView != null && windowManager != null) {
                Log.d(TAG, "立即移除Modal视图")
                windowManager?.removeView(modalView)
                isShowing = false
                Log.d(TAG, "Modal视图移除成功，isShowing = $isShowing")
            }
        } catch (e: Exception) {
            Log.e(TAG, "移除Modal视图时出错", e)
        } finally {
            // 清理资源
            modalView = null
            modalInnerContainer = null
            isShowing = false
        }
    }

    /**
     * 检查Modal是否正在显示
     */
    fun isModalShowing(): Boolean {
        return isShowing
    }

    /**
     * 强制关闭Modal
     */
    fun forceHideModal() {
        if (isShowing) {
            hideModalImmediately()
        }
    }
}
