package com.xunhe.aishoucang.lib

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.util.Log
import android.widget.Toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * 长截图服务拼接功能
 * 包含图像拼接和结果处理的方法
 */
internal fun stitchImages(service: LongScreenshotService) {
    if (service.capturedBitmaps.isEmpty()) {
        Log.e(LongScreenshotService.TAG, "没有可拼接的图像")
        service.stopLongScreenshot()
        return
    }

    // 立即隐藏暂停按钮，不使用动画
    service.pauseButton?.hideImmediately()

    // 更新进度
    service.longScreenshotHelper?.updateProgress(90, "正在拼接图像...")

    GlobalScope.launch(Dispatchers.IO) {
        try {
            // 计算总高度
            var totalHeight = 0
            val overlaps = mutableListOf<Int>()

            // 第一张图片不需要计算重叠
            totalHeight += service.capturedBitmaps[0].height

            // 使用固定的重叠区域（每张图片高度的20%）
            for (i in 1 until service.capturedBitmaps.size) {
                val currentBitmap = service.capturedBitmaps[i]

                // 固定重叠区域为当前图片高度的20%
                val overlap = (currentBitmap.height * 0.2).toInt()
                overlaps.add(overlap)

                Log.d(LongScreenshotService.TAG, "图片 $i 使用固定重叠区域: $overlap")

                // 添加非重叠部分的高度
                totalHeight += (currentBitmap.height - overlap)
            }

            // 创建最终的长图
            val resultBitmap = Bitmap.createBitmap(
                service.capturedBitmaps[0].width,
                totalHeight,
                Bitmap.Config.ARGB_8888
            )

            // 绘制长图
            val canvas = Canvas(resultBitmap)
            var currentY = 0

            // 绘制第一张图片
            canvas.drawBitmap(service.capturedBitmaps[0], 0f, currentY.toFloat(), null)
            currentY += service.capturedBitmaps[0].height

            // 绘制后续图片，考虑重叠区域
            for (i in 1 until service.capturedBitmaps.size) {
                val overlap = overlaps[i - 1]
                currentY -= overlap

                // 绘制当前图片
                canvas.drawBitmap(service.capturedBitmaps[i], 0f, currentY.toFloat(), null)
                currentY += service.capturedBitmaps[i].height
            }

            // 保存结果
            val file = saveBitmapToFile(service, resultBitmap)
            resultBitmap.recycle()

            // 释放所有截图
            service.capturedBitmaps.forEach { it.recycle() }
            service.capturedBitmaps.clear()

            if (file != null) {
                // 处理长截图结果
                processLongScreenshotResult(service, file.absolutePath)
            } else {
                Log.e(LongScreenshotService.TAG, "保存长截图失败")
                withContext(Dispatchers.Main) {
                    Toast.makeText(service, "保存长截图失败", Toast.LENGTH_SHORT).show()
                    service.stopLongScreenshot()
                }
            }
        } catch (e: Exception) {
            Log.e(LongScreenshotService.TAG, "拼接图像失败", e)
            withContext(Dispatchers.Main) {
                Toast.makeText(service, "拼接图像失败: ${e.message}", Toast.LENGTH_SHORT).show()
                service.stopLongScreenshot()
            }
        }
    }
}

/**
 * 查找最佳拼接点
 * 此方法已不再使用，保留为了兼容性
 * 现在使用固定的重叠区域（图片高度的20%）
 */
internal fun findBestStitchingPoint(topBitmap: Bitmap, bottomBitmap: Bitmap): Int {
    // 返回固定的重叠区域（图片高度的20%）
    val overlap = (topBitmap.height * 0.2).toInt()
    Log.d(LongScreenshotService.TAG, "使用固定重叠区域: $overlap")
    return overlap
}
