package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.widget.Toast


/**
 * 美团应用处理器
 */
class MeituanAppHandler : AppHandler {
    companion object {
        private const val TAG = "<PERSON>tuanAppHandler"
    }

    override fun handle(context: Context) {
        Toast.makeText(context, "正在处理美团应用", Toast.LENGTH_SHORT)
            .show()
        Log.d(TAG, "正在处理美团应用")

        // 暂时直接通知内容准备完成
        SharePanelHelper.notifyContentReady()

        // TODO: 实现美团特定的操作逻辑
    }
}
