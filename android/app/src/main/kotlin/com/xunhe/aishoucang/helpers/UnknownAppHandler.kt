package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.widget.Toast


/**
 * 未知应用处理器
 * 用于处理未识别的应用类型
 */
class UnknownAppHandler : AppHandler {
    companion object {
        private const val TAG = "UnknownAppHandler"
    }

    override fun handle(context: Context) {
        // 对于未知应用，直接通知内容准备完成
        SharePanelHelper.notifyContentReady()
    }
}
