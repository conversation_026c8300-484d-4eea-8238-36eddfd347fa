package com.xunhe.aishoucang.lib

import android.content.Context
import android.widget.Toast

/**
 * Toast 辅助类，用于显示原生 Android Toast 消息
 */
class ToastHelper private constructor(private val context: Context) {
    companion object {
        // Toast 显示时长 - 短
        const val LENGTH_SHORT = Toast.LENGTH_SHORT
        
        // Toast 显示时长 - 长
        const val LENGTH_LONG = Toast.LENGTH_LONG
        
        // 单例实例
        @Volatile
        private var instance: ToastHelper? = null
        
        /**
         * 获取ToastHelper实例
         */
        fun getInstance(context: Context): ToastHelper {
            return instance ?: synchronized(this) {
                instance ?: ToastHelper(context.applicationContext).also { 
                    instance = it 
                }
            }
        }
        
        /**
         * 显示 Toast 消息
         */
        fun show(context: Context, message: String, duration: Int = Toast.LENGTH_SHORT) {
            getInstance(context).show(message, duration)
        }
    }
    
    /**
     * 显示 Toast 消息
     */
    fun show(message: String, duration: Int = Toast.LENGTH_SHORT) {
        // 在主线程中显示 Toast
        android.os.Handler(android.os.Looper.getMainLooper()).post {
            Toast.makeText(context, message, duration).show()
        }
    }
}
