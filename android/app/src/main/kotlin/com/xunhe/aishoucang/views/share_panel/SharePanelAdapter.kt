package com.xunhe.aishoucang.views.share_panel

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.model.Favorite

/**
 * 分享面板适配器
 */
class SharePanelAdapter(
    private val context: Context,
    private val items: MutableList<SharePanelItem>, // ✅ 改为可变列表
    val onItemClick: (SharePanelItem) -> Unit       // ✅ 公开属性，便于拖动更新后保留回调
) : RecyclerView.Adapter<SharePanelAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.share_panel_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.bind(item)
        holder.itemView.setOnClickListener {
            onItemClick(item)
        }
    }

    override fun getItemCount(): Int = items.size

    // ✅ 拖动交换数据的方法（被 ItemTouchHelper 调用）
    fun moveItem(fromPosition: Int, toPosition: Int) {
        if (fromPosition < 0 || toPosition < 0 || fromPosition >= items.size || toPosition >= items.size) return
        val item = items.removeAt(fromPosition)
        items.add(toPosition, item)
        notifyItemMoved(fromPosition, toPosition)
    }

    // 获取点击回调，用于刷新时重用
    fun getOnItemClickListener(): (SharePanelItem) -> Unit {
        return onItemClick
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val shareIcon: View = itemView.findViewById(R.id.share_icon)
        private val coverImage: ImageView = itemView.findViewById(R.id.cover_image)
        private val bookmarkIcon: ImageView = itemView.findViewById(R.id.bookmark_icon)
        private val shareName: TextView = itemView.findViewById(R.id.share_name)

        fun bind(item: SharePanelItem) {
            // 设置图标颜色（当没有封面时显示）
            shareIcon.setBackgroundColor(item.iconColor)
            shareName.text = item.name

            // 如果是收藏夹项，可以添加特殊样式
            if (item.isFavorite) {
                // 设置文本样式
                shareName.setTextColor(Color.parseColor("#FF5722")) // 使用深橙色

                // 处理封面图片
                val favorite = item.favoriteData
                if (favorite != null && favorite.cover.isNotEmpty()) {
                    // 显示封面图片
                    coverImage.visibility = View.VISIBLE

                    // 使用Glide加载图片，不应用圆角（圆角已应用到父容器）
                    Glide.with(context)
                        .load(favorite.cover)
                        .apply(RequestOptions()
                            .centerCrop()
                            .error(R.drawable.favorite_icon_background))
                        .into(coverImage)

                    // 隐藏书签图标，让封面图片完全显示
                    bookmarkIcon.visibility = View.GONE
                } else {
                    // 没有封面，显示默认背景和图标
                    coverImage.visibility = View.GONE
                    bookmarkIcon.visibility = View.VISIBLE
                }
            } else {
                // 恢复默认样式
                shareName.setTextColor(Color.BLACK)
                coverImage.visibility = View.GONE
                bookmarkIcon.visibility = View.VISIBLE
            }
        }
    }
}

/**
 * 分享面板项数据模型
 */
data class SharePanelItem(
    val id: String,
    val name: String,
    val iconColor: Int,
    val iconDrawable: Drawable? = null,
    val isFavorite: Boolean = false,         // 是否为收藏夹项
    val favoriteData: Favorite? = null       // 收藏夹数据，当isFavorite为true时有效
)
