package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import org.json.JSONObject

/**
 * 业务提取器示例
 * 演示如何使用WebViewHtmlExtractor提取业务特定的数据
 */
object BusinessExtractorExample {
    private const val TAG = "BusinessExtractorExample"

    /**
     * 提取小红书商品数据
     *
     * @param context 上下文
     * @param url 小红书商品URL
     * @param callback 回调函数，返回提取的数据和可能的错误信息
     */
    fun extractXiaohongshuGoods(
        context: Context,
        url: String,
        callback: (data: JSONObject?, error: String?) -> Unit
    ) {
        // 使用业务特定的JavaScript文件
        WebViewHtmlExtractor.executeBusinessJavaScript(
            context,
            url,
            "XiaoHongshuGoods", // 对应assets/js/XiaoHongshuGoods.js文件
            emptyMap() // 不需要替换参数，因为选择器已经在JavaScript文件中定义
        ) { result, error ->
            if (error != null) {
                Log.e(TAG, "提取小红书商品数据失败: $error")
                callback(null, error)
                return@executeBusinessJavaScript
            }

            if (result != null) {
                try {
                    // 解析JSON结果
                    val jsonObject = JSONObject(result)
                    
                    // 检查是否有错误
                    if (jsonObject.has("error")) {
                        val errorMessage = jsonObject.getString("error")
                        Log.e(TAG, "提取小红书商品数据时JavaScript报错: $errorMessage")
                        callback(null, errorMessage)
                        return@executeBusinessJavaScript
                    }
                    
                    // 返回提取的数据
                    callback(jsonObject, null)
                } catch (e: Exception) {
                    Log.e(TAG, "解析小红书商品数据失败: ${e.message}", e)
                    callback(null, "解析小红书商品数据失败: ${e.message}")
                }
            } else {
                callback(null, "提取小红书商品数据失败: 结果为空")
            }
        }
    }
}
