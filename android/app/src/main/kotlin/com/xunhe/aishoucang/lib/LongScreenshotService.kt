package com.xunhe.aishoucang.lib

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.graphics.RectF
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.view.accessibility.AccessibilityNodeInfo
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.xunhe.aishoucang.MainActivity
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.lib.LongScreenshotPauseButton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.Executors

/**
 * 长截图服务
 * 用于在后台执行长截图操作，包括滚动捕获、图像拼接等
 */
class LongScreenshotService : Service() {

    // 媒体投影相关
    internal var mediaProjection: MediaProjection? = null
    internal var virtualDisplay: VirtualDisplay? = null
    internal var imageReader: ImageReader? = null
    internal var screenDensity: Int = 0
    internal var screenWidth: Int = 0
    internal var screenHeight: Int = 0
    internal var safeAreaTop: Int = 0
    internal var safeAreaBottom: Int = 0
    internal var handler = Handler(Looper.getMainLooper())
    internal val executor = Executors.newSingleThreadExecutor()

    // 长截图相关
    internal var isCapturing = false
    internal var isCancelled = false
    internal var capturedBitmaps = mutableListOf<Bitmap>()
    internal var scrollHeight = 0
    internal var totalScrolls = 0
    internal var currentScroll = 0
    internal var longScreenshotHelper: LongScreenshotHelper? = null
    internal var accessibilityHelper: AccessibilityHelper? = null
    internal var pauseButton: LongScreenshotPauseButton? = null

    // 保存的根节点，用于执行滚动操作
    internal var savedRootNode: AccessibilityNodeInfo? = null

    // TAG常量，供其他文件使用
    companion object {
        internal const val TAG = "LongScreenshotService"
        private const val NOTIFICATION_ID = 2002
        private const val CHANNEL_ID = "long_screenshot_channel"
        private const val CHANNEL_NAME = "长截图服务"

        // 广播Action
        const val ACTION_CANCEL_LONG_SCREENSHOT = "com.xunhe.aishoucang.ACTION_CANCEL_LONG_SCREENSHOT"

        // Intent Action
        private const val ACTION_START_LONG_SCREENSHOT = "com.xunhe.aishoucang.ACTION_START_LONG_SCREENSHOT"
        private const val ACTION_START_LONG_SCREENSHOT_WITH_SAVED_PERMISSION = "com.xunhe.aishoucang.ACTION_START_LONG_SCREENSHOT_WITH_SAVED_PERMISSION"

        // Intent Extra
        private const val EXTRA_RESULT_CODE = "result_code"
        private const val EXTRA_DATA = "data"

        /**
         * 启动长截图服务（通过权限结果）
         */
        fun startLongScreenshotService(context: Context, resultCode: Int, data: Intent) {
            val intent = Intent(context, LongScreenshotService::class.java).apply {
                action = ACTION_START_LONG_SCREENSHOT
                putExtra(EXTRA_RESULT_CODE, resultCode)
                putExtra(EXTRA_DATA, data)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 启动长截图服务（使用已保存的权限结果）
         */
        fun startLongScreenshotServiceWithSavedPermission(context: Context) {
            val intent = Intent(context, LongScreenshotService::class.java).apply {
                action = ACTION_START_LONG_SCREENSHOT_WITH_SAVED_PERMISSION
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }

    // 取消广播接收器
    private val cancelReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == ACTION_CANCEL_LONG_SCREENSHOT) {
                Log.d(TAG, "收到取消长截图广播")
                isCancelled = true
                stopLongScreenshot()
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "长截图服务已创建")

        // 获取屏幕信息
        val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val metrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(metrics)
        screenDensity = metrics.densityDpi
        screenWidth = metrics.widthPixels
        screenHeight = metrics.heightPixels

        // 计算安全区域（避开状态栏和导航栏）
        calculateSafeArea()

        // 注册取消广播接收器
        registerCancelReceiver()

        // 获取辅助类实例
        longScreenshotHelper = LongScreenshotHelper.getInstance(this)
        accessibilityHelper = AccessibilityHelper.getInstance(this)
        pauseButton = LongScreenshotPauseButton.getInstance(this)
    }

    override fun onDestroy() {
        Log.d(TAG, "长截图服务即将销毁")

        // Android 14+ 需要立即停止前台服务状态，避免超时异常
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14+
            try {
                stopForeground(STOP_FOREGROUND_REMOVE) // 立即移除通知并停止前台服务状态
                Log.d(TAG, "Android 14+ 已立即停止前台服务状态")
            } catch (e: Exception) {
                Log.e(TAG, "Android 14+ 停止前台服务状态失败", e)
            }
        } else {
            // Android 13 及以下的标准处理
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    stopForeground(true) // 移除通知并停止前台服务状态
                    Log.d(TAG, "已停止前台服务状态")
                }
            } catch (e: Exception) {
                Log.e(TAG, "停止前台服务状态失败", e)
            }
        }

        // 取消注册广播接收器
        unregisterCancelReceiver()

        // 释放资源
        releaseResources()

        super.onDestroy()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "长截图服务已启动: ${intent?.action}")

        // 创建通知
        createNotificationChannel()
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)

        // 处理Intent
        when (intent?.action) {
            ACTION_START_LONG_SCREENSHOT -> {
                val resultCode = intent.getIntExtra(EXTRA_RESULT_CODE, 0)
                val data = intent.getParcelableExtra<Intent>(EXTRA_DATA)

                if (data != null) {
                    // 执行长截图
                    performLongScreenshot(resultCode, data)
                } else {
                    Log.e(TAG, "长截图数据为空")
                    stopSelf()
                }
            }
            ACTION_START_LONG_SCREENSHOT_WITH_SAVED_PERMISSION -> {
                // 使用已保存的权限执行长截图
                performLongScreenshotWithSavedPermission()
            }
            else -> {
                Log.e(TAG, "未知的Action: ${intent?.action}")
                stopSelf()
            }
        }

        return START_NOT_STICKY
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用于长截图服务的通知"
                enableLights(false)
                enableVibration(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        // 创建PendingIntent，点击通知时打开MainActivity
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("长截图服务")
            .setContentText("正在进行长截图...")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }

    /**
     * 注册取消广播接收器
     * 使用工具类安全注册，处理Android 13+兼容性问题
     */
    private fun registerCancelReceiver() {
        val filter = IntentFilter(ACTION_CANCEL_LONG_SCREENSHOT)
        BroadcastReceiverUtils.registerInternalReceiver(
            context = this,
            receiver = cancelReceiver,
            filter = filter,
            logTag = TAG
        )
    }

    /**
     * 取消注册广播接收器
     * 使用工具类安全取消注册
     */
    private fun unregisterCancelReceiver() {
        BroadcastReceiverUtils.safeUnregisterReceiver(
            context = this,
            receiver = cancelReceiver,
            logTag = TAG
        )
    }
}
