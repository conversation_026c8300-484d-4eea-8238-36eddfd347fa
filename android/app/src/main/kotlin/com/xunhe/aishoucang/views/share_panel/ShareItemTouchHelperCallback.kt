package com.xunhe.aishoucang.views.share_panel

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView

/**
 * 拖动排序回调，用于支持分享项在 RecyclerView 中的位置拖动
 */
class ShareItemTouchHelperCallback(
    private val adapter: SharePanelAdapter
) : ItemTouchHelper.Callback() {

    // 定义允许的拖动方向（上下左右），不允许滑动删除
    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN or
                        ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
        val swipeFlags = 0
        return makeMovementFlags(dragFlags, swipeFlags)
    }

    // 拖动发生时交换 item 的位置
    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        adapter.moveItem(viewHolder.adapterPosition, target.adapterPosition)
        return true
    }

    // 不启用滑动删除
    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        // No-op
    }

    // 是否启用长按开始拖动
    override fun isLongPressDragEnabled(): Boolean = true
}
