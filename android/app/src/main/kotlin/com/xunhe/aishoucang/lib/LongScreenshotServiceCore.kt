package com.xunhe.aishoucang.lib

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.PixelFormat
import android.graphics.Rect
import android.graphics.RectF
import android.hardware.display.DisplayManager
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.util.Log
import android.widget.Toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * 长截图服务核心功能
 * 包含长截图的核心实现方法
 */
internal fun LongScreenshotService.calculateSafeArea() {
    // 获取状态栏高度
    var statusBarHeight = 0
    val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
    if (resourceId > 0) {
        statusBarHeight = resources.getDimensionPixelSize(resourceId)
    }

    // 获取导航栏高度
    var navigationBarHeight = 0
    val navResourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android")
    if (navResourceId > 0) {
        navigationBarHeight = resources.getDimensionPixelSize(navResourceId)
    }

    // 设置安全区域
    safeAreaTop = statusBarHeight
    safeAreaBottom = screenHeight - navigationBarHeight

    Log.d(LongScreenshotService.TAG, "安全区域计算完成: 顶部=$safeAreaTop, 底部=$safeAreaBottom, 屏幕高度=$screenHeight")
}

/**
 * 执行长截图（通过权限结果）
 */
internal fun LongScreenshotService.performLongScreenshot(resultCode: Int, data: Intent) {
    try {
        // 获取MediaProjection
        val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        mediaProjection = projectionManager.getMediaProjection(resultCode, data)

        if (mediaProjection == null) {
            Log.e(LongScreenshotService.TAG, "无法获取MediaProjection")
            stopSelf()
            return
        }

        // Android 14+需要注册回调
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val callback = object : MediaProjection.Callback() {
                override fun onStop() {
                    Log.d(LongScreenshotService.TAG, "MediaProjection已停止，停止长截图")
                    stopLongScreenshot()
                }
            }
            mediaProjection?.registerCallback(callback, handler)
            Log.d(LongScreenshotService.TAG, "已注册MediaProjection回调（Android 14+）")
        }

        // 保存权限结果到单例，以便下次使用
        MediaProjectionSingleton.getInstance().savePermissionResult(resultCode, data)

        // 开始长截图流程
        startLongScreenshotProcess()
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "执行长截图失败", e)
        stopSelf()
    }
}

/**
 * 执行长截图（使用保存的权限结果）
 */
internal fun LongScreenshotService.performLongScreenshotWithSavedPermission() {
    try {
        // 从单例获取保存的权限结果
        val mediaProjectionSingleton = MediaProjectionSingleton.getInstance()
        val resultCode = mediaProjectionSingleton.getResultCode()
        val resultData = mediaProjectionSingleton.getResultData()

        if (resultData == null) {
            Log.e(LongScreenshotService.TAG, "保存的权限结果数据为空")
            stopSelf()
            return
        }

        // 创建新的MediaProjection对象
        val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        mediaProjection = projectionManager.getMediaProjection(resultCode, resultData)

        if (mediaProjection == null) {
            Log.e(LongScreenshotService.TAG, "无法创建MediaProjection对象")
            // 权限可能已过期，清除保存的权限
            mediaProjectionSingleton.clearPermission()
            stopSelf()
            return
        }

        // Android 14+需要注册回调
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val callback = object : MediaProjection.Callback() {
                override fun onStop() {
                    Log.d(LongScreenshotService.TAG, "MediaProjection已停止，停止长截图")
                    stopLongScreenshot()
                }
            }
            mediaProjection?.registerCallback(callback, handler)
            Log.d(LongScreenshotService.TAG, "已注册MediaProjection回调（Android 14+）")
        }

        // 开始长截图流程
        startLongScreenshotProcess()
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "使用保存的权限结果执行长截图失败", e)
        // 出现异常时清除保存的权限
        MediaProjectionSingleton.getInstance().clearPermission()
        stopSelf()
    }
}

/**
 * 开始长截图流程
 */
internal fun LongScreenshotService.startLongScreenshotProcess() {
    if (isCapturing) {
        Log.d(LongScreenshotService.TAG, "长截图已在进行中，忽略此次请求")
        return
    }

    isCapturing = true
    isCancelled = false
    capturedBitmaps.clear()
    currentScroll = 0

    // 从LongScreenshotHelper获取保存的根节点
    savedRootNode = longScreenshotHelper?.getSavedRootNode()
    Log.d(LongScreenshotService.TAG, "从LongScreenshotHelper获取保存的根节点: ${savedRootNode != null}")

    // 显示进度界面
    longScreenshotHelper?.showLongScreenshotProgressOverlay()

    // 计算滚动高度（安全区域高度的50%，确保有足够的重叠区域）
    val safeAreaHeight = safeAreaBottom - safeAreaTop
    scrollHeight = (safeAreaHeight * 0.5).toInt()

    // 设置一个较大的最大滚动次数，实际滚动次数将由内容底部检测决定
    totalScrolls = 20
    Log.d(LongScreenshotService.TAG, "设置最大滚动次数: $totalScrolls, 每次滚动高度: $scrollHeight")

    Log.d(LongScreenshotService.TAG, "开始长截图流程: 安全区域高度=$safeAreaHeight, 滚动高度=$scrollHeight, 预估总滚动次数=$totalScrolls")

    // 延迟一段时间后开始截图，确保UI准备就绪
    handler.postDelayed({
        // 开始截图循环
        captureScreenshotLoop(this)
    }, 1000)
}
