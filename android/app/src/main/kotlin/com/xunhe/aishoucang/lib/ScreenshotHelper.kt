package com.xunhe.aishoucang.lib

import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.graphics.RectF
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.GestureDetector
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.Toast
import androidx.core.view.GestureDetectorCompat
import com.xunhe.aishoucang.R

/**
 * 截图助手
 * 用于显示截图操作界面，包含删除和确认按钮
 */
class ScreenshotHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "ScreenshotHelper"
        const val REQUEST_SCREENSHOT_PERMISSION = 1001

        @Volatile
        private var instance: ScreenshotHelper? = null

        // 保存最后一次选区信息
        private var lastSelectionRect: RectF? = null

        fun getInstance(context: Context): ScreenshotHelper {
            return instance ?: synchronized(this) {
                instance ?: ScreenshotHelper(context.applicationContext).also { instance = it }
            }
        }

        /**
         * 处理截图权限结果
         */
        fun handleScreenshotPermissionResult(context: Context, requestCode: Int, resultCode: Int, data: Intent?) {
            if (requestCode == REQUEST_SCREENSHOT_PERMISSION && resultCode == android.app.Activity.RESULT_OK && data != null) {
                // 获取选区信息
                val selectionRect = lastSelectionRect
                if (selectionRect == null) {
                    Log.e(TAG, "选区信息为空")
                    Toast.makeText(context, "选区信息为空", Toast.LENGTH_SHORT).show()
                    return
                }

                // 启动前台服务进行截图
                try {
                    // 显示提示
                    Toast.makeText(context, "正在截图，请稍候...", Toast.LENGTH_SHORT).show()

                    // 启动截图服务
                    ScreenshotService.startScreenshotService(context, resultCode, data, selectionRect)

                    Log.d(TAG, "已启动截图服务")
                } catch (e: Exception) {
                    Log.e(TAG, "启动截图服务失败", e)
                    Toast.makeText(context, "启动截图服务失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private var windowManager: WindowManager? = null
    private var overlayView: android.view.View? = null
    private var overlayParams: WindowManager.LayoutParams? = null
    private var isOverlayShowing = false
    private var selectionView: ScreenshotSelectionView? = null
    private var gestureDetector: GestureDetectorCompat? = null
    private var floatingWindowHelper: FloatingWindowHelper? = null

    // 保存悬浮窗位置信息
    private var floatingWindowX: Int? = null
    private var floatingWindowY: Int? = null

    // SharedPreferences辅助类
    private var prefsHelper: SharedPreferencesHelper? = null

    // 自定义视图，用于处理返回按钮事件
    private inner class BackKeyListenerView(context: Context) : View(context) {
        init {
            // 确保视图可以获取焦点
            isFocusable = true
            isFocusableInTouchMode = true
        }

        // 重写 dispatchKeyEvent 方法
        override fun dispatchKeyEvent(event: KeyEvent): Boolean {
            Log.d(TAG, "截图界面 dispatchKeyEvent: ${event.keyCode}, action=${event.action}")

            // 检测返回按钮
            if (event.keyCode == KeyEvent.KEYCODE_BACK) {
                Log.d(TAG, "截图界面检测到返回按钮事件")
                Log.d(TAG, "截图界面检测到返回按钮点击，关闭截图界面")
                // 在主线程中执行，确保UI操作在主线程
                Handler(Looper.getMainLooper()).post {
                    hideScreenshotOverlay()
                }
                // 消费这个事件
                return true
            }
            return super.dispatchKeyEvent(event)
        }

        // 重写 onKeyDown 方法
        override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
            Log.d(TAG, "截图界面 onKeyDown: $keyCode")

            // 检测返回按钮
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                Log.d(TAG, "截图界面 onKeyDown 检测到返回按钮点击，关闭截图界面")
                // 在主线程中执行，确保UI操作在主线程
                Handler(Looper.getMainLooper()).post {
                    hideScreenshotOverlay()
                }
                return true
            }
            return super.onKeyDown(keyCode, event)
        }
    }

    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        prefsHelper = SharedPreferencesHelper.getInstance(context)
    }

    /**
     * 显示截图操作界面
     */
    fun showScreenshotOverlay(onConfirm: () -> Unit, onDelete: () -> Unit) {
        Log.d(TAG, "开始执行showScreenshotOverlay方法")

        if (isOverlayShowing) {
            // 如果界面已显示，则隐藏
            Log.d(TAG, "截图操作界面已显示，隐藏界面")
            hideScreenshotOverlay()
            return
        }

        // 获取FloatingWindowHelper实例
        floatingWindowHelper = FloatingWindowHelper.getInstance(context)

        // 获取并保存悬浮窗位置，然后隐藏悬浮窗
        if (floatingWindowHelper?.isFloatingWindowShowing() == true) {
            // 先获取悬浮窗位置
            val position = floatingWindowHelper?.getFloatingWindowPosition()
            floatingWindowX = position?.first
            floatingWindowY = position?.second

            // 同时保存到内存变量和SharedPreferences
            if (floatingWindowX != null && floatingWindowY != null) {
                prefsHelper?.saveFloatingWindowPosition(floatingWindowX!!, floatingWindowY!!)
                Log.d(TAG, "保存悬浮窗位置到SharedPreferences: x=$floatingWindowX, y=$floatingWindowY")
            } else {
                Log.d(TAG, "无法获取悬浮窗位置，使用默认位置")
            }

            // 然后隐藏悬浮窗
            floatingWindowHelper?.hideFloatingWindow()
        }

        // 创建并显示截图操作界面
        createScreenshotOverlay(onConfirm, onDelete)
    }

    /**
     * 隐藏截图操作界面
     */
    fun hideScreenshotOverlay() {
        if (overlayView != null && isOverlayShowing) {
            try {
                // 直接移除视图，不使用动画，因为返回按钮需要立即响应
                Log.d(TAG, "移除截图界面")
                try {
                    windowManager?.removeView(overlayView)
                    overlayView = null
                    isOverlayShowing = false
                    Log.d(TAG, "截图操作界面已移除")

                    // 显示悬浮窗，恢复到原位置
                    if (floatingWindowHelper?.isFloatingWindowShowing() == false) {
                        // 尝试从内存变量获取位置
                        if (floatingWindowX != null && floatingWindowY != null) {
                            Log.d(TAG, "从内存变量恢复悬浮窗位置: x=$floatingWindowX, y=$floatingWindowY")
                            floatingWindowHelper?.showFloatingWindowAtPosition(null, floatingWindowX, floatingWindowY)
                        } else {
                            // 尝试从SharedPreferences获取位置
                            val x = prefsHelper?.getFloatingWindowX()
                            val y = prefsHelper?.getFloatingWindowY()

                            if (x != null && y != null && (x != 0 || y != 0)) {
                                Log.d(TAG, "从SharedPreferences恢复悬浮窗位置: x=$x, y=$y")
                                floatingWindowHelper?.showFloatingWindowAtPosition(null, x, y)
                            } else {
                                // 如果没有保存的位置，则正常显示悬浮窗
                                Log.d(TAG, "没有保存的位置，正常显示悬浮窗")
                                floatingWindowHelper?.showFloatingWindow(null)
                            }
                        }

                        // 清除内存中保存的位置
                        floatingWindowX = null
                        floatingWindowY = null
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "移除截图操作界面失败", e)
                }
            } catch (e: Exception) {
                Log.e(TAG, "隐藏截图操作界面过程中出现异常", e)
            }
        } else {
            Log.d(TAG, "截图操作界面为空或未显示，无需隐藏")
        }
    }

    /**
     * 创建截图操作界面
     */
    private fun createScreenshotOverlay(onConfirm: () -> Unit, onDelete: () -> Unit) {
        try {
            if (overlayView != null) {
                // 如果界面已存在，先移除
                windowManager?.removeView(overlayView)
                overlayView = null
            }

            val inflater = LayoutInflater.from(context)

            // 创建全屏覆盖层
            val rootView = inflater.inflate(R.layout.screenshot_overlay_layout, null)

            // 创建一个包含返回按钮监听的容器
            val container = FrameLayout(context)
            container.addView(rootView)

            // 添加一个不可见的视图来处理返回按钮事件
            val backKeyListener = BackKeyListenerView(context)

            // 设置布局参数，确保视图能够接收事件
            val params = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            container.addView(backKeyListener, params)

            // 请求焦点以接收按键事件
            backKeyListener.requestFocus()

            Log.d(TAG, "添加了返回按钮监听器，hasFocus=${backKeyListener.hasFocus()}")

            overlayView = container

            // 设置布局参数 - 全屏
            overlayParams = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY

                // 设置窗口标志：
                // 1. FLAG_LAYOUT_IN_SCREEN - 窗口占满整个屏幕
                // 2. FLAG_NOT_TOUCH_MODAL - 允许窗口外的触摸事件传递到下层窗口
                // 3. 不设置 FLAG_NOT_FOCUSABLE - 使窗口可以获取焦点，从而接收按键事件
                flags = WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL

                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.CENTER
            }

            // 设置按钮点击事件
            setupButtonListeners(onConfirm, onDelete)

            // 添加覆盖层到窗口
            windowManager?.addView(overlayView, overlayParams)
            isOverlayShowing = true

            // 添加显示动画
            try {
                val animation = AnimationUtils.loadAnimation(context, R.anim.menu_fade_in)
                overlayView?.startAnimation(animation)
                Log.d(TAG, "应用动画到截图操作界面")
            } catch (e: Exception) {
                Log.e(TAG, "截图操作界面显示动画执行失败", e)
            }

            Log.d(TAG, "截图操作界面已创建并显示")
        } catch (e: Exception) {
            Log.e(TAG, "创建截图操作界面失败", e)
        }
    }

    /**
     * 设置按钮点击事件
     */
    private fun setupButtonListeners(onConfirm: () -> Unit, onDelete: () -> Unit) {
        // 获取原始布局视图（第一个子视图）
        val rootView = (overlayView as? FrameLayout)?.getChildAt(0)

        // 获取选择视图
        selectionView = rootView?.findViewById(R.id.selection_view)

        // 删除按钮
        rootView?.findViewById<ImageButton>(R.id.delete_button)?.setOnClickListener {
            Log.d(TAG, "点击了删除按钮")
            onDelete()
            hideScreenshotOverlay()
        }

        // 确认按钮
        rootView?.findViewById<ImageButton>(R.id.confirm_button)?.setOnClickListener {
            Log.d(TAG, "点击了确认按钮")
            // 获取选择区域
            val selectionRect = selectionView?.getSelectionRect() ?: RectF()
            Log.d(TAG, "选择区域: $selectionRect")

            // 获取截图并处理
            captureAndProcessScreenshot(selectionRect)

            onConfirm()
            hideScreenshotOverlay()
        }
    }

    /**
     * 获取截图并处理
     */
    private fun captureAndProcessScreenshot(selectionRect: RectF) {
        try {
            // 保存选区信息
            ScreenshotHelper.lastSelectionRect = selectionRect

            // 检查是否有已保存的权限结果
            val mediaProjectionSingleton = MediaProjectionSingleton.getInstance()
            if (mediaProjectionSingleton.hasScreenshotPermission()) {
                // 使用已保存的权限结果
                Log.d(TAG, "使用已保存的权限结果进行截图")

                // 直接启动截图服务
                ScreenshotService.startScreenshotServiceWithSavedPermission(context, selectionRect)
                return
            }

            // 如果没有有效的权限结果，则请求新的权限
            Toast.makeText(context, "请在通知栏中点击允许截图", Toast.LENGTH_LONG).show()

            // 创建一个新的透明Activity来请求截图权限
            val intent = Intent(context, ScreenshotPermissionActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)

            Log.d(TAG, "已启动截图权限请求Activity，等待用户授权")
        } catch (e: Exception) {
            Log.e(TAG, "请求截图权限失败", e)
            Toast.makeText(context, "请求截图权限失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }


    /**
     * 检查截图操作界面是否正在显示
     */
    fun isOverlayVisible(): Boolean {
        return isOverlayShowing
    }
}
