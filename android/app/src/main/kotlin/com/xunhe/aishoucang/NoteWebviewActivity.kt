package com.xunhe.aishoucang

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.webkit.WebView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.xunhe.aishoucang.lib.NoteWebviewHelper
import com.xunhe.aishoucang.lib.NoteDetail

/**
 * 笔记WebView Activity
 * 用于展示从API获取的HTML内容
 */
class NoteWebviewActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "NoteWebviewActivity"
    }
    
    private lateinit var webView: WebView
    private lateinit var noteWebviewHelper: NoteWebviewHelper

    private var noteId: String? = null
    private var apiUrl: String? = null

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置全屏模式，隐藏标题栏
        supportActionBar?.hide()

        setContentView(R.layout.activity_note_webview)

        // 获取传入的参数
        noteId = intent.getStringExtra("note_id")
        apiUrl = intent.getStringExtra("api_url")

        Log.d(TAG, "创建笔记WebView Activity, noteId: $noteId, apiUrl: $apiUrl")

        // 初始化视图
        initViews()

        // 初始化Helper
        noteWebviewHelper = NoteWebviewHelper.getInstance(this)

        // 加载内容
        loadContent()
    }
    
    private fun initViews() {
        webView = findViewById(R.id.webview)
    }
    
    private fun loadContent() {
        // 如果有API URL，则从API获取HTML内容
        if (!apiUrl.isNullOrEmpty()) {
            loadFromApi()
        } else {
            // 否则显示默认内容或错误信息
            loadDefaultContent()
        }
    }
    
    private fun loadFromApi() {
        Log.d(TAG, "从API加载笔记详情: $apiUrl")

        noteWebviewHelper.fetchNoteDetailFromApi(apiUrl!!) { noteDetail, error ->
            runOnUiThread {
                if (noteDetail != null) {
                    // 成功获取笔记详情，加载HTML内容

                    if (noteDetail.html.isNotEmpty()) {
                        noteWebviewHelper.loadHtmlInWebView(webView, noteDetail.html)
                        Log.d(TAG, "成功加载笔记HTML内容到WebView")
                    } else {
                        // HTML为空，显示其他内容信息
                        showNoteContent(noteDetail)
                        Log.d(TAG, "HTML内容为空，显示笔记基本信息")
                    }
                } else {
                    // 获取失败，显示错误信息
                    val errorMsg = error ?: "获取笔记详情失败"
                    showError(errorMsg)
                    Log.e(TAG, "获取笔记详情失败: $errorMsg")
                }
            }
        }
    }
    
    private fun loadDefaultContent() {
        Log.d(TAG, "加载默认内容")
        
        // 显示默认的HTML内容
        val defaultHtml = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>笔记详情</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        margin: 20px;
                        line-height: 1.6;
                        color: #333;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    .title {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 20px;
                        color: #1EB9EF;
                    }
                    .content {
                        font-size: 16px;
                        margin-bottom: 20px;
                    }
                    .info {
                        background-color: #f5f5f5;
                        padding: 15px;
                        border-radius: 8px;
                        font-size: 14px;
                        color: #666;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="title">笔记WebView</div>
                    <div class="content">
                        这是一个用于展示笔记内容的WebView页面。
                    </div>
                    <div class="info">
                        <p><strong>笔记ID:</strong> ${noteId ?: "未指定"}</p>
                        <p><strong>API地址:</strong> ${apiUrl ?: "未指定"}</p>
                        <p><strong>说明:</strong> 当前为默认内容，实际使用时会从API获取HTML内容进行展示。</p>
                    </div>
                </div>
            </body>
            </html>
        """.trimIndent()
        
        noteWebviewHelper.loadHtmlInWebView(webView, defaultHtml)
    }

    private fun showNoteContent(noteDetail: NoteDetail) {
        Log.d(TAG, "显示笔记基本内容")

        // 当HTML为空时，使用其他字段构建显示内容
        val contentHtml = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${noteDetail.title}</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        margin: 20px;
                        line-height: 1.6;
                        color: #333;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    .title {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 20px;
                        color: #1EB9EF;
                    }
                    .cover {
                        width: 100%;
                        max-width: 400px;
                        height: auto;
                        border-radius: 8px;
                        margin-bottom: 20px;
                    }
                    .desc {
                        font-size: 16px;
                        color: #666;
                        margin-bottom: 20px;
                        padding: 15px;
                        background-color: #f5f5f5;
                        border-radius: 8px;
                    }
                    .content {
                        font-size: 16px;
                        margin-bottom: 20px;
                        white-space: pre-wrap;
                    }
                    .meta {
                        font-size: 14px;
                        color: #999;
                        border-top: 1px solid #eee;
                        padding-top: 15px;
                        margin-top: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="title">${noteDetail.title}</div>
                    ${if (noteDetail.cover.isNotEmpty()) "<img class=\"cover\" src=\"${noteDetail.cover}\" alt=\"封面图片\" />" else ""}
                    ${if (noteDetail.desc.isNotEmpty()) "<div class=\"desc\">${noteDetail.desc}</div>" else ""}
                    ${if (noteDetail.content.isNotEmpty()) "<div class=\"content\">${noteDetail.content}</div>" else ""}
                    <div class="meta">
                        <p><strong>笔记ID:</strong> ${noteDetail.id}</p>
                        <p><strong>创建时间:</strong> ${noteDetail.createTime}</p>
                        <p><strong>更新时间:</strong> ${noteDetail.updateTime}</p>
                    </div>
                </div>
            </body>
            </html>
        """.trimIndent()

        noteWebviewHelper.loadHtmlInWebView(webView, contentHtml)
    }
    
    private fun showError(message: String) {
        val errorHtml = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>加载失败</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        margin: 20px;
                        text-align: center;
                        color: #333;
                    }
                    .error-container {
                        max-width: 400px;
                        margin: 50px auto;
                    }
                    .error-icon {
                        font-size: 48px;
                        color: #ff6b6b;
                        margin-bottom: 20px;
                    }
                    .error-title {
                        font-size: 20px;
                        font-weight: bold;
                        margin-bottom: 10px;
                        color: #ff6b6b;
                    }
                    .error-message {
                        font-size: 14px;
                        color: #666;
                        line-height: 1.5;
                    }
                </style>
            </head>
            <body>
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">加载失败</div>
                    <div class="error-message">$message</div>
                </div>
            </body>
            </html>
        """.trimIndent()
        
        noteWebviewHelper.loadHtmlInWebView(webView, errorHtml)
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // 直接关闭当前Activity，返回到笔记列表
        finish()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        try {
            webView.destroy()
        } catch (e: Exception) {
            Log.e(TAG, "销毁WebView时出错", e)
        }
    }
}
