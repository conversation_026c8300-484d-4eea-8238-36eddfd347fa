package com.xunhe.aishoucang.lib

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityManager
import android.view.accessibility.AccessibilityNodeInfo
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.ConcurrentHashMap

/**
 * 无障碍服务助手类
 * 负责对外提供无障碍服务的控制与操作接口
 */
class AccessibilityHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "AccessibilityHelper"

        /**
         * 获取实例（非单例，每次调用都创建新实例）
         */
        fun getInstance(context: Context): AccessibilityHelper = AccessibilityHelper(context.applicationContext)
    }

    /**
     * 封装日志输出方法
     */
    private fun logE(tag: String, msg: String, e: Throwable? = null) {
        if (e != null) Log.e(tag, msg, e) else Log.e(tag, msg)
    }

    fun getRootNode(): AccessibilityNodeInfo? {
        return AppAccessibilityService.getInstance()?.rootInActiveWindow
    }

    /**
     * 判断当前应用的无障碍服务是否启用
     */
    fun isAccessibilityServiceEnabled(): Boolean {
        val manager =
            context.getSystemService(Context.ACCESSIBILITY_SERVICE) as? AccessibilityManager
                ?: return false
        return manager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK)
            .any { it.id.contains(context.packageName) }
    }

    /**
     * 打开无障碍设置页面（直接跳转到系统无障碍设置总页面）
     */
    fun openAccessibilitySettings(context: Context) {
        // 直接跳转到系统无障碍设置总页面
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            Log.d("AccessibilityJump", "跳转到系统无障碍设置总页面")
            context.startActivity(intent)
            return
        } catch (e: Exception) {
            Log.e("AccessibilityJump", "跳转失败", e)
        }

    }

    /**
     * 根据 View ID 查找并点击元素
     */
    fun findAndClickElementById(elementId: String): Boolean {
        return AppAccessibilityService.getInstance()?.findAndClickElementById(elementId) ?: false
    }

    /**
     * 根据id判断元素是否存在
     */
    fun IsExistElementById(elementId: String): Boolean {
        val service = AppAccessibilityService.getInstance() ?: return false
        val rootNode = service.rootInActiveWindow ?: return false

        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                val nodes = rootNode.findAccessibilityNodeInfosByViewId(elementId)
                nodes != null && nodes.isNotEmpty()
            } else {
                // 兼容低版本Android，使用递归查找
                val resultList = mutableListOf<AccessibilityNodeInfo>()
                service.findNodesByViewIdRecursive(rootNode, elementId, resultList)
                resultList.isNotEmpty()
            }
        } catch (e: Exception) {
            Log.e(TAG, "判断元素是否存在时出错: $elementId", e)
            false
        }
    }

    /**
     * 根据描述文本查找节点
     */
    fun findNodesByDescription(description: String): List<AccessibilityNodeInfo>? {
        return AppAccessibilityService.getInstance()?.findNodesByDescription(description)
    }

    /**
     * 根据内容文本查找节点
     * @param content 要匹配的内容文本
     * @return 匹配的节点列表，如果没有匹配或出错则返回null
     */
    fun findByContent(content: String): List<AccessibilityNodeInfo>? {
        return AppAccessibilityService.getInstance()?.findNodesByContent(content)
    }

    /**
     * 获取当前前台 Activity 的包名/类名
     */
    fun getCurrentForegroundActivity(): String? =
        AppAccessibilityService.getCurrentForegroundActivity()

    /**
     * 刷新无障碍节点层次结构
     *
     * 在界面状态变化后调用此方法，确保获取到最新的界面状态
     * 例如：点击操作后、滚动操作后、等待新界面加载后等场景
     *
     * @return 刷新是否成功，如果无障碍服务未运行则返回false
     */
    fun refreshAccessibilityNodeHierarchy(): Boolean {
        return AppAccessibilityService.getInstance()?.refreshRootNode() ?: false
    }

    /**
     * 按坐标点击屏幕
     * @param x 横坐标
     * @param y 纵坐标
     * @param duration 点击持续时间(毫秒)，默认10ms
     * @param callback 可选的回调函数，通知手势完成或取消
     * @return 是否成功发送手势指令
     */
    fun clickAtCoordinate(
        x: Float,
        y: Float,
        duration: Long = 10,
        callback: AccessibilityService.GestureResultCallback? = null
    ): Boolean {
        val service = AppAccessibilityService.getInstance() ?: return false

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                // 创建点击路径
                val path = android.graphics.Path()
                path.moveTo(x, y)

                // 创建点击描述
                val clickStroke = android.accessibilityservice.GestureDescription.StrokeDescription(
                    path, 0, duration
                )

                // 创建手势并分发
                val gestureBuilder = android.accessibilityservice.GestureDescription.Builder()
                    .addStroke(clickStroke)
                    .build()

                // 发送手势
                service.dispatchGesture(
                    gestureBuilder,
                    callback ?: object : AccessibilityService.GestureResultCallback() {
                        override fun onCompleted(gestureDescription: android.accessibilityservice.GestureDescription) {
                            Log.d(TAG, "坐标点击(x=$x, y=$y)完成")
                        }

                        override fun onCancelled(gestureDescription: android.accessibilityservice.GestureDescription) {
                            Log.e(TAG, "坐标点击(x=$x, y=$y)取消")
                        }
                    },
                    null
                )
            } catch (e: Exception) {
                Log.e(TAG, "坐标点击失败", e)
                false
            }
        } else {
            Log.e(TAG, "设备Android版本过低(需要API 24+)，不支持坐标点击")
            false
        }
    }

    /**
     * 在屏幕上执行长按操作
     * @param x 横坐标
     * @param y 纵坐标
     * @param duration 长按持续时间(毫秒)，默认500ms
     * @param callback 可选的回调函数，通知手势完成或取消
     * @return 是否成功发送手势指令
     */
    fun longPressAtCoordinate(
        x: Float,
        y: Float,
        duration: Long = 500,
        callback: AccessibilityService.GestureResultCallback? = null
    ): Boolean {
        return clickAtCoordinate(x, y, duration, callback)
    }

    /**
     * 执行从一个点到另一个点的滑动操作
     * @param startX 起始点横坐标
     * @param startY 起始点纵坐标
     * @param endX 结束点横坐标
     * @param endY 结束点纵坐标
     * @param duration 滑动持续时间(毫秒)，默认300ms
     * @param callback 可选的回调函数，通知手势完成或取消
     * @return 是否成功发送手势指令
     */
    fun swipe(
        startX: Float,
        startY: Float,
        endX: Float,
        endY: Float,
        duration: Long = 300,
        callback: AccessibilityService.GestureResultCallback? = null
    ): Boolean {
        val service = AppAccessibilityService.getInstance() ?: return false

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                // 创建滑动路径
                val path = android.graphics.Path()
                path.moveTo(startX, startY)
                path.lineTo(endX, endY)

                // 创建滑动描述
                val swipeStroke = android.accessibilityservice.GestureDescription.StrokeDescription(
                    path, 0, duration
                )

                // 创建手势并分发
                val gestureBuilder = android.accessibilityservice.GestureDescription.Builder()
                    .addStroke(swipeStroke)
                    .build()

                // 发送手势
                service.dispatchGesture(
                    gestureBuilder,
                    callback ?: object : AccessibilityService.GestureResultCallback() {
                        override fun onCompleted(gestureDescription: android.accessibilityservice.GestureDescription) {
                            Log.d(TAG, "滑动操作完成")
                        }

                        override fun onCancelled(gestureDescription: android.accessibilityservice.GestureDescription) {
                            Log.e(TAG, "滑动操作取消")
                        }
                    },
                    null
                )
            } catch (e: Exception) {
                Log.e(TAG, "滑动操作失败", e)
                false
            }
        } else {
            Log.e(TAG, "设备Android版本过低(需要API 24+)，不支持滑动操作")
            false
        }
    }

    /**
     * 执行复杂的自定义手势
     * @param path 手势路径
     * @param duration 手势持续时间(毫秒)
     * @param callback 可选的回调函数，通知手势完成或取消
     * @return 是否成功发送手势指令
     */
    fun performGesture(
        path: android.graphics.Path,
        duration: Long = 500,
        callback: AccessibilityService.GestureResultCallback? = null
    ): Boolean {
        val service = AppAccessibilityService.getInstance() ?: return false

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                // 创建手势描述
                val gestureStroke = android.accessibilityservice.GestureDescription.StrokeDescription(
                    path, 0, duration
                )

                // 创建手势并分发
                val gestureBuilder = android.accessibilityservice.GestureDescription.Builder()
                    .addStroke(gestureStroke)
                    .build()

                // 发送手势
                service.dispatchGesture(
                    gestureBuilder,
                    callback ?: object : AccessibilityService.GestureResultCallback() {
                        override fun onCompleted(gestureDescription: android.accessibilityservice.GestureDescription) {
                            Log.d(TAG, "自定义手势完成")
                        }

                        override fun onCancelled(gestureDescription: android.accessibilityservice.GestureDescription) {
                            Log.e(TAG, "自定义手势取消")
                        }
                    },
                    null
                )
            } catch (e: Exception) {
                Log.e(TAG, "自定义手势失败", e)
                false
            }
        } else {
            Log.e(TAG, "设备Android版本过低(需要API 24+)，不支持自定义手势")
            false
        }
    }

    /**
     * 设置前台 Activity 变化监听器（兼容性方法）
     * 为了保持向后兼容性，自动生成一个默认的监听器ID
     * 注意：如果多次调用此方法，会覆盖之前的监听器
     */
    fun setOnForegroundActivityChangeListener(listener: ((String) -> Unit)?) {
        val defaultId = "default_foreground_activity_listener"
        if (listener != null) {
            AppAccessibilityService.addForegroundActivityChangeListener(defaultId, listener)
        } else {
            AppAccessibilityService.removeForegroundActivityChangeListener(defaultId)
        }
    }

    /**
     * 添加前台 Activity 变化监听器（推荐使用）
     * @param id 监听器的唯一标识符
     * @param listener 监听器回调函数
     */
    fun addForegroundActivityChangeListener(id: String, listener: (String) -> Unit) {
        AppAccessibilityService.addForegroundActivityChangeListener(id, listener)
    }

    /**
     * 移除前台 Activity 变化监听器
     * @param id 监听器的唯一标识符
     * @return 是否成功移除监听器
     */
    fun removeForegroundActivityChangeListener(id: String): Boolean {
        return AppAccessibilityService.removeForegroundActivityChangeListener(id)
    }

    /**
     * 元素点击结果监听器（已废弃，保留兼容性）
     */
    fun setOnElementActionResultListener(listener: ((Boolean, String) -> Unit)?) {
        // 兼容保留方法
    }

    /**
     * 无障碍服务实现类，系统回调接收无障碍事件
     */
    class AppAccessibilityService : AccessibilityService() {
        companion object {
            private const val TAG = "AppAccessibilityService"

            @Volatile
            private var instance: AppAccessibilityService? = null

            // 监听器列表：当前前台页面变化，使用 Map 存储 ID 和监听器的对应关系
            private val foregroundActivityChangeListeners = ConcurrentHashMap<String, (String) -> Unit>()

            private var currentForegroundActivity: String = ""

            fun getInstance(): AppAccessibilityService? = instance

            fun getCurrentForegroundActivity(): String = currentForegroundActivity

            /**
             * 添加前台Activity变化监听器
             * @param id 监听器的唯一标识符
             * @param listener 监听器回调函数
             */
            fun addForegroundActivityChangeListener(id: String, listener: (String) -> Unit) {
                foregroundActivityChangeListeners[id] = listener
                Log.d(TAG, "添加前台Activity变化监听器，ID: $id")
                if (currentForegroundActivity.isNotEmpty()) {
                    listener(currentForegroundActivity)
                }
            }

            /**
             * 根据ID移除前台Activity变化监听器
             * @param id 监听器的唯一标识符
             * @return 是否成功移除监听器
             */
            fun removeForegroundActivityChangeListener(id: String): Boolean {
                val removed = foregroundActivityChangeListeners.remove(id) != null
                Log.d(TAG, "移除前台Activity变化监听器，ID: $id, 结果: ${if (removed) "成功" else "失败"}")
                return removed
            }

            /**
             * 获取当前注册的监听器数量
             * @return 监听器数量
             */
            fun getForegroundActivityChangeListenerCount(): Int {
                return foregroundActivityChangeListeners.size
            }

            /**
             * 清除所有前台Activity变化监听器
             */
            fun clearAllForegroundActivityChangeListeners() {
                val count = foregroundActivityChangeListeners.size
                foregroundActivityChangeListeners.clear()
                Log.d(TAG, "清除所有前台Activity变化监听器，共清除 $count 个")
            }
        }

        /**
         * 系统回调：服务连接成功
         */
        override fun onServiceConnected() {
            super.onServiceConnected()
            Log.d(TAG, "无障碍服务已连接")
            instance = this
        }

        /**
         * 系统回调：接收无障碍事件
         */
        override fun onAccessibilityEvent(event: AccessibilityEvent) {
            if (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
                updateForegroundActivityIfChanged(
                    event.packageName?.toString(),
                    event.className?.toString()
                )
            }
        }

        /**
         * 检查并更新前台 Activity 状态，通知监听器
         */
        private fun updateForegroundActivityIfChanged(packageName: String?, className: String?) {
            if (packageName.isNullOrEmpty() || className.isNullOrEmpty()) return
            val newActivity = "$packageName/$className"
            if (newActivity != currentForegroundActivity) {
                currentForegroundActivity = newActivity
                Log.d(TAG, "前台Activity变化: $newActivity")
                Handler(Looper.getMainLooper()).post {
                    // 通知所有前台Activity变化监听器
                    foregroundActivityChangeListeners.forEach { (id, listener) ->
                        try {
                            listener(newActivity)
                        } catch (e: Exception) {
                            Log.e(TAG, "通知前台Activity变化监听器时出错，ID: $id", e)
                        }
                    }
                }
            }
        }

        /**
         * 系统回调：服务中断
         */
        override fun onInterrupt() {
            Log.d(TAG, "无障碍服务已中断")
        }

        /**
         * 系统回调：服务销毁
         */
        override fun onDestroy() {
            super.onDestroy()
            Log.d(TAG, "无障碍服务已销毁")
            instance = null
        }

        /**
         * 根据 View ID 查找元素并点击
         */
        fun findAndClickElementById(elementId: String): Boolean {
            val rootNode = rootInActiveWindow ?: return false
            val nodes = findNodesByViewId(rootNode, elementId)
            return nodes.firstOrNull()?.let { node ->
                val result = node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                node.recycle()
                result
            } ?: false
        }

        /**
         * 根据 View ID 查找节点
         */
        fun findNodesByViewId(
            rootNode: AccessibilityNodeInfo,
            viewId: String
        ): List<AccessibilityNodeInfo> {
            return try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                    rootNode.findAccessibilityNodeInfosByViewId(viewId) ?: emptyList()
                } else {
                    mutableListOf<AccessibilityNodeInfo>().apply {
                        findNodesByViewIdRecursive(rootNode, viewId, this)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "根据ID查找节点时出错", e)
                emptyList()
            }
        }

        /**
         * 递归查找节点（兼容低版本 Android）
         */
        fun findNodesByViewIdRecursive(
            node: AccessibilityNodeInfo,
            viewId: String,
            result: MutableList<AccessibilityNodeInfo>
        ) {
            if (node.viewIdResourceName == viewId) {
                result.add(AccessibilityNodeInfo.obtain(node))
            }
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    findNodesByViewIdRecursive(child, viewId, result)
                    child.recycle()
                }
            }
        }

        /**
         * 占位方法：设置元素点击监听（不支持）
         */
        fun setElementClickListener(elementId: String, listener: () -> Unit): Boolean {
            return false // 不支持直接监听点击
        }

        /**
         * 根据描述文本查找节点
         */
        fun findNodesByDescription(description: String): List<AccessibilityNodeInfo>? {
            val rootNode = rootInActiveWindow ?: return null
            return findNodesByDescriptionRecursive(rootNode, description, mutableListOf())
        }

        /**
         * 根据内容文本查找节点
         * @param content 要匹配的内容文本
         * @return 匹配的节点列表，如果没有匹配则返回空列表
         */
        fun findNodesByContent(content: String): List<AccessibilityNodeInfo>? {
            val rootNode = rootInActiveWindow ?: return null
            return findNodesByContentRecursive(rootNode, content, mutableListOf())
        }

        /**
         * 递归查找匹配内容的节点
         */
        private fun findNodesByContentRecursive(
            node: AccessibilityNodeInfo,
            content: String,
            result: MutableList<AccessibilityNodeInfo>
        ): List<AccessibilityNodeInfo> {
            try {
                // 检查当前节点的文本内容是否匹配
                if (node.text?.toString() == content) {
                    result.add(AccessibilityNodeInfo.obtain(node))
                }

                // 递归查找子节点
                for (i in 0 until node.childCount) {
                    node.getChild(i)?.let { child ->
                        findNodesByContentRecursive(child, content, result)
                        child.recycle()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "递归查找内容节点时出错", e)
            }

            return result
        }

        /**
         * 递归查找匹配描述的节点
         */
        private fun findNodesByDescriptionRecursive(
            node: AccessibilityNodeInfo,
            description: String,
            result: MutableList<AccessibilityNodeInfo>
        ): List<AccessibilityNodeInfo> {
            try {
                // 检查当前节点的描述是否匹配
                if (node.contentDescription?.toString() == description) {
                    result.add(AccessibilityNodeInfo.obtain(node))
                }

                // 递归查找子节点
                for (i in 0 until node.childCount) {
                    node.getChild(i)?.let { child ->
                        findNodesByDescriptionRecursive(child, description, result)
                        child.recycle()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "递归查找描述节点时出错", e)
            }

            return result
        }

        /**
         * 递归刷新节点及其所有子节点
         *
         * 该方法会递归调用每个节点的refresh()方法，确保获取到最新的节点状态
         * 在执行复杂的界面操作后调用此方法，可以确保后续操作基于最新的界面状态
         *
         * @param node 要刷新的节点，通常是rootInActiveWindow
         * @return 刷新是否成功
         */
        fun refreshChildList(node: AccessibilityNodeInfo?): Boolean {
            if (node == null) return false

            try {
                // 刷新当前节点
                val refreshSuccess = node.refresh()

                // 递归刷新所有子节点
                for (i in 0 until node.childCount) {
                    node.getChild(i)?.let { child ->
                        try {
                            refreshChildList(child)
                            child.recycle() // 回收子节点资源
                        } catch (e: Exception) {
                            Log.e(TAG, "刷新子节点时出错", e)
                        }
                    }
                }

                return refreshSuccess
            } catch (e: Exception) {
                Log.e(TAG, "刷新节点层次结构时出错", e)
                return false
            }
        }

        /**
         * 刷新当前活动窗口的根节点及其所有子节点
         *
         * 在界面状态变化后调用此方法，确保获取到最新的界面状态
         * 例如：点击操作后、滚动操作后、等待新界面加载后等场景
         *
         * @return 刷新是否成功
         */
        fun refreshRootNode(): Boolean {
            val rootNode = rootInActiveWindow ?: return false

            try {
                val result = refreshChildList(rootNode)
                Log.d(TAG, "刷新根节点${if (result) "成功" else "失败"}")
                return result
            } catch (e: Exception) {
                Log.e(TAG, "刷新根节点时出错", e)
                return false
            }
        }
    }
}
