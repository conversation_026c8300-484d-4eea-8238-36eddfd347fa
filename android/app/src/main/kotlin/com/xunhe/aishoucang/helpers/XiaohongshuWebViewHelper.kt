package com.xunhe.aishoucang.helpers

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.*
import java.net.URL
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import java.lang.ref.WeakReference

/**
 * 小红书WebView辅助工具
 * 用于加载小红书链接并提取头像和Scheme URL
 */
/**
 * 存储从小红书页面提取的图片信息
 */
data class XiaohongshuImageInfo(
    val noteTitle: String? = null,      // 笔记标题
    val noteDescription: String? = null, // 笔记描述
    val avatarImageUrl: String? = null, // 头像图片URL
    val noteImageUrls: List<String> = emptyList() // 笔记中的图片URL列表
)

object XiaohongshuWebViewHelper {
    private const val TAG = "XiaohongshuWebViewHelper"

    // 共享线程池，避免每次调用都创建新的线程池
    private val sharedExecutor = Executors.newSingleThreadExecutor()

    // 超时时间常量
    private const val TIMEOUT_MS = 15000L  // 15秒超时，减少原来的30秒

    // 轮询间隔和最大轮询次数
    private const val POLLING_INTERVAL_MS = 300L  // 300毫秒轮询一次，比原来的500毫秒更快
    private const val MAX_POLLING_COUNT = 15  // 最多轮询15次，总共4.5秒

    // WebView销毁延迟时间
    private const val WEBVIEW_DESTROY_DELAY_MS = 200L // 销毁WebView前的延迟时间

    /**
     * 安全地销毁WebView，最大程度减少销毁后的活动
     *
     * @param webView 要销毁的WebView
     * @param handler 主线程Handler
     * @param onDestroyed 销毁完成后的回调
     */
    private fun safeDestroyWebView(webView: WebView?, handler: Handler, onDestroyed: () -> Unit) {
        if (webView == null) {
            onDestroyed()
            return
        }

        // 使用弱引用避免内存泄漏
        val weakWebView = WeakReference(webView)

        handler.post {
            try {
                val view = weakWebView.get()
                if (view != null) {
                    // 1. 停止加载
                    view.stopLoading()

                    // 2. 暂停WebView活动
                    view.onPause()
                    view.pauseTimers()

                    // 3. 加载空白页面中断当前资源加载
                    view.loadUrl("about:blank")

                    // 4. 移除所有监听器，使用空实现替代null
                    view.setWebViewClient(object : WebViewClient() {})
                    view.setWebChromeClient(object : WebChromeClient() {})

                    // 5. 短暂延迟后再销毁
                    handler.postDelayed({
                        try {
                            val delayedView = weakWebView.get()
                            if (delayedView != null) {
                                delayedView.clearHistory()
                                delayedView.clearCache(true)
                                delayedView.clearFormData()
                                delayedView.clearSslPreferences()
                                delayedView.clearMatches()

                                // 从父视图中移除
                                val parent = delayedView.parent
                                if (parent != null) {
                                    try {
                                        (parent as android.view.ViewGroup).removeView(delayedView)
                                    } catch (e: Exception) {
                                        Log.w(TAG, "从父视图移除WebView时出错: ${e.message}")
                                    }
                                }

                                // 最后销毁
                                delayedView.destroy()
                                Log.d(TAG, "WebView已安全销毁")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "销毁WebView时出错: ${e.message}", e)
                        } finally {
                            // 确保回调一定会执行
                            onDestroyed()
                        }
                    }, WEBVIEW_DESTROY_DELAY_MS)
                } else {
                    // WebView已经被回收，直接执行回调
                    onDestroyed()
                }
            } catch (e: Exception) {
                Log.e(TAG, "准备销毁WebView时出错: ${e.message}", e)
                onDestroyed()
            }
        }
    }

    /**
     * 从JavaScript返回的结果中提取图片信息
     *
     * @param result JavaScript执行结果
     * @return 提取的图片信息对象，如果提取失败则返回null
     */
    private fun extractImagesFromJavaScriptResult(result: String?): XiaohongshuImageInfo? {
        if (result == null) return null

        try {
            // 处理返回的JSON字符串
            val processedResult = result.trim()

            // 如果结果以引号开始和结束，去除这些引号
            val jsonString = if (processedResult.startsWith("\"") && processedResult.endsWith("\"")) {
                // 需要处理JSON字符串中的转义字符
                processedResult.substring(1, processedResult.length - 1)
                    .replace("\\\"", "\"")
                    .replace("\\n", "\n")
                    .replace("\\r", "")
                    .replace("\\t", "  ")
                    .replace("\\\\", "\\")
            } else {
                processedResult
            }

            // 提取笔记标题
            val titlePattern = "\"noteTitle\":\"(.*?)\"".toRegex()
            val titleMatch = titlePattern.find(jsonString)
            val noteTitle = titleMatch?.groupValues?.get(1)

            // 提取笔记描述
            val descriptionPattern = "\"noteDescription\":\"(.*?)\"".toRegex()
            val descriptionMatch = descriptionPattern.find(jsonString)
            val noteDescription = descriptionMatch?.groupValues?.get(1)

            // 提取头像图片URL
            val avatarPattern = "\"avatarImage\":\"(.*?)\"".toRegex()
            val avatarMatch = avatarPattern.find(jsonString)
            val avatarImageUrl = avatarMatch?.groupValues?.get(1)

            // 提取笔记图片URL列表
            val noteImagesPattern = "\"noteImages\":\\[(.*?)\\]".toRegex()
            val noteImagesMatch = noteImagesPattern.find(jsonString)
            val noteImageUrls = if (noteImagesMatch != null) {
                val noteImagesJson = noteImagesMatch.groupValues[1]
                // 分割并清理图片URL
                noteImagesJson.split(",")
                    .map { it.trim().replace("\"", "") }
                    .filter { it.isNotEmpty() }
            } else {
                emptyList()
            }

            // 提取initialState并打印
            val initialStatePattern = "\"initialState\":(.*?),\"debug\"".toRegex(RegexOption.DOT_MATCHES_ALL)
            val initialStateMatch = initialStatePattern.find(jsonString)
            if (initialStateMatch != null) {
                val initialStateJson = initialStateMatch.groupValues[1].trim()
                Log.d(TAG, "提取到的__INITIAL_STATE__: $initialStateJson")
            } else {
                Log.w(TAG, "未能提取到__INITIAL_STATE__")
            }

            // 创建并返回图片信息对象
            return XiaohongshuImageInfo(
                noteTitle = noteTitle,
                noteDescription = noteDescription,
                avatarImageUrl = avatarImageUrl,
                noteImageUrls = noteImageUrls
            )
        } catch (e: Exception) {
            Log.w(TAG, "解析JavaScript返回结果时出错: ${e.message}")
            return null
        }
    }

    /**
     * 使用WebView加载URL并监控资源
     *
     * @param context 上下文
     * @param url 要加载的URL
     * @param callback 回调函数，返回提取的头像URL和scheme URL
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun loadUrlAndMonitorResources(
        context: Context,
        url: String,
        callback: (String?, String?, String?, String?, String?) -> Unit
    ) {
        // 使用原子布尔值跟踪是否已完成
        val isCompleted = AtomicBoolean(false)

        // 主线程Handler
        val mainHandler = Handler(Looper.getMainLooper())

        // 保存提取的信息
        var extractedAvatarUrl: String? = null
        var schemeURL: String? = null
        var noteImageUrl: String? = null
        var noteTitle: String? = null
        var noteDescription: String? = null

        // 轮询计时器
        var avatarCheckTimer: Runnable? = null

        // 记录是否已经开始轮询
        val isPolling = AtomicBoolean(false)

        // 记录开始时间
        val startTime = System.currentTimeMillis()

        // 已处理的资源URL集合，避免重复处理相同的URL
        val processedUrls = mutableSetOf<String>()

        // 超时任务引用，用于取消
        var timeoutRunnable: Runnable? = null

        // 在后台线程中执行
        sharedExecutor.execute {
            var webView: WebView? = null

            try {
                // 在主线程中创建和配置WebView
                mainHandler.post {
                    try {
                        Log.d(TAG, "创建WebView并加载URL: $url")

                        // 创建WebView
                        webView = WebView(context)

                        // 配置WebView
                        webView?.settings?.apply {
                            javaScriptEnabled = true
                            domStorageEnabled = true
                            loadsImagesAutomatically = true
                            userAgentString =
                                "Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36"
                        }

                        // 设置WebViewClient
                        webView?.webViewClient = object : WebViewClient() {
                            private var redirectCount = 0
                            private val MAX_REDIRECTS = 10
                            private var hasRecordedPageFinish = false

                            // 处理重定向
                            override fun shouldOverrideUrlLoading(
                                view: WebView,
                                request: WebResourceRequest
                            ): Boolean {
                                val scheme = request.url.scheme
                                val urlString = request.url.toString()

                                // 避免重复处理相同的URL
                                if (processedUrls.contains(urlString)) {
                                    return false
                                }
                                processedUrls.add(urlString)

                                // 拦截非HTTP开头的跳转
                                if (scheme == null || !scheme.startsWith("http")) {
                                    Log.w(TAG, "拦截非HTTP跳转: ${request.url}")
                                    val fullSchemeURL = request.url.toString()

                                    // 处理schemeURL格式，提取需要的部分
                                    schemeURL = try {
                                        // 去除URL中的查询参数（?及其后面的所有内容）
                                        val questionMarkIndex = fullSchemeURL.indexOf('?')
                                        val cleanSchemeURL = if (questionMarkIndex > 0) {
                                            fullSchemeURL.substring(0, questionMarkIndex)
                                        } else {
                                            fullSchemeURL
                                        }

                                        Log.d(TAG, "格式化后的schemeURL: $cleanSchemeURL")
                                        cleanSchemeURL
                                    } catch (e: Exception) {
                                        Log.e(TAG, "处理schemeURL时出错: ${e.message}")
                                        fullSchemeURL
                                    }

                                    // 如果已获取头像，直接完成
                                    if (extractedAvatarUrl != null) {
                                        Log.d(TAG, "获取到schemeURL，完成处理")
                                        checkAndComplete()
                                    }
                                    return true
                                }

                                Log.d(TAG, "允许重定向: ${request.url}")

                                // 增加重定向计数
                                redirectCount++

                                // 检查重定向次数是否超过限制
                                if (redirectCount > MAX_REDIRECTS) {
                                    Log.e(TAG, "重定向次数超过限制: $redirectCount")
                                    cleanupAndComplete()
                                    return true
                                }

                                // 允许重定向
                                return false
                            }

                            // 拦截资源请求
                            override fun shouldInterceptRequest(
                                view: WebView,
                                request: WebResourceRequest
                            ): WebResourceResponse? {
                                val requestUrl = request.url.toString()

                                // 避免重复处理相同的资源URL

                                try {
                                    // 监控资源URL
                                    Log.d(TAG, "资源加载: $requestUrl")
                                    // 检查是否是头像资源
                                    if (isAvatarResource(requestUrl) && extractedAvatarUrl == null) {
                                        Log.d(TAG, "检测到头像资源: $requestUrl")
                                        extractedAvatarUrl = requestUrl

                                        // 如果已获取schemeURL，直接完成
                                        if (schemeURL != null) {
                                            checkAndComplete()
                                        } else if (!isPolling.getAndSet(true)) {
                                            // 否则开始轮询检查schemeURL，使用常量定义的轮询次数和间隔
                                            Log.d(TAG, "已获取头像，开始轮询检查schemeURL...")
                                            var pollCount = 0

                                            avatarCheckTimer = object : Runnable {
                                                override fun run() {
                                                    // 已经完成或已获取schemeURL，停止轮询
                                                    if (isCompleted.get() || schemeURL != null) {
                                                        if (schemeURL != null && !isCompleted.get()) {
                                                            Log.d(TAG, "轮询中发现schemeURL，完成处理")
                                                            checkAndComplete()
                                                        }
                                                        return
                                                    }

                                                    pollCount++
                                                    if (pollCount >= MAX_POLLING_COUNT) {
                                                        // 达到最大轮询次数，使用当前结果完成
                                                        Log.d(TAG, "轮询结束，未获取到schemeURL，使用当前结果完成")
                                                        checkAndComplete()
                                                        return
                                                    }

                                                    // 继续轮询
                                                    Log.d(TAG, "轮询检查schemeURL: $pollCount/$MAX_POLLING_COUNT")
                                                    mainHandler.postDelayed(this, POLLING_INTERVAL_MS)
                                                }
                                            }

                                            // 开始第一次轮询
                                            mainHandler.post(avatarCheckTimer as Runnable)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.w(TAG, "处理资源请求时出错: ${e.message}")
                                }

                                // 直接返回原始响应，不进行HTML处理
                                return super.shouldInterceptRequest(view, request)
                            }

                            /**
                             * 判断是否是头像资源
                             */
                            private fun isAvatarResource(url: String): Boolean {
                                return try {
                                    val host = URL(url).host
                                    host.contains("avatar") && url.contains("xhscdn.com")
                                } catch (e: Exception) {
                                    false
                                }
                            }



                            override fun onPageFinished(view: WebView, loadedUrl: String) {
                                // 只记录一次页面完成事件，避免重复日志
                                if (!hasRecordedPageFinish) {
                                    Log.d(TAG, "页面加载完成: $loadedUrl")
                                    hasRecordedPageFinish = true

                                    // 捕获重定向后的HTML内容
                                    try {
                                        Log.d(TAG, "尝试获取重定向后的HTML内容: $loadedUrl")
                                        // 获取并打印完整的HTML内容
                                        view.evaluateJavascript(
                                            XiaohongshuJsExtractor.getHtmlExtractorScript(),
                                            { htmlResult ->
                                                if (htmlResult != null) {
                                                    try {
                                                        // 处理返回的HTML字符串（去除引号和转义字符）
                                                        val processedHtml = if (htmlResult.startsWith("\"") && htmlResult.endsWith("\"")) {
                                                            htmlResult.substring(1, htmlResult.length - 1)
                                                                .replace("\\\"", "\"")
                                                                .replace("\\n", "\n")
                                                                .replace("\\r", "")
                                                                .replace("\\t", "  ")
                                                                .replace("\\\\", "\\")
                                                        } else {
                                                            htmlResult
                                                        }

                                                        // 打印HTML内容（分段打印以避免日志截断）
                                                        Log.d(TAG, "页面HTML内容开始 =====================")
                                                        val chunkSize = 1000
                                                        var i = 0
                                                        while (i < processedHtml.length) {
                                                            val end = minOf(i + chunkSize, processedHtml.length)
                                                            Log.d(TAG, processedHtml.substring(i, end))
                                                            i = end
                                                        }
                                                        Log.d(TAG, "页面HTML内容结束 =====================")
                                                    } catch (e: Exception) {
                                                        Log.e(TAG, "处理HTML内容时出错: ${e.message}")
                                                    }
                                                }
                                            }
                                        )
                                        // 使用JavaScript获取页面中的图片资源
                                        view.evaluateJavascript(
                                            XiaohongshuJsExtractor.getExtractorScript(),
                                            { result ->
                                                if (result != null) {
                                                    try {
                                                        Log.d(TAG, "JSON信息:${result}")
                                                        // 使用封装方法提取图片信息
                                                        val imageInfo = extractImagesFromJavaScriptResult(result)

                                                        if (imageInfo != null) {
                                                            // 打印提取的图片信息
                                                            Log.d(TAG, "提取的图片信息:")
                                                            Log.d(TAG, "- 笔记标题: ${imageInfo.noteTitle ?: "未获取"}")
                                                            Log.d(TAG, "- 笔记描述: ${imageInfo.noteDescription ?: "未获取"}")
                                                            Log.d(TAG, "- 头像图片: ${imageInfo.avatarImageUrl ?: "未获取"}")

                                                            if (imageInfo.noteImageUrls.isNotEmpty()) {
                                                                Log.d(TAG, "- 笔记图片: 找到 ${imageInfo.noteImageUrls.size} 张图片")
                                                                imageInfo.noteImageUrls.forEachIndexed { index, url ->
                                                                    Log.d(TAG, "  图片 $index: $url")
                                                                }
                                                            } else {
                                                                Log.d(TAG, "- 笔记图片: 未找到")
                                                            }

                                                            // 更新提取的头像URL
                                                            if (imageInfo.avatarImageUrl != null && extractedAvatarUrl == null) {
                                                                extractedAvatarUrl = imageInfo.avatarImageUrl
                                                                Log.d(TAG, "从HTML中提取到头像URL: $extractedAvatarUrl")
                                                            }

                                                            // 保存笔记标题和描述
                                                            noteTitle = imageInfo.noteTitle
                                                            noteDescription = imageInfo.noteDescription

                                                            // 如果有笔记图片，使用第一张作为封面图片
                                                            if (imageInfo.noteImageUrls.isNotEmpty()) {
                                                                noteImageUrl = imageInfo.noteImageUrls[0]
                                                                Log.d(TAG, "使用第一张笔记图片作为封面图片: $noteImageUrl")
                                                            }

                                                            // 如果已获取schemeURL，直接完成
                                                            if (schemeURL != null) {
                                                                Log.d(TAG, "已获取schemeURL，完成处理")
                                                                checkAndComplete()
                                                            }
                                                        } else {
                                                            Log.d(TAG, "未能从JavaScript结果中提取图片信息")
                                                        }


                                                    } catch (e: Exception) {
                                                        Log.w(TAG, "解析JavaScript返回结果时出错: ${e.message}")
                                                    }
                                                }
                                            }
                                        )
                                    } catch (e: Exception) {
                                        Log.w(TAG, "获取重定向后的HTML内容时出错: ${e.message}")
                                    }
                                }
                            }

                            /**
                             * 检查是否可以完成提取，并回调结果
                             */
                            private fun checkAndComplete() {
                                if (!isCompleted.getAndSet(true)) {
                                    Log.d(TAG, "已获取到所需资源，准备完成处理")
                                    Log.d(TAG, "- 头像URL: ${extractedAvatarUrl ?: "未获取"}")
                                    Log.d(TAG, "- 自定义URL: ${schemeURL ?: "未获取"}")

                                    // 取消超时任务
                                    timeoutRunnable?.let { mainHandler.removeCallbacks(it) }
                                    timeoutRunnable = null

                                    cleanupAndComplete()
                                }
                            }

                            /**
                             * 清理资源并完成回调
                             */
                            private fun cleanupAndComplete() {
                                try {
                                    // 移除轮询检查器
                                    mainHandler.post {
                                        avatarCheckTimer?.let { mainHandler.removeCallbacks(it) }
                                        avatarCheckTimer = null
                                    }

                                    // 使用安全销毁WebView的方法
                                    safeDestroyWebView(webView, mainHandler) {
                                        webView = null
                                        // 确保回调一定会执行
                                        callback(extractedAvatarUrl, schemeURL, noteImageUrl, noteTitle, noteDescription)
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "清理WebView资源时出错: ${e.message}", e)
                                    // 确保回调一定会执行
                                    mainHandler.post {
                                        callback(extractedAvatarUrl, schemeURL, noteImageUrl, noteTitle, noteDescription)
                                    }
                                }
                            }
                        }

                        // 加载URL
                        webView?.loadUrl(url)
                    } catch (e: Exception) {
                        Log.e(TAG, "WebView创建或配置失败", e)
                        callback(null, null, null, null, null)
                    }
                }

                // 设置超时
                timeoutRunnable = Runnable {
                    if (!isCompleted.getAndSet(true)) {
                        Log.e(TAG, "加载超时，耗时: ${System.currentTimeMillis() - startTime}ms")

                        try {
                            // 移除轮询检查器
                            mainHandler.post {
                                avatarCheckTimer?.let { mainHandler.removeCallbacks(it) }
                                avatarCheckTimer = null
                            }

                            // 使用安全销毁WebView的方法
                            safeDestroyWebView(webView, mainHandler) {
                                webView = null
                                // 确保回调一定会执行
                                callback(extractedAvatarUrl, schemeURL, noteImageUrl, noteTitle, noteDescription)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "超时处理：清理WebView资源时出错: ${e.message}", e)
                            // 确保回调一定会执行
                            mainHandler.post {
                                callback(extractedAvatarUrl, schemeURL, noteImageUrl, noteTitle, noteDescription)
                            }
                        }
                    }
                }

                // 设置超时任务
                mainHandler.postDelayed(timeoutRunnable!!, TIMEOUT_MS)

            } catch (e: Exception) {
                Log.e(TAG, "处理过程中出错", e)
                if (!isCompleted.getAndSet(true)) {
                    // 取消超时任务
                    timeoutRunnable?.let { mainHandler.removeCallbacks(it) }
                    timeoutRunnable = null

                    try {
                        // 移除轮询检查器
                        mainHandler.post {
                            avatarCheckTimer?.let { mainHandler.removeCallbacks(it) }
                            avatarCheckTimer = null
                        }

                        // 使用安全销毁WebView的方法
                        safeDestroyWebView(webView, mainHandler) {
                            webView = null
                            // 确保回调一定会执行
                            callback(extractedAvatarUrl, schemeURL, noteImageUrl, noteTitle, noteDescription)
                        }
                    } catch (e2: Exception) {
                        Log.e(TAG, "异常处理：清理WebView资源时出错: ${e2.message}", e2)
                        // 确保回调一定会执行
                        mainHandler.post {
                            callback(extractedAvatarUrl, schemeURL, noteImageUrl, noteTitle, noteDescription)
                        }
                    }
                }
            }
            // 注意：不需要关闭共享线程池，它会在整个应用生命周期内复用
        }
    }
}