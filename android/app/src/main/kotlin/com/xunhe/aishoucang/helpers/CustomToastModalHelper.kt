package com.xunhe.aishoucang.helpers

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.TextView
import com.xunhe.aishoucang.R

/**
 * 自定义Toast Modal辅助类
 * 使用悬浮窗来显示确认对话框，支持自定义动画和回调
 */
object CustomToastModalHelper {
    private const val TAG = "CustomToastModalHelper"

    // Modal视图
    private var modalView: View? = null

    // Modal内部容器（实际显示内容的容器）
    private var modalInnerContainer: View? = null

    // 窗口参数
    private var params: WindowManager.LayoutParams? = null

    // 窗口管理器
    private var windowManager: WindowManager? = null

    // 是否正在显示
    private var isShowing = false

    // 用于延迟关闭的Handler
    private val handler = Handler(Looper.getMainLooper())

    // 移除视图的Runnable
    private val removeViewRunnable = Runnable {
        Log.d(TAG, "动画延时结束，执行移除视图")
        hideModalImmediately()
    }

    // 回调接口
    interface ModalCallback {
        fun onConfirm()
        fun onCancel()
    }

    /**
     * 显示Modal
     * @param context 上下文
     * @param title 标题
     * @param content 内容
     * @param cancelText 取消按钮文本
     * @param confirmText 确认按钮文本
     * @param callback 回调接口
     */
    fun showModal(
        context: Context,
        title: String = "确认删除",
        content: String = "确认要删除这个收藏吗？",
        cancelText: String = "取消",
        confirmText: String = "确定",
        callback: ModalCallback? = null
    ) {
        try {
            // 如果已经在显示，先移除旧的Modal
            if (isShowing) {
                Log.d(TAG, "已有Modal在显示，先移除")
                hideModalImmediately()
            }

            // 移除所有可能的回调
            handler.removeCallbacks(removeViewRunnable)

            // 获取窗口管理器
            if (windowManager == null) {
                windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            }

            // 创建Modal视图
            createModalView(context, title, content, cancelText, confirmText, callback)

            // 添加到窗口
            windowManager?.addView(modalView, params)
            isShowing = true
            Log.d(TAG, "添加Modal到窗口成功，isShowing = $isShowing")

            // 开始显示动画 - 应用到内部容器
            val animation = AnimationUtils.loadAnimation(context, R.anim.modal_in)
            modalInnerContainer?.startAnimation(animation)

        } catch (e: Exception) {
            Log.e(TAG, "显示Modal时出错", e)
        }
    }

    /**
     * 创建Modal视图
     */
    private fun createModalView(
        context: Context,
        title: String,
        content: String,
        cancelText: String,
        confirmText: String,
        callback: ModalCallback?
    ) {
        // 创建视图
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        modalView = inflater.inflate(R.layout.toast_modal_layout, null)

        // 获取内部容器引用
        modalInnerContainer = modalView?.findViewById(R.id.modal_container)

        // 设置标题和内容
        modalView?.findViewById<TextView>(R.id.modal_title)?.text = title
        modalView?.findViewById<TextView>(R.id.modal_content)?.text = content

        // 设置按钮文本
        modalView?.findViewById<TextView>(R.id.btn_cancel)?.text = cancelText
        modalView?.findViewById<TextView>(R.id.btn_confirm)?.text = confirmText

        // 设置取消按钮点击事件
        modalView?.findViewById<TextView>(R.id.btn_cancel)?.setOnClickListener {
            Log.d(TAG, "取消按钮被点击")
            callback?.onCancel()
            hideModalWithAnimation()
        }

        // 设置确认按钮点击事件
        modalView?.findViewById<TextView>(R.id.btn_confirm)?.setOnClickListener {
            Log.d(TAG, "确认按钮被点击")
            callback?.onConfirm()
            hideModalWithAnimation()
        }

        // 点击背景关闭Modal
        modalView?.setOnClickListener {
            Log.d(TAG, "背景被点击，执行关闭")
            callback?.onCancel()
            hideModalWithAnimation()
        }

        // 阻止点击Modal内容区域时关闭
        modalInnerContainer?.setOnClickListener {
            // 空实现，阻止事件冒泡
        }

        // 获取屏幕尺寸
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels

        // 设置窗口参数
        params = WindowManager.LayoutParams().apply {
            type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else WindowManager.LayoutParams.TYPE_PHONE
            format = PixelFormat.TRANSLUCENT
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
            width = screenWidth
            height = screenHeight
        }
    }

    /**
     * 使用动画隐藏Modal
     */
    private fun hideModalWithAnimation() {
        if (!isShowing || modalView == null) {
            Log.d(TAG, "hideModalWithAnimation: Modal不在显示或视图为空")
            return
        }

        try {
            Log.d(TAG, "开始执行Modal退出动画")
            val context = modalView?.context
            if (context != null) {
                // 播放退出动画 - 应用到内部容器
                val animation = AnimationUtils.loadAnimation(context, R.anim.modal_out)
                animation.setAnimationListener(object : Animation.AnimationListener {
                    override fun onAnimationStart(animation: Animation?) {
                        // 动画开始时不做任何操作
                    }

                    override fun onAnimationEnd(animation: Animation?) {
                        // 动画结束时立即移除视图
                        Log.d(TAG, "动画结束，立即移除视图")
                        hideModalImmediately()
                    }

                    override fun onAnimationRepeat(animation: Animation?) {
                        // 动画重复时不做任何操作
                    }
                })
                modalInnerContainer?.startAnimation(animation)
            } else {
                // 如果无法获取context，直接移除视图
                Log.d(TAG, "无法获取context，直接移除视图")
                hideModalImmediately()
            }
        } catch (e: Exception) {
            Log.e(TAG, "执行Modal退出动画时出错", e)
            // 出错时直接移除视图
            hideModalImmediately()
        }
    }

    /**
     * 立即隐藏Modal（无动画）
     */
    private fun hideModalImmediately() {
        Log.d(TAG, "开始执行hideModalImmediately")
        try {
            // 移除所有可能的回调
            handler.removeCallbacks(removeViewRunnable)

            if (modalView != null && modalView?.parent != null) {
                windowManager?.removeView(modalView)
                Log.d(TAG, "Modal视图成功从窗口移除")
            } else {
                Log.d(TAG, "Modal视图为空或没有父视图，无需移除")
            }
        } catch (e: Exception) {
            Log.e(TAG, "移除Modal视图时出错", e)
        } finally {
            modalView = null
            modalInnerContainer = null
            isShowing = false
            Log.d(TAG, "Modal变量已重置，isShowing = $isShowing")
        }
    }

    /**
     * 便捷方法：显示删除确认Modal
     * @param context 上下文
     * @param content 内容文本
     * @param callback 回调接口
     */
    fun showDeleteConfirmModal(
        context: Context,
        content: String = "确认要删除这个收藏吗？",
        callback: ModalCallback? = null
    ) {
        showModal(
            context = context,
            title = "确认删除",
            content = content,
            cancelText = "取消",
            confirmText = "确定",
            callback = callback
        )
    }

    /**
     * 检查Modal是否正在显示
     */
    fun isModalShowing(): Boolean {
        return isShowing
    }

    /**
     * 强制关闭Modal
     */
    fun forceHideModal() {
        if (isShowing) {
            hideModalImmediately()
        }
    }
}
