package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.api.BookMark
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import org.json.JSONObject

/**
 * 拼多多应用收藏项处理器
 */
object PinDuoDuoAppItemHandler {
    private const val TAG = "PinDuoDuoAppItemHandler"

    /**
     * 处理拼多多应用的收藏项
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     */
    fun handle(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "处理拼多多应用收藏项:")
        Log.d(TAG, "- 应用包名: $appPackage")
        Log.d(TAG, "- 剪贴板内容: $clipboardContent")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        // 获取从AppHandler传递过来的商品标题
        val productTitle: String? = SharePanelHelper.getTempData("pinduoduo_product_title")
        Log.d(TAG, "- 商品标题: $productTitle")

        // 清理临时数据
        SharePanelHelper.removeTempData("pinduoduo_product_title")

        // 检查必要参数
        if (clipboardContent.isNullOrBlank()) {
            Log.e(TAG, "剪贴板内容为空，无法处理")
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "没有获取到分享链接，无法收藏")
            return
        }

        if (favoriteItem?.id.isNullOrBlank()) {
            Log.e(TAG, "收藏夹ID为空，无法处理")
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "收藏夹信息异常，无法收藏")
            return
        }

        // 使用WebView加载剪切板内容并执行JS文件拦截scheme URL
        Log.d(TAG, "开始使用WebView加载剪切板内容并执行PinDuoDuoGoods.js")

        WebViewHtmlExtractor.executeBusinessJavaScript(
            context,
            clipboardContent,
            "PinDuoDuoGoods", // 对应assets/js/PinDuoDuoGoods.js文件
            emptyMap() // 不需要替换参数
        ) { result, error ->
            handleJavaScriptResult(context, result, error, productTitle, favoriteItem)
        }
    }

    /**
     * 处理JavaScript执行结果
     *
     * @param context 上下文
     * @param result JavaScript执行结果
     * @param error 错误信息
     * @param productTitle 商品标题
     * @param favoriteItem 收藏夹项
     */
    private fun handleJavaScriptResult(
        context: Context,
        result: String?,
        error: String?,
        productTitle: String?,
        favoriteItem: SharePanelItem?
    ) {
        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

        if (error != null) {
            Log.e(TAG, "PinDuoDuoGoods.js执行失败: $error")
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "获取拼多多商品信息失败，请重试")
            return
        }

        if (result != null) {
            try {
                Log.d(TAG, "PinDuoDuoGoods.js执行成功，返回结果: $result")

                // 解析JSON结果
                val jsonObject = JSONObject(result)

                // 检查是否有错误
                if (jsonObject.has("error")) {
                    val errorMessage = jsonObject.getString("error")
                    Log.e(TAG, "JavaScript返回错误: $errorMessage")
                    floatingWindowHelper.hideLoading()
                    CustomToastHelper.showToast(context, "处理拼多多内容时出错，请重试")
                    return
                }

                // 获取scheme URL
                var schemeUrl: String? = null
                if (jsonObject.has("schemeURL")) {
                    schemeUrl = jsonObject.getString("schemeURL")
                    Log.d(TAG, "获取到scheme URL: $schemeUrl")
                }

                // 检查是否有拦截到的scheme URL
                if (result.contains("InterceptedSchemeUrl:")) {
                    val interceptedUrl = result.substringAfter("InterceptedSchemeUrl:").trim()
                    if (interceptedUrl.isNotBlank()) {
                        schemeUrl = interceptedUrl
                        Log.d(TAG, "使用拦截到的scheme URL: $schemeUrl")
                    }
                }

                if (schemeUrl.isNullOrBlank()) {
                    Log.e(TAG, "未能获取到有效的scheme URL")
                    floatingWindowHelper.hideLoading()
                    CustomToastHelper.showToast(context, "无法获取拼多多商品链接，请重试")
                    return
                }

                // 直接保存书签
                saveBookmark(context, productTitle, schemeUrl, favoriteItem)

            } catch (e: Exception) {
                Log.e(TAG, "解析PinDuoDuoGoods.js结果失败: ${e.message}", e)
                floatingWindowHelper.hideLoading()
                CustomToastHelper.showToast(context, "获取拼多多商品信息失败，请重试")
            }
        } else {
            Log.e(TAG, "PinDuoDuoGoods.js执行结果为空")
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "获取拼多多商品信息失败，请重试")
        }
    }

    /**
     * 保存书签
     *
     * @param context 上下文
     * @param productTitle 商品标题
     * @param schemeUrl scheme URL
     * @param favoriteItem 收藏夹项
     */
    private fun saveBookmark(
        context: Context,
        productTitle: String?,
        schemeUrl: String,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "开始保存拼多多书签")
        Log.d(TAG, "- 商品标题: $productTitle")
        Log.d(TAG, "- scheme URL: $schemeUrl")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

        // 获取平台类型
        val platformType = SharePanelHelper.getCurrentPlatformType(schemeUrl)

        // 调用BookMark API保存书签
        BookMark.addBookMark(
            context = context,
            influencer_name = "拼多多商品", // 作者名称设为空字符串
            influencer_avatar = "", // 作者头像设为空字符串
            cover = "", // 封面设为空字符串
            title = productTitle ?: "", // 使用商品标题，如果为空则设为空字符串
            desc = "", // 描述设为空字符串
            parent_id = favoriteItem?.id ?: "",
            scheme_url = schemeUrl,
            platform_type = platformType
        ) { success, errorMessage ->
            // 隐藏加载动画
            floatingWindowHelper.hideLoading()

            if (success) {
                CustomToastHelper.showToast(context, "主人，收藏成功啦~")
                Log.d(TAG, "拼多多书签保存成功")
            } else {
                CustomToastHelper.showToast(context, "哎呀，收藏失败~请稍后再试")
            }
        }
    }
}
