package com.xunhe.aishoucang.helpers

import android.util.Log

/**
 * 小红书JavaScript提取器
 * 封装了从小红书页面提取数据的JavaScript代码
 */
object XiaohongshuJsExtractor {
    private const val TAG = "XiaohongshuJsExtractor"

    /**
     * 获取提取小红书页面数据的JavaScript代码
     * 包括标题、描述、头像和图片等信息
     *
     * @return 用于提取数据的JavaScript代码
     */
    fun getExtractorScript(): String {
        return """
        (function() {
            // 使用.title选择器获取标题
            var noteTitle = "";
            var noteDescription = "";
            var titleElement = document.querySelector('.title');

            // 尝试从__INITIAL_STATE__获取标题
            try {
                if (window.__INITIAL_STATE__ && 
                    window.__INITIAL_STATE__.noteData && 
                    window.__INITIAL_STATE__.noteData.data && 
                    window.__INITIAL_STATE__.noteData.data.noteData && 
                    window.__INITIAL_STATE__.noteData.data.noteData.title) {
                    noteTitle = window.__INITIAL_STATE__.noteData.data.noteData.title;
                }
            } catch(e) {
                console.log("从__INITIAL_STATE__获取标题失败: " + e.message);
            }
            
            // 如果从__INITIAL_STATE__获取失败，尝试从DOM获取
            if (!noteTitle && titleElement) {
                noteTitle = titleElement.textContent.trim();
            }

            // 如果没有找到标题，尝试从.author-desc获取
            if (!noteTitle) {
                var authorDescElement = document.querySelector('.author-desc');
                if (authorDescElement) {
                    // 获取纯中文部分作为标题
                    var chineseText = "";
                    var textContent = authorDescElement.textContent || "";
                    var chineseChars = textContent.match(/[\u4e00-\u9fa5]+/g);
                    if (chineseChars && chineseChars.length > 0) {
                        chineseText = chineseChars.join('');
                        if (chineseText) {
                            noteTitle = chineseText;
                        }
                    }

                    // 获取所有span标签内容作为描述
                    var spanElements = authorDescElement.querySelectorAll('span');
                    var combinedText = "";

                    if (spanElements && spanElements.length > 0) {
                        for (var i = 0; i < spanElements.length; i++) {
                            combinedText += spanElements[i].textContent || "";
                        }

                        // 如果有内容，设置为描述
                        if (combinedText) {
                            // 匹配所有中文字符
                            var descChineseChars = combinedText.match(/[\u4e00-\u9fa5]/g);
                            if (descChineseChars) {
                                // 取前100个中文字符
                                if (descChineseChars.length > 100) {
                                    noteDescription = descChineseChars.slice(0, 100).join('') + '...';
                                } else {
                                    noteDescription = descChineseChars.join('');
                                }
                            }
                        }
                    }
                }
            }

            // 如果还没有找到描述，尝试从.desc获取
            if (!noteDescription) {
                var descElement = document.querySelector('.desc');
                if (descElement) {
                    // 获取所有span标签
                    var spanElements = descElement.querySelectorAll('span');
                    var combinedText = "";

                    // 拼接所有span标签的内容
                    if (spanElements && spanElements.length > 0) {
                        for (var i = 0; i < spanElements.length; i++) {
                            combinedText += spanElements[i].textContent || "";
                        }
                    } else {
                        // 如果没有span标签，则使用desc元素的文本内容
                        combinedText = descElement.textContent || "";
                    }

                    // 匹配所有中文字符
                    var chineseChars = combinedText.match(/[\u4e00-\u9fa5]/g);

                    if (chineseChars) {
                        // 取前100个中文字符
                        if (chineseChars.length > 100) {
                            noteDescription = chineseChars.slice(0, 100).join('') + '...';
                        } else {
                            noteDescription = chineseChars.join('');
                        }
                    }
                }
            }

            // 尝试从__INITIAL_STATE__获取描述
            if (!noteDescription) {
                try {
                    if (window.__INITIAL_STATE__ && 
                        window.__INITIAL_STATE__.noteData && 
                        window.__INITIAL_STATE__.noteData.data && 
                        window.__INITIAL_STATE__.noteData.data.noteData && 
                        window.__INITIAL_STATE__.noteData.data.noteData.desc) {
                        
                        var stateDesc = window.__INITIAL_STATE__.noteData.data.noteData.desc;
                        
                        // 匹配所有中文字符
                        var chineseChars = stateDesc.match(/[\u4e00-\u9fa5]/g);
                        
                        if (chineseChars) {
                            // 取前100个中文字符
                            if (chineseChars.length > 100) {
                                noteDescription = chineseChars.slice(0, 100).join('') + '...';
                            } else {
                                noteDescription = chineseChars.join('');
                            }
                        } else {
                            // 如果没有中文字符，直接使用原始描述
                            noteDescription = stateDesc;
                        }
                    }
                } catch(e) {
                    console.log("从__INITIAL_STATE__获取描述失败: " + e.message);
                }
            }

            // 获取class中包含author-avatar的节点下的img标签的src属性作为头像
            var avatarImage = "";
            var debugInfo = {
                foundElements: [],
                method: ""
            };

            // 直接使用querySelector查找.author-avatar下的img元素
            var avatarImgElement = document.querySelector('.author-avatar img');
            if (avatarImgElement) {
                avatarImage = avatarImgElement.getAttribute('src');
                debugInfo.method = "querySelector-direct";

                // 记录找到的元素信息
                var parentElement = avatarImgElement.parentElement;
                if (parentElement) {
                    debugInfo.foundElements.push({
                        tagName: parentElement.tagName,
                        className: parentElement.className
                    });
                }
            }

            // 如果没有找到头像，尝试从其他地方获取
            if (!avatarImage) {
                // 尝试使用querySelector查找包含author-avatar的元素
                var avatarElem = document.querySelector('[class*="author-avatar"] img');
                if (avatarElem) {
                    avatarImage = avatarElem.getAttribute('src');
                    debugInfo.method = "querySelector-author-avatar";
                }
            }

            // 如果仍然没有找到，尝试其他可能的头像位置
            if (!avatarImage) {
                var avatarElem = document.querySelector('.avatar img');
                if (avatarElem) {
                    avatarImage = avatarElem.getAttribute('src');
                    debugInfo.method = "querySelector-avatar";
                }
            }

            // 尝试从__INITIAL_STATE__获取头像
            if (!avatarImage) {
                try {
                    if (window.__INITIAL_STATE__ && 
                        window.__INITIAL_STATE__.noteData && 
                        window.__INITIAL_STATE__.noteData.data && 
                        window.__INITIAL_STATE__.noteData.data.noteData && 
                        window.__INITIAL_STATE__.noteData.data.noteData.user && 
                        window.__INITIAL_STATE__.noteData.data.noteData.user.avatar) {
                        
                        avatarImage = window.__INITIAL_STATE__.noteData.data.noteData.user.avatar;
                        debugInfo.method = "initial-state-user-avatar";
                    }
                } catch(e) {
                    console.log("从__INITIAL_STATE__获取头像失败: " + e.message);
                }
            }

            // 记录所有包含avatar的元素（用于调试）
            var avatarElements = document.querySelectorAll('[class*="avatar"]');
            if (avatarElements.length > 0) {
                for (var i = 0; i < Math.min(avatarElements.length, 5); i++) {
                    debugInfo.foundElements.push({
                        tagName: avatarElements[i].tagName,
                        className: avatarElements[i].className,
                        hasImg: avatarElements[i].getElementsByTagName('img').length > 0
                    });
                }
            }

            // 获取所有笔记图片
            var noteImages = [];

            // 首先尝试从DOM获取图片
            var noteImageBoxes = document.getElementsByClassName('note-image-box');
            if (noteImageBoxes && noteImageBoxes.length > 0) {
                for (var i = 0; i < noteImageBoxes.length; i++) {
                    var imgElements = noteImageBoxes[i].getElementsByTagName('img');
                    for (var j = 0; j < imgElements.length; j++) {
                        var src = imgElements[j].getAttribute('src');
                        if (src) {
                            noteImages.push(src);
                        }
                    }
                }
            }

            // 如果从DOM没有获取到图片，尝试从__INITIAL_STATE__获取
            if (noteImages.length === 0) {
                try {
                    if (window.__INITIAL_STATE__ && 
                        window.__INITIAL_STATE__.noteData && 
                        window.__INITIAL_STATE__.noteData.data && 
                        window.__INITIAL_STATE__.noteData.data.noteData && 
                        window.__INITIAL_STATE__.noteData.data.noteData.imageList && 
                        window.__INITIAL_STATE__.noteData.data.noteData.imageList.length > 0) {
                        
                        var imageList = window.__INITIAL_STATE__.noteData.data.noteData.imageList;
                        
                        // 遍历imageList，提取url属性
                        for (var i = 0; i < imageList.length; i++) {
                            if (imageList[i] && imageList[i].url) {
                                noteImages.push(imageList[i].url);
                            }
                        }
                        
                        if (noteImages.length > 0) {
                            console.log("从__INITIAL_STATE__.noteData.data.noteData.imageList获取到" + noteImages.length + "张图片");
                        }
                    }
                } catch(e) {
                    console.log("从__INITIAL_STATE__获取图片列表失败: " + e.message);
                }
            }

            // 获取window.__INITIAL_STATE__
            var initialState = null;
            try {
                initialState = window.__INITIAL_STATE__;
            } catch(e) {
                initialState = { error: "无法获取__INITIAL_STATE__: " + e.message };
            }

            // 返回结果对象
            return JSON.stringify({
                noteTitle: noteTitle,
                noteDescription: noteDescription,
                avatarImage: avatarImage,
                noteImages: noteImages,
                initialState: initialState,
                debug: debugInfo
            });
        })();
        """
    }

    /**
     * 获取提取HTML内容的JavaScript代码
     *
     * @return 用于提取HTML内容的JavaScript代码
     */
    fun getHtmlExtractorScript(): String {
        return "(function() { return document.documentElement.outerHTML; })();"
    }
}
