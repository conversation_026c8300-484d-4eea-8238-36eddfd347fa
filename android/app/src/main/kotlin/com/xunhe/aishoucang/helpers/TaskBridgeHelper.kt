package com.xunhe.aishoucang.helpers

import android.util.Log
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * 任务桥接助手类
 * 负责Android端与Flutter端的任务API通信
 */
class TaskBridgeHelper private constructor() {
    
    companion object {
        private const val TAG = "TaskBridgeHelper"
        private const val TASK_BRIDGE_CHANNEL = "com.xunhe.aishoucang/task_bridge"
        
        @Volatile
        private var INSTANCE: TaskBridgeHelper? = null
        
        fun getInstance(): TaskBridgeHelper {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TaskBridgeHelper().also { INSTANCE = it }
            }
        }
    }
    
    private var methodChannel: MethodChannel? = null
    private var isInitialized = false
    
    /**
     * 初始化方法通道
     */
    fun initialize(flutterEngine: FlutterEngine) {
        try {
            methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, TASK_BRIDGE_CHANNEL)
            isInitialized = true
            Log.d(TAG, "TaskBridgeHelper 初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "TaskBridgeHelper 初始化失败: ${e.message}")
            isInitialized = false
        }
    }
    
    /**
     * 检查是否已初始化
     */
    fun isReady(): Boolean {
        return isInitialized && methodChannel != null
    }
    
    /**
     * 获取任务列表
     */
    suspend fun getTaskList(
        page: Int = 1,
        pageSize: Int = 10,
        taskType: Int? = null,
        platform: String? = null,
        status: Int? = null
    ): Map<String, Any> {
        if (!isReady()) {
            throw IllegalStateException("TaskBridgeHelper 未初始化")
        }
        
        return suspendCancellableCoroutine { continuation ->
            val arguments = mutableMapOf<String, Any>(
                "page" to page,
                "pageSize" to pageSize
            )
            
            taskType?.let { arguments["taskType"] = it }
            platform?.let { arguments["platform"] = it }
            status?.let { arguments["status"] = it }
            
            Log.d(TAG, "调用Flutter端获取任务列表: $arguments")
            
            methodChannel?.invokeMethod("getTaskList", arguments, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    try {
                        Log.d(TAG, "获取任务列表成功: $result")
                        val resultMap = result as? Map<String, Any> ?: mapOf(
                            "success" to false,
                            "error" to "返回数据格式错误"
                        )
                        continuation.resume(resultMap)
                    } catch (e: Exception) {
                        Log.e(TAG, "处理任务列表结果失败: ${e.message}")
                        continuation.resumeWithException(e)
                    }
                }
                
                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    val error = Exception("Flutter调用失败: $errorCode - $errorMessage")
                    Log.e(TAG, "获取任务列表失败: $errorCode - $errorMessage")
                    continuation.resumeWithException(error)
                }
                
                override fun notImplemented() {
                    val error = Exception("Flutter端未实现getTaskList方法")
                    Log.e(TAG, "Flutter端未实现getTaskList方法")
                    continuation.resumeWithException(error)
                }
            })
        }
    }
    
    /**
     * 通知任务更新
     */
    fun notifyTaskUpdate(taskId: String, status: String) {
        if (!isReady()) {
            Log.w(TAG, "TaskBridgeHelper 未初始化，无法通知任务更新")
            return
        }
        
        val arguments = mapOf(
            "taskId" to taskId,
            "status" to status
        )
        
        methodChannel?.invokeMethod("onTaskUpdate", arguments, object : MethodChannel.Result {
            override fun success(result: Any?) {
                Log.d(TAG, "通知任务更新成功: $taskId -> $status")
            }
            
            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                Log.e(TAG, "通知任务更新失败: $errorCode - $errorMessage")
            }
            
            override fun notImplemented() {
                Log.w(TAG, "Flutter端未实现onTaskUpdate方法")
            }
        })
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        methodChannel = null
        isInitialized = false
        Log.d(TAG, "TaskBridgeHelper 资源清理完成")
    }
}
