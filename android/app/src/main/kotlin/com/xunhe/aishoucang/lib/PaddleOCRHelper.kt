package com.xunhe.aishoucang.lib

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import java.io.File
import java.io.IOException
import com.baidu.paddle.lite.demo.ocr.OcrSdkHelper
import com.baidu.paddle.lite.demo.ocr.OcrResultModel
import java.util.ArrayList

/**
 * PaddleOCR 辅助类，替代原MLKitHelper，提供文字识别功能
 */
class PaddleOCRHelper(private val context: Context) {

    private val TAG = "PaddleOCRHelper"

    // PaddleOCR SDK 实例
    private val ocrSdkHelper = OcrSdkHelper()

    // 是否已初始化
    private var isInitialized = false

    // 上次OCR结果
    private var lastResultText: String? = null

    init {
        // 初始化OCR SDK
        initOcrSdk()
    }

    /**
     * 初始化OCR SDK
     */
    private fun initOcrSdk() {
        try {
            // 模型和字典路径
            val modelPath = "models/ch_PP-OCRv2"
            val labelPath = "labels/ppocr_keys_v1.txt"

            // 初始化参数
            val useOpencl = 0  // 不使用GPU
            val cpuThreadNum = 4  // CPU线程数
            val cpuPowerMode = "LITE_POWER_HIGH"  // CPU功耗模式

            // 执行初始化
            isInitialized = ocrSdkHelper.init(
                context,
                modelPath,
                labelPath,
                useOpencl,
                cpuThreadNum,
                cpuPowerMode
            )

            if (isInitialized) {
                Log.i(TAG, "PaddleOCR初始化成功")
            } else {
                Log.e(TAG, "PaddleOCR初始化失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "PaddleOCR初始化异常", e)
            isInitialized = false
        }
    }

    /**
     * 从Uri或文件路径加载Bitmap
     */
    private fun getBitmapFromUri(imageUri: String): Bitmap? {
        return try {
            // 尝试直接从文件路径创建
            if (imageUri.startsWith("/")) {
                val file = File(imageUri)
                if (file.exists()) {
                    return BitmapFactory.decodeFile(file.absolutePath)
                }
            }

            // 如果是content://开头的URI
            if (imageUri.startsWith("content://")) {
                val inputStream = context.contentResolver.openInputStream(Uri.parse(imageUri))
                return BitmapFactory.decodeStream(inputStream)
            }

            // 尝试将路径转换为文件URI
            val fileUri = Uri.fromFile(File(imageUri))
            val inputStream = context.contentResolver.openInputStream(fileUri)
            BitmapFactory.decodeStream(inputStream)
        } catch (e: IOException) {
            Log.e(TAG, "无法从URI加载图片: $imageUri", e)
            null
        } catch (e: Exception) {
            Log.e(TAG, "处理图片时出错: $imageUri", e)
            null
        }
    }

    /**
     * 识别图片中的文字
     * 实现与原MLKitHelper相同的接口
     *
     * @param imageUri 图片URI
     * @param useChinese 是否使用中文识别器 (PaddleOCR默认支持中文，此参数仅保持接口一致)
     * @param result Flutter结果回调
     */
    fun recognizeText(imageUri: String, useChinese: Boolean, result: MethodChannel.Result) {
        // 检查初始化状态
        if (!isInitialized) {
            // 尝试重新初始化
            initOcrSdk()
            if (!isInitialized) {
                result.error("INIT_ERROR", "OCR引擎未初始化", null)
                return
            }
        }

        // 加载图片
        val bitmap = getBitmapFromUri(imageUri) ?: run {
            result.error("IMAGE_ERROR", "无法加载图片", null)
            return
        }

        try {
            // 使用PaddleOCR进行识别 (使用同步API)
            val ocrResults: ArrayList<OcrResultModel>? =
                ocrSdkHelper.recognize(bitmap, true, true, true)

            if (ocrResults == null || ocrResults.isEmpty()) {
                // 没有识别到文字
                result.success(
                    mapOf(
                        "text" to "",
                        "blocks" to emptyList<Map<String, Any>>()
                    )
                )
                return
            }

            // 获取文本结果
            lastResultText = ocrSdkHelper.getTextResult()

            // 将PaddleOCR结果转换为与MLKit相同的格式
            val blocks = ArrayList<Map<String, Any?>>()

            for (ocrResult in ocrResults) {
                val points = ocrResult.points

                // 计算边界框
                val boundingBox = if (points != null && points.size >= 4) {
                    var left = Int.MAX_VALUE
                    var top = Int.MAX_VALUE
                    var right = 0
                    var bottom = 0

                    for (point in points) {
                        if (point.x < left) left = point.x
                        if (point.y < top) top = point.y
                        if (point.x > right) right = point.x
                        if (point.y > bottom) bottom = point.y
                    }

                    mapOf(
                        "left" to left,
                        "top" to top,
                        "right" to right,
                        "bottom" to bottom
                    )
                } else null

                // 构建与MLKit兼容的结果格式
                val blockMap = HashMap<String, Any?>()
                blockMap["text"] = ocrResult.label
                blockMap["boundingBox"] = boundingBox

                val linesList = ArrayList<Map<String, Any?>>()
                val lineMap = HashMap<String, Any?>()
                lineMap["text"] = ocrResult.label
                lineMap["boundingBox"] = boundingBox
                linesList.add(lineMap)

                blockMap["lines"] = linesList
                blocks.add(blockMap)
            }

            // 返回结果
            val resultMap = HashMap<String, Any>()
            resultMap["text"] = lastResultText ?: ""
            resultMap["blocks"] = blocks

            result.success(resultMap)
        } catch (e: Exception) {
            Log.e(TAG, "文字识别失败", e)
            result.error("TEXT_RECOGNITION_ERROR", "文字识别失败: ${e.message}", null)
        }
    }

    /**
     * 同步识别图片中的文字
     * @param imageUri 图片URI
     * @param useChinese 是否使用中文识别器
     * @param callback 回调函数，返回识别结果
     */
    fun recognizeTextSync(
        imageUri: String,
        useChinese: Boolean,
        callback: (success: Boolean, text: String?, error: String?) -> Unit
    ) {
        // 检查初始化状态
        if (!isInitialized) {
            initOcrSdk()
            if (!isInitialized) {
                callback(false, null, "OCR引擎未初始化")
                return
            }
        }

        // 加载图片
        val bitmap = getBitmapFromUri(imageUri) ?: run {
            callback(false, null, "无法加载图片")
            return
        }

        try {
            // 使用PaddleOCR进行识别
            val ocrResults = ocrSdkHelper.recognize(bitmap, true, true, true)
            if (ocrResults == null || ocrResults.isEmpty()) {
                callback(true, "", null) // 返回空字符串表示成功但没有识别到文字
                return
            }

            // 获取完整的文本结果
            val textList = ocrSdkHelper.getResultModels()
            val sb = StringBuilder()
            for (item in textList) {
                sb.append(item?.getLabel() ?: "")
            }
            val answer = sb.toString()
            callback(true, answer, null)
        } catch (e: Exception) {
            Log.e(TAG, "文字识别失败", e)
            callback(false, null, "文字识别失败: ${e.message}")
        }
    }

    /**
     * 同步识别图片中的文字（带详细结果）
     * @param imageUri 图片URI
     * @param useChinese 是否使用中文识别器
     * @param callback 回调函数，返回识别结果和原始OCR对象
     */
    fun recognizeTextSyncWithDetails(
        imageUri: String,
        useChinese: Boolean,
        callback: (success: Boolean, text: String?, ocrResults: ArrayList<OcrResultModel>?, error: String?) -> Unit
    ) {
        // 检查初始化状态
        if (!isInitialized) {
            initOcrSdk()
            if (!isInitialized) {
                callback(false, null, null, "OCR引擎未初始化")
                return
            }
        }

        // 加载图片
        val bitmap = getBitmapFromUri(imageUri) ?: run {
            callback(false, null, null, "无法加载图片")
            return
        }

        try {
            Log.d(TAG, "开始OCR识别，图片路径: $imageUri")

            // 使用PaddleOCR进行识别
            val ocrResults = ocrSdkHelper.recognize(bitmap, true, true, true)

            Log.d(TAG, "OCR识别完成，结果数量: ${ocrResults?.size ?: 0}")

            if (ocrResults == null || ocrResults.isEmpty()) {
                Log.d(TAG, "未识别到任何文字")
                callback(true, "", ArrayList(), null) // 返回空结果
                return
            }

            // 获取完整的文本结果
            val textList = ocrSdkHelper.getResultModels()
            val sb = StringBuilder()
            for (item in textList) {
                val label = item?.getLabel() ?: ""
                sb.append(label)
                Log.d(TAG, "文本片段: \"$label\"")
            }
            val answer = sb.toString()

            Log.d(TAG, "完整识别文本: \"$answer\"")

            // 返回详细结果
            callback(true, answer, ocrResults, null)
        } catch (e: Exception) {
            Log.e(TAG, "文字识别失败", e)
            callback(false, null, null, "文字识别失败: ${e.message}")
        }
    }

    /**
     * 释放资源
     */
    fun close() {
        try {
            if (isInitialized) {
                ocrSdkHelper.release()
                isInitialized = false
            }
        } catch (e: Exception) {
            Log.e(TAG, "释放OCR资源出错", e)
        }
    }
} 