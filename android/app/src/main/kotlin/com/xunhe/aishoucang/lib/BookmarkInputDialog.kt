package com.xunhe.aishoucang.lib

import android.app.AlertDialog
import android.content.Context
import android.graphics.Color
import android.graphics.PixelFormat
import android.os.Build
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import android.widget.Toast
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.api.BookMark
import com.xunhe.aishoucang.api.FavoriteApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 书签输入对话框
 * 用于获取用户输入的标题和描述，并添加到收藏夹
 */
class BookmarkInputDialog(private val context: Context) {

    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var favoriteId: String? = null

    /**
     * 显示书签输入对话框
     *
     * @param imageUrl 图片URL
     * @param onSuccess 成功回调
     */
    fun show(imageUrl: String, onSuccess: () -> Unit) {
        // 调用带封面的方法，但封面URL与原图URL相同
        showWithCover(imageUrl, imageUrl, onSuccess)
    }

    /**
     * 显示书签输入对话框（带封面URL）
     *
     * @param imageUrl 原始图片URL
     * @param coverUrl 封面图片URL
     * @param onSuccess 成功回调
     */
    fun showWithCover(imageUrl: String, coverUrl: String, onSuccess: () -> Unit) {
        try {
            // 获取WindowManager
            windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager

            // 创建对话框视图
            val inflater = LayoutInflater.from(context)
            val view = inflater.inflate(R.layout.dialog_bookmark_input, null)

            // 创建一个包含半透明背景的容器
            val container = FrameLayout(context)

            // 设置半透明黑色背景
            val backgroundColor = Color.argb(180, 0, 0, 0) // 70%透明度的黑色
            container.setBackgroundColor(backgroundColor)

            // 设置容器的点击事件，点击空白区域关闭对话框
            container.setOnClickListener {
                // 点击容器背景时关闭对话框
                dismiss()
            }

            // 将对话框视图添加到容器中，并居中显示
            val layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            )
            layoutParams.gravity = Gravity.CENTER
            layoutParams.setMargins(50, 0, 50, 0) // 左右边距
            view.layoutParams = layoutParams
            container.addView(view)

            // 获取视图中的控件
            val titleEditText = view.findViewById<EditText>(R.id.edit_title)
            val descEditText = view.findViewById<EditText>(R.id.edit_desc)
            val favoriteTextView = view.findViewById<TextView>(R.id.text_favorite)
            val cancelButton = view.findViewById<Button>(R.id.btn_cancel)
            val saveButton = view.findViewById<Button>(R.id.btn_save)

            // 设置收藏夹选择点击事件
            favoriteTextView.setOnClickListener {
                showFavoriteSelector(favoriteTextView)
            }

            // 设置取消按钮点击事件
            cancelButton.setOnClickListener {
                dismiss()
            }

            // 设置保存按钮点击事件
            saveButton.setOnClickListener {
                val title = titleEditText.text.toString().trim()
                val desc = descEditText.text.toString().trim()

                // 验证标题不能为空
                if (TextUtils.isEmpty(title)) {
                    Toast.makeText(context, "标题不能为空", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                // 验证收藏夹ID不能为空
                if (favoriteId == null) {
                    Toast.makeText(context, "请选择收藏夹", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                // 添加书签到收藏夹，使用封面URL作为封面
                addBookmarkWithCover(title, desc, favoriteId!!, imageUrl, coverUrl, onSuccess)
            }

            // 创建WindowManager.LayoutParams
            val params = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT

                // 设置窗口类型
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                }

                // 设置窗口标志
                flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH

                // 设置窗口格式
                format = PixelFormat.TRANSLUCENT

                // 设置窗口位置
                gravity = Gravity.CENTER
            }

            // 保存视图引用
            overlayView = container

            // 添加视图到窗口
            windowManager?.addView(container, params)

            // 加载默认收藏夹
            loadDefaultFavorite(favoriteTextView)
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(context, "显示对话框失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 关闭对话框
     */
    private fun dismiss() {
        try {
            if (overlayView != null) {
                windowManager?.removeView(overlayView)
                overlayView = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 加载默认收藏夹
     */
    private fun loadDefaultFavorite(favoriteTextView: TextView) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 获取收藏夹列表
                val favoriteApi = FavoriteApi(context)
                val result = favoriteApi.getFavoriteList("me")

                withContext(Dispatchers.Main) {
                    if (result.isSuccess) {
                        val favorites = result.getOrNull()
                        if (!favorites.isNullOrEmpty()) {
                            // 使用第一个收藏夹作为默认收藏夹
                            val defaultFavorite = favorites[0]
                            favoriteId = defaultFavorite.id
                            favoriteTextView.text = defaultFavorite.name
                        } else {
                            favoriteTextView.text = "未找到收藏夹"
                        }
                    } else {
                        favoriteTextView.text = "加载收藏夹失败"
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    favoriteTextView.text = "加载收藏夹失败"
                }
            }
        }
    }

    /**
     * 显示收藏夹选择器
     */
    private fun showFavoriteSelector(favoriteTextView: TextView) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 获取收藏夹列表
                val favoriteApi = FavoriteApi(context)
                val result = favoriteApi.getFavoriteList("me")

                withContext(Dispatchers.Main) {
                    if (result.isSuccess) {
                        val favorites = result.getOrNull()
                        if (!favorites.isNullOrEmpty()) {
                            // 创建收藏夹名称数组
                            val favoriteNames = favorites.map { it.name }.toTypedArray()

                            try {
                                // 显示选择对话框（使用应用上下文，避免Context错误）
                                val dialog = AlertDialog.Builder(context.applicationContext)
                                    .setTitle("选择收藏夹")
                                    .setItems(favoriteNames) { _, which ->
                                        // 更新选中的收藏夹
                                        favoriteId = favorites[which].id
                                        favoriteTextView.text = favorites[which].name
                                    }
                                    .setNegativeButton("取消", null)
                                    .create()

                                // 设置对话框类型为系统警告窗口
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                    dialog.window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
                                } else {
                                    dialog.window?.setType(WindowManager.LayoutParams.TYPE_PHONE)
                                }

                                dialog.show()
                            } catch (e: Exception) {
                                e.printStackTrace()
                                Toast.makeText(context, "显示收藏夹选择器失败: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            Toast.makeText(context, "未找到收藏夹", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        Toast.makeText(context, "加载收藏夹失败", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "加载收藏夹失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    /**
     * 添加书签到收藏夹
     */
    private fun addBookmark(title: String, desc: String, favoriteId: String, imageUrl: String, onSuccess: () -> Unit) {
        // 调用带封面的方法，但封面URL与原图URL相同
        addBookmarkWithCover(title, desc, favoriteId, imageUrl, imageUrl, onSuccess)
    }

    /**
     * 添加书签到收藏夹（带封面URL）
     */
    private fun addBookmarkWithCover(title: String, desc: String, favoriteId: String, imageUrl: String, coverUrl: String, onSuccess: () -> Unit) {
        // 初始化BookMark API
        BookMark.init(context)

        // 获取平台类型（截图收藏通常无法确定平台类型，设为null）
        val platformType: String? = null

        // 调用API添加书签
        BookMark.addBookMark(
            context = context,
            influencer_name = null, // 截图收藏不需要作者信息
            influencer_avatar = null,
            cover = coverUrl, // 使用压缩后的封面图片URL作为封面
            title = title,
            desc = if (desc.isEmpty()) null else desc,
            parent_id = favoriteId,
            scheme_url = imageUrl, // 使用原始图片URL作为scheme_url
            platform_type = platformType,
            callback = { success, errorMessage ->
                if (success) {
                    Toast.makeText(context, "保存成功", Toast.LENGTH_SHORT).show()
                    dismiss() // 关闭悬浮窗
                    onSuccess()
                } else {
                    Toast.makeText(context, "保存失败: $errorMessage", Toast.LENGTH_SHORT).show()
                }
            }
        )
    }
}
