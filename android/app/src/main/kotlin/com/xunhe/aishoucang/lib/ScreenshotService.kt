package com.xunhe.aishoucang.lib

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.RectF
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.widget.Toast
import com.xunhe.aishoucang.MainActivity
import com.xunhe.aishoucang.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 截图服务
 * 用于在前台运行截图操作，满足Android 10及以上版本的安全要求
 */
class ScreenshotService : Service() {
    companion object {
        private const val TAG = "ScreenshotService"
        private const val NOTIFICATION_ID = 2001
        private const val CHANNEL_ID = "screenshot_channel"

        // 广播Action
        const val ACTION_START_SCREENSHOT = "com.xunhe.aishoucang.START_SCREENSHOT"
        const val ACTION_START_SCREENSHOT_WITH_PROJECTION = "com.xunhe.aishoucang.START_SCREENSHOT_WITH_PROJECTION"
        const val ACTION_START_SCREENSHOT_WITH_SAVED_PERMISSION = "com.xunhe.aishoucang.START_SCREENSHOT_WITH_SAVED_PERMISSION"

        // Intent Extra Keys
        const val EXTRA_RESULT_CODE = "result_code"
        const val EXTRA_DATA = "data"
        const val EXTRA_RECT_LEFT = "rect_left"
        const val EXTRA_RECT_TOP = "rect_top"
        const val EXTRA_RECT_RIGHT = "rect_right"
        const val EXTRA_RECT_BOTTOM = "rect_bottom"

        /**
         * 启动截图服务（通过权限结果）
         */
        fun startScreenshotService(context: Context, resultCode: Int, data: Intent, rect: RectF) {
            val intent = Intent(context, ScreenshotService::class.java).apply {
                action = ACTION_START_SCREENSHOT
                putExtra(EXTRA_RESULT_CODE, resultCode)
                putExtra(EXTRA_DATA, data)
                putExtra(EXTRA_RECT_LEFT, rect.left)
                putExtra(EXTRA_RECT_TOP, rect.top)
                putExtra(EXTRA_RECT_RIGHT, rect.right)
                putExtra(EXTRA_RECT_BOTTOM, rect.bottom)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 启动截图服务（使用已保存的权限结果）
         */
        fun startScreenshotServiceWithSavedPermission(context: Context, rect: RectF) {
            val intent = Intent(context, ScreenshotService::class.java).apply {
                action = ACTION_START_SCREENSHOT_WITH_SAVED_PERMISSION
                putExtra(EXTRA_RECT_LEFT, rect.left)
                putExtra(EXTRA_RECT_TOP, rect.top)
                putExtra(EXTRA_RECT_RIGHT, rect.right)
                putExtra(EXTRA_RECT_BOTTOM, rect.bottom)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 启动截图服务（直接使用已保存的MediaProjection对象）
         * 注意：此方法已不再使用，因为MediaProjection对象不能在服务之间传递
         */
        @Deprecated("MediaProjection对象不能在服务之间传递，请使用startScreenshotServiceWithSavedPermission")
        fun startScreenshotServiceWithProjection(context: Context, mediaProjection: MediaProjection, rect: RectF) {
            val intent = Intent(context, ScreenshotService::class.java).apply {
                action = ACTION_START_SCREENSHOT_WITH_PROJECTION
                putExtra(EXTRA_RECT_LEFT, rect.left)
                putExtra(EXTRA_RECT_TOP, rect.top)
                putExtra(EXTRA_RECT_RIGHT, rect.right)
                putExtra(EXTRA_RECT_BOTTOM, rect.bottom)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }

    private var mediaProjection: MediaProjection? = null

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "截图服务已创建")

        // 创建通知渠道
        createNotificationChannel()

        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification())
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "截图服务已启动: ${intent?.action}")

        // 确保每次启动服务时都调用startForeground
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForeground(NOTIFICATION_ID, createNotification())
        }

        if (intent?.action == ACTION_START_SCREENSHOT) {
            val resultCode = intent.getIntExtra(EXTRA_RESULT_CODE, 0)
            val data = intent.getParcelableExtra<Intent>(EXTRA_DATA)

            if (data != null) {
                // 创建选区
                val rect = RectF(
                    intent.getFloatExtra(EXTRA_RECT_LEFT, 0f),
                    intent.getFloatExtra(EXTRA_RECT_TOP, 0f),
                    intent.getFloatExtra(EXTRA_RECT_RIGHT, 0f),
                    intent.getFloatExtra(EXTRA_RECT_BOTTOM, 0f)
                )

                // 执行截图
                performScreenshot(resultCode, data, rect)
            } else {
                Log.e(TAG, "截图数据为空")
                stopSelf()
            }
        } else if (intent?.action == ACTION_START_SCREENSHOT_WITH_SAVED_PERMISSION) {
            // 从单例获取保存的权限结果
            val mediaProjectionSingleton = MediaProjectionSingleton.getInstance()

            if (mediaProjectionSingleton.hasScreenshotPermission()) {
                // 创建选区
                val rect = RectF(
                    intent.getFloatExtra(EXTRA_RECT_LEFT, 0f),
                    intent.getFloatExtra(EXTRA_RECT_TOP, 0f),
                    intent.getFloatExtra(EXTRA_RECT_RIGHT, 0f),
                    intent.getFloatExtra(EXTRA_RECT_BOTTOM, 0f)
                )

                // 使用保存的权限结果执行截图
                performScreenshotWithSavedPermission(rect)
            } else {
                Log.e(TAG, "没有保存的截图权限")
                stopSelf()
            }
        } else if (intent?.action == ACTION_START_SCREENSHOT_WITH_PROJECTION) {
            // 此分支已不再使用，但保留以兼容旧代码
            Log.e(TAG, "不再支持直接传递MediaProjection对象")
            stopSelf()
        } else {
            Log.d(TAG, "未知的服务启动请求")
            stopSelf()
        }

        return START_NOT_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "截图服务即将销毁")

        // Android 14+ 需要立即停止前台服务状态，避免超时异常
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14+
            try {
                stopForeground(STOP_FOREGROUND_REMOVE) // 立即移除通知并停止前台服务状态
                Log.d(TAG, "Android 14+ 已立即停止前台服务状态")
            } catch (e: Exception) {
                Log.e(TAG, "Android 14+ 停止前台服务状态失败", e)
            }
        } else {
            // Android 13 及以下的标准处理
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    stopForeground(true) // 移除通知并停止前台服务状态
                    Log.d(TAG, "已停止前台服务状态")
                }
            } catch (e: Exception) {
                Log.e(TAG, "停止前台服务状态失败", e)
            }
        }

        // 释放MediaProjection
        try {
            mediaProjection?.stop()
            mediaProjection = null
            Log.d(TAG, "MediaProjection已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放MediaProjection失败", e)
        }

        Log.d(TAG, "截图服务已销毁")
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "截图服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用于在截图时显示通知"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        // 创建PendingIntent，点击通知时打开MainActivity
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        // 创建通知
        val builder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Notification.Builder(this, CHANNEL_ID)
        } else {
            Notification.Builder(this)
        }

        return builder
            .setContentTitle("正在截图")
            .setContentText("请稍候...")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .build()
    }

    /**
     * 执行截图（通过权限结果）
     */
    private fun performScreenshot(resultCode: Int, data: Intent, rect: RectF) {
        try {
            // 获取MediaProjection
            val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            mediaProjection = projectionManager.getMediaProjection(resultCode, data)

            if (mediaProjection == null) {
                Log.e(TAG, "无法获取MediaProjection")
                stopSelf()
                return
            }

            // Android 14+需要注册回调
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                val callback = object : MediaProjection.Callback() {
                    override fun onStop() {
                        Log.d(TAG, "MediaProjection已停止，停止截图服务")
                        stopSelf()
                    }
                }
                mediaProjection?.registerCallback(callback, null)
                Log.d(TAG, "已注册MediaProjection回调（Android 14+）")
            }

            // 保存权限结果到单例，以便下次使用
            MediaProjectionSingleton.getInstance().savePermissionResult(resultCode, data)

            // 使用ScreenshotCaptureHelper执行截图
            val screenshotCaptureHelper = ScreenshotCaptureHelper.getInstance(this)
            screenshotCaptureHelper.captureScreenshotWithProjection(mediaProjection!!, rect) { success, filePath, error ->
                processScreenshotResult(success, filePath, error)
            }
        } catch (e: Exception) {
            Log.e(TAG, "执行截图失败", e)
            stopSelf()
        }
    }

    /**
     * 执行截图（使用保存的权限结果）
     */
    private fun performScreenshotWithSavedPermission(rect: RectF) {
        try {
            // 从单例获取保存的权限结果
            val mediaProjectionSingleton = MediaProjectionSingleton.getInstance()
            val resultCode = mediaProjectionSingleton.getResultCode()
            val resultData = mediaProjectionSingleton.getResultData()

            if (resultData == null) {
                Log.e(TAG, "保存的权限结果数据为空")
                stopSelf()
                return
            }

            // 创建新的MediaProjection对象
            val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            mediaProjection = projectionManager.getMediaProjection(resultCode, resultData)

            if (mediaProjection == null) {
                Log.e(TAG, "无法创建MediaProjection对象")
                // 权限可能已过期，清除保存的权限
                mediaProjectionSingleton.clearPermission()
                stopSelf()
                return
            }

            // Android 14+需要注册回调
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                val callback = object : MediaProjection.Callback() {
                    override fun onStop() {
                        Log.d(TAG, "MediaProjection已停止，停止截图服务")
                        stopSelf()
                    }
                }
                mediaProjection?.registerCallback(callback, null)
                Log.d(TAG, "已注册MediaProjection回调（Android 14+）")
            }

            // 使用ScreenshotCaptureHelper执行截图
            val screenshotCaptureHelper = ScreenshotCaptureHelper.getInstance(this)
            screenshotCaptureHelper.captureScreenshotWithProjection(mediaProjection!!, rect) { success, filePath, error ->
                processScreenshotResult(success, filePath, error)
            }
        } catch (e: Exception) {
            Log.e(TAG, "使用保存的权限结果执行截图失败", e)
            // 出现异常时清除保存的权限
            MediaProjectionSingleton.getInstance().clearPermission()
            stopSelf()
        }
    }

    /**
     * 执行截图（使用已保存的MediaProjection）
     * 注意：此方法已不再使用，因为MediaProjection对象不能在服务之间传递
     */
    @Deprecated("MediaProjection对象不能在服务之间传递，请使用performScreenshotWithSavedPermission")
    private fun performScreenshotWithProjection(projection: MediaProjection, rect: RectF) {
        try {
            // 保存MediaProjection引用
            mediaProjection = projection

            // 使用ScreenshotCaptureHelper执行截图
            val screenshotCaptureHelper = ScreenshotCaptureHelper.getInstance(this)
            screenshotCaptureHelper.captureScreenshotWithProjection(projection, rect) { success, filePath, error ->
                processScreenshotResult(success, filePath, error)
            }
        } catch (e: Exception) {
            Log.e(TAG, "使用已保存的MediaProjection执行截图失败", e)
            stopSelf()
        }
    }

    /**
     * 处理截图结果
     */
    private fun processScreenshotResult(success: Boolean, filePath: String?, error: String?) {
        if (success && filePath != null) {
            Log.d(TAG, "截图成功，文件路径: $filePath")

            // 使用SourceToOssLinkHelper上传截图到OSS
            GlobalScope.launch {
                try {
                    // 先压缩图片
                    val imageCompressHelper = ImageCompressHelper.getInstance(this@ScreenshotService)
                    val compressedFilePath = imageCompressHelper.compressImage(filePath)
                    Log.d(TAG, "图片压缩完成，压缩后路径: $compressedFilePath")

                    // 上传压缩后的图片到OSS，使用OssManager
                    val ossManager = com.xunhe.aishoucang.helpers.OssManager.getInstance(this@ScreenshotService)
                    val ossUrl = ossManager.uploadFile(compressedFilePath)
                    Log.i(TAG, "截图上传成功，OSS链接: $ossUrl")

                    // 生成更小的封面图片（用于书签列表显示）
                    val coverFilePath = imageCompressHelper.compressImage(compressedFilePath, quality = 60, maxSize = 800)
                    Log.d(TAG, "封面图片生成完成，路径: $coverFilePath")

                    // 上传封面图片到OSS，使用OssManager
                    val coverOssUrl = ossManager.uploadFile(coverFilePath)
                    Log.i(TAG, "封面图片上传成功，OSS链接: $coverOssUrl")

                    // 显示输入对话框
                    withContext(Dispatchers.Main) {
                        // 显示书签输入对话框，传递原始图片URL和封面URL
                        val bookmarkInputDialog = BookmarkInputDialog(this@ScreenshotService)
                        bookmarkInputDialog.showWithCover(ossUrl, coverOssUrl) {
                            // 成功添加书签后的回调
                            Toast.makeText(this@ScreenshotService, "已添加到收藏夹", Toast.LENGTH_SHORT).show()
                        }
                    }

                    // 删除临时文件
                    try {
                        // 删除压缩后的文件（如果与原文件不同）
                        if (compressedFilePath != filePath) {
                            val compressedFile = File(compressedFilePath)
                            if (compressedFile.exists()) {
                                val deleted = compressedFile.delete()
                                Log.d(TAG, "删除压缩后的文件: ${if (deleted) "成功" else "失败"}")
                            }
                        }

                        // 删除封面文件（如果与压缩文件不同）
                        if (coverFilePath != compressedFilePath && coverFilePath != filePath) {
                            val coverFile = File(coverFilePath)
                            if (coverFile.exists()) {
                                val deleted = coverFile.delete()
                                Log.d(TAG, "删除封面文件: ${if (deleted) "成功" else "失败"}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "删除临时文件失败: ${e.message}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "上传截图到OSS失败", e)

                    // 显示错误提示
                    withContext(Dispatchers.Main) {
                        Toast.makeText(this@ScreenshotService, "上传截图失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                } finally {
                    // 停止服务
                    stopSelf()
                }
            }
        } else {
            Log.e(TAG, "截图失败: $error")

            // 显示错误提示
            Toast.makeText(this, "截图失败: $error", Toast.LENGTH_SHORT).show()

            // 停止服务
            stopSelf()
        }
    }
}
