package com.xunhe.aishoucang.helpers

import android.content.Context
import android.net.Uri
import android.util.Log
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import com.xunhe.aishoucang.views.share_panel.SharePanelItem

/**
 * 微信应用收藏项处理器
 */
object WechatAppItemHandler {
    private const val TAG = "WechatAppItemHandler"

    /**
     * 处理微信应用的收藏项
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     */
    fun handle(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "处理微信应用收藏项:")
        Log.d(TAG, "- 应用包名: $appPackage")
        Log.d(TAG, "- 剪贴板内容: $clipboardContent")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        // 检查复制链接是否有内容，并且url的host是mp.weixin.qq.com
        if (clipboardContent.isNullOrEmpty()) {
            Log.e(TAG, "剪贴板内容为空")
            // 隐藏悬浮窗上的加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
            floatingWindowHelper.hideLoading()

            // 显示错误提示
            CustomToastHelper.showToast(context, "还没有复制分享链接，复制后再试")
            return
        }

        // 检查URL的host是否为mp.weixin.qq.com
        try {
            val uri = Uri.parse(clipboardContent)
            val host = uri.host
            if (host != "mp.weixin.qq.com") {
                Log.e(TAG, "链接不是微信公众号文章链接，host: $host")
                // 隐藏悬浮窗上的加载动画
                val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
                floatingWindowHelper.hideLoading()

                // 显示错误提示
                CustomToastHelper.showToast(context, "请复制微信公众号文章链接")
                return
            }
            Log.d(TAG, "检查通过，这是微信公众号文章链接: $clipboardContent")
        } catch (e: Exception) {
            Log.e(TAG, "解析URL时出错: ${e.message}", e)
            // 隐藏悬浮窗上的加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
            floatingWindowHelper.hideLoading()

            // 显示错误提示
            CustomToastHelper.showToast(context, "链接格式不正确")
            return
        }

        // 使用WebViewExtractor提取微信内容
        WechatWebViewExtractor.extractFromUrl(context, clipboardContent, favoriteItem)
    }
}