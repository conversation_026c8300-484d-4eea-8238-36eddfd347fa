package com.xunhe.aishoucang.lib

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import android.widget.ImageButton
import com.xunhe.aishoucang.R

/**
 * 长截图暂停按钮
 * 用于在长截图过程中显示一个暂停按钮，点击后立即停止截图并开始拼接流程
 */
class LongScreenshotPauseButton private constructor(private val context: Context) {
    companion object {
        private const val TAG = "LongScreenshotPauseBtn"

        @Volatile
        private var instance: LongScreenshotPauseButton? = null

        fun getInstance(context: Context): LongScreenshotPauseButton {
            return instance ?: synchronized(this) {
                instance ?: LongScreenshotPauseButton(context.applicationContext).also { instance = it }
            }
        }
    }

    private var windowManager: WindowManager? = null
    private var pauseButtonView: View? = null
    private var pauseButtonParams: WindowManager.LayoutParams? = null
    private var isPauseButtonShowing = false
    private var pauseButtonCallback: (() -> Unit)? = null

    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }

    /**
     * 显示暂停按钮
     * @param callback 点击暂停按钮后的回调
     */
    fun showPauseButton(callback: () -> Unit) {
        if (isPauseButtonShowing) {
            Log.d(TAG, "暂停按钮已显示，不重复创建")
            return
        }

        pauseButtonCallback = callback
        createAndShowPauseButton()
    }

    /**
     * 隐藏暂停按钮
     */
    fun hidePauseButton() {
        if (!isPauseButtonShowing || pauseButtonView == null) {
            Log.d(TAG, "暂停按钮未显示，无需隐藏")
            return
        }

        // 确保在主线程中执行
        android.os.Handler(android.os.Looper.getMainLooper()).post {
            try {
                // 应用退出动画，但使用更短的动画时间
                val animation = AnimationUtils.loadAnimation(context, R.anim.menu_fade_out)
                animation.duration = 150 // 设置更短的动画时间，原来可能是300ms
                animation.setAnimationListener(object : android.view.animation.Animation.AnimationListener {
                    override fun onAnimationStart(animation: android.view.animation.Animation?) {}

                    override fun onAnimationEnd(animation: android.view.animation.Animation?) {
                        // 动画结束后移除视图
                        try {
                            windowManager?.removeView(pauseButtonView)
                            pauseButtonView = null
                            isPauseButtonShowing = false
                            Log.d(TAG, "暂停按钮已隐藏")
                        } catch (e: Exception) {
                            Log.e(TAG, "移除暂停按钮失败", e)
                        }
                    }

                    override fun onAnimationRepeat(animation: android.view.animation.Animation?) {}
                })

                pauseButtonView?.startAnimation(animation)
            } catch (e: Exception) {
                // 如果动画失败，直接移除视图
                Log.e(TAG, "暂停按钮退出动画执行失败，直接移除", e)
                try {
                    windowManager?.removeView(pauseButtonView)
                    pauseButtonView = null
                    isPauseButtonShowing = false
                    Log.d(TAG, "暂停按钮已直接移除")
                } catch (e2: Exception) {
                    Log.e(TAG, "移除暂停按钮失败", e2)
                }
            }
        }
    }

    /**
     * 创建并显示暂停按钮
     */
    private fun createAndShowPauseButton() {
        try {
            // 创建暂停按钮视图
            val inflater = LayoutInflater.from(context)
            val view = inflater.inflate(R.layout.pause_button_layout, null)

            // 设置暂停按钮点击事件
            val pauseButton = view.findViewById<ImageButton>(R.id.pause_button)
            pauseButton.setOnClickListener {
                Log.d(TAG, "暂停按钮被点击")
                // 调用回调函数
                pauseButtonCallback?.invoke()
                // 隐藏暂停按钮
                hidePauseButton()
            }

            // 创建WindowManager.LayoutParams
            val params = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT

                // 设置窗口类型
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                }

                // 设置窗口标志
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL

                // 设置窗口格式
                format = PixelFormat.TRANSLUCENT

                // 设置窗口位置（右下角）
                gravity = Gravity.BOTTOM or Gravity.END
                x = 50 // 距离右边缘的距离
                y = 150 // 距离底部的距离
            }

            // 保存视图和参数引用
            pauseButtonView = view
            pauseButtonParams = params

            // 添加视图到窗口
            windowManager?.addView(view, params)
            isPauseButtonShowing = true

            // 应用进入动画
            try {
                val animation = AnimationUtils.loadAnimation(context, R.anim.menu_fade_in)
                view.startAnimation(animation)
            } catch (e: Exception) {
                Log.e(TAG, "暂停按钮进入动画执行失败", e)
            }

            Log.d(TAG, "暂停按钮已显示")
        } catch (e: Exception) {
            Log.e(TAG, "创建暂停按钮失败", e)
        }
    }

    /**
     * 立即隐藏暂停按钮（不使用动画）
     * 用于紧急情况，需要立即隐藏暂停按钮
     */
    fun hideImmediately() {
        if (!isPauseButtonShowing || pauseButtonView == null) {
            Log.d(TAG, "暂停按钮未显示，无需隐藏")
            return
        }

        // 确保在主线程中执行
        android.os.Handler(android.os.Looper.getMainLooper()).post {
            try {
                windowManager?.removeView(pauseButtonView)
                pauseButtonView = null
                isPauseButtonShowing = false
                Log.d(TAG, "暂停按钮已立即隐藏")
            } catch (e: Exception) {
                Log.e(TAG, "立即隐藏暂停按钮失败", e)
            }
        }
    }

    /**
     * 检查暂停按钮是否正在显示
     */
    fun isPauseButtonVisible(): Boolean {
        return isPauseButtonShowing
    }
}
