package com.xunhe.aishoucang.adapters

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.models.TaskQueueItem
import com.xunhe.aishoucang.models.TaskStatus
import java.text.SimpleDateFormat
import java.util.*

/**
 * 任务队列适配器
 */
class TaskQueueAdapter(
    private val context: Context,
    private val items: MutableList<TaskQueueItem>,
    private val onItemClick: (TaskQueueItem) -> Unit = {}
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_TASK = 0
        private const val VIEW_TYPE_LOADING = 1
    }

    private val dateFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
    private var showLoadingMore = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_TASK -> {
                val view = LayoutInflater.from(context).inflate(R.layout.task_queue_item, parent, false)
                TaskViewHolder(view)
            }
            VIEW_TYPE_LOADING -> {
                val view = LayoutInflater.from(context).inflate(R.layout.loading_more_item, parent, false)
                LoadingViewHolder(view)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is TaskViewHolder -> {
                val item = items[position]
                holder.bind(item)
            }
            is LoadingViewHolder -> {
                // 加载更多项不需要绑定数据
            }
        }
    }

    override fun getItemCount(): Int = items.size + if (showLoadingMore) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (position < items.size) VIEW_TYPE_TASK else VIEW_TYPE_LOADING
    }

    /**
     * 更新数据
     */
    fun updateItems(newItems: List<TaskQueueItem>) {
        items.clear()
        items.addAll(newItems)
        notifyDataSetChanged()
    }

    /**
     * 添加新任务
     */
    fun addItem(item: TaskQueueItem) {
        items.add(0, item) // 添加到顶部
        notifyItemInserted(0)
    }

    /**
     * 更新任务状态
     */
    fun updateItem(itemId: String, updatedItem: TaskQueueItem) {
        val index = items.indexOfFirst { it.id == itemId }
        if (index != -1) {
            items[index] = updatedItem
            notifyItemChanged(index)
        }
    }

    /**
     * 移除任务
     */
    fun removeItem(itemId: String) {
        val index = items.indexOfFirst { it.id == itemId }
        if (index != -1) {
            items.removeAt(index)
            notifyItemRemoved(index)
        }
    }

    /**
     * 显示加载更多指示器
     */
    fun showLoadingMore() {
        if (!showLoadingMore) {
            showLoadingMore = true
            notifyItemInserted(items.size)
        }
    }

    /**
     * 隐藏加载更多指示器
     */
    fun hideLoadingMore() {
        if (showLoadingMore) {
            showLoadingMore = false
            notifyItemRemoved(items.size)
        }
    }

    inner class TaskViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleText: TextView = itemView.findViewById(R.id.task_title)
        private val descriptionText: TextView = itemView.findViewById(R.id.task_description)
        private val statusText: TextView = itemView.findViewById(R.id.task_status)
        private val timeText: TextView = itemView.findViewById(R.id.task_time)
        private val platformIcon: ImageView = itemView.findViewById(R.id.platform_icon)
        private val platformName: TextView = itemView.findViewById(R.id.platform_name)

        fun bind(item: TaskQueueItem) {
            titleText.text = item.title
            descriptionText.text = item.description
            statusText.text = item.status.displayName
            timeText.text = dateFormat.format(item.updatedAt)

            // 设置状态颜色
            try {
                statusText.setTextColor(Color.parseColor(item.status.color))
            } catch (e: Exception) {
                statusText.setTextColor(Color.WHITE)
            }

            // 设置平台信息
            if (item.platformName.isNotEmpty()) {
                platformName.text = item.platformName
                platformName.visibility = View.VISIBLE
                platformIcon.visibility = View.VISIBLE

                // 根据平台设置图标（这里先使用默认图标，后续可以根据平台类型设置不同图标）
                when (item.platform) {
                    "bilibili" -> platformIcon.setImageResource(android.R.drawable.ic_media_play)
                    "wechat" -> platformIcon.setImageResource(android.R.drawable.ic_dialog_email)
                    "douyin" -> platformIcon.setImageResource(android.R.drawable.ic_media_play)
                    "xiaohongshu" -> platformIcon.setImageResource(android.R.drawable.ic_menu_gallery)
                    else -> platformIcon.setImageResource(android.R.drawable.ic_menu_info_details)
                }
            } else {
                platformName.visibility = View.GONE
                platformIcon.visibility = View.GONE
            }

            // 设置点击事件
            itemView.setOnClickListener {
                onItemClick(item)
            }
        }
    }

    /**
     * 加载更多ViewHolder
     */
    inner class LoadingViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        // 加载更多项不需要额外的绑定逻辑
    }
}
