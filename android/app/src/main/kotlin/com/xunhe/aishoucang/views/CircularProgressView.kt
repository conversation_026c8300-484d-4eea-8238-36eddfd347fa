package com.xunhe.aishoucang.views

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator

/**
 * 自定义圆形进度视图
 * 高端简洁的设计，支持动画和百分比显示
 */
class CircularProgressView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 画笔
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val glowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    // 进度相关
    private var progress = 0f
    private var maxProgress = 100f
    
    // 尺寸相关
    private var centerX = 0f
    private var centerY = 0f
    private var radius = 0f
    private val strokeWidth = 6f
    
    // 颜色
    private val backgroundColor = Color.parseColor("#33FFFFFF")
    private val progressColor = Color.parseColor("#6B9EFF")
    private val glowColor = Color.parseColor("#4D6B9EFF")
    
    // 动画
    private var progressAnimator: ValueAnimator? = null
    
    init {
        setupPaints()
    }
    
    private fun setupPaints() {
        // 背景圆环画笔
        backgroundPaint.apply {
            style = Paint.Style.STROKE
            strokeWidth = <EMAIL>
            color = backgroundColor
            strokeCap = Paint.Cap.ROUND
        }
        
        // 进度圆环画笔
        progressPaint.apply {
            style = Paint.Style.STROKE
            strokeWidth = <EMAIL>
            color = progressColor
            strokeCap = Paint.Cap.ROUND
        }
        
        // 发光效果画笔
        glowPaint.apply {
            style = Paint.Style.STROKE
            strokeWidth = <EMAIL> + 4f
            color = glowColor
            strokeCap = Paint.Cap.ROUND
            maskFilter = BlurMaskFilter(8f, BlurMaskFilter.Blur.OUTER)
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        centerX = w / 2f
        centerY = h / 2f
        radius = (minOf(w, h) / 2f) - strokeWidth - 8f // 留出发光效果的空间
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // 绘制背景圆环
        canvas.drawCircle(centerX, centerY, radius, backgroundPaint)
        
        // 绘制进度圆环
        if (progress > 0) {
            val sweepAngle = (progress / maxProgress) * 360f
            val rect = RectF(
                centerX - radius,
                centerY - radius,
                centerX + radius,
                centerY + radius
            )
            
            // 绘制发光效果
            canvas.drawArc(rect, -90f, sweepAngle, false, glowPaint)
            
            // 绘制进度圆环
            canvas.drawArc(rect, -90f, sweepAngle, false, progressPaint)
        }
    }
    
    /**
     * 设置进度（带动画）
     * @param targetProgress 目标进度值 (0-100)
     * @param duration 动画持续时间（毫秒）
     */
    fun setProgress(targetProgress: Float, duration: Long = 800) {
        progressAnimator?.cancel()
        
        progressAnimator = ValueAnimator.ofFloat(progress, targetProgress).apply {
            this.duration = duration
            interpolator = AccelerateDecelerateInterpolator()
            addUpdateListener { animator ->
                progress = animator.animatedValue as Float
                invalidate()
            }
            start()
        }
    }
    
    /**
     * 立即设置进度（无动画）
     * @param targetProgress 目标进度值 (0-100)
     */
    fun setProgressImmediate(targetProgress: Float) {
        progressAnimator?.cancel()
        progress = targetProgress
        invalidate()
    }
    
    /**
     * 获取当前进度
     * @return 当前进度值 (0-100)
     */
    fun getProgress(): Float {
        return progress
    }
    
    /**
     * 重置进度
     */
    fun reset() {
        progressAnimator?.cancel()
        progress = 0f
        invalidate()
    }
    
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        progressAnimator?.cancel()
    }
}
