package com.xunhe.aishoucang.api

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.lib.RequestHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import com.xunhe.aishoucang.helpers.ConfigHelper

/**
 * 书签相关API
 */
object BookMark {
    private const val TAG = "BookMark"
    private var requestHelper: RequestHelper? = null

    /**
     * 初始化RequestHelper
     */
    fun init(context: Context) {
        if (requestHelper == null) {
            requestHelper = RequestHelper.getInstance(context)
        }
    }

    /**
     * 添加书签
     *
     * @param context 上下文
     * @param influencer_name 作者名称
     * @param influencer_avatar 作者头像URL
     * @param cover 封面图片URL
     * @param title 标题
     * @param desc 描述
     * @param parent_id 收藏夹ID
     * @param scheme_url 自定义URL方案
     * @param platform_type 平台类型（可选字段）
     * @param callback 回调函数，成功返回true，失败返回false和错误信息
     */
    fun addBookMark(
        context: Context,
        influencer_name: String?,
        influencer_avatar: String?,
        cover: String?,
        title: String?,
        desc: String?,
        parent_id: String,
        scheme_url: String,
        platform_type: String? = null,
        callback: ((Boolean, String?) -> Unit)? = null
    ) {
        // 确保RequestHelper已初始化
        init(context)

        // 创建请求体
        val requestBody = JSONObject().apply {
            put("parent_id", parent_id)
            influencer_name?.let { put("influencer_name", it) }
            influencer_avatar?.let { put("influencer_avatar", it) }
            cover?.let { put("cover", it) }
            title?.let { put("title", it) }
            desc?.let { put("desc", it) }
            put("scheme_url", scheme_url)
            platform_type?.let { put("platform_type", it) }
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "发送添加书签请求: $title 到收藏夹ID: $parent_id")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper?.postJson(
                    "$apiBaseUrl/bookmark/add",
                    requestBody,
                    null
                )

                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestHelper.ApiResult.Success<String> -> {
                            try {
                                val jsonResponse = JSONObject(result.data)
                                val success = jsonResponse.optInt("code", -1) == 0
                                val message = jsonResponse.optString("message", "未知错误")

                                if (success) {
                                    callback?.invoke(true, null)
                                } else {
                                    callback?.invoke(false, message)
                                }
                            } catch (e: Exception) {
                                callback?.invoke(false, "解析响应失败: ${e.message}")
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            callback?.invoke(false, "服务器错误: ${result.message}")
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            callback?.invoke(false, "网络请求异常: ${result.throwable.message}")
                        }
                        null -> {
                            callback?.invoke(false, "系统错误: RequestHelper未初始化")
                        }
                        else -> {
                            callback?.invoke(false, "系统错误: 未知响应类型")
                        }
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Log.e(TAG, "添加书签过程中出错: ${e.message}")
                    callback?.invoke(false, "添加书签过程中出错: ${e.message}")
                }
            }
        }
    }
}