package com.xunhe.aishoucang

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import androidx.appcompat.app.AppCompatActivity

/**
 * WebView活动
 * 用于在应用内显示网页
 */
class WebViewActivity : AppCompatActivity() {
    
    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    
    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_webview)
        
        // 获取URL
        val url = intent.getStringExtra("url") ?: "https://www.baidu.com"
        
        // 设置标题栏
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = url
        }
        
        // 初始化视图
        webView = findViewById(R.id.webview)
        progressBar = findViewById(R.id.progress_bar)
        
        // 配置WebView
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadsImagesAutomatically = true
            useWideViewPort = true
            loadWithOverviewMode = true
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
        }
        
        // 设置WebViewClient
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                supportActionBar?.title = view?.title ?: url
            }
            
            override fun shouldOverrideUrlLoading(view: WebView, request: android.webkit.WebResourceRequest): Boolean {
                val url = request.url.toString()
                val scheme = request.url.scheme
                
                // 拦截非http和https开头的协议
                if (scheme == null || (!scheme.equals("http", ignoreCase = true) && !scheme.equals("https", ignoreCase = true))) {
                    android.util.Log.d("WebViewActivity", "拦截到非HTTP(S)协议跳转: $url, scheme: $scheme")
                    
                    // 处理拦截到的协议，移除查询参数
                    try {
                        val uri = android.net.Uri.parse(url)
                        val path = uri.path
                        val cleanUrl = if (path != null) {
                            // 重新构建URL，移除查询参数
                            "${uri.scheme}://${uri.authority}${path}"
                        } else {
                            // 如果没有path，则使用主机名
                            "${uri.scheme}://${uri.authority}"
                        }
                        
                        android.util.Log.d("WebViewActivity", "处理后的URL: $cleanUrl")
                        
                        // 使用Intent跳转
                        try {
                            val intent = android.content.Intent(android.content.Intent.ACTION_VIEW, android.net.Uri.parse(cleanUrl))
                            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                            startActivity(intent)
                        } catch (e: Exception) {
                            android.util.Log.e("WebViewActivity", "无法启动应用处理该URL: $cleanUrl", e)
                            android.widget.Toast.makeText(this@WebViewActivity, "没有应用可以处理此链接: $cleanUrl", android.widget.Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("WebViewActivity", "处理URL时出错: $url", e)
                    }
                    
                    return true // 返回true表示WebView不处理这个URL
                }
                
                return false // 返回false让WebView处理http和https协议
            }
        }
        
        // 设置WebChromeClient以显示加载进度
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                progressBar.progress = newProgress
                if (newProgress == 100) {
                    progressBar.visibility = View.GONE
                } else {
                    progressBar.visibility = View.VISIBLE
                }
            }
            
            override fun onReceivedTitle(view: WebView?, title: String?) {
                supportActionBar?.title = title ?: url
            }
        }
        
        // 加载URL
        webView.loadUrl(url)
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onDestroy() {
        webView.destroy()
        super.onDestroy()
    }
}
