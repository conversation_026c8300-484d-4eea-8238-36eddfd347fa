package com.xunhe.aishoucang.lib

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.Toast

/**
 * 截图权限请求Activity
 * 这是一个透明的Activity，用于请求截图权限，不会干扰用户当前的操作
 */
class ScreenshotPermissionActivity : Activity() {
    companion object {
        private const val TAG = "ScreenshotPermissionActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置为透明主题
        setTheme(android.R.style.Theme_Translucent_NoTitleBar)

        // 设置窗口为透明
        window.setFlags(
            WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
            WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
        )

        // 立即请求截图权限
        requestScreenshotPermission()
    }

    /**
     * 请求截图权限
     */
    private fun requestScreenshotPermission() {
        try {
            val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            startActivityForResult(
                projectionManager.createScreenCaptureIntent(),
                ScreenshotHelper.REQUEST_SCREENSHOT_PERMISSION
            )
        } catch (e: Exception) {
            Log.e(TAG, "请求截图权限失败", e)
            Toast.makeText(this, "请求截图权限失败: ${e.message}", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    /**
     * 处理权限请求结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        Log.d(TAG, "收到Activity结果: requestCode=$requestCode, resultCode=$resultCode")

        if (requestCode == ScreenshotHelper.REQUEST_SCREENSHOT_PERMISSION) {
            if (resultCode == RESULT_OK && data != null) {
                // 保存权限结果到单例
                val mediaProjectionSingleton = MediaProjectionSingleton.getInstance()
                mediaProjectionSingleton.savePermissionResult(resultCode, data)

                // 处理截图权限结果
                ScreenshotHelper.handleScreenshotPermissionResult(this, requestCode, resultCode, data)
                Log.d(TAG, "截图权限已授予，正在处理")
            } else {
                Log.d(TAG, "用户拒绝了截图权限")
                Toast.makeText(this, "截图已取消", Toast.LENGTH_SHORT).show()
            }
        }

        // 无论结果如何，都关闭此Activity
        finish()
    }
}
