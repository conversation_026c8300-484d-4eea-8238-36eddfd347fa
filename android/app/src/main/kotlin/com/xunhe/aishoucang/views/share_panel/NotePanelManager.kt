package com.xunhe.aishoucang.views.share_panel

import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.api.NoteApi
import com.xunhe.aishoucang.api.NoteItem
import com.xunhe.aishoucang.api.NoteListResponse
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.xunhe.aishoucang.helpers.UpdateNoteHelper

/**
 * 笔记面板管理器
 * 负责管理收藏面板中的快速总结到笔记区域
 */
class NotePanelManager(private val context: Context) {
    private val TAG = "NotePanelManager"
    
    // 笔记数据列表
    private val noteItems = mutableListOf<NoteItem>()
    
    // 分页相关参数
    private var currentPage = 1
    private var isLoading = false
    private var hasMoreData = true
    private val pageSize = 8 // 固定显示8个笔记（2行4列）
    
    // 视图引用
    private var rootView: View? = null
    private var noteSwipeRefreshLayout: SwipeRefreshLayout? = null
    private var noteRecyclerView: RecyclerView? = null
    private var noteAdapter: NotePanelAdapter? = null
    
    /**
     * 初始化笔记面板
     * @param rootView 包含笔记区域的根视图
     */
    fun setupNotePanel(rootView: View) {
        this.rootView = rootView

        // 初始化SwipeRefreshLayout
        noteSwipeRefreshLayout = rootView.findViewById(R.id.note_swipe_refresh_layout)
        setupSwipeRefreshLayout()

        // 初始化RecyclerView
        noteRecyclerView = rootView.findViewById(R.id.note_recycler_view)
        setupRecyclerView()

        // 加载笔记数据
        loadNotes()
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        noteRecyclerView?.let { recyclerView ->
            // 设置GridLayoutManager，每行4列
            val layoutManager = GridLayoutManager(context, 4)
            recyclerView.layoutManager = layoutManager

            // 初始化适配器
            noteAdapter = NotePanelAdapter(context, noteItems) { note ->
                handleNoteClick(note)
            }
            recyclerView.adapter = noteAdapter
        }
    }

    /**
     * 设置下拉刷新布局
     */
    private fun setupSwipeRefreshLayout() {
        noteSwipeRefreshLayout?.let { swipeRefresh ->
            // 设置下拉刷新监听器
            swipeRefresh.setOnRefreshListener {
                Log.d(TAG, "笔记区域触发下拉刷新")
                refreshNotes()
            }

            // 设置下拉刷新的颜色，使用项目主题色
            swipeRefresh.setColorSchemeColors(
                Color.parseColor("#1EB9EF"), // 主色调
                Color.parseColor("#4DCBF7"), // 浅色调
                Color.parseColor("#0A9BD0")  // 深色调
            )
        }
    }



    /**
     * 加载笔记数据
     */
    private fun loadNotes() {
        if (isLoading) return

        isLoading = true
        currentPage = 1

        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始加载笔记列表")

                val noteApi = NoteApi()
                noteApi.getNoteList(
                    context = context,
                    page = currentPage,
                    pageSize = 20 // 一次加载更多数据
                ) { success: Boolean, response: NoteListResponse?, error: String? ->
                    if (success && response != null) {
                        noteItems.clear()
                        noteItems.addAll(response.notes)
                        hasMoreData = response.notes.size >= 20

                        CoroutineScope(Dispatchers.Main).launch {
                            // 更新适配器
                            noteAdapter?.updateNotes(noteItems)
                            // 结束刷新动画
                            noteSwipeRefreshLayout?.isRefreshing = false
                        }

                        Log.d(TAG, "成功加载${response.notes.size}个笔记")
                    } else {
                        Log.e(TAG, "加载笔记失败: $error")
                        CoroutineScope(Dispatchers.Main).launch {
                            // 结束刷新动画
                            noteSwipeRefreshLayout?.isRefreshing = false
                        }
                    }
                    isLoading = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载笔记异常", e)
                isLoading = false
                CoroutineScope(Dispatchers.Main).launch {
                    // 结束刷新动画
                    noteSwipeRefreshLayout?.isRefreshing = false
                }
            }
        }
    }
    



    /**
     * 处理笔记点击事件
     * 现在用于快速总结到笔记功能，点击笔记会触发更新笔记流程
     */
    private fun handleNoteClick(note: NoteItem) {
        Log.d(TAG, "点击笔记进行快速总结: ${note.title}")

        try {
            // 使用UpdateNoteHelper处理更新笔记逻辑
            UpdateNoteHelper.handleUpdateNoteClick(context, note.id)
        } catch (e: Exception) {
            Log.e(TAG, "处理更新笔记异常", e)
            android.widget.Toast.makeText(context, "更新笔记失败", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 刷新笔记数据
     */
    fun refreshNotes() {
        loadNotes()
    }

    /**
     * 获取当前显示的笔记数量
     */
    fun getCurrentNoteCount(): Int {
        return noteItems.size
    }

    /**
     * 检查是否有更多数据
     */
    fun hasMoreData(): Boolean {
        return hasMoreData
    }
}
