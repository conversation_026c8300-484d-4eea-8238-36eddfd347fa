package com.xunhe.aishoucang.lib

import android.graphics.Bitmap
import android.graphics.Color
import android.util.Log

/**
 * 长截图服务滚动功能
 * 包含滚动和内容检测的方法
 */
internal fun scrollScreen(service: LongScreenshotService, callback: (Boolean) -> Unit) {
    try {
        // 检查是否已取消
        if (service.isCancelled) {
            Log.d(LongScreenshotService.TAG, "长截图已取消，跳过滚动操作")
            callback(false)
            return
        }

        Log.d(LongScreenshotService.TAG, "开始执行滚动操作")

        // 使用无障碍服务执行滚动操作
        val screenHeight = service.safeAreaBottom - service.safeAreaTop
        val startX = service.screenWidth / 2f
        val startY = service.safeAreaBottom - 100f // 从底部附近开始滚动
        val endX = startX
        val endY = startY - service.scrollHeight // 向上滚动屏幕高度的50%

        Log.d(LongScreenshotService.TAG, "执行滚动: 从($startX, $startY)到($endX, $endY)")

        // 使用手势滚动
        val success = service.accessibilityHelper?.swipe(
            startX, startY, endX, endY, 300,
            object : android.accessibilityservice.AccessibilityService.GestureResultCallback() {
                override fun onCompleted(gestureDescription: android.accessibilityservice.GestureDescription) {
                    Log.d(LongScreenshotService.TAG, "滚动完成")
                    // 再次检查是否已取消
                    if (service.isCancelled) {
                        Log.d(LongScreenshotService.TAG, "滚动完成后发现长截图已取消")
                        callback(false)
                        return
                    }

                    // 等待内容稳定
                    service.handler.postDelayed({
                        // 再次检查是否已取消
                        if (service.isCancelled) {
                            Log.d(LongScreenshotService.TAG, "等待内容稳定后发现长截图已取消")
                            callback(false)
                        } else {
                            callback(true)
                        }
                    }, 500)
                }

                override fun onCancelled(gestureDescription: android.accessibilityservice.GestureDescription) {
                    Log.e(LongScreenshotService.TAG, "滚动取消")
                    callback(false)
                }
            }
        ) ?: false

        if (!success) {
            Log.e(LongScreenshotService.TAG, "发送滚动手势失败")
            callback(false)
        }
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "滚动屏幕失败", e)
        callback(false)
    }
}

/**
 * 检查是否到达内容底部
 * 通过比较当前截图和前一张截图来判断
 */
internal fun isAtContentBottom(service: LongScreenshotService): Boolean {
    // 如果只有一张截图，肯定没到底部
    if (service.capturedBitmaps.size <= 1) {
        return false
    }

    try {
        val currentBitmap = service.capturedBitmaps.last()
        val previousBitmap = service.capturedBitmaps[service.capturedBitmaps.size - 2]

        // 比较当前截图和上一张截图的相似度
        val similarity = compareImages(previousBitmap, currentBitmap)
        Log.d(LongScreenshotService.TAG, "图片相似度: $similarity")

        // 如果相似度超过90%，认为已到达底部
        return similarity > 0.9
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "检查内容底部失败", e)
        return false
    }
}

/**
 * 比较两张图片底部区域的相似度
 * 此方法保留但不再使用
 */
internal fun compareBottomAreas(bitmap1: Bitmap, bitmap2: Bitmap): Double {
    try {
        // 获取底部区域（底部20%的区域）
        val height = bitmap1.height
        val compareHeight = (height * 0.2).toInt()
        val startY = height - compareHeight

        // 计算不同像素的数量
        var differentPixels = 0
        val totalPixels = bitmap1.width * compareHeight

        for (y in startY until height) {
            for (x in 0 until bitmap1.width) {
                val pixel1 = bitmap1.getPixel(x, y)
                val pixel2 = bitmap2.getPixel(x, y)

                // 如果像素不同，增加计数
                if (pixel1 != pixel2) {
                    differentPixels++
                }
            }
        }

        // 计算相似度（相同像素的比例）
        return 1.0 - (differentPixels.toDouble() / totalPixels.toDouble())
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "比较底部区域失败", e)
        return 0.0
    }
}

/**
 * 比较两张图片的整体相似度
 */
internal fun compareImages(bitmap1: Bitmap, bitmap2: Bitmap): Double {
    try {
        // 确保两张图片尺寸相同
        if (bitmap1.width != bitmap2.width || bitmap1.height != bitmap2.height) {
            Log.e(LongScreenshotService.TAG, "图片尺寸不同，无法比较")
            return 0.0
        }

        // 采样率，不需要比较每个像素，可以跳过一些像素以提高性能
        val sampleRate = 5

        // 计算不同像素的数量
        var differentPixels = 0
        var sampledPixels = 0

        for (y in 0 until bitmap1.height step sampleRate) {
            for (x in 0 until bitmap1.width step sampleRate) {
                val pixel1 = bitmap1.getPixel(x, y)
                val pixel2 = bitmap2.getPixel(x, y)

                // 计算颜色差异
                val rDiff = Math.abs(android.graphics.Color.red(pixel1) - android.graphics.Color.red(pixel2))
                val gDiff = Math.abs(android.graphics.Color.green(pixel1) - android.graphics.Color.green(pixel2))
                val bDiff = Math.abs(android.graphics.Color.blue(pixel1) - android.graphics.Color.blue(pixel2))

                // 如果颜色差异大于阈值，认为是不同像素
                if (rDiff + gDiff + bDiff > 30) {
                    differentPixels++
                }

                sampledPixels++
            }
        }

        // 计算相似度（相同像素的比例）
        val similarity = 1.0 - (differentPixels.toDouble() / sampledPixels.toDouble())
        Log.d(LongScreenshotService.TAG, "图片相似度计算: 不同像素=$differentPixels, 采样像素=$sampledPixels, 相似度=$similarity")
        return similarity
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "比较图片失败", e)
        return 0.0
    }
}

/**
 * 停止长截图
 */
internal fun LongScreenshotService.stopLongScreenshot() {
    Log.d(LongScreenshotService.TAG, "停止长截图")

    // 隐藏进度界面
    longScreenshotHelper?.hideLongScreenshotProgressOverlay()

    // 立即隐藏暂停按钮，不使用动画
    pauseButton?.hideImmediately()

    // 释放资源
    releaseResources()

    // 重置状态
    isCapturing = false
    isCancelled = false

    // 延迟停止服务，确保UI操作完成
    handler.postDelayed({
        stopSelf()
    }, 1000)
}

/**
 * 释放资源
 */
internal fun LongScreenshotService.releaseResources() {
    try {
        Log.d(LongScreenshotService.TAG, "开始释放所有资源")

        // 释放虚拟显示
        virtualDisplay?.release()
        virtualDisplay = null
        Log.d(LongScreenshotService.TAG, "已释放虚拟显示")

        // 释放ImageReader
        imageReader?.close()
        imageReader = null
        Log.d(LongScreenshotService.TAG, "已释放ImageReader")

        // 停止MediaProjection
        mediaProjection?.stop()
        mediaProjection = null
        Log.d(LongScreenshotService.TAG, "已停止MediaProjection")

        // 释放位图资源
        Log.d(LongScreenshotService.TAG, "开始释放${capturedBitmaps.size}张位图资源")
        capturedBitmaps.forEach { it.recycle() }
        capturedBitmaps.clear()
        Log.d(LongScreenshotService.TAG, "已释放所有位图资源")

        // 移除所有待执行的延迟任务
        handler.removeCallbacksAndMessages(null)
        Log.d(LongScreenshotService.TAG, "已移除所有待执行的延迟任务")

        // 清空根节点引用
        savedRootNode = null
        Log.d(LongScreenshotService.TAG, "已清空根节点引用")

        Log.d(LongScreenshotService.TAG, "所有资源释放完成")
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "释放资源失败", e)
    }
}
