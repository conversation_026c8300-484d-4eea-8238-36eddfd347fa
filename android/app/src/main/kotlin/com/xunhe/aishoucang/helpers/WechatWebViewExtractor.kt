package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.xunhe.aishoucang.api.BookMark
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import com.xunhe.aishoucang.helpers.CustomToastHelper
import org.json.JSONObject

/**
 * 微信WebView提取器
 * 用于使用WebView打开微信链接并提取内容
 */
object WechatWebViewExtractor {
    private const val TAG = "WechatWebViewExtractor"

    // 标记是否已保存，避免重复保存
    private var hasSaved = false

    /**
     * 使用WebView打开微信链接并提取内容
     *
     * @param context 上下文
     * @param clipboardContent 剪贴板内容（微信链接）
     * @param favoriteItem 收藏夹项
     */
    fun extractFromUrl(
        context: Context,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "开始使用WebView提取微信内容")
        Log.d(TAG, "剪贴板内容: $clipboardContent")

        // 重置保存状态
        hasSaved = false

        // 显示加载动画
        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
        floatingWindowHelper.showLoading()

        if (clipboardContent.isNullOrEmpty()) {
            Log.e(TAG, "剪贴板内容为空")
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "剪贴板内容为空，无法提取")
            return
        }

        // 检查收藏夹是否选择
        if (favoriteItem == null) {
            Log.e(TAG, "未选择收藏夹")
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "请先选择一个收藏夹")
            return
        }

        // 使用WebViewHtmlExtractor执行业务特定的JavaScript
        WebViewHtmlExtractor.executeBusinessJavaScript(
            context,
            clipboardContent,
            "WechatArticle"
        ) { result, error ->
            // 隐藏加载动画
            floatingWindowHelper.hideLoading()

            if (error != null) {
                Log.e(TAG, "执行WechatArticle.js失败: $error")
                CustomToastHelper.showToast(context, "提取微信内容失败: $error")
                return@executeBusinessJavaScript
            }

            // 处理JavaScript返回的结果
            if (result != null) {
                Log.d(TAG, "WechatArticle.js执行成功，返回结果: $result")

                try {
                    // 解析JSON结果
                    val jsonObject = JSONObject(result)

                    // 提取数据
                    val title = jsonObject.optString("title", "")
                    val author = jsonObject.optString("author", "")
                    val publishTime = jsonObject.optString("publishTime", "")
                    val originalCoverImage = jsonObject.optString("coverImage", "")
                    val authorAvatar = jsonObject.optString("authorAvatar", "")
                    val content = jsonObject.optString("content", "")
                    val url = jsonObject.optString("url", "")

                    // 提取图片列表
                    val images = try {
                        val imagesArray = jsonObject.optJSONArray("images")
                        if (imagesArray != null) {
                            List(imagesArray.length()) { i -> imagesArray.getString(i) }
                        } else {
                            emptyList()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "解析图片列表失败", e)
                        emptyList<String>()
                    }

                    // 如果原始封面图片为空，则使用文章中的第一张图片作为封面
                    val coverImage = if (originalCoverImage.isNotEmpty()) {
                        originalCoverImage
                    } else if (images.isNotEmpty()) {
                        Log.d(TAG, "使用文章中的第一张图片作为封面")
                        images[0]
                    } else {
                        ""
                    }

                    Log.d(TAG, "解析结果 - 标题: $title")
                    Log.d(TAG, "解析结果 - 作者: $author")
                    Log.d(TAG, "解析结果 - 发布时间: $publishTime")
                    Log.d(TAG, "解析结果 - 原始封面图片: $originalCoverImage")
                    Log.d(TAG, "解析结果 - 最终封面图片: $coverImage")
                    Log.d(TAG, "解析结果 - 公众号头像: $authorAvatar")
                    Log.d(TAG, "解析结果 - 内容: ${content.take(50)}...")
                    Log.d(TAG, "解析结果 - URL: $url")
                    Log.d(TAG, "解析结果 - 图片数量: ${images.size}")

                    // 检查必要数据是否存在
                    if (title.isEmpty()) {
                        Log.e(TAG, "标题为空，无法保存")
                        CustomToastHelper.showToast(context, "提取失败: 未能获取文章标题")
                        return@executeBusinessJavaScript
                    }

                    if (url.isEmpty()) {
                        Log.e(TAG, "URL为空，无法保存")
                        CustomToastHelper.showToast(context, "提取失败: 未能获取文章链接")
                        return@executeBusinessJavaScript
                    }

                    // 保存书签到数据库
                    saveBookmark(
                        context = context,
                        title = title,
                        author = author,
                        publishTime = publishTime,
                        coverImage = coverImage,
                        authorAvatar = authorAvatar,
                        content = content,
                        url = url,
                        favoriteItem = favoriteItem
                    )

                } catch (e: Exception) {
                    Log.e(TAG, "解析JavaScript返回结果失败", e)
                    CustomToastHelper.showToast(context, "解析微信内容失败: ${e.message}")
                }
            } else {
                Log.e(TAG, "JavaScript返回结果为空")
                CustomToastHelper.showToast(context, "提取微信内容失败: 返回结果为空")
            }
        }
    }

    /**
     * 保存书签到数据库
     */
    private fun saveBookmark(
        context: Context,
        title: String,
        author: String,
        publishTime: String,
        coverImage: String?,
        authorAvatar: String?,
        content: String,
        url: String,
        favoriteItem: SharePanelItem
    ) {
        // 确保只保存一次
        if (hasSaved) {
            Log.d(TAG, "书签已保存，跳过重复保存")
            return
        }

        // 标记为已保存
        hasSaved = true

        // 显示加载动画
        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
        floatingWindowHelper.showLoading()

        // 初始化BookMark API
        BookMark.init(context)

        // 构建描述信息（作者 + 发布时间）
        val desc = if (author.isNotEmpty() && publishTime.isNotEmpty()) {
            "$author · $publishTime"
        } else if (author.isNotEmpty()) {
            author
        } else if (publishTime.isNotEmpty()) {
            publishTime
        } else {
            content.take(100) // 如果没有作者和发布时间，使用内容前100个字符作为描述
        }

        // 获取平台类型
        val platformType = SharePanelHelper.getCurrentPlatformType(url)

        // 调用BookMark API保存书签
        BookMark.addBookMark(
            context = context,
            influencer_name = author.ifEmpty { "微信公众号" },
            influencer_avatar = authorAvatar,
            cover = coverImage,
            title = title,
            desc = desc,
            parent_id = favoriteItem.id,
            scheme_url = url,
            platform_type = platformType,
            callback = { success, errorMessage ->
                // 隐藏加载动画
                floatingWindowHelper.hideLoading()

                if (success) {
                    Log.d(TAG, "保存书签成功")
                    CustomToastHelper.showToast(context, "收藏成功")
                } else {
                    Log.e(TAG, "保存书签失败: $errorMessage")
                    CustomToastHelper.showToast(context, "收藏失败: $errorMessage")
                }
            }
        )
    }
}
