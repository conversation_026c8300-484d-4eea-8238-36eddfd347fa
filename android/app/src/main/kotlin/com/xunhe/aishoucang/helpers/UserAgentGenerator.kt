package com.xunhe.aishoucang.helpers

import kotlin.random.Random

/**
 * User-Agent生成器
 * 随机生成各种浏览器的User-Agent字符串，用于绕过服务器的反爬虫检测
 */
object UserAgentGenerator {
    
    // Chrome浏览器版本列表
    private val chromeVersions = listOf(
        "120.0.0.0", "*********", "*********", "*********", "116.0.0.0",
        "115.0.0.0", "114.0.0.0", "113.0.0.0", "112.0.0.0", "111.0.0.0"
    )
    
    // Firefox浏览器版本列表
    private val firefoxVersions = listOf(
        "121.0", "120.0", "119.0", "118.0", "117.0",
        "116.0", "115.0", "114.0", "113.0", "112.0"
    )
    
    // Safari浏览器版本列表
    private val safariVersions = listOf(
        "17.2", "17.1", "17.0", "16.6", "16.5",
        "16.4", "16.3", "16.2", "16.1", "16.0"
    )
    
    // Windows版本列表
    private val windowsVersions = listOf(
        "Windows NT 10.0; Win64; x64",
        "Windows NT 10.0; WOW64",
        "Windows NT 6.3; Win64; x64",
        "Windows NT 6.1; Win64; x64",
        "Windows NT 6.1; WOW64"
    )
    
    // macOS版本列表
    private val macVersions = listOf(
        "Intel Mac OS X 10_15_7",
        "Intel Mac OS X 10_15_6",
        "Intel Mac OS X 10_14_6",
        "Intel Mac OS X 10_13_6",
        "Intel Mac OS X 11_7_10",
        "Intel Mac OS X 12_7_2"
    )
    
    // Android版本列表
    private val androidVersions = listOf(
        "Android 14", "Android 13", "Android 12", "Android 11", "Android 10",
        "Android 9", "Android 8.1.0", "Android 8.0.0"
    )
    
    // 手机型号列表
    private val phoneModels = listOf(
        "SM-G998B", "SM-G991B", "SM-G996B", "SM-G973F", "SM-G975F",
        "Pixel 7", "Pixel 6", "Pixel 5", "Pixel 4",
        "iPhone14,3", "iPhone13,2", "iPhone12,1", "iPhone11,8",
        "Mi 13", "Mi 12", "Mi 11", "Redmi Note 12",
        "ONEPLUS A6000", "ONEPLUS A9000", "ONEPLUS A11000"
    )
    
    /**
     * 生成随机的Chrome桌面版User-Agent
     */
    private fun generateChromeDesktop(): String {
        val chromeVersion = chromeVersions.random()
        val windowsVersion = windowsVersions.random()
        return "Mozilla/5.0 ($windowsVersion) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/$chromeVersion Safari/537.36"
    }
    
    /**
     * 生成随机的Chrome Mac版User-Agent
     */
    private fun generateChromeMac(): String {
        val chromeVersion = chromeVersions.random()
        val macVersion = macVersions.random()
        return "Mozilla/5.0 (Macintosh; $macVersion) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/$chromeVersion Safari/537.36"
    }
    
    /**
     * 生成随机的Chrome移动版User-Agent
     */
    private fun generateChromeMobile(): String {
        val chromeVersion = chromeVersions.random()
        val androidVersion = androidVersions.random()
        val phoneModel = phoneModels.random()
        return "Mozilla/5.0 (Linux; $androidVersion; $phoneModel) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/$chromeVersion Mobile Safari/537.36"
    }
    
    /**
     * 生成随机的Firefox桌面版User-Agent
     */
    private fun generateFirefoxDesktop(): String {
        val firefoxVersion = firefoxVersions.random()
        val windowsVersion = windowsVersions.random()
        return "Mozilla/5.0 ($windowsVersion; rv:$firefoxVersion) Gecko/20100101 Firefox/$firefoxVersion"
    }
    
    /**
     * 生成随机的Firefox Mac版User-Agent
     */
    private fun generateFirefoxMac(): String {
        val firefoxVersion = firefoxVersions.random()
        val macVersion = macVersions.random().replace("Intel ", "")
        return "Mozilla/5.0 (Macintosh; Intel $macVersion) Gecko/20100101 Firefox/$firefoxVersion"
    }
    
    /**
     * 生成随机的Safari Mac版User-Agent
     */
    private fun generateSafariMac(): String {
        val safariVersion = safariVersions.random()
        val macVersion = macVersions.random()
        val webkitVersion = "605.1.15"
        return "Mozilla/5.0 (Macintosh; $macVersion) AppleWebKit/$webkitVersion (KHTML, like Gecko) Version/$safariVersion Safari/$webkitVersion"
    }
    
    /**
     * 生成随机的Safari iOS版User-Agent
     */
    private fun generateSafariIOS(): String {
        val iosVersions = listOf("17_2", "17_1", "17_0", "16_7", "16_6", "16_5")
        val iosVersion = iosVersions.random()
        val safariVersion = safariVersions.random()
        val webkitVersion = "605.1.15"
        val devices = listOf("iPhone14,3", "iPhone13,2", "iPhone12,1", "iPhone11,8")
        val device = devices.random()
        return "Mozilla/5.0 ($device; CPU iPhone OS $iosVersion like Mac OS X) AppleWebKit/$webkitVersion (KHTML, like Gecko) Version/$safariVersion Mobile/15E148 Safari/$webkitVersion"
    }
    
    /**
     * 生成随机的Edge桌面版User-Agent
     */
    private fun generateEdgeDesktop(): String {
        val edgeVersions = listOf("120.0.0.0", "*********", "*********", "*********")
        val edgeVersion = edgeVersions.random()
        val windowsVersion = windowsVersions.random()
        return "Mozilla/5.0 ($windowsVersion) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/$edgeVersion Safari/537.36 Edg/$edgeVersion"
    }
    
    /**
     * 生成随机User-Agent
     * 随机选择不同的浏览器和平台组合
     */
    fun generateRandomUserAgent(): String {
        val generators = listOf(
            ::generateChromeDesktop,
            ::generateChromeMac,
            ::generateChromeMobile,
            ::generateFirefoxDesktop,
            ::generateFirefoxMac,
            ::generateSafariMac,
            ::generateSafariIOS,
            ::generateEdgeDesktop
        )
        
        return generators.random().invoke()
    }
    
    /**
     * 生成移动端User-Agent
     * 专门用于移动端网站访问
     */
    fun generateMobileUserAgent(): String {
        val mobileGenerators = listOf(
            ::generateChromeMobile,
            ::generateSafariIOS
        )
        
        return mobileGenerators.random().invoke()
    }
    
    /**
     * 生成桌面端User-Agent
     * 专门用于桌面端网站访问
     */
    fun generateDesktopUserAgent(): String {
        val desktopGenerators = listOf(
            ::generateChromeDesktop,
            ::generateChromeMac,
            ::generateFirefoxDesktop,
            ::generateFirefoxMac,
            ::generateSafariMac,
            ::generateEdgeDesktop
        )
        
        return desktopGenerators.random().invoke()
    }
    
    /**
     * 根据URL生成合适的User-Agent
     * 针对不同网站使用不同的策略
     */
    fun generateUserAgentForUrl(url: String): String {
        return when {
            // 小红书使用移动端User-Agent效果更好，并且使用较新的Chrome版本
            url.contains("xhscdn.com") || url.contains("xiaohongshu.com") -> {
                val androidVersion = androidVersions.filter { it.contains("13") || it.contains("14") }.random()
                val phoneModel = phoneModels.filter { it.startsWith("SM-") || it.startsWith("Pixel") }.random()
                val chromeVersion = chromeVersions.take(3).random() // 使用最新的3个版本
                "Mozilla/5.0 (Linux; $androidVersion; $phoneModel) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/$chromeVersion Mobile Safari/537.36"
            }
            // 抖音使用桌面端User-Agent
            url.contains("douyin.com") || url.contains("douyinvod.com") -> {
                generateDesktopUserAgent()
            }
            // B站使用桌面端Chrome
            url.contains("bilibili.com") || url.contains("bilivideo.com") -> {
                generateChromeDesktop()
            }
            // 微博使用移动端
            url.contains("weibo.com") || url.contains("sinaimg.cn") -> {
                generateChromeMobile()
            }
            // 其他网站随机选择
            else -> {
                generateRandomUserAgent()
            }
        }
    }

    /**
     * 获取适合特定网站的Referer
     */
    fun getRefererForUrl(url: String): String {
        return when {
            url.contains("xhscdn.com") || url.contains("xiaohongshu.com") -> {
                "https://www.xiaohongshu.com/"
            }
            url.contains("douyin.com") || url.contains("douyinvod.com") -> {
                "https://www.douyin.com/"
            }
            url.contains("bilibili.com") || url.contains("bilivideo.com") -> {
                "https://www.bilibili.com/"
            }
            url.contains("weibo.com") || url.contains("sinaimg.cn") -> {
                "https://weibo.com/"
            }
            else -> {
                "https://www.google.com/"
            }
        }
    }
}
