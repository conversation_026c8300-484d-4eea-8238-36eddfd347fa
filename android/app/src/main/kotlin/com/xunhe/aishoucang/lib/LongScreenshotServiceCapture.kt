package com.xunhe.aishoucang.lib

import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.media.Image
import android.media.ImageReader
import android.util.Log

/**
 * 长截图服务截图功能
 * 包含截图循环和图像处理的方法
 */
internal fun captureScreenshotLoop(service: LongScreenshotService) {
    if (service.isCancelled) {
        Log.d(LongScreenshotService.TAG, "长截图已取消，停止截图循环")
        service.stopLongScreenshot()
        return
    }

    // 更新进度
    val progress = ((service.currentScroll.toFloat() / service.totalScrolls.toFloat()) * 100).toInt()
    service.longScreenshotHelper?.updateProgress(progress, "正在截图 (${service.currentScroll + 1}/${service.totalScrolls})")

    // 显示暂停按钮（如果尚未显示）
    if (service.pauseButton?.isPauseButtonVisible() != true) {
        service.pauseButton?.showPauseButton {
            // 暂停按钮点击回调
            Log.d(LongScreenshotService.TAG, "暂停按钮被点击，立即停止截图并开始拼接")

            // 设置取消标志，确保停止后续的滚动操作
            service.isCancelled = true

            // 如果已经有截图，则开始拼接
            if (service.capturedBitmaps.isNotEmpty()) {
                // 取消所有待执行的延迟任务
                service.handler.removeCallbacksAndMessages(null)

                // 拼接图像
                stitchImages(service)
            } else {
                // 如果没有截图，则直接停止
                service.stopLongScreenshot()
            }
        }
    }

    // 截取当前屏幕
    captureCurrentScreen(service) { success ->
        if (!success || service.isCancelled) {
            Log.d(LongScreenshotService.TAG, "截图失败或已取消，停止截图循环")
            service.stopLongScreenshot()
            return@captureCurrentScreen
        }

        // 检查是否到达内容底部
        if (isAtContentBottom(service)) {
            Log.d(LongScreenshotService.TAG, "已到达内容底部，开始拼接图像")
            // 拼接图像
            stitchImages(service)
            return@captureCurrentScreen
        }

        // 滚动屏幕
        scrollScreen(service) { scrollSuccess ->
            if (!scrollSuccess || service.isCancelled) {
                Log.d(LongScreenshotService.TAG, "滚动失败或已取消，停止截图循环")
                service.stopLongScreenshot()
                return@scrollScreen
            }

            // 增加当前滚动计数
            service.currentScroll++

            // 等待内容加载
            service.handler.postDelayed({
                // 继续截图循环
                captureScreenshotLoop(service)
            }, 1000) // 等待1秒让内容加载
        }
    }
}

/**
 * 截取当前屏幕
 */
internal fun captureCurrentScreen(service: LongScreenshotService, callback: (Boolean) -> Unit) {
    try {
        // 检查是否已取消
        if (service.isCancelled) {
            Log.d(LongScreenshotService.TAG, "长截图已取消，跳过截图")
            callback(false)
            return
        }

        // 创建ImageReader
        service.imageReader = ImageReader.newInstance(
            service.screenWidth, service.screenHeight, PixelFormat.RGBA_8888, 2
        )

        // 创建虚拟显示
        service.virtualDisplay = service.mediaProjection?.createVirtualDisplay(
            "long_screenshot_display",
            service.screenWidth, service.screenHeight, service.screenDensity,
            DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
            service.imageReader?.surface, null, service.handler
        )

        // 延迟一段时间后获取图像，确保虚拟显示已经准备好
        service.handler.postDelayed({
            // 再次检查是否已取消
            if (service.isCancelled) {
                Log.d(LongScreenshotService.TAG, "延迟后发现长截图已取消，跳过截图")
                // 释放虚拟显示资源
                service.virtualDisplay?.release()
                service.virtualDisplay = null
                service.imageReader?.close()
                service.imageReader = null
                callback(false)
                return@postDelayed
            }

            captureImage(service) { success, bitmap ->
                // 释放虚拟显示资源
                service.virtualDisplay?.release()
                service.virtualDisplay = null
                service.imageReader?.close()
                service.imageReader = null

                // 再次检查是否已取消
                if (service.isCancelled) {
                    Log.d(LongScreenshotService.TAG, "截图完成后发现长截图已取消")
                    bitmap?.recycle()
                    callback(false)
                    return@captureImage
                }

                if (success && bitmap != null) {
                    // 裁剪安全区域
                    val safeAreaBitmap = cropSafeArea(service, bitmap)

                    // 修改：不再回收原始bitmap，因为现在cropSafeArea直接返回原始bitmap
                    // 如果safeAreaBitmap与bitmap不同，才回收原始bitmap
                    if (safeAreaBitmap != bitmap && safeAreaBitmap != null) {
                        bitmap.recycle()
                    }

                    if (safeAreaBitmap != null) {
                        // 直接使用裁剪后的截图，不进行压缩
                        service.capturedBitmaps.add(safeAreaBitmap)
                        Log.d(LongScreenshotService.TAG, "截图已添加到列表")
                        callback(true)
                    } else {
                        Log.e(LongScreenshotService.TAG, "裁剪安全区域失败")
                        callback(false)
                    }
                } else {
                    Log.e(LongScreenshotService.TAG, "截取当前屏幕失败")
                    callback(false)
                }
            }
        }, 300)
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "截取当前屏幕失败", e)
        callback(false)
    }
}

/**
 * 捕获图像
 */
internal fun captureImage(service: LongScreenshotService, callback: (Boolean, Bitmap?) -> Unit) {
    service.executor.execute {
        try {
            // 检查是否已取消
            if (service.isCancelled) {
                Log.d(LongScreenshotService.TAG, "长截图已取消，跳过图像捕获")
                service.handler.post {
                    callback(false, null)
                }
                return@execute
            }

            val image = service.imageReader?.acquireLatestImage()
            if (image == null) {
                service.handler.post {
                    callback(false, null)
                }
                return@execute
            }

            // 再次检查是否已取消
            if (service.isCancelled) {
                Log.d(LongScreenshotService.TAG, "获取图像后发现长截图已取消")
                image.close()
                service.handler.post {
                    callback(false, null)
                }
                return@execute
            }

            // 将Image转换为Bitmap
            val bitmap = imageToBitmap(service, image)
            image.close()

            if (bitmap == null) {
                service.handler.post {
                    callback(false, null)
                }
                return@execute
            }

            // 再次检查是否已取消
            if (service.isCancelled) {
                Log.d(LongScreenshotService.TAG, "转换Bitmap后发现长截图已取消")
                bitmap.recycle()
                service.handler.post {
                    callback(false, null)
                }
                return@execute
            }

            service.handler.post {
                callback(true, bitmap)
            }
        } catch (e: Exception) {
            Log.e(LongScreenshotService.TAG, "捕获图像失败", e)
            service.handler.post {
                callback(false, null)
            }
        }
    }
}

/**
 * 将Image转换为Bitmap
 */
internal fun imageToBitmap(service: LongScreenshotService, image: Image): Bitmap? {
    try {
        val planes = image.planes
        val buffer = planes[0].buffer
        val pixelStride = planes[0].pixelStride
        val rowStride = planes[0].rowStride
        val rowPadding = rowStride - pixelStride * service.screenWidth

        // 创建Bitmap
        val bitmap = Bitmap.createBitmap(
            service.screenWidth + rowPadding / pixelStride,
            service.screenHeight,
            Bitmap.Config.ARGB_8888
        )

        buffer.rewind()
        bitmap.copyPixelsFromBuffer(buffer)

        return bitmap
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "转换图像失败", e)
        return null
    }
}

/**
 * 裁剪安全区域
 * 修改：不再裁剪安全区域，直接返回原始bitmap
 */
internal fun cropSafeArea(service: LongScreenshotService, bitmap: Bitmap): Bitmap? {
    try {
        // 直接返回原始bitmap，不进行裁剪
        Log.d(LongScreenshotService.TAG, "不再裁剪安全区域，直接使用原始图像")
        return bitmap
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "处理图像失败", e)
        return null
    }
}

/**
 * 压缩Bitmap
 * @param bitmap 原始Bitmap
 * @param quality 压缩质量 (0-100)
 * @return 压缩后的Bitmap
 */
internal fun compressBitmap(bitmap: Bitmap, quality: Int): Bitmap {
    try {
        // 创建一个ByteArrayOutputStream
        val outputStream = java.io.ByteArrayOutputStream()

        // 使用指定的质量压缩Bitmap到输出流
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)

        // 将输出流转换为字节数组
        val byteArray = outputStream.toByteArray()

        // 关闭输出流
        outputStream.close()

        // 从字节数组创建新的Bitmap
        return android.graphics.BitmapFactory.decodeByteArray(byteArray, 0, byteArray.size)
    } catch (e: Exception) {
        Log.e(LongScreenshotService.TAG, "压缩Bitmap失败", e)
        // 如果压缩失败，返回原始Bitmap
        return bitmap
    }
}
