package com.xunhe.aishoucang.helpers

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.*
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.TextView
import com.xunhe.aishoucang.R

/**
 * 自定义Toast辅助类
 * 使用悬浮窗来模拟Android原生的Toast效果，支持自定义动画和显示时长
 */
object CustomToastHelper {
    private const val TAG = "CustomToastHelper"

    // 短时间显示，2秒
    const val LENGTH_SHORT = 2000

    // 长时间显示，3.5秒
    const val LENGTH_LONG = 3500

    // Toast视图
    private var toastView: View? = null

    // Toast内部容器（实际显示内容的容器）
    private var toastInnerContainer: View? = null

    // 窗口参数
    private var params: WindowManager.LayoutParams? = null

    // 窗口管理器
    private var windowManager: WindowManager? = null

    // 是否正在显示
    private var isShowing = false

    // 用于延迟关闭的Handler
    private val handler = Handler(Looper.getMainLooper())

    // 关闭Toast的Runnable
    private val hideRunnable = Runnable {
        Log.d(TAG, "延时结束，执行hideToast()")
        hideToastWithAnimation()
    }

    // 移除视图的Runnable
    private val removeViewRunnable = Runnable {
        Log.d(TAG, "动画延时结束，执行移除视图")
        hideToastImmediately()
    }

    /**
     * 显示Toast
     * @param context 上下文
     * @param message 要显示的消息
     * @param duration 显示时长，默认为短时间
     */
    fun showToast(context: Context, message: String, duration: Int = LENGTH_SHORT) {
        try {
            // 如果已经在显示，先移除旧的Toast
            if (isShowing) {
                Log.d(TAG, "已有Toast在显示，先移除")
                hideToastImmediately()
            }

            // 移除所有可能的回调
            handler.removeCallbacks(hideRunnable)
            handler.removeCallbacks(removeViewRunnable)

            // 获取窗口管理器
            if (windowManager == null) {
                windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            }

            // 创建Toast视图
            createToastView(context, message)

            // 添加到窗口
            windowManager?.addView(toastView, params)
            isShowing = true
            Log.d(TAG, "添加Toast到窗口成功，isShowing = $isShowing")

            // 开始显示动画 - 应用到内部容器
            val animation = AnimationUtils.loadAnimation(context, R.anim.toast_in)
            toastInnerContainer?.startAnimation(animation)

            // 设置定时关闭
            Log.d(TAG, "设置Toast显示时长: $duration 毫秒")
            handler.postDelayed(hideRunnable, duration.toLong())

        } catch (e: Exception) {
            Log.e(TAG, "显示Toast时出错", e)
        }
    }

    /**
     * 创建Toast视图
     */
    private fun createToastView(context: Context, message: String) {
        // 创建视图
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        toastView = inflater.inflate(R.layout.toast_layout, null)

        // 获取内部容器引用
        toastInnerContainer = toastView?.findViewById(R.id.toast_container)

        // 设置消息文本
        toastView?.findViewById<TextView>(R.id.toast_message)?.text = message

        // 添加点击事件，点击任意位置关闭Toast
        toastView?.setOnClickListener {
            Log.d(TAG, "Toast被点击，执行关闭")
            hideToastWithAnimation()
        }

        // 获取屏幕高度
        val displayMetrics = context.resources.displayMetrics
        val screenHeight = displayMetrics.heightPixels
        val screenWidth = displayMetrics.widthPixels

        // 计算35%的屏幕高度
        val toastHeight = (screenHeight * 0.35).toInt()
        Log.d(TAG, "屏幕高度: $screenHeight, Toast高度(35%): $toastHeight")

        // 设置窗口参数
        params = WindowManager.LayoutParams().apply {
            type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else WindowManager.LayoutParams.TYPE_PHONE
            format = PixelFormat.TRANSLUCENT
            // 使用FLAG_NOT_TOUCH_MODAL允许点击事件穿透到Toast外部
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
            width = screenWidth.toInt()
            // 设置高度为屏幕的35%
            height = toastHeight
            gravity = Gravity.BOTTOM
        }
    }

    /**
     * 使用动画隐藏Toast
     * 不依赖动画回调，而是使用定时器来确保视图被移除
     */
    private fun hideToastWithAnimation() {
        if (!isShowing || toastView == null) {
            Log.d(TAG, "hideToastWithAnimation: Toast不在显示或视图为空")
            return
        }

        try {
            Log.d(TAG, "开始执行Toast退出动画")
            val context = toastView?.context
            if (context != null) {
                // 播放退出动画 - 应用到内部容器，并使用动画监听器确保动画完成后立即移除视图
                val animation = AnimationUtils.loadAnimation(context, R.anim.toast_out)
                animation.setAnimationListener(object : Animation.AnimationListener {
                    override fun onAnimationStart(animation: Animation?) {
                        // 动画开始时不做任何操作
                    }

                    override fun onAnimationEnd(animation: Animation?) {
                        // 动画结束时立即移除视图
                        Log.d(TAG, "动画结束，立即移除视图")
                        hideToastImmediately()
                    }

                    override fun onAnimationRepeat(animation: Animation?) {
                        // 动画重复时不做任何操作
                    }
                })
                toastInnerContainer?.startAnimation(animation)

                // 作为备份措施，仍然设置一个延迟移除，但时间更长，确保不会干扰动画回调
                handler.removeCallbacks(removeViewRunnable)
                handler.postDelayed(removeViewRunnable, 400)
            } else {
                // 如果无法获取上下文，直接移除
                Log.d(TAG, "无法获取上下文，直接移除Toast")
                hideToastImmediately()
            }
        } catch (e: Exception) {
            Log.e(TAG, "隐藏Toast时出错", e)
            hideToastImmediately()
        }
    }

    /**
     * 立即隐藏Toast（无动画）
     */
    private fun hideToastImmediately() {
        Log.d(TAG, "开始执行hideToastImmediately")
        try {
            // 移除所有可能的回调
            handler.removeCallbacks(hideRunnable)
            handler.removeCallbacks(removeViewRunnable)

            if (toastView != null && toastView?.parent != null) {
                windowManager?.removeView(toastView)
                Log.d(TAG, "Toast视图成功从窗口移除")
            } else {
                Log.d(TAG, "Toast视图为空或没有父视图，无需移除")
            }
        } catch (e: Exception) {
            Log.e(TAG, "移除Toast视图时出错", e)
        } finally {
            toastView = null
            isShowing = false
            Log.d(TAG, "Toast变量已重置，isShowing = $isShowing")
        }
    }

    /**
     * 便捷方法：显示短时间Toast
     * @param context 上下文
     * @param message 要显示的消息
     */
    fun showShortToast(context: Context, message: String) {
        showToast(context, message, LENGTH_SHORT)
    }

    /**
     * 便捷方法：显示长时间Toast
     * @param context 上下文
     * @param message 要显示的消息
     */
    fun showLongToast(context: Context, message: String) {
        showToast(context, message, LENGTH_LONG)
    }
}