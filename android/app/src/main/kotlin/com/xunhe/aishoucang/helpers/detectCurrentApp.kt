package com.xunhe.aishoucang.helpers

import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo

/**
 * 应用检测工具类
 * 用于判断当前运行的应用类型
 */
object AppDetector {
    private const val TAG = "AppDetector"

    /**
     * 判断当前运行的APP类型
     *
     * @param rootNode 无障碍服务获取的根节点
     * @return 应用类型的中文名称
     */
    fun detectCurrentApp(rootNode: AccessibilityNodeInfo): String {
        val currentPackage = rootNode.packageName?.toString() ?: ""
        Log.d(TAG, "当前包名: $currentPackage")
        return when {
            currentPackage.startsWith("com.ss.android.ugc.aweme") -> "抖音"
            currentPackage.startsWith("com.kuaishou.nebula") ||
                    currentPackage.startsWith("com.smile.gifmaker") -> "快手"
            currentPackage.startsWith("com.xingin.xhs") -> "小红书"
            currentPackage.startsWith("tv.danmaku.bili") -> "B站"
            currentPackage.startsWith("com.tencent.mm") -> "微信"
            currentPackage.startsWith("com.sankuai.meituan") -> "美团"
            currentPackage.startsWith("com.douban.frodo") -> "豆瓣"
            currentPackage.startsWith("com.xunmeng.pinduoduo") -> "拼多多"
            currentPackage.startsWith("com.taobao.taobao") -> "淘宝"
            else -> "未知应用"
        }
    }
}
