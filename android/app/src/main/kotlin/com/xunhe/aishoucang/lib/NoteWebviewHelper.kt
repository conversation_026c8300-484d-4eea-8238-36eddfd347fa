package com.xunhe.aishoucang.lib

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebChromeClient
import com.xunhe.aishoucang.NoteWebviewActivity
import com.xunhe.aishoucang.helpers.ConfigHelper
import kotlinx.coroutines.*
import org.json.JSONObject

/**
 * 笔记详情数据类
 */
data class NoteDetail(
    val id: String,
    val parentId: String,
    val userId: String,
    val title: String,
    val cover: String,
    val desc: String,
    val content: String,
    val html: String,
    val createTime: String,
    val updateTime: String
)

/**
 * 笔记WebView辅助类
 * 用于展示从接口获取的HTML内容
 */
class NoteWebviewHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "NoteWebviewHelper"
        
        @Volatile
        private var instance: NoteWebviewHelper? = null
        
        fun getInstance(context: Context): NoteWebviewHelper {
            return instance ?: synchronized(this) {
                instance ?: NoteWebviewHelper(context.applicationContext).also { instance = it }
            }
        }
    }
    
    private val requestHelper = RequestHelper.getInstance(context)
    private val mainHandler = Handler(Looper.getMainLooper())
    
    /**
     * 打开笔记WebView
     * @param noteId 笔记ID
     * @param callback 回调函数，返回是否成功打开
     */
    fun openNoteWebview(
        noteId: String? = null,
        callback: (Boolean, String?) -> Unit
    ) {
        Log.d(TAG, "准备打开笔记WebView, noteId: $noteId")

        if (noteId.isNullOrEmpty()) {
            Log.w(TAG, "笔记ID为空，无法打开笔记详情")
            callback(false, "笔记ID不能为空")
            return
        }

        try {
            // 构建API URL
            val apiBaseUrl = ConfigHelper.getString("api_base_url")
            val apiUrl = "$apiBaseUrl/note/detail?id=$noteId"

            val intent = Intent(context, NoteWebviewActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                putExtra("note_id", noteId)
                putExtra("api_url", apiUrl)
            }

            context.startActivity(intent)
            callback(true, null)
            Log.d(TAG, "成功打开笔记WebView, API URL: $apiUrl")
        } catch (e: Exception) {
            Log.e(TAG, "打开笔记WebView失败", e)
            callback(false, "打开WebView失败: ${e.message}")
        }
    }
    
    /**
     * 从API获取笔记详情和HTML内容
     * @param apiUrl API地址
     * @param callback 回调函数，返回笔记详情数据或错误信息
     */
    fun fetchNoteDetailFromApi(
        apiUrl: String,
        callback: (NoteDetail?, String?) -> Unit
    ) {
        Log.d(TAG, "开始从API获取笔记详情: $apiUrl")

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = requestHelper.get(apiUrl)

                mainHandler.post {
                    when (result) {
                        is RequestHelper.ApiResult.Success -> {
                            try {
                                val jsonObject = JSONObject(result.data)

                                // 检查API响应格式
                                val code = jsonObject.optInt("code", -1)
                                if (code != 0) {
                                    val message = jsonObject.optString("message", "API返回错误")
                                    Log.e(TAG, "API返回错误: code=$code, message=$message")
                                    callback(null, message)
                                    return@post
                                }

                                // 解析data字段
                                val dataObject = jsonObject.optJSONObject("data")
                                if (dataObject == null) {
                                    Log.e(TAG, "API响应中缺少data字段")
                                    callback(null, "响应数据格式错误")
                                    return@post
                                }

                                // 解析笔记详情
                                val noteDetail = NoteDetail(
                                    id = dataObject.optString("id", ""),
                                    parentId = dataObject.optString("parent_id", ""),
                                    userId = dataObject.optString("user_id", ""),
                                    title = dataObject.optString("title", ""),
                                    cover = dataObject.optString("cover", ""),
                                    desc = dataObject.optString("desc", ""),
                                    content = dataObject.optString("content", ""),
                                    html = dataObject.optString("html", ""),
                                    createTime = dataObject.optString("create_time", ""),
                                    updateTime = dataObject.optString("update_time", "")
                                )

                                if (noteDetail.html.isNotEmpty()) {
                                    Log.d(TAG, "成功获取笔记详情和HTML内容")
                                    callback(noteDetail, null)
                                } else {
                                    Log.w(TAG, "API返回的HTML内容为空")
                                    callback(noteDetail, null) // 即使HTML为空也返回其他数据
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析API响应失败", e)
                                callback(null, "解析响应失败: ${e.message}")
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            Log.e(TAG, "API请求失败: ${result.message}")
                            callback(null, "请求失败: ${result.message}")
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "API请求异常", result.throwable)
                            callback(null, "请求异常: ${result.throwable.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取笔记详情时发生异常", e)
                mainHandler.post {
                    callback(null, "获取笔记详情失败: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 在WebView中加载HTML内容
     * @param webView WebView实例
     * @param htmlContent HTML内容
     * @param baseUrl 基础URL，用于相对路径资源加载
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun loadHtmlInWebView(
        webView: WebView,
        htmlContent: String,
        baseUrl: String? = null
    ) {
        Log.d(TAG, "在WebView中加载HTML内容")
        
        try {
            // 配置WebView
            webView.settings.apply {
                javaScriptEnabled = true
                domStorageEnabled = true
                loadsImagesAutomatically = true
                useWideViewPort = true
                loadWithOverviewMode = true
                setSupportZoom(true)
                builtInZoomControls = true
                displayZoomControls = false
            }
            
            // 设置WebViewClient
            webView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    Log.d(TAG, "HTML内容加载完成")
                }
                
                override fun onReceivedError(
                    view: WebView?,
                    errorCode: Int,
                    description: String?,
                    failingUrl: String?
                ) {
                    Log.e(TAG, "WebView加载错误: $description")
                }
            }
            
            // 设置WebChromeClient
            webView.webChromeClient = object : WebChromeClient() {
                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    Log.d(TAG, "HTML加载进度: $newProgress%")
                }
            }
            
            // 加载HTML内容
            val finalBaseUrl = baseUrl ?: "file:///android_asset/"
            webView.loadDataWithBaseURL(
                finalBaseUrl,
                htmlContent,
                "text/html",
                "UTF-8",
                null
            )
            
            Log.d(TAG, "HTML内容已加载到WebView")
        } catch (e: Exception) {
            Log.e(TAG, "在WebView中加载HTML失败", e)
        }
    }
}
