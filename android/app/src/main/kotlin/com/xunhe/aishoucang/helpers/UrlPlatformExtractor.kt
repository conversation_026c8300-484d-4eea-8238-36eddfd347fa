package com.xunhe.aishoucang.helpers

import android.util.Log
import java.net.URL

/**
 * URL平台识别工具类
 * 负责从URL中识别平台信息
 */
object UrlPlatformExtractor {
    private const val TAG = "UrlPlatformExtractor"

    /**
     * 从URL中提取平台信息
     *
     * @param url 要分析的URL
     * @return 平台标识字符串，如果无法识别则返回"unknown"
     */
    fun extractPlatformFromUrl(url: String): String {
        if (url.isEmpty()) {
            return "unknown"
        }

        try {
            val uri = URL(url)
            val host = uri.host?.lowercase() ?: ""
            
            Log.d(TAG, "分析URL: $url")
            Log.d(TAG, "主机名: $host")

            return when {
                // B站
                host.contains("bilibili.com") || host.contains("b23.tv") -> "bilibili"
                
                // 小红书
                host.contains("xiaohongshu.com") || host.contains("xhslink.com") -> "xiaohongshu"
                
                // 抖音
                host.contains("douyin.com") || host.contains("iesdouyin.com") -> "douyin"
                
                // 微信公众号
                host.contains("mp.weixin.qq.com") -> "wechat"
                
                // 快手
                host.contains("kuaishou.com") || host.contains("kwai.com") -> "kuaishou"
                
                // 豆瓣
                host.contains("douban.com") -> "douban"
                
                // 美团
                host.contains("meituan.com") -> "meituan"
                
                // 拼多多
                host.contains("yangkeduo.com") || host.contains("pinduoduo.com") -> "pinduoduo"
                
                // 淘宝
                host.contains("taobao.com") || host.contains("tmall.com") -> "taobao"
                
                // 京东
                host.contains("jd.com") -> "jingdong"
                
                // YouTube
                host.contains("youtube.com") || host.contains("youtu.be") -> "youtube"
                
                // 知乎
                host.contains("zhihu.com") -> "zhihu"
                
                // 微博
                host.contains("weibo.com") || host.contains("weibo.cn") -> "weibo"
                
                else -> {
                    Log.d(TAG, "无法识别的平台: $host")
                    "unknown"
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析URL时发生错误: $url", e)
            return "unknown"
        }
    }

    /**
     * 从URL中提取标题（简单实现，基于URL路径）
     *
     * @param url 要分析的URL
     * @return 提取的标题，如果无法提取则返回默认标题
     */
    fun extractTitleFromUrl(url: String): String {
        if (url.isEmpty()) {
            return "未知内容"
        }

        try {
            val uri = URL(url)
            val host = uri.host?.lowercase() ?: ""
            val path = uri.path ?: ""
            
            // 根据不同平台提取标题
            return when {
                host.contains("bilibili.com") || host.contains("b23.tv") -> "B站视频"
                host.contains("xiaohongshu.com") || host.contains("xhslink.com") -> "小红书笔记"
                host.contains("douyin.com") || host.contains("iesdouyin.com") -> "抖音视频"
                host.contains("mp.weixin.qq.com") -> "微信公众号文章"
                host.contains("kuaishou.com") || host.contains("kwai.com") -> "快手视频"
                host.contains("douban.com") -> "豆瓣内容"
                host.contains("youtube.com") || host.contains("youtu.be") -> "YouTube视频"
                host.contains("zhihu.com") -> "知乎内容"
                host.contains("weibo.com") || host.contains("weibo.cn") -> "微博内容"
                else -> "网页内容"
            }
        } catch (e: Exception) {
            Log.e(TAG, "从URL提取标题时发生错误: $url", e)
            return "网页内容"
        }
    }

    /**
     * 判断URL是否为视频内容
     *
     * @param url 要分析的URL
     * @return true如果是视频内容，否则返回false
     */
    fun isVideoContent(url: String): Boolean {
        if (url.isEmpty()) {
            return false
        }

        try {
            val uri = URL(url)
            val host = uri.host?.lowercase() ?: ""
            
            return when {
                // 视频平台
                host.contains("bilibili.com") ||
                host.contains("douyin.com") ||
                host.contains("kuaishou.com") ||
                host.contains("youtube.com") ||
                host.contains("youtu.be") -> true
                
                // 小红书可能包含视频
                host.contains("xiaohongshu.com") -> true
                
                else -> false
            }
        } catch (e: Exception) {
            Log.e(TAG, "判断视频内容时发生错误: $url", e)
            return false
        }
    }
}
