package com.xunhe.aishoucang.helpers

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.ViewGroup
import android.webkit.JavascriptInterface
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.lang.ref.WeakReference
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.CountDownLatch

/**
 * WebView HTML提取器
 * 用于打开WebView，注入findResult方法，等待JavaScript执行，然后立即释放WebView资源
 */
object WebViewHtmlExtractor {
    private const val TAG = "WebViewHtmlExtractor"

    // 共享线程池，避免每次调用都创建新的线程池
    private val sharedExecutor = Executors.newSingleThreadExecutor()

    // 超时时间常量
    private const val TIMEOUT_MS = 10000L  // 10秒超时，与JavaScript中的超时时间保持一致

    // WebView销毁延迟时间
    private const val WEBVIEW_DESTROY_DELAY_MS = 100L // 销毁WebView前的延迟时间

    // JavaScript文件路径常量
    private const val JS_EXTRACT_HTML = "js/extract_html.js"
    private const val JS_WAIT_FOR_NODE = "js/wait_for_node.js"

    // 业务特定的JavaScript文件路径前缀
    private const val JS_BUSINESS_PREFIX = "js/"

    /**
     * JavaScript接口类，用于注入findResult方法
     */
    class JavaScriptInterface(private val resultCallback: (String) -> Unit) {
        @JavascriptInterface
        fun findResult(result: String) {
            Log.d(TAG, "JavaScript调用了findResult方法，参数: $result")
            resultCallback(result)
        }
    }

    /**
     * 从assets目录加载JavaScript文件
     *
     * @param context 上下文
     * @param filePath JavaScript文件路径
     * @param replacements 替换参数的键值对
     * @return 加载的JavaScript代码
     */
    private fun loadJavaScriptFromAssets(
        context: Context,
        filePath: String,
        replacements: Map<String, String> = emptyMap()
    ): String {
        try {
            context.assets.open(filePath).use { inputStream ->
                BufferedReader(InputStreamReader(inputStream)).use { reader ->
                    val stringBuilder = StringBuilder()
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        stringBuilder.append(line).append("\n")
                    }

                    // 替换参数
                    var jsCode = stringBuilder.toString()
                    for ((key, value) in replacements) {
                        jsCode = jsCode.replace("\${$key}", value)
                    }

                    return jsCode
                }
            }
        } catch (e: IOException) {
            Log.e(TAG, "加载JavaScript文件失败: $filePath", e)
            return "Android.findResult('加载JavaScript文件失败: $filePath');"
        }
    }

    /**
     * 安全地销毁WebView，最大程度减少销毁后的活动
     *
     * @param webView 要销毁的WebView
     * @param handler 主线程Handler
     * @param onDestroyed 销毁完成后的回调
     */
    private fun safeDestroyWebView(webView: WebView?, handler: Handler, onDestroyed: () -> Unit) {
        if (webView == null) {
            onDestroyed()
            return
        }

        try {
            // 使用弱引用避免内存泄漏
            val weakWebView = WeakReference(webView)

            // 在主线程中执行清理操作
            handler.post {
                try {
                    val view = weakWebView.get()
                    if (view != null) {
                        // 停止加载
                        view.stopLoading()

                        // 移除所有JavaScript接口
                        try {
                            view.removeJavascriptInterface("Android")
                        } catch (e: Exception) {
                            Log.w(TAG, "移除JavaScript接口时出错: ${e.message}")
                        }

                        // 加载空白页面
                        view.loadUrl("about:blank")

                        // 清除WebView状态
                        view.clearHistory()
                        view.clearCache(true)
                        view.clearFormData()
                        view.clearSslPreferences()
                        view.clearMatches()

                        // 不需要显式设置WebViewClient和WebChromeClient为null
                        // 在WebView销毁时，这些引用会自动被清理

                        // 短暂延迟后再销毁
                        handler.postDelayed({
                            try {
                                val delayedView = weakWebView.get()
                                if (delayedView != null) {
                                    // 从父视图中移除
                                    val parent = delayedView.parent
                                    if (parent != null) {
                                        try {
                                            (parent as ViewGroup).removeView(delayedView)
                                        } catch (e: Exception) {
                                            Log.w(TAG, "从父视图移除WebView时出错: ${e.message}")
                                        }
                                    }

                                    // 最后销毁
                                    delayedView.destroy()
                                    Log.d(TAG, "WebView已安全销毁")
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "销毁WebView时出错: ${e.message}", e)
                            } finally {
                                // 确保回调一定会执行
                                onDestroyed()
                            }
                        }, WEBVIEW_DESTROY_DELAY_MS)
                    } else {
                        // WebView已经被回收，直接执行回调
                        onDestroyed()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "准备销毁WebView时出错: ${e.message}", e)
                    onDestroyed()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "销毁WebView过程中出错: ${e.message}", e)
            onDestroyed()
        }
    }

    /**
     * 使用WebView加载URL并执行JavaScript，等待findResult方法被调用
     *
     * @param context 上下文
     * @param url 要加载的URL
     * @param jsToExecute 要执行的JavaScript代码
     * @param callback 回调函数，返回findResult的参数和可能的错误信息
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun executeJavaScriptWithCallback(
        context: Context,
        url: String,
        jsToExecute: String,
        callback: (result: String?, error: String?) -> Unit
    ) {
        // 使用原子布尔值跟踪是否已完成
        val isCompleted = AtomicBoolean(false)

        // 使用CountDownLatch等待JavaScript执行完成
        val latch = CountDownLatch(1)

        // 存储JavaScript执行结果
        var jsResult: String? = null

        // 存储拦截到的scheme URL
        var interceptedSchemeUrl: String? = null

        // 主线程Handler
        val mainHandler = Handler(Looper.getMainLooper())

        // 超时处理
        var timeoutRunnable: Runnable? = null

        // 在共享线程池中执行WebView操作
        sharedExecutor.execute {
            var webView: WebView? = null

            try {
                // 设置超时处理
                timeoutRunnable = Runnable {
                    if (!isCompleted.getAndSet(true)) {
                        Log.e(TAG, "JavaScript执行超时")
                        latch.countDown() // 释放等待的线程

                        // 安全销毁WebView
                        safeDestroyWebView(webView, mainHandler) {
                            webView = null
                            callback(null, "网络请求超时，请稍后重试")
                        }
                    }
                }

                // 在主线程中创建和配置WebView
                mainHandler.post {
                    try {
                        // 创建WebView
                        webView = WebView(context)

                        // 配置WebView设置
                        val settings = webView?.settings
                        settings?.javaScriptEnabled = true
                        settings?.domStorageEnabled = true
                        settings?.allowFileAccess = true
                        settings?.allowContentAccess = true
                        settings?.loadsImagesAutomatically = true

                        // 添加JavaScript接口
                        webView?.addJavascriptInterface(JavaScriptInterface { result ->
                            // JavaScript调用了findResult方法
                            if (!isCompleted.getAndSet(true)) {
                                // 保存结果
                                jsResult = result

                                // 取消超时任务
                                timeoutRunnable?.let { mainHandler.removeCallbacks(it) }

                                // 立即销毁WebView
                                safeDestroyWebView(webView, mainHandler) {
                                    webView = null

                                    // 检查是否有拦截到的scheme URL
                                    if (interceptedSchemeUrl != null) {
                                        // 将拦截到的scheme URL添加到结果中
                                        val resultWithScheme = result + "\nInterceptedSchemeUrl: " + interceptedSchemeUrl
                                        callback(resultWithScheme, null)
                                    } else {
                                        callback(result, null)
                                    }
                                }

                                // 释放等待的线程
                                latch.countDown()
                            }
                        }, "Android")

                        // 使用原子布尔值跟踪JavaScript是否已执行
                        val jsExecuted = AtomicBoolean(false)

                        // 设置WebViewClient
                        webView?.webViewClient = object : WebViewClient() {
                            override fun shouldOverrideUrlLoading(
                                view: WebView,
                                request: WebResourceRequest
                            ): Boolean {
                                val scheme = request.url.scheme
                                val urlString = request.url.toString()

                                // 拦截非HTTP(S)开头的跳转
                                if (scheme == null || (!scheme.startsWith("http"))) {
                                    Log.d(TAG, "拦截原生scheme URL跳转: $urlString")

                                    // 记录scheme URL但不允许跳转
                                    interceptedSchemeUrl = urlString

                                    // 如果已经获取到JavaScript结果，可以考虑提前完成
                                    if (jsResult != null && !isCompleted.getAndSet(true)) {
                                        // 取消超时任务
                                        timeoutRunnable?.let { mainHandler.removeCallbacks(it) }

                                        // 立即销毁WebView
                                        safeDestroyWebView(webView, mainHandler) {
                                            webView = null
                                            // 将拦截到的scheme URL添加到结果中
                                            val resultWithScheme = jsResult + "\nInterceptedSchemeUrl: " + interceptedSchemeUrl
                                            callback(resultWithScheme, null)
                                        }

                                        // 释放等待的线程
                                        latch.countDown()
                                    }

                                    return true // 返回true表示WebView不处理这个URL
                                }

                                // 允许HTTP(S)重定向
                                Log.d(TAG, "允许重定向: $urlString")

                                // 如果配置了重定向时重置JavaScript执行状态
                                jsExecuted.set(false)
                                Log.d(TAG, "检测到重定向，重置JavaScript执行状态")

                                return false // 返回false让WebView处理http和https协议
                            }

                            override fun shouldInterceptRequest(view: WebView, request: WebResourceRequest): WebResourceResponse? {
                                // 不拦截资源请求
                                return super.shouldInterceptRequest(view, request)
                            }

                            override fun onPageFinished(view: WebView, loadedUrl: String) {
                                // 确保JavaScript只执行一次
                                if (!jsExecuted.getAndSet(true)) {
                                    Log.d(TAG, "页面加载完成，执行JavaScript: $loadedUrl")

                                    // 执行JavaScript代码
                                    view.evaluateJavascript(jsToExecute, null)
                                } else {
                                    Log.d(TAG, "页面加载完成，但JavaScript已执行，跳过: $loadedUrl")
                                }
                            }
                        }

                        // 加载URL
                        webView?.loadUrl(url)

                        // 设置超时
                        mainHandler.postDelayed(timeoutRunnable!!, TIMEOUT_MS)
                    } catch (e: Exception) {
                        Log.e(TAG, "WebView创建或配置失败: ${e.message}", e)
                        if (!isCompleted.getAndSet(true)) {
                            latch.countDown() // 释放等待的线程
                            callback(null, "网络连接异常，请检查网络后重试")
                        }
                    }
                }

                // 等待JavaScript执行完成或超时
                try {
                    latch.await() // 阻塞等待JavaScript执行完成
                } catch (e: InterruptedException) {
                    Log.e(TAG, "等待JavaScript执行被中断: ${e.message}", e)
                    if (!isCompleted.getAndSet(true)) {
                        // 取消超时任务
                        timeoutRunnable?.let { mainHandler.removeCallbacks(it) }

                        // 安全销毁WebView
                        mainHandler.post {
                            safeDestroyWebView(webView, mainHandler) {
                                webView = null
                                callback(null, "收藏中断，请重试")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "执行JavaScript过程中出错: ${e.message}", e)
                if (!isCompleted.getAndSet(true)) {
                    // 取消超时任务
                    timeoutRunnable?.let { mainHandler.removeCallbacks(it) }

                    // 安全销毁WebView
                    mainHandler.post {
                        safeDestroyWebView(webView, mainHandler) {
                            webView = null
                            callback(null, "处理过程中出现异常，请重试")
                        }
                    }

                    // 释放等待的线程
                    latch.countDown()
                }
            }
        }
    }

    /**
     * 使用WebView加载URL并轮询检查指定节点是否存在
     * 一旦节点出现就调用findResult方法
     *
     * @param context 上下文
     * @param url 要加载的URL
     * @param selector CSS选择器，用于查找目标节点（例如：".goods-name"）
     * @param callback 回调函数，返回findResult的参数和可能的错误信息
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun waitForNodeAndExecute(
        context: Context,
        url: String,
        selector: String,
        callback: (result: String?, error: String?) -> Unit
    ) {
        // 加载JavaScript文件并替换参数
        val jsToExecute = loadJavaScriptFromAssets(
            context,
            JS_WAIT_FOR_NODE,
            mapOf("selector" to selector)
        )

        // 使用executeJavaScriptWithCallback方法执行JavaScript
        executeJavaScriptWithCallback(context, url, jsToExecute, callback)
    }

    /**
     * 使用WebView加载URL并提取HTML内容
     *
     * @param context 上下文
     * @param url 要加载的URL
     * @param callback 回调函数，返回提取的HTML内容和可能的错误信息
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun extractHtmlFromUrl(
        context: Context,
        url: String,
        callback: (html: String?, error: String?) -> Unit
    ) {
        // 加载JavaScript文件
        val jsToExecute = loadJavaScriptFromAssets(context, JS_EXTRACT_HTML)

        // 使用executeJavaScriptWithCallback方法执行JavaScript
        executeJavaScriptWithCallback(context, url, jsToExecute, callback)
    }

    /**
     * 使用WebView加载URL并执行自定义JavaScript
     *
     * @param context 上下文
     * @param url 要加载的URL
     * @param jsFilePath JavaScript文件路径（相对于assets目录）
     * @param replacements 替换参数的键值对
     * @param callback 回调函数，返回findResult的参数和可能的错误信息
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun executeCustomJavaScript(
        context: Context,
        url: String,
        jsFilePath: String,
        replacements: Map<String, String> = emptyMap(),
        callback: (result: String?, error: String?) -> Unit
    ) {
        // 加载JavaScript文件并替换参数
        val jsToExecute = loadJavaScriptFromAssets(context, jsFilePath, replacements)

        // 使用executeJavaScriptWithCallback方法执行JavaScript
        executeJavaScriptWithCallback(context, url, jsToExecute, callback)
    }

    /**
     * 使用WebView加载URL并执行业务特定的JavaScript
     * 业务特定的JavaScript文件应该放在assets/js/目录下，并且文件名应该是业务名称，例如：XiaoHongshuGoods.js
     *
     * @param context 上下文
     * @param url 要加载的URL
     * @param businessName 业务名称，例如：XiaoHongshuGoods
     * @param replacements 替换参数的键值对
     * @param callback 回调函数，返回findResult的参数和可能的错误信息
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun executeBusinessJavaScript(
        context: Context,
        url: String,
        businessName: String,
        replacements: Map<String, String> = emptyMap(),
        callback: (result: String?, error: String?) -> Unit
    ) {
        // 构建业务特定的JavaScript文件路径
        val jsFilePath = "$JS_BUSINESS_PREFIX$businessName.js"

        // 加载JavaScript文件并替换参数
        val jsToExecute = loadJavaScriptFromAssets(context, jsFilePath, replacements)

        // 使用executeJavaScriptWithCallback方法执行JavaScript
        executeJavaScriptWithCallback(context, url, jsToExecute, callback)
    }
}