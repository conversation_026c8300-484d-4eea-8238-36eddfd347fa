package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import android.view.WindowManager
import com.xunhe.aishoucang.lib.AccessibilityHelper
import java.util.regex.Pattern

/**
 * 抖音应用处理器
 */
class DouyinAppHandler : AppHandler {
    companion object {
        private const val TAG = "DouyinAppHandler"

        // 分享容器的资源ID
        private const val SHARE_CONTAINER_ID = "com.ss.android.ugc.aweme:id/share_container"

        // 分享链接按钮文本的正则表达式模式 - 匹配包含"分享链接"的文本
        private val SHARE_LINK_BUTTON_PATTERN = Pattern.compile(".*分享链接.*")
    }

    // 保存找到的分享容器，供后续使用
    private var cachedShareContainer: AccessibilityNodeInfo? = null

    /**
     * 查找匹配正则表达式的文本节点
     *
     * @param service 无障碍服务实例
     * @param pattern 正则表达式模式
     * @return 匹配的节点列表
     */
    private fun findNodesByTextPattern(service: AccessibilityHelper.AppAccessibilityService, pattern: Pattern): List<AccessibilityNodeInfo> {
        val rootNode = service.rootInActiveWindow ?: return emptyList()
        val result = mutableListOf<AccessibilityNodeInfo>()

        try {
            findNodesByTextPatternRecursive(rootNode, pattern, result)
        } catch (e: Exception) {
            Log.e(TAG, "查找匹配正则表达式的文本节点时出错: ${e.message}", e)
        }

        return result
    }

    /**
     * 递归查找匹配正则表达式的文本节点
     */
    private fun findNodesByTextPatternRecursive(
        node: AccessibilityNodeInfo,
        pattern: Pattern,
        result: MutableList<AccessibilityNodeInfo>
    ) {
        try {
            // 检查当前节点的文本内容是否匹配正则表达式
            val nodeText = node.text?.toString()
            if (nodeText != null && pattern.matcher(nodeText).matches()) {
                Log.d(TAG, "找到匹配正则的文本节点: $nodeText")
                result.add(AccessibilityNodeInfo.obtain(node))
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                node.getChild(i)?.let { child ->
                    findNodesByTextPatternRecursive(child, pattern, result)
                    child.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "递归查找匹配正则表达式的文本节点时出错", e)
        }
    }

    /**
     * 查找所有指定资源ID的节点并打印详细信息
     * 使用原生Android无障碍方法查找，并过滤可见的节点
     *
     * @param service 无障碍服务实例
     * @param resourceId 资源ID
     * @return 匹配且可见的节点列表
     */
    private fun findAllNodesByResourceIdAndPrint(service: AccessibilityHelper.AppAccessibilityService, resourceId: String): List<AccessibilityNodeInfo> {
        val rootNode = service.rootInActiveWindow ?: return emptyList()

        try {
            Log.d(TAG, "=== 查找资源ID: $resourceId 的所有节点 ===")

            // 使用原生Android无障碍方法查找所有匹配的节点
            val allNodes = rootNode.findAccessibilityNodeInfosByViewId(resourceId) ?: emptyList()

            Log.d(TAG, "原生方法找到 ${allNodes.size} 个匹配的节点")

            // 过滤出可见的节点
            val visibleNodes = allNodes.filter { node ->
                val isVisible = node.isVisibleToUser
                Log.d(TAG, "节点可见性检查: 资源ID=${node.viewIdResourceName}, 可见=$isVisible")
                isVisible
            }

            Log.d(TAG, "过滤后可见的节点数量: ${visibleNodes.size}")

            // 打印所有可见节点的详细信息
            visibleNodes.forEachIndexed { index, node ->
                printNodeDetails(node, index, true)
            }

            // 打印不可见节点的基本信息（用于调试）
            val invisibleNodes = allNodes.filter { !it.isVisibleToUser }
            if (invisibleNodes.isNotEmpty()) {
                Log.d(TAG, "=== 不可见的节点信息（调试用） ===")
                invisibleNodes.forEachIndexed { index, node ->
                    printNodeDetails(node, index, false)
                }
            }

            Log.d(TAG, "=== 节点查找和打印完成 ===")

            return visibleNodes
        } catch (e: Exception) {
            Log.e(TAG, "查找和打印资源ID节点时出错: ${e.message}", e)
            return emptyList()
        }
    }

    /**
     * 打印节点的详细信息
     *
     * @param node 要打印的节点
     * @param index 节点索引
     * @param isVisible 节点是否可见（用于日志标识）
     */
    private fun printNodeDetails(node: AccessibilityNodeInfo, index: Int, isVisible: Boolean) {
        try {
            val rect = android.graphics.Rect()
            node.getBoundsInScreen(rect)

            val visibilityTag = if (isVisible) "可见" else "不可见"
            Log.d(TAG, "--- ${visibilityTag}节点 #${index + 1} 详细信息 ---")
            Log.d(TAG, "资源ID: ${node.viewIdResourceName}")
            Log.d(TAG, "类名: ${node.className}")
            Log.d(TAG, "文本: ${node.text}")
            Log.d(TAG, "描述: ${node.contentDescription}")
            Log.d(TAG, "包名: ${node.packageName}")
            Log.d(TAG, "边界: $rect")
            Log.d(TAG, "可点击: ${node.isClickable}")
            Log.d(TAG, "可滚动: ${node.isScrollable}")
            Log.d(TAG, "可聚焦: ${node.isFocusable}")
            Log.d(TAG, "已启用: ${node.isEnabled}")
            Log.d(TAG, "已选中: ${node.isSelected}")
            Log.d(TAG, "可见: ${node.isVisibleToUser}")
            Log.d(TAG, "子节点数: ${node.childCount}")

            // 打印父节点信息
            val parent = node.parent
            if (parent != null) {
                Log.d(TAG, "父节点类名: ${parent.className}")
                Log.d(TAG, "父节点资源ID: ${parent.viewIdResourceName}")
                Log.d(TAG, "父节点可见: ${parent.isVisibleToUser}")
                parent.recycle()
            } else {
                Log.d(TAG, "父节点: null")
            }

            Log.d(TAG, "--- ${visibilityTag}节点 #${index + 1} 信息结束 ---")
        } catch (e: Exception) {
            Log.e(TAG, "打印节点详细信息时出错: ${e.message}", e)
        }
    }

    /**
     * 判断是否为抖音短视频页面
     *
     * 通过检查页面是否存在可见的分享容器(com.ss.android.ugc.aweme:id/share_container)来判断
     */
    private fun isDouyinShortVideoPage(context: Context): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        // 查找并打印所有可见的分享容器节点
        val visibleShareContainerNodes = findAllNodesByResourceIdAndPrint(service, SHARE_CONTAINER_ID)

        // 使用第一个可见的节点作为主要的分享容器
        val shareContainerNode = visibleShareContainerNodes.firstOrNull()
        val isShortVideoPage = shareContainerNode != null

        Log.d(TAG, "找到可见的分享容器节点数量: ${visibleShareContainerNodes.size}")
        Log.d(TAG, "是否为抖音短视频页面: $isShortVideoPage")

        // 如果找到了可见的分享容器，缓存第一个供后续使用
        if (shareContainerNode != null) {
            cachedShareContainer = shareContainerNode
            Log.d(TAG, "已缓存第一个可见的分享容器节点")
        } else {
            Log.d(TAG, "未找到可见的分享容器节点")
        }

        // 回收除了第一个之外的其他节点资源
        visibleShareContainerNodes.drop(1).forEach { node ->
            try {
                node.recycle()
            } catch (e: Exception) {
                Log.e(TAG, "回收分享容器节点资源时出错: ${e.message}")
            }
        }

        return isShortVideoPage
    }

    override fun handle(context: Context) {
        Log.d(TAG, "开始处理抖音页面")

        val isShortVideoPage = isDouyinShortVideoPage(context)

        if (isShortVideoPage) {
            Log.d(TAG, "检测到抖音短视频页面")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.DOUYIN_TYPE_SHORT_VIDEO)
            handleCommonVideo(context)
        } else if (isDouyinGoodsPage(context)) {
            Log.d(TAG, "检测到抖音商品页面")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.DOUYIN_TYPE_GOODS)
            handleCommonGoods(context)
        } else {
            Log.d(TAG, "不是抖音短视频页面")
            CustomToastHelper.showToast(context, "抖音当前页面暂不支持收藏")
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            SharePanelHelper.hideSharePanel(context, windowManager, false)
        }
    }

    /**
     * 判断当前页面是否为抖音商品页面
     *
     * 通过检查页面是否同时存在"购物车"、"客服"、"进店"等商品页面特有的按钮来判断
     * 参考小红书的实现逻辑，使用原生Android无障碍API
     * 需要同时满足多个条件才判断为商品页面
     *
     * @param context 上下文
     * @return 是否为商品页面
     */
    private fun isDouyinGoodsPage(context: Context): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        try {
            var hasCartButton = false
            var hasServiceButton = false
            var hasShopButton = false

            // 查找文本为"购物车"的节点
            val cartNodes = service.findNodesByContent("购物车")
            if (cartNodes != null && cartNodes.isNotEmpty()) {
                Log.d(TAG, "找到'购物车'按钮")
                hasCartButton = true
                // 释放资源
                cartNodes.forEach { it.recycle() }
            }

            // 查找文本为"客服"的节点
            val serviceNodes = service.findNodesByContent("客服")
            if (serviceNodes != null && serviceNodes.isNotEmpty()) {
                Log.d(TAG, "找到'客服'按钮")
                hasServiceButton = true
                // 释放资源
                serviceNodes.forEach { it.recycle() }
            }

            // 查找文本为"进店"的节点
            val shopNodes = service.findNodesByContent("进店")
            if (shopNodes != null && shopNodes.isNotEmpty()) {
                Log.d(TAG, "找到'进店'按钮")
                hasShopButton = true
                // 释放资源
                shopNodes.forEach { it.recycle() }
            }

            // 需要同时存在购物车、客服、进店三个按钮才判断为商品页面
            val isGoodsPage = hasCartButton && hasServiceButton && hasShopButton

            Log.d(TAG, "抖音商品页面判断结果: 购物车=$hasCartButton, 客服=$hasServiceButton, 进店=$hasShopButton, 最终结果=$isGoodsPage")

            return isGoodsPage
        } catch (e: Exception) {
            Log.e(TAG, "判断是否为抖音商品页面时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 处理抖音商品页面
     *
     * 实现逻辑：
     * 1. 查找并点击分享按钮
     * 2. 等待分享面板出现
     * 3. 查找"复制链接"按钮并直接点击（抖音特殊：不点击父节点）
     * 4. 通知内容已准备好
     *
     * @param context 上下文
     */
    private fun handleCommonGoods(context: Context) {
        Log.d(TAG, "开始处理抖音商品页面")

        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return
        val rootNode = service.rootInActiveWindow ?: return

        try {
            // 1. 查找并点击分享按钮（使用desc属性）
            val helper = AccessibilityHelper.getInstance(context)
            val shareNodes = helper.findNodesByDescription("分享")
            if (shareNodes == null || shareNodes.isEmpty()) {
                Log.e(TAG, "未找到desc为'分享'的按钮")
                CustomToastHelper.showToast(context, "未找到分享按钮，请稍后再试")
                hideSharePanel(context)
                return
            }

            val shareNode = shareNodes[0]
            Log.d(TAG, "找到desc为'分享'的按钮，准备点击")
            val shareClickResult = shareNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            Log.d(TAG, "点击分享按钮结果: $shareClickResult")

            // 释放其余分享节点资源
            shareNodes.forEach { it.recycle() }

            if (!shareClickResult) {
                Log.e(TAG, "点击分享按钮失败")
                CustomToastHelper.showToast(context, "点击分享按钮失败，请重试")
                hideSharePanel(context)
                return
            }

            // 2. 启动子线程轮询检查复制链接节点
            startPollingForCopyLinkButton(context, helper)

        } catch (e: Exception) {
            Log.e(TAG, "处理抖音商品页面时出错: ${e.message}", e)
            CustomToastHelper.showToast(context, "处理抖音商品页面时出错，请重试")
            hideSharePanel(context)
        } finally {
            // 确保根节点资源被回收
            try {
                rootNode.recycle()
            } catch (e: Exception) {
                Log.e(TAG, "回收根节点资源时出错: ${e.message}")
            }
        }
    }

    private fun handleCommonVideo(context: Context) {
        Log.d(TAG, "开始处理视频")

        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return
        val rootNode = service.rootInActiveWindow ?: return

        // 使用之前缓存的分享容器
        if (cachedShareContainer == null) {
            Log.e(TAG, "没有缓存的分享容器，无法继续操作")
            rootNode.recycle()
            hideSharePanel(context)
            return
        }

        // 获取分享容器的父节点
        val parentNode = cachedShareContainer?.parent
        if (parentNode == null) {
            Log.e(TAG, "分享容器的父节点为空，无法继续操作")
            rootNode.recycle()
            hideSharePanel(context)
            return
        }

        Log.d(TAG, "点击分享容器的父节点")
        val clickResult = parentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
        Log.d(TAG, "点击分享容器父节点结果: $clickResult")

        // 等待分享面板出现
        Thread.sleep(500)

        try {
            val updatedRoot = service.rootInActiveWindow ?: return
            val shareLinkNodes = findNodesByTextPattern(service, SHARE_LINK_BUTTON_PATTERN)
            Log.d(TAG, "找到 ${shareLinkNodes.size} 个分享链接按钮")

            if (shareLinkNodes.isNotEmpty()) {
                val targetShareButton = shareLinkNodes[0]
                val parent = targetShareButton.parent
                val grandParent = parent?.parent

                if (grandParent != null) {
                    Log.d(TAG, "点击分享链接按钮")
                    grandParent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    SharePanelHelper.notifyContentReady()
                } else {
                    Log.e(TAG, "分享链接按钮的祖父节点为空")
                    hideSharePanel(context)
                }

                // 回收资源
                parent?.recycle()
                grandParent?.recycle()
            } else {
                Log.e(TAG, "没有找到分享链接按钮")
                hideSharePanel(context)
            }

            // 回收节点资源
            shareLinkNodes.forEach { it.recycle() }
            updatedRoot.recycle()
        } catch (e: Exception) {
            Log.e(TAG, "处理分享面板时出错: ${e.message}", e)
            hideSharePanel(context)
        } finally {
            // 清理资源
            parentNode.recycle()
            cachedShareContainer?.recycle()
            cachedShareContainer = null
            rootNode.recycle()
        }
    }

    private fun hideSharePanel(context: Context) {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        SharePanelHelper.hideSharePanel(context, windowManager, false)
    }

    /**
     * 启动子线程轮询检查复制链接节点
     * 每100ms检查一次，最多检查10s
     *
     * @param context 上下文
     * @param helper 无障碍助手实例
     */
    private fun startPollingForCopyLinkButton(context: Context, helper: AccessibilityHelper) {
        Log.d(TAG, "开始轮询检查复制链接节点")

        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        var pollCount = 0
        val maxPollCount = 100 // 10s / 100ms = 100次
        val pollInterval = 100L // 100ms

        val pollRunnable = object : Runnable {
            override fun run() {
                pollCount++
                Log.d(TAG, "轮询检查复制链接节点，第${pollCount}次/共${maxPollCount}次")

                try {
                    // 优化查找逻辑：同时查找desc属性和text属性为"复制链接"的节点
                    var copyNode: AccessibilityNodeInfo? = null
                    var isDescriptionNode = false

                    // 获取原生无障碍服务实例
                    val service = AccessibilityHelper.AppAccessibilityService.getInstance()
                    if (service == null) {
                        Log.e(TAG, "无障碍服务实例为空，停止轮询")
                        CustomToastHelper.showToast(context, "无障碍服务未启用，请重试")
                        hideSharePanel(context)
                        return
                    }

                    // 首先查找desc属性为"复制链接"的节点
                    val copyNodesByDesc = helper.findNodesByDescription("复制链接")
                    if (copyNodesByDesc != null && copyNodesByDesc.isNotEmpty()) {
                        Log.d(TAG, "找到desc为'复制链接'的按钮")
                        copyNode = copyNodesByDesc[0]
                        isDescriptionNode = true

                        // 释放其余desc节点资源
                        copyNodesByDesc.drop(1).forEach { it.recycle() }
                    } else {
                        // 如果没找到desc属性的节点，再查找text属性为"复制链接"的节点（使用原生API）
                        val copyNodesByText = service.findNodesByContent("复制链接")
                        if (copyNodesByText != null && copyNodesByText.isNotEmpty()) {
                            Log.d(TAG, "找到text为'复制链接'的按钮")
                            copyNode = copyNodesByText[0]
                            isDescriptionNode = false

                            // 释放其余text节点资源
                            copyNodesByText.drop(1).forEach { it.recycle() }
                        }
                    }

                    if (copyNode != null) {
                        Log.d(TAG, "找到复制链接按钮，停止轮询。节点类型: ${if (isDescriptionNode) "desc属性" else "text属性"}")

                        val copyClickResult = if (isDescriptionNode) {
                            // desc属性的复制链接节点：直接点击它本身
                            Log.d(TAG, "准备直接点击desc属性的复制链接按钮")
                            copyNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        } else {
                            // text属性的复制链接节点：智能点击逻辑
                            Log.d(TAG, "准备智能点击text属性的复制链接按钮")

                            // 先检查复制链接节点本身是否可点击
                            if (copyNode.isClickable) {
                                Log.d(TAG, "复制链接节点本身可点击，直接点击")
                                copyNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                            } else {
                                Log.d(TAG, "复制链接节点本身不可点击，检查父节点")
                                val parentNode = copyNode.parent

                                if (parentNode != null) {
                                    if (parentNode.isClickable) {
                                        Log.d(TAG, "父节点可点击，点击父节点")
                                        val result = parentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                                        parentNode.recycle()
                                        result
                                    } else {
                                        Log.d(TAG, "父节点不可点击，检查祖父节点")
                                        val grandParentNode = parentNode.parent

                                        if (grandParentNode != null && grandParentNode.isClickable) {
                                            Log.d(TAG, "祖父节点可点击，点击祖父节点")
                                            val result = grandParentNode.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                                            grandParentNode.recycle()
                                            parentNode.recycle()
                                            result
                                        } else {
                                            Log.e(TAG, "祖父节点为空或不可点击，无法执行点击操作")
                                            grandParentNode?.recycle()
                                            parentNode.recycle()
                                            false
                                        }
                                    }
                                } else {
                                    Log.e(TAG, "text属性复制链接按钮的父节点为空")
                                    false
                                }
                            }
                        }

                        Log.d(TAG, "点击复制链接按钮结果: $copyClickResult")

                        // 释放复制链接节点资源
                        copyNode.recycle()

                        if (copyClickResult) {
                            // 通知内容已准备好
                            SharePanelHelper.notifyContentReady()
                            Log.d(TAG, "抖音商品页面处理完成，已复制链接")
                        } else {
                            Log.e(TAG, "点击复制链接按钮失败")
                            CustomToastHelper.showToast(context, "复制链接失败，请重试")
                            hideSharePanel(context)
                        }

                        return // 找到并处理完成，停止轮询
                    }

                    // 如果还没有达到最大轮询次数，继续轮询
                    if (pollCount < maxPollCount) {
                        Log.d(TAG, "未找到复制链接按钮，${pollInterval}ms后继续轮询")
                        handler.postDelayed(this, pollInterval)
                    } else {
                        // 超时，停止轮询
                        Log.e(TAG, "轮询超时(${maxPollCount * pollInterval}ms)，未找到复制链接按钮")
                        CustomToastHelper.showToast(context, "未找到复制链接按钮，请手动操作")
                        hideSharePanel(context)
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "轮询检查复制链接节点时出错: ${e.message}", e)
                    CustomToastHelper.showToast(context, "检查复制链接按钮时出错，请重试")
                    hideSharePanel(context)
                }
            }
        }

        // 开始轮询
        handler.post(pollRunnable)
    }
}
