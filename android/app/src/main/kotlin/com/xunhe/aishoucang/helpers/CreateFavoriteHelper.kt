package com.xunhe.aishoucang.helpers

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.helpers.ConfigHelper
import com.xunhe.aishoucang.lib.RequestHelper
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject

/**
 * 创建收藏夹助手类
 * 用于处理创建收藏夹的UI和逻辑
 */
object CreateFavoriteHelper {
    private const val TAG = "CreateFavoriteHelper"

    // 弹窗视图和参数
    private var dialogView: View? = null
    private var dialogParams: WindowManager.LayoutParams? = null

    /**
     * 显示创建收藏夹对话框
     *
     * @param context 上下文
     * @param onSuccess 创建成功后的回调
     */
    fun showCreateFavoriteDialog(context: Context, onSuccess: () -> Unit) {
        try {
            // 获取窗口管理器
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager

            // 如果已经有对话框显示，先移除
            if (dialogView != null) {
                try {
                    windowManager.removeView(dialogView)
                } catch (e: Exception) {
                    Log.e(TAG, "移除已有对话框失败", e)
                }
                dialogView = null
            }

            // 创建对话框视图
            val inflater = LayoutInflater.from(context)
            dialogView = inflater.inflate(R.layout.create_favorite_dialog, null)

            // 获取输入框和按钮
            val nameEditText = dialogView?.findViewById<EditText>(R.id.favorite_name_edit_text)
            val cancelButton = dialogView?.findViewById<Button>(R.id.cancel_dialog_button)
            val createButton = dialogView?.findViewById<Button>(R.id.create_favorite_button)

            // 获取屏幕宽度，计算对话框宽度
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val dialogWidth = (screenWidth * 0.80).toInt() // 对话框宽度为屏幕宽度的80%，使其更窄

            // 创建窗口参数
            dialogParams = WindowManager.LayoutParams().apply {
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else WindowManager.LayoutParams.TYPE_PHONE
                format = PixelFormat.TRANSLUCENT
                flags = WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS
                width = dialogWidth
                height = WindowManager.LayoutParams.WRAP_CONTENT
                gravity = Gravity.CENTER
                dimAmount = 0.5f // 适度的背景暗化效果，使阴影看起来更自然
                windowAnimations = R.style.DialogAnimation // 使用专门为对话框设计的动画
            }

            // 设置取消按钮点击事件
            cancelButton?.setOnClickListener {
                hideDialog(context)
            }

            // 设置创建按钮点击事件
            createButton?.setOnClickListener {
                val name = nameEditText?.text?.toString()?.trim() ?: ""

                // 验证名称不为空
                if (name.isEmpty()) {
                    Toast.makeText(context, "请输入收藏夹名称", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                // 创建收藏夹
                createFavorite(context, name) { success ->
                    if (success) {
                        hideDialog(context)
                        onSuccess()
                    }
                }
            }

            // 显示对话框
            windowManager.addView(dialogView, dialogParams)

            // 添加动画效果
            dialogView?.post {
                try {
                    val animation = AnimationUtils.loadAnimation(context, R.anim.slide_up)
                    dialogView?.startAnimation(animation)
                } catch (e: Exception) {
                    Log.e(TAG, "启动动画失败", e)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "显示创建收藏夹对话框失败", e)
            Toast.makeText(context, "创建收藏夹失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 隐藏对话框
     */
    private fun hideDialog(context: Context) {
        try {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            if (dialogView != null) {
                windowManager.removeView(dialogView)
                dialogView = null
            }
        } catch (e: Exception) {
            Log.e(TAG, "隐藏对话框失败", e)
        }
    }

    /**
     * 创建收藏夹
     *
     * @param context 上下文
     * @param name 收藏夹名称
     * @param callback 回调函数，参数为是否成功
     */
    private fun createFavorite(context: Context, name: String, callback: (Boolean) -> Unit) {
        // 显示加载提示
        Toast.makeText(context, "正在创建收藏夹...", Toast.LENGTH_SHORT).show()

        // 获取用户ID
        val userId = SharedPreferencesHelper.getInstance(context).getUserId()
        if (userId.isEmpty()) {
            Toast.makeText(context, "请先登录", Toast.LENGTH_SHORT).show()
            callback(false)
            return
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 创建请求体
                val requestBody = JSONObject().apply {
                    put("name", name)
                }

                // 获取API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                // 发送创建收藏夹请求
                val requestHelper = RequestHelper.getInstance(context)
                val result = requestHelper.postJson(
                    "$apiBaseUrl/favorites/create",
                    requestBody,
                    null
                )

                // 切换到主线程处理结果
                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestHelper.ApiResult.Success -> {
                            // 解析响应
                            val jsonResponse = JSONObject(result.data)

                            // 检查API响应状态
                            if (jsonResponse.optInt("code", -1) == 0) {
                                Log.d(TAG, "创建收藏夹成功: $name")
                                Toast.makeText(context, "已创建收藏夹\"${name}\"", Toast.LENGTH_SHORT).show()
                                callback(true)
                            } else {
                                // API返回错误
                                val message = jsonResponse.optString("message", "未知错误")
                                Log.e(TAG, "创建收藏夹API错误: $message")
                                Toast.makeText(context, "创建失败: $message", Toast.LENGTH_SHORT).show()
                                callback(false)
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            Log.e(TAG, "创建收藏夹HTTP错误: ${result.code}, ${result.message}")
                            Toast.makeText(context, "创建失败: ${result.message}", Toast.LENGTH_SHORT).show()
                            callback(false)
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "创建收藏夹异常", result.throwable)
                            Toast.makeText(context, "创建失败: ${result.throwable.message}", Toast.LENGTH_SHORT).show()
                            callback(false)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "创建收藏夹失败", e)
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "创建失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    callback(false)
                }
            }
        }
    }
}
