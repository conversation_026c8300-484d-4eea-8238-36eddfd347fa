package com.xunhe.aishoucang.lib

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebChromeClient
import com.xunhe.aishoucang.WebViewActivity
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicBoolean
import com.xunhe.aishoucang.lib.WebViewVideoClient
import java.lang.ref.WeakReference

/**
 * WebView辅助类
 * 用于创建离屏WebView并提取视频链接
 */
class WebViewHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "WebViewHelper"

        @Volatile
        private var instance: WebViewHelper? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): WebViewHelper =
            instance ?: synchronized(this) {
                instance ?: WebViewHelper(context.applicationContext).also { instance = it }
            }
    }

    // 原生跳转协议
    var schemeURL: String? = ""

    // 用于在主线程中操作WebView
    private val mainHandler = Handler(Looper.getMainLooper())

    // 定时器相关函数移到类成员
    private var destroyRunnable: Runnable? = null
    private var webViewForDestroy: WebView? = null
    private val WEBVIEW_DESTROY_DELAY_MS = 500L // 销毁WebView前的延迟时间

    private fun scheduleDestroy(delay: Long = 10000L) {
        destroyRunnable?.let { mainHandler.removeCallbacks(it) }
        destroyRunnable = Runnable {
            safeDestroyWebView(webViewForDestroy, mainHandler) {
                webViewForDestroy = null
            }
        }
        mainHandler.postDelayed(destroyRunnable!!, delay)
    }

    private fun cancelDestroy() {
        destroyRunnable?.let { mainHandler.removeCallbacks(it) }
        destroyRunnable = null
    }

    /**
     * 安全地销毁WebView，最大程度减少销毁后的活动
     *
     * @param webView 要销毁的WebView
     * @param handler 主线程Handler
     * @param onDestroyed 销毁完成后的回调
     */
    private fun safeDestroyWebView(webView: WebView?, handler: Handler, onDestroyed: () -> Unit) {
        if (webView == null) {
            onDestroyed()
            return
        }

        try {
            // 使用弱引用避免内存泄漏
            val weakWebView = WeakReference(webView)

            // 在主线程中执行清理操作
            handler.post {
                try {
                    val view = weakWebView.get()
                    if (view != null) {
                        // 1. 停止加载
                        view.stopLoading()

                        // 2. 暂停WebView活动
                        view.onPause()
                        view.pauseTimers()

                        // 3. 加载空白页面中断当前资源加载
                        view.loadUrl("about:blank")

                        // 4. 移除所有监听器，使用空实现替代null
                        view.setWebViewClient(object : WebViewClient() {})
                        view.setWebChromeClient(object : WebChromeClient() {})

                        // 5. 短暂延迟后再销毁
                        handler.postDelayed({
                            try {
                                val delayedView = weakWebView.get()
                                if (delayedView != null) {
                                    delayedView.clearHistory()
                                    delayedView.clearCache(true)
                                    delayedView.clearFormData()
                                    delayedView.clearSslPreferences()
                                    delayedView.clearMatches()

                                    // 从父视图中移除
                                    val parent = delayedView.parent
                                    if (parent != null) {
                                        try {
                                            (parent as ViewGroup).removeView(delayedView)
                                        } catch (e: Exception) {
                                            Log.w(TAG, "从父视图移除WebView时出错: ${e.message}")
                                        }
                                    }

                                    // 最后销毁
                                    delayedView.destroy()
                                    Log.d(TAG, "WebView已安全销毁")
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "销毁WebView时出错: ${e.message}", e)
                            } finally {
                                // 确保回调一定会执行
                                onDestroyed()
                            }
                        }, WEBVIEW_DESTROY_DELAY_MS)
                    } else {
                        // WebView已经被回收，直接执行回调
                        onDestroyed()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "准备销毁WebView时出错: ${e.message}", e)
                    onDestroyed()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "销毁WebView过程中出错: ${e.message}", e)
            onDestroyed()
        }
    }

    /**
     * 使用系统浏览器或WebView打开URL
     * @param url 要打开的URL
     * @param useSystemBrowser 是否使用系统浏览器打开，默认为false（使用应用内WebView）
     */
    fun openWebPage(url: String, useSystemBrowser: Boolean = false) {
        if (useSystemBrowser) {
            // 使用系统浏览器打开URL
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } else {
            // 使用应用内WebView打开URL
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra("url", url)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        }
    }

    /**
     * 从 URL 中提取视频链接
     * @param url 网页URL
     * @param timeout 超时时间（毫秒）
     * @param filters 自定义过滤器映射，key为资源类型，value为过滤函数
     * @param callback 回调函数，返回提取结果，Map中的key对应filters中的key，value为过滤到的URL列表
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun extractVideoUrl(
        url: String,
        timeout: Long = 10000, // 默认10秒超时
        filters: Map<String, (url: String, contentType: String) -> Boolean>? = null,
        callback: (success: Boolean, result: Map<String, List<String>>?, errorMessage: String?) -> Unit
    ) {
        val executor = Executors.newSingleThreadExecutor()
        val isCompleted = AtomicBoolean(false)
        var foundAnyMatch = false
        val resultMap = mutableMapOf<String, MutableList<String>>()
        lateinit var resultError: String
        val webViewReady = AtomicBoolean(false)
        var webView: WebView? = null
        try {
            mainHandler.post {
                try {
                    Log.d(TAG, "开始创建WebView并加载URL: $url")
                    webView = WebView(context)
                    webViewForDestroy = webView
                    webView?.settings?.apply {
                        javaScriptEnabled = true
                        domStorageEnabled = true
                        loadsImagesAutomatically = true
                        blockNetworkImage = false
                        userAgentString = com.xunhe.aishoucang.helpers.UserAgentGenerator.generateUserAgentForUrl(url)
                    }
                    webView?.webViewClient = WebViewVideoClient(
                        schemeUrlSetter = { scheme ->
                            schemeURL = scheme
                            scheduleDestroy(timeout) // 拦截到原生协议重置定时器
                        },
                        isCompleted = isCompleted,
                        mainHandler = mainHandler,
                        webViewProvider = { webView },
                        callback = { success, schemeUrl, error ->
                            // 转换返回格式
                            if (success && schemeUrl != null) {
                                // 如果有原生跳转协议，添加到结果映射
                                resultMap.getOrPut("scheme") { mutableListOf() }.add(schemeUrl)
                            }
                            callback(success, if (success) resultMap else null, error)
                        },
                        filters = filters,
                        resultMapSetter = { key, url ->
                            resultMap.getOrPut(key) { mutableListOf() }.add(url)
                            foundAnyMatch = true
                        },
                        foundMatchSetter = { found -> foundAnyMatch = found },
                        getResultMap = { resultMap },
                        getFoundAnyMatch = { foundAnyMatch },
                        tag = TAG,
                        scheduleDestroy = { scheduleDestroy(timeout) },
                        cancelAndDestroy = {
                            cancelDestroy()
                            safeDestroyWebView(webView, mainHandler) {
                                webView = null
                                webViewForDestroy = null
                            }
                        }
                    )
                    webView?.loadUrl(url)
                    webViewReady.set(true)
                    scheduleDestroy(timeout) // extractVideoUrl调用后立即设置timeout定时器
                } catch (e: Exception) {
                    Log.e(TAG, "创建或配置WebView时出错", e)
                    resultError = "WebView错误: ${e.message}"
                    safeDestroyWebView(webView, mainHandler) {
                        webView = null
                        webViewForDestroy = null
                        if (!isCompleted.getAndSet(true)) {
                            callback(false, null, resultError)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取视频链接过程中出错", e)
            if (!isCompleted.getAndSet(true)) {
                mainHandler.post {
                    cancelDestroy()
                    safeDestroyWebView(webView, mainHandler) {
                        webView = null
                        webViewForDestroy = null
                        callback(false, null, "提取视频链接时出错: ${e.message}")
                    }
                }
            }
        } finally {
            executor.shutdown()
        }
    }
}
