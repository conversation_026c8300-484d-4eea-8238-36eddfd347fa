package com.xunhe.aishoucang.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.xunhe.aishoucang.helpers.WechatHelper

/**
 * 微信支付回调Activity
 * 
 * 用于接收微信支付的回调结果
 * 必须在包名下的wxapi目录中，且名称必须为WXPayEntryActivity
 * 虽然本应用不使用支付功能，但为了兼容性需要此Activity
 */
class WXPayEntryActivity : Activity(), IWXAPIEventHandler {
    private val TAG = "WXPayEntryActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 必须调用此方法，否则不会回调onReq和onResp
        try {
            WechatHelper.getInstance(this).wxApi.handleIntent(intent, this)
        } catch (e: Exception) {
            Log.e(TAG, "处理微信支付回调时出错: ${e.message}", e)
            finish()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        
        // 必须调用此方法，否则不会回调onReq和onResp
        try {
            WechatHelper.getInstance(this).wxApi.handleIntent(intent, this)
        } catch (e: Exception) {
            Log.e(TAG, "处理微信支付回调时出错: ${e.message}", e)
            finish()
        }
    }

    /**
     * 微信发送请求到第三方应用时，会回调到该方法
     */
    override fun onReq(req: BaseReq) {
        Log.d(TAG, "收到微信支付请求: ${req.type}")
        finish()
    }

    /**
     * 第三方应用发送到微信的请求处理后的响应结果，会回调到该方法
     */
    override fun onResp(resp: BaseResp) {
        Log.d(TAG, "收到微信支付响应: type=${resp.type}, errCode=${resp.errCode}")
        
        if (resp.type == ConstantsAPI.COMMAND_PAY_BY_WX) {
            // 处理支付回调
            when (resp.errCode) {
                BaseResp.ErrCode.ERR_OK -> {
                    // 支付成功
                    Log.d(TAG, "微信支付成功")
                    WechatHelper.getInstance(this).onPaySuccess()
                }
                BaseResp.ErrCode.ERR_USER_CANCEL -> {
                    // 用户取消支付
                    Log.d(TAG, "用户取消微信支付")
                    WechatHelper.getInstance(this).onPayCancel()
                }
                else -> {
                    // 支付失败
                    Log.e(TAG, "微信支付失败，错误码: ${resp.errCode}, 错误信息: ${resp.errStr}")
                    WechatHelper.getInstance(this).onPayFail(resp.errCode, resp.errStr)
                }
            }
        }
        
        finish()
    }
}
