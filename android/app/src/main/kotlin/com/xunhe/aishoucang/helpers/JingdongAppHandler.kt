package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.lib.AccessibilityHelper
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.ContentTypeConstants
import com.xunhe.aishoucang.helpers.CustomToastHelper

/**
 * 京东应用处理器
 * 参考小红书的逻辑，实现各个场景的分发处理
 */
class JingdongAppHandler : AppHandler {
    companion object {
        private const val TAG = "JingdongAppHandler"
    }

    // 缓存找到的分享节点，供后续使用
    private var cachedShareNode: AccessibilityNodeInfo? = null

    override fun handle(context: Context) {
        Log.d(TAG, "正在处理京东应用")

        // 获取无障碍服务实例
        val helper = AccessibilityHelper.getInstance(context)
        val service = AccessibilityHelper.AppAccessibilityService.getInstance()

        if (service == null) {
            Log.e(TAG, "无障碍服务未运行，无法继续操作")
            CustomToastHelper.showToast(context, "无障碍服务未运行，请检查权限设置")
            return
        }

        // 业务场景分发 - 参考小红书的逻辑，各个场景分发到不同的函数
        if (isJingdongProductPage(context)) {
            // 商品详情页
            Log.d(TAG, "检测到京东商品详情页")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.JINGDONG_TYPE_PRODUCT)
            handleProductPage(context)
        } else if (isJingdongShopPage(context)) {
            // 店铺页
            Log.d(TAG, "检测到京东店铺页")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.JINGDONG_TYPE_SHOP)
            handleShopPage(context)
        } else if (isJingdongLivePage(context)) {
            // 直播页
            Log.d(TAG, "检测到京东直播页")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.JINGDONG_TYPE_LIVE)
            handleLivePage(context)
        } else {
            // 未知类型或不支持的页面
            Log.d(TAG, "京东当前页面暂不支持")
            SharePanelHelper.setCurrentContentType(ContentTypeConstants.TYPE_UNKNOWN)
            CustomToastHelper.showToast(context, "京东当前页面暂不支持")
            // 通知内容准备完成，避免界面一直处于加载状态
            SharePanelHelper.notifyContentReady()
        }
    }

    /**
     * 判断当前页面是否为京东商品详情页
     *
     * 通过检查页面是否存在特定的商品页面元素来判断
     *
     * @param context 上下文
     * @return 是否为商品详情页
     */
    private fun isJingdongProductPage(context: Context): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false
        val rootNode = service.rootInActiveWindow ?: return false

        try {
            var hasShareImageView = false
            var hasShopTextView = false

            // 1. 使用原生API直接查找desc为"分享"的节点，然后检查是否为ImageView
            val shareNodes = service.findNodesByDescription("分享")
            if (shareNodes != null) {
                for (node in shareNodes) {
                    if (node.className?.toString() == "android.widget.ImageView") {
                        Log.d(TAG, "找到ImageView节点，desc为'分享'")
                        hasShareImageView = true
                        // 缓存找到的分享节点，供后续点击使用
                        cachedShareNode = AccessibilityNodeInfo.obtain(node)
                        break
                    }
                }
                // 释放节点资源
                shareNodes.forEach { it.recycle() }
            }

            val shopNodes = service.findNodesByContent("店铺")
            if (shopNodes != null && shopNodes.isNotEmpty()) {
                Log.d(TAG, "找到text为'店铺'的节点")
                hasShopTextView = true
                shopNodes.forEach { it.recycle() }
            }

            // 判断是否为商品详情页：需要同时满足有分享ImageView、加入购物车TextView和店铺TextView
            val isProductPage = hasShareImageView && hasShopTextView

            Log.d(TAG, "商品详情页判断结果: 分享ImageView=$hasShareImageView, 店铺TextView=$hasShopTextView, 最终结果=$isProductPage")

            return isProductPage
        } catch (e: Exception) {
            Log.e(TAG, "判断是否为京东商品详情页时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 判断当前页面是否为京东店铺页
     *
     * @param context 上下文
     * @return 是否为店铺页
     */
    private fun isJingdongShopPage(context: Context): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        try {
            // 检查是否存在"关注店铺"按钮
            val followNodes = service.findNodesByContent("关注店铺")
            if (followNodes != null && followNodes.isNotEmpty()) {
                Log.d(TAG, "找到'关注店铺'按钮，判断为店铺页")
                followNodes.forEach { it.recycle() }
                return true
            }

            // 检查是否存在"进店逛逛"按钮
            val shopNodes = service.findNodesByContent("进店逛逛")
            if (shopNodes != null && shopNodes.isNotEmpty()) {
                Log.d(TAG, "找到'进店逛逛'按钮，判断为店铺页")
                shopNodes.forEach { it.recycle() }
                return true
            }

            return false
        } catch (e: Exception) {
            Log.e(TAG, "判断是否为京东店铺页时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 判断当前页面是否为京东直播页
     *
     * @param context 上下文
     * @return 是否为直播页
     */
    private fun isJingdongLivePage(context: Context): Boolean {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return false

        try {
            // 检查是否存在"直播"相关元素
            val liveNodes = service.findNodesByContent("直播")
            if (liveNodes != null && liveNodes.isNotEmpty()) {
                Log.d(TAG, "找到'直播'元素，判断为直播页")
                liveNodes.forEach { it.recycle() }
                return true
            }

            return false
        } catch (e: Exception) {
            Log.e(TAG, "判断是否为京东直播页时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 处理商品详情页
     *
     * @param context 上下文
     */
    private fun handleProductPage(context: Context) {
        Log.d(TAG, "开始处理京东商品详情页")
        val helper = AccessibilityHelper.getInstance(context)
        val service = AccessibilityHelper.AppAccessibilityService.getInstance() ?: return

        // 使用缓存的分享节点或重新查找
        var shareNode = cachedShareNode
        var clickResult = false

        if (shareNode != null) {
            Log.d(TAG, "使用缓存的分享节点")
        } else {
            Log.d(TAG, "缓存的分享节点不存在，重新查找")
            // 如果没有缓存的节点，重新查找
            val shareNodes = service.findNodesByContent("分享")
            if (shareNodes != null && shareNodes.isNotEmpty()) {
                shareNode = shareNodes[0]
                // 释放其余节点资源
                for (i in 1 until shareNodes.size) {
                    shareNodes[i].recycle()
                }
            }
        }

        if (shareNode != null) {
            Log.d(TAG, "找到分享按钮，准备点击")

            // 检查节点是否可点击
            if (shareNode.isClickable) {
                // 直接点击节点
                clickResult = shareNode.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
                Log.d(TAG, "直接点击'分享'按钮结果: $clickResult")
            } else {
                // 如果节点本身不可点击，尝试点击其父节点
                val parent = shareNode.parent
                if (parent != null && parent.isClickable) {
                    clickResult = parent.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
                    Log.d(TAG, "点击'分享'按钮的父节点结果: $clickResult")
                    parent.recycle()
                }
            }

            // 清理缓存的节点（使用后释放）
            if (cachedShareNode != null) {
                cachedShareNode?.recycle()
                cachedShareNode = null
            } else {
                // 如果是重新查找的节点，释放它
                shareNode.recycle()
            }
        } else {
            CustomToastHelper.showShortToast(context, "当前内容无法收藏，未找到分享按钮")
            return
        }

        if (clickResult) {
            // 等待分享面板出现，然后查找复制链接
            Thread.sleep(1000)

            // 查找复制链接按钮，点击它的父节点
            val copyNodes = service.findNodesByContent("复制链接")
            if (copyNodes != null && copyNodes.isNotEmpty()) {
                val copyNode = copyNodes[0]
                var copyClickResult = false

                // 点击父节点而不是节点本身
                val parent = copyNode.parent
                if (parent != null) {
                    copyClickResult = parent.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
                    Log.d(TAG, "点击'复制链接'按钮的父节点结果: $copyClickResult")
                    parent.recycle()
                } else {
                    Log.d(TAG, "'复制链接'按钮没有父节点，尝试直接点击")
                    copyClickResult = copyNode.performAction(android.view.accessibility.AccessibilityNodeInfo.ACTION_CLICK)
                    Log.d(TAG, "直接点击'复制链接'按钮结果: $copyClickResult")
                }

                if (copyClickResult) {
                    SharePanelHelper.notifyContentReady()
                }

                // 释放资源
                copyNodes.forEach { it.recycle() }
            } else {
                Log.d(TAG, "未找到'复制链接'按钮")
            }
        }
    }

    /**
     * 处理店铺页
     *
     * @param context 上下文
     */
    private fun handleShopPage(context: Context) {
        Log.d(TAG, "开始处理京东店铺页")
        // 店铺页的处理逻辑，类似商品页
        handleProductPage(context)
    }

    /**
     * 处理直播页
     *
     * @param context 上下文
     */
    private fun handleLivePage(context: Context) {
        Log.d(TAG, "开始处理京东直播页")
        // 直播页的处理逻辑，类似商品页
        handleProductPage(context)
    }


}
