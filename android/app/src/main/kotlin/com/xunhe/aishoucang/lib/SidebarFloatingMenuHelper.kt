package com.xunhe.aishoucang.lib

import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

class SidebarFloatingMenuHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "SidebarFloatingMenuHelper"
        
        @Volatile
        private var instance: SidebarFloatingMenuHelper? = null

        fun getInstance(context: Context): SidebarFloatingMenuHelper {
            return instance ?: synchronized(this) {
                instance ?: SidebarFloatingMenuHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    private var isShowing = false

    fun show() {
        try {
            val intent = Intent(context, SidebarFloatingMenuService::class.java)
            context.startService(intent)
            isShowing = true
            Log.d(TAG, "显示侧边栏悬浮菜单")
        } catch (e: Exception) {
            Log.e(TAG, "显示侧边栏悬浮菜单时出错", e)
        }
    }

    fun hide() {
        try {
            stopSidebarFloatingMenuService()
            isShowing = false
            Log.d(TAG, "隐藏侧边栏悬浮菜单")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏侧边栏悬浮菜单时出错", e)
        }
    }

    fun showSidebarFloatingMenuAtPosition(x: Int?, y: Int?) {
        try {
            val intent = Intent(context, SidebarFloatingMenuService::class.java).apply {
                x?.let { putExtra("position_x", it) }
                y?.let { putExtra("position_y", it) }
            }
            context.startService(intent)
            isShowing = true
            Log.d(TAG, "在指定位置(x=$x, y=$y)显示侧边栏悬浮菜单")
        } catch (e: Exception) {
            Log.e(TAG, "在指定位置显示侧边栏悬浮菜单时出错", e)
        }
    }

    fun isSidebarFloatingMenuShowing(): Boolean {
        return isShowing
    }

    fun updateShowingState(showing: Boolean) {
        isShowing = showing
        Log.d(TAG, "更新悬浮菜单显示状态: $showing")
    }

    fun getSidebarFloatingMenuPosition(): Pair<Int, Int>? {
        return try {
            val position = SidebarFloatingMenuService.lastPosition
            Log.d(TAG, "从服务获取侧边栏悬浮菜单位置: $position")
            position
        } catch (e: Exception) {
            Log.e(TAG, "获取侧边栏悬浮菜单位置失败", e)
            null
        }
    }

    private fun stopSidebarFloatingMenuService() {
        try {
            val intent = Intent(context, SidebarFloatingMenuService::class.java)
            context.stopService(intent)
            Log.d(TAG, "侧边栏悬浮菜单服务已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止侧边栏悬浮菜单服务失败", e)
        }
    }
}
