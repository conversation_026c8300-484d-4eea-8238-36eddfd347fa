package com.xunhe.aishoucang.helpers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX
import com.tencent.mm.opensdk.modelmsg.WXImageObject
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage
import com.tencent.mm.opensdk.modelmsg.WXTextObject
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 微信SDK助手类
 *
 * 封装微信SDK的初始化和调用方法，提供登录、分享等功能接口
 */
class WechatHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "WechatHelper"

        // 微信开放平台AppID
        private var APP_ID: String = "wxb0872bb3945f31bc"

        // 单例实例
        @Volatile
        private var instance: WechatHelper? = null

        fun getInstance(context: Context): WechatHelper {
            return instance ?: synchronized(this) {
                instance ?: WechatHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    // 微信API接口
    val wxApi: IWXAPI by lazy {
        // 从配置中获取APP_ID，如果需要可以从ConfigHelper中读取
        // 例如：APP_ID = ConfigHelper.getString("wechat_app_id") ?: APP_ID

        // 通过WXAPIFactory获取IWXAPI实例
        val api = WXAPIFactory.createWXAPI(context, APP_ID, true)

        // 将应用的AppID注册到微信
        api.registerApp(APP_ID)

        // 注册广播接收器，监听微信启动广播
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                // 将应用注册到微信
                api.registerApp(APP_ID)
                Log.d(TAG, "收到微信启动广播，重新注册应用到微信")
            }
        }
        val filter = IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP)

        // Android 13+ 需要明确指定 RECEIVER_EXPORTED 或 RECEIVER_NOT_EXPORTED
        // 微信启动广播来自外部应用(微信)，所以使用 RECEIVER_EXPORTED
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(receiver, filter)
        }

        api
    }

    // 登录回调监听器列表
    private val authListeners = CopyOnWriteArrayList<WechatAuthListener>()

    // 分享回调监听器列表
    private val shareListeners = CopyOnWriteArrayList<WechatShareListener>()

    // 支付回调监听器列表
    private val payListeners = CopyOnWriteArrayList<WechatPayListener>()

    /**
     * 检查微信是否已安装
     */
    fun isWechatInstalled(): Boolean {
        return wxApi.isWXAppInstalled
    }

    /**
     * 获取微信支持的API版本
     */
    fun getWechatSupportedApi(): Int {
        return wxApi.wxAppSupportAPI
    }

    /**
     * 打开微信
     */
    fun openWechat(): Boolean {
        return wxApi.openWXApp()
    }

    /**
     * 添加微信登录回调监听器
     */
    fun addAuthListener(listener: WechatAuthListener) {
        if (!authListeners.contains(listener)) {
            authListeners.add(listener)
        }
    }

    /**
     * 移除微信登录回调监听器
     */
    fun removeAuthListener(listener: WechatAuthListener) {
        authListeners.remove(listener)
    }

    /**
     * 添加微信分享回调监听器
     */
    fun addShareListener(listener: WechatShareListener) {
        if (!shareListeners.contains(listener)) {
            shareListeners.add(listener)
        }
    }

    /**
     * 移除微信分享回调监听器
     */
    fun removeShareListener(listener: WechatShareListener) {
        shareListeners.remove(listener)
    }

    /**
     * 添加微信支付回调监听器
     */
    fun addPayListener(listener: WechatPayListener) {
        if (!payListeners.contains(listener)) {
            payListeners.add(listener)
        }
    }

    /**
     * 移除微信支付回调监听器
     */
    fun removePayListener(listener: WechatPayListener) {
        payListeners.remove(listener)
    }

    /**
     * 发起微信登录授权
     *
     * @param scope 应用授权作用域，默认为snsapi_userinfo
     * @param state 用于保持请求和回调的状态，授权请求后原样带回
     * @return 是否成功发起授权请求
     */
    fun login(scope: String = "snsapi_userinfo", state: String = "wechat_login"): Boolean {
        if (!isWechatInstalled()) {
            Log.e(TAG, "微信未安装，无法发起登录授权")
            return false
        }

        val req = SendAuth.Req()
        req.scope = scope
        req.state = state

        val result = wxApi.sendReq(req)
        Log.d(TAG, "发起微信登录授权: $result")

        return result
    }

    /**
     * 分享文本到微信
     *
     * @param text 要分享的文本内容
     * @param scene 分享场景，默认为会话场景
     * @return 是否成功发起分享请求
     */
    fun shareText(text: String, scene: Int = SendMessageToWX.Req.WXSceneSession): Boolean {
        if (!isWechatInstalled()) {
            Log.e(TAG, "微信未安装，无法发起分享")
            return false
        }

        // 创建文本对象
        val textObj = WXTextObject()
        textObj.text = text

        // 创建媒体消息
        val msg = WXMediaMessage()
        msg.mediaObject = textObj
        msg.description = text

        // 创建发送请求
        val req = SendMessageToWX.Req()
        req.transaction = buildTransaction("text")
        req.message = msg
        req.scene = scene

        val result = wxApi.sendReq(req)
        Log.d(TAG, "发起微信文本分享: $result")

        return result
    }

    /**
     * 分享图片到微信
     *
     * @param imagePath 图片路径
     * @param scene 分享场景，默认为会话场景
     * @return 是否成功发起分享请求
     */
    fun shareImage(imagePath: String, scene: Int = SendMessageToWX.Req.WXSceneSession): Boolean {
        if (!isWechatInstalled()) {
            Log.e(TAG, "微信未安装，无法发起分享")
            return false
        }

        try {
            // 读取图片文件
            val file = File(imagePath)
            if (!file.exists()) {
                Log.e(TAG, "图片文件不存在: $imagePath")
                return false
            }

            val bitmap = BitmapFactory.decodeStream(FileInputStream(file))

            // 创建图片对象
            val imgObj = WXImageObject(bitmap)

            // 创建媒体消息
            val msg = WXMediaMessage()
            msg.mediaObject = imgObj

            // 设置缩略图
            val thumbBmp = Bitmap.createScaledBitmap(bitmap, 120, 120, true)
            bitmap.recycle()
            msg.thumbData = bitmapToByteArray(thumbBmp, 32)
            thumbBmp.recycle()

            // 创建发送请求
            val req = SendMessageToWX.Req()
            req.transaction = buildTransaction("img")
            req.message = msg
            req.scene = scene

            val result = wxApi.sendReq(req)
            Log.d(TAG, "发起微信图片分享: $result")

            return result
        } catch (e: Exception) {
            Log.e(TAG, "分享图片到微信时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 分享网页到微信
     *
     * @param url 网页链接
     * @param title 标题
     * @param description 描述
     * @param thumbImagePath 缩略图路径，可为空
     * @param scene 分享场景，默认为会话场景
     * @return 是否成功发起分享请求
     */
    fun shareWebpage(
        url: String,
        title: String,
        description: String,
        thumbImagePath: String? = null,
        scene: Int = SendMessageToWX.Req.WXSceneSession
    ): Boolean {
        if (!isWechatInstalled()) {
            Log.e(TAG, "微信未安装，无法发起分享")
            return false
        }

        try {
            // 创建网页对象
            val webpageObj = WXWebpageObject()
            webpageObj.webpageUrl = url

            // 创建媒体消息
            val msg = WXMediaMessage()
            msg.mediaObject = webpageObj
            msg.title = title
            msg.description = description

            // 设置缩略图
            if (!thumbImagePath.isNullOrEmpty()) {
                val file = File(thumbImagePath)
                if (file.exists()) {
                    val bitmap = BitmapFactory.decodeStream(FileInputStream(file))
                    val thumbBmp = Bitmap.createScaledBitmap(bitmap, 120, 120, true)
                    bitmap.recycle()
                    msg.thumbData = bitmapToByteArray(thumbBmp, 32)
                    thumbBmp.recycle()
                }
            }

            // 创建发送请求
            val req = SendMessageToWX.Req()
            req.transaction = buildTransaction("webpage")
            req.message = msg
            req.scene = scene

            val result = wxApi.sendReq(req)
            Log.d(TAG, "发起微信网页分享: $result")

            return result
        } catch (e: Exception) {
            Log.e(TAG, "分享网页到微信时出错: ${e.message}", e)
            return false
        }
    }

    /**
     * 处理通用响应
     */
    fun onResponseReceived(resp: BaseResp) {
        Log.d(TAG, "收到微信响应: type=${resp.type}, errCode=${resp.errCode}")
    }

    /**
     * 处理授权成功回调
     */
    fun onAuthSuccess(code: String) {
        Log.d(TAG, "微信授权成功，授权码: $code")
        Log.d(TAG, "授权码详情: 长度=${code.length}, 内容=$code")

        // 打印授权码的每个字符的ASCII码，用于调试
        val charCodes = code.map { it.code }
        Log.d(TAG, "授权码字符ASCII码: $charCodes")

        for (listener in authListeners) {
            listener.onAuthSuccess(code)
        }
    }

    /**
     * 处理授权取消回调
     */
    fun onAuthCancel() {
        Log.d(TAG, "用户取消微信授权")
        for (listener in authListeners) {
            listener.onAuthCancel()
        }
    }

    /**
     * 处理授权拒绝回调
     */
    fun onAuthDenied() {
        Log.d(TAG, "用户拒绝微信授权")
        for (listener in authListeners) {
            listener.onAuthDenied()
        }
    }

    /**
     * 处理授权失败回调
     */
    fun onAuthFail(errCode: Int, errMsg: String) {
        Log.e(TAG, "微信授权失败，错误码: $errCode, 错误信息: $errMsg")
        for (listener in authListeners) {
            listener.onAuthFail(errCode, errMsg)
        }
    }

    /**
     * 处理分享成功回调
     */
    fun onShareSuccess() {
        Log.d(TAG, "微信分享成功")
        for (listener in shareListeners) {
            listener.onShareSuccess()
        }
    }

    /**
     * 处理分享取消回调
     */
    fun onShareCancel() {
        Log.d(TAG, "用户取消微信分享")
        for (listener in shareListeners) {
            listener.onShareCancel()
        }
    }

    /**
     * 处理分享失败回调
     */
    fun onShareFail(errCode: Int, errMsg: String) {
        Log.e(TAG, "微信分享失败，错误码: $errCode, 错误信息: $errMsg")
        for (listener in shareListeners) {
            listener.onShareFail(errCode, errMsg)
        }
    }

    /**
     * 处理支付成功回调
     */
    fun onPaySuccess() {
        Log.d(TAG, "微信支付成功")
        for (listener in payListeners) {
            listener.onPaySuccess()
        }
    }

    /**
     * 处理支付取消回调
     */
    fun onPayCancel() {
        Log.d(TAG, "用户取消微信支付")
        for (listener in payListeners) {
            listener.onPayCancel()
        }
    }

    /**
     * 处理支付失败回调
     */
    fun onPayFail(errCode: Int, errMsg: String) {
        Log.e(TAG, "微信支付失败，错误码: $errCode, 错误信息: $errMsg")
        for (listener in payListeners) {
            listener.onPayFail(errCode, errMsg)
        }
    }

    /**
     * 构建唯一的事务标识
     */
    private fun buildTransaction(type: String): String {
        return "$type${System.currentTimeMillis()}"
    }

    /**
     * 将Bitmap转换为字节数组
     */
    private fun bitmapToByteArray(bitmap: Bitmap, maxKb: Int): ByteArray {
        val output = ByteArrayOutputStream()

        // 初始压缩比例
        var quality = 100
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, output)

        // 如果图片太大，逐步降低质量
        while (output.toByteArray().size > maxKb * 1024 && quality > 10) {
            output.reset()
            quality -= 10
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, output)
        }

        return output.toByteArray()
    }
}

/**
 * 微信登录回调监听器接口
 */
interface WechatAuthListener {
    /**
     * 授权成功回调
     *
     * @param code 授权码，用于获取用户信息
     */
    fun onAuthSuccess(code: String)

    /**
     * 授权取消回调
     */
    fun onAuthCancel()

    /**
     * 授权拒绝回调
     */
    fun onAuthDenied()

    /**
     * 授权失败回调
     *
     * @param errCode 错误码
     * @param errMsg 错误信息
     */
    fun onAuthFail(errCode: Int, errMsg: String)
}

/**
 * 微信分享回调监听器接口
 */
interface WechatShareListener {
    /**
     * 分享成功回调
     */
    fun onShareSuccess()

    /**
     * 分享取消回调
     */
    fun onShareCancel()

    /**
     * 分享失败回调
     *
     * @param errCode 错误码
     * @param errMsg 错误信息
     */
    fun onShareFail(errCode: Int, errMsg: String)
}

/**
 * 微信支付回调监听器接口
 */
interface WechatPayListener {
    /**
     * 支付成功回调
     */
    fun onPaySuccess()

    /**
     * 支付取消回调
     */
    fun onPayCancel()

    /**
     * 支付失败回调
     *
     * @param errCode 错误码
     * @param errMsg 错误信息
     */
    fun onPayFail(errCode: Int, errMsg: String)
}
