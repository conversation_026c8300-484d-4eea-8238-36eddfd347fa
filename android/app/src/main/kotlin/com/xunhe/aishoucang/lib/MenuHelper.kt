package com.xunhe.aishoucang.lib

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.Toast
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.lib.ScreenshotHelper

class MenuHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "MenuHelper"

        @Volatile
        private var instance: MenuHelper? = null

        fun getInstance(context: Context): MenuHelper {
            return instance ?: synchronized(this) {
                instance ?: MenuHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    private var windowManager: WindowManager? = null
    private var menuView: android.view.View? = null
    private var menuParams: WindowManager.LayoutParams? = null
    private var isMenuShowing = false

    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }

    /**
     * 显示下拉菜单
     */
    fun showMenu(
        floatingButtonParams: WindowManager.LayoutParams?,
        floatingButtonView: android.view.View?,
        onMenuItemClick: (menuItemId: Int) -> Unit
    ) {
        Log.d(TAG, "开始执行showMenu方法")

        if (isMenuShowing) {
            // 如果菜单已显示，则隐藏
            Log.d(TAG, "菜单已显示，隐藏菜单")
            hideMenu()
            return
        }

        // 创建并显示菜单
        createFloatingMenu(floatingButtonParams, floatingButtonView, onMenuItemClick)
    }

    /**
     * 隐藏下拉菜单
     */
    fun hideMenu() {
        Log.d(TAG, "开始执行hideMenu方法，menuView=${menuView}, isMenuShowing=${isMenuShowing}")

        if (menuView != null && isMenuShowing) {
            try {
                // 先应用退出动画到菜单内容
                try {
                    val menuContent = menuView?.findViewById<LinearLayout>(R.id.menu_content_container)?.getChildAt(0)
                    val animation = AnimationUtils.loadAnimation(context, R.anim.menu_fade_out)

                    animation.setAnimationListener(object : Animation.AnimationListener {
                        override fun onAnimationStart(animation: Animation?) {}

                        override fun onAnimationEnd(animation: Animation?) {
                            // 动画结束后移除菜单视图
                            try {
                                windowManager?.removeView(menuView)
                                menuView = null
                                isMenuShowing = false
                                Log.d(TAG, "下拉菜单已移除")
                            } catch (e: Exception) {
                                Log.e(TAG, "移除菜单视图失败", e)
                            }
                        }

                        override fun onAnimationRepeat(animation: Animation?) {}
                    })

                    Log.d(TAG, "应用退出动画到菜单内容：menuContent=${menuContent}")
                    menuContent?.startAnimation(animation)
                } catch (e: Exception) {
                    // 如果动画失败，直接移除菜单
                    Log.e(TAG, "菜单退出动画执行失败，直接移除", e)
                    windowManager?.removeView(menuView)
                    menuView = null
                    isMenuShowing = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "隐藏菜单过程中出现异常", e)
            }
        } else {
            Log.d(TAG, "菜单视图为空或未显示，无需隐藏")
        }
    }

    /**
     * 创建下拉菜单
     */
    private fun createFloatingMenu(
        floatingButtonParams: WindowManager.LayoutParams?,
        floatingButtonView: android.view.View?,
        onMenuItemClick: (menuItemId: Int) -> Unit
    ) {
        try {
            if (menuView != null) {
                // 如果菜单已存在，先移除
                windowManager?.removeView(menuView)
                menuView = null
            }

            val inflater = LayoutInflater.from(context)

            // 创建全屏容器
            menuView = inflater.inflate(R.layout.floating_menu_container, null)

            // 获取菜单内容容器
            val menuContentContainer = menuView?.findViewById<LinearLayout>(R.id.menu_content_container)

            // 获取屏幕宽度
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels

            // 计算菜单宽度（屏幕宽度减去20dp）
            val menuWidth = screenWidth - (20 * displayMetrics.density).toInt()

            // 将菜单内容添加到容器中
            val menuContent = inflater.inflate(R.layout.floating_menu_layout, null)

            // 设置菜单内容宽度
            val menuLayoutParams = LinearLayout.LayoutParams(menuWidth, LinearLayout.LayoutParams.WRAP_CONTENT)
            menuContent.layoutParams = menuLayoutParams

            // 在Android 14+设备上隐藏截图相关菜单项
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 14 (API 34)
                Log.d(TAG, "Android 14+设备，隐藏截图相关菜单项")
                // 隐藏截图收藏菜单项
                menuContent.findViewById<LinearLayout>(R.id.menu_item_share)?.visibility = View.GONE
                // 隐藏截长图菜单项
                menuContent.findViewById<LinearLayout>(R.id.menu_item_long_screenshot)?.visibility = View.GONE
            }

            menuContentContainer?.addView(menuContent)

            // 添加日志以便调试
            Log.d(TAG, "菜单内容已添加到容器，menuContent=${menuContent}, menuWidth=${menuWidth}, screenWidth=${screenWidth}")

            // 设置菜单布局参数 - 全屏
            menuParams = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                format = PixelFormat.TRANSLUCENT
            }

            // 设置菜单位置，在悬浮按钮下方
            // 获取悬浮按钮的尺寸
            val iconButton = floatingButtonView?.findViewById<ImageButton>(R.id.find_and_click_button)
            val buttonHeight = iconButton?.height ?: 48

            // 计算菜单内容位置
            menuContentContainer?.let {
                // 设置菜单内容位置，使其始终位于屏幕中央
                val layoutParams = it.layoutParams as FrameLayout.LayoutParams
                layoutParams.gravity = Gravity.CENTER // 水平和垂直都居中
                layoutParams.topMargin = 0 // 不需要顶部边距，因为已经居中
                it.layoutParams = layoutParams

                // 添加日志以便调试
                Log.d(TAG, "设置菜单位置：gravity=CENTER, topMargin=0")
            }

            // 设置全屏容器的点击事件，点击任意位置关闭菜单
            menuView?.findViewById<FrameLayout>(R.id.menu_root_container)?.setOnClickListener {
                Log.d(TAG, "点击了根容器，关闭菜单")
                hideMenu()
            }

            // 设置菜单内容容器的点击事件，防止点击传递到根容器
            menuContentContainer?.setOnClickListener { view ->
                // 拦截点击事件，不做任何处理，防止点击传递到根容器
                Log.d(TAG, "点击了菜单内容容器，拦截事件")
                true
            }

            // 确保菜单内容容器可点击
            menuContentContainer?.isClickable = true

            // 设置菜单项点击事件
            setupMenuItemListeners(onMenuItemClick)

            // 添加菜单到窗口
            windowManager?.addView(menuView, menuParams)
            isMenuShowing = true

            // 添加显示动画 - 应用到菜单内容而不是整个窗口
            try {
                val animation = AnimationUtils.loadAnimation(context, R.anim.menu_fade_in)
                val menuContent = menuView?.findViewById<LinearLayout>(R.id.menu_content_container)?.getChildAt(0)
                menuContent?.startAnimation(animation)
                Log.d(TAG, "应用动画到菜单内容：menuContent=${menuContent}")
            } catch (e: Exception) {
                Log.e(TAG, "菜单显示动画执行失败", e)
            }

            Log.d(TAG, "下拉菜单已创建并显示")
        } catch (e: Exception) {
            Log.e(TAG, "创建下拉菜单失败", e)
        }
    }

    /**
     * 设置菜单项点击事件
     */
    private fun setupMenuItemListeners(onMenuItemClick: (menuItemId: Int) -> Unit) {
        // 获取菜单内容视图
        val menuContent = menuView?.findViewById<LinearLayout>(R.id.menu_content_container)?.getChildAt(0)

        // 检查是否为Android 14+设备
        val isAndroid14Plus = Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE

        // 截图收藏菜单项 - 仅在Android 14以下设备上设置点击事件
        if (!isAndroid14Plus) {
            menuContent?.findViewById<LinearLayout>(R.id.menu_item_share)?.setOnClickListener {
                Log.d(TAG, "点击了截图收藏菜单项")
                // 隐藏菜单
                hideMenu()
                // 显示截图操作界面
                val screenshotHelper = ScreenshotHelper.getInstance(context)
                screenshotHelper.showScreenshotOverlay(
                    onConfirm = {
                        // 确认按钮点击事件
                        Log.d(TAG, "截图收藏确认")
                        // 注意：实际的截图处理逻辑已经在ScreenshotHelper中实现
                    },
                    onDelete = {
                        // 删除按钮点击事件
                        Log.d(TAG, "取消截图收藏")
                    }
                )
                onMenuItemClick(R.id.menu_item_share)
            }
        }

        // 截长图菜单项 - 仅在Android 14以下设备上设置点击事件
        if (!isAndroid14Plus) {
            menuContent?.findViewById<LinearLayout>(R.id.menu_item_long_screenshot)?.setOnClickListener {
                Log.d(TAG, "点击了截长图菜单项")
                // 隐藏菜单
                hideMenu()
                // 显示截长图操作界面
                val longScreenshotHelper = LongScreenshotHelper.getInstance(context)
                longScreenshotHelper.startLongScreenshot()
                onMenuItemClick(R.id.menu_item_long_screenshot)
            }
        }

        // 收藏和设置菜单项已移除

        // 关闭悬浮窗菜单项
        menuContent?.findViewById<LinearLayout>(R.id.menu_item_close_floating_window)?.setOnClickListener {
            Log.d(TAG, "点击了关闭悬浮窗菜单项")
            hideMenu()
            // 创建一个新的ID用于关闭悬浮窗菜单项
            val closeFloatingWindowId = 1001
            onMenuItemClick(closeFloatingWindowId)
        }

        // 添加日志以便调试
        Log.d(TAG, "菜单项点击事件已设置，menuContent=${menuContent}")
    }

    /**
     * 检查菜单是否正在显示
     */
    fun isMenuVisible(): Boolean {
        return isMenuShowing
    }
}
