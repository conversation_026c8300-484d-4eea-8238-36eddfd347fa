package com.xunhe.aishoucang.lib

import android.os.Handler
import android.util.Log
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import java.util.concurrent.atomic.AtomicBoolean

class WebViewVideoClient(
    private val schemeUrlSetter: (String) -> Unit,
    private val isCompleted: AtomicBoolean,
    private val mainHandler: Handler,
    private val webViewProvider: () -> WebView?,
    private val callback: (success: Boolean, videoUrl: String?, errorMessage: String?) -> Unit,
    private val filters: Map<String, (url: String, contentType: String) -> Boolean>?,
    private val resultMapSetter: (key: String, url: String) -> Unit,
    private val foundMatchSetter: (Boolean) -> Unit,
    private val getResultMap: () -> Map<String, List<String>>,
    private val getFoundAnyMatch: () -> Boolean,
    private val tag: String,
    private val scheduleDestroy: () -> Unit,
    private val cancelAndDestroy: () -> Unit
) : WebViewClient() {
    private var redirectCount = 0
    private val MAX_REDIRECTS = 10 // 最大重定向次数
    private var nativeSchemeIntercepted = false
    private var interceptedNativeSchemeUrl: String? = null // 保存拦截到的原生协议URL

    // 跟踪每种类型过滤器匹配的数量
    private val filterTypeMatches = mutableMapOf<String, Int>()
    private var totalMatches = 0
    private var allFilterTypesMatched = false

    override fun shouldOverrideUrlLoading(
        view: WebView,
        request: WebResourceRequest
    ): Boolean {
        val scheme = request.url.scheme
        if (scheme == null || !scheme.startsWith("http")) {
            val nativeUrl = request.url.toString()
            Log.w(tag, "非HTTP(S)跳转被阻止: $nativeUrl")
            schemeUrlSetter(nativeUrl)
            nativeSchemeIntercepted = true
            interceptedNativeSchemeUrl = nativeUrl // 保存拦截到的原生协议URL

            // 将原生协议URL添加到结果中
            resultMapSetter("scheme", nativeUrl)

            scheduleDestroy() // 重置定时器

            // 如果所有类型的filter都匹配到了资源，立即销毁webview
            if (allFilterTypesMatched) {
                cancelAndDestroy()
                if (!isCompleted.getAndSet(true)) {
                    callback(true, interceptedNativeSchemeUrl, null)
                }
            }
            return true
        }

        Log.d(tag, "重定向到: ${request.url}")
        redirectCount++

        if (redirectCount > MAX_REDIRECTS) {
            Log.e(tag, "重定向次数超过限制: $redirectCount")
            if (!isCompleted.getAndSet(true)) {
                mainHandler.post {
                    cancelAndDestroy()
                    callback(false, null, "重定向次数超过限制")
                }
            }
            return true
        }

        return false
    }

    override fun shouldInterceptRequest(
        view: WebView,
        request: WebResourceRequest
    ): WebResourceResponse? {
        val url = request.url.toString()
        lateinit var connection: java.net.URLConnection
        var inputStream: java.io.InputStream? = null

        try {
            connection = java.net.URL(url).openConnection()
            connection.connectTimeout = 3000
            connection.connect()

            val responseContentType = connection.contentType ?: ""

            if(responseContentType.contains("image")) {
              Log.d(tag, "图片资源：${url}")
            }

            if (filters != null && filters.isNotEmpty()) {
                // 检查每种类型的过滤器
                for ((key, filter) in filters) {
                    if (filter(url, responseContentType)) {
                        // 添加到结果映射
                        resultMapSetter(key, url)

                        // 记录此类型过滤器已匹配
                        filterTypeMatches[key] = (filterTypeMatches[key] ?: 0) + 1
                        totalMatches++

                        // 设置找到匹配标志
                        foundMatchSetter(true)

                        // 重置定时器
                        scheduleDestroy()

                        // 检查是否所有类型都匹配了
                        allFilterTypesMatched = filterTypeMatches.keys.size == filters.size

                        // 如果拦截到原生协议且所有类型的过滤器都匹配到了资源，立即销毁webview
                        if (nativeSchemeIntercepted && allFilterTypesMatched) {
                            if (!isCompleted.getAndSet(true)) {
                                mainHandler.post {
                                    cancelAndDestroy()
                                    callback(true, interceptedNativeSchemeUrl, null)
                                }
                            }
                        }
                    }
                }
            }

            // 如果已经找到了匹配但尚未完成，延迟返回结果
            if (getFoundAnyMatch() && !isCompleted.get()) {
                mainHandler.postDelayed({
                    if (!isCompleted.getAndSet(true)) {
                        Log.d(tag, "已找到匹配资源，延迟返回结果")
                        cancelAndDestroy()
                        callback(true, interceptedNativeSchemeUrl, null)
                    }
                }, 5000)
            }

        } catch (e: Exception) {
            // 忽略异常，继续处理
        } finally {
            try {
                inputStream?.close()
            } catch (e: Exception) {
                // 忽略异常
            }
        }

        return super.shouldInterceptRequest(view, request)
    }

    override fun onPageFinished(view: WebView, loadedUrl: String) {
        Log.d(tag, "页面加载完成: $loadedUrl")
    }
}