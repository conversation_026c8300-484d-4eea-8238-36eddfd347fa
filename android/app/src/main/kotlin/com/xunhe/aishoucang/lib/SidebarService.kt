package com.xunhe.aishoucang.lib


import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager

import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.lib.AccessibilityHelper
import com.xunhe.aishoucang.helpers.AppHandlerFactory

class SidebarService : Service() {
    companion object {
        private const val TAG = "SidebarService"
        var lastPosition: Pair<Int, Int>? = null
    }

    private var windowManager: WindowManager? = null
    private var sidebarView: View? = null
    private var params: WindowManager.LayoutParams? = null

    // 拖动相关变量
    private var initialY = 0f
    private var initialTouchY = 0f
    private var isDragging = false

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "侧边栏服务创建")
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "侧边栏服务启动")

        val positionX = intent?.getIntExtra("position_x", Int.MIN_VALUE)
        val positionY = intent?.getIntExtra("position_y", Int.MIN_VALUE)

        createSidebar(
            if (positionX == Int.MIN_VALUE) null else positionX,
            if (positionY == Int.MIN_VALUE) null else positionY
        )

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "侧边栏服务销毁")
        removeSidebar()
    }

    private fun createSidebar(positionX: Int? = null, positionY: Int? = null) {
        try {
            if (sidebarView != null) {
                Log.d(TAG, "侧边栏已存在，先移除")
                removeSidebar()
            }

            val inflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
            sidebarView = inflater.inflate(R.layout.sidebar_layout, null)

            // 设置触摸事件处理拖动 - 直接在sidebarView上设置
            sidebarView?.setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        // 记录初始位置
                        initialY = params?.y?.toFloat() ?: 0f
                        initialTouchY = event.rawY
                        isDragging = false
                        Log.d(TAG, "开始触摸侧边栏容器，初始Y: $initialY, 触摸Y: $initialTouchY")
                        true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        // 计算Y方向的移动距离
                        val deltaY = event.rawY - initialTouchY
                        val newY = (initialY + deltaY).toInt()

                        // 限制Y坐标在屏幕范围内
                        val screenHeight = getScreenHeight()
                        val sidebarHeight = sidebarView?.height ?: 0
                        val constrainedY = when {
                            newY < 0 -> 0
                            newY + sidebarHeight > screenHeight -> screenHeight - sidebarHeight
                            else -> newY
                        }

                        // 更新位置（只改变Y坐标，X坐标保持不变）
                        params?.y = constrainedY
                        windowManager?.updateViewLayout(sidebarView, params)

                        // 标记为拖动状态
                        if (!isDragging && Math.abs(deltaY) > 10) {
                            isDragging = true
                            Log.d(TAG, "开始拖动侧边栏容器")
                        }

                        true
                    }
                    MotionEvent.ACTION_UP -> {
                        Log.d(TAG, "触摸结束，是否拖动: $isDragging")

                        // 如果没有拖动，则视为点击
                        if (!isDragging) {
                            Log.d(TAG, "侧边栏容器被点击")
                            showSidebarFloatingMenu()
                        } else {
                            // 更新最后位置
                            lastPosition = Pair(params?.x ?: 0, params?.y ?: 0)
                            Log.d(TAG, "拖动结束，新位置: ${lastPosition}")
                        }

                        isDragging = false
                        true
                    }
                    else -> false
                }
            }

            // 获取屏幕尺寸
            val screenWidth = getScreenWidth()
            val screenHeight = getScreenHeight()
            Log.d(TAG, "屏幕尺寸: ${screenWidth}x${screenHeight}")

            // 设置布局参数
            params = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                // 使用FLAG_NOT_FOCUSABLE避免获取焦点，但仍可接收触摸事件
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                        WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                format = PixelFormat.TRANSLUCENT
                // 设置gravity为左上角，使用绝对坐标
                gravity = Gravity.TOP or Gravity.LEFT
            }

            // 设置初始位置 - 确保在可见区域
            val defaultX = positionX ?: 0 // 距离屏幕左边缘0px，贴边显示
            val defaultY = positionY ?: (screenHeight * 0.7).toInt() // 屏幕高度的70%

            params?.x = defaultX
            params?.y = defaultY

            Log.d(TAG, "创建侧边栏，位置: x=${params?.x}, y=${params?.y}, gravity=${params?.gravity}")

            windowManager?.addView(sidebarView, params)

            // 更新最后位置
            lastPosition = Pair(params?.x ?: 0, params?.y ?: 0)

            Log.d(TAG, "侧边栏创建成功，视图已添加到窗口管理器")

        } catch (e: Exception) {
            Log.e(TAG, "创建侧边栏失败", e)
        }
    }

    private fun removeSidebar() {
        try {
            sidebarView?.let { view ->
                windowManager?.removeView(view)
                sidebarView = null
                Log.d(TAG, "侧边栏已移除")
            }
        } catch (e: Exception) {
            Log.e(TAG, "移除侧边栏失败", e)
        }
    }

    private fun getScreenWidth(): Int {
        return resources.displayMetrics.widthPixels
    }

    private fun getScreenHeight(): Int {
        return resources.displayMetrics.heightPixels
    }

    private fun showSidebarFloatingMenu() {
        try {
            Log.d(TAG, "侧边栏被点击，直接拉起收藏面板")

            // 直接拉起收藏面板
            showCollectionPanel()

        } catch (e: Exception) {
            Log.e(TAG, "拉起收藏面板失败", e)
        }
    }

    /**
     * 显示收藏面板
     * 使用与收藏悬浮窗相同的逻辑
     */
    private fun showCollectionPanel() {
        try {
            Log.d(TAG, "开始显示收藏面板")

            // 获取当前应用包名
            val helper = AccessibilityHelper.getInstance(applicationContext)
            val rootNode = helper.getRootNode()
            val currentPackage = rootNode?.packageName?.toString()

            // 判断当前应用是否是支持的应用
            val handler = AppHandlerFactory.getHandlerByPackage(currentPackage)
            val isUnknownApp = handler is com.xunhe.aishoucang.helpers.UnknownAppHandler

            Log.d(TAG, "当前应用包名: $currentPackage, 是否为未知应用: $isUnknownApp")

            // 调用SharePanelHelper显示分享面板，传递unSupport参数
            SharePanelHelper.showSharePanel(applicationContext, windowManager, isUnknownApp)

            Log.d(TAG, "收藏面板显示成功")
        } catch (e: Exception) {
            Log.e(TAG, "显示收藏面板失败", e)
            CustomToastHelper.showShortToast(this, "显示收藏面板失败")
        }
    }


}
