package com.xunhe.aishoucang.api

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.helpers.ConfigHelper
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.lib.RequestHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * 任务API接口
 * 负责处理任务相关的网络请求
 */
class TaskApi private constructor(private val context: Context) {
    companion object {
        private const val TAG = "TaskApi"

        @Volatile
        private var instance: TaskApi? = null

        fun getInstance(context: Context): TaskApi {
            return instance ?: synchronized(this) {
                instance ?: TaskApi(context.applicationContext).also { instance = it }
            }
        }
    }

    private val requestHelper = RequestHelper.getInstance(context)

    /**
     * 创建任务
     *
     * @param taskType 任务类型：1=提取文案，2=下载视频，3=创建笔记，4=更新笔记
     * @param title 任务标题，长度不超过255个字符
     * @param platform 平台信息，长度不超过50个字符，如：'bilibili'、'xiaohongshu'
     * @param url 要提取的地址
     * @param sourceType 内容源类型：1=音视频内容，2=图文内容
     * @param noteId 笔记ID，更新笔记任务时必填
     * @param callback 回调函数，参数为(success: Boolean, result: String?, error: String?)
     */
    fun createTask(
        taskType: Int,
        title: String,
        platform: String,
        url: String,
        sourceType: Int = 0,
        noteId: String? = null,
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        Log.d(TAG, "开始创建任务")
        Log.d(TAG, "任务类型: $taskType")
        Log.d(TAG, "任务标题: $title")
        Log.d(TAG, "平台信息: $platform")
        Log.d(TAG, "URL: $url")
        Log.d(TAG, "内容源类型: $sourceType")
        Log.d(TAG, "笔记ID: $noteId")

        // 创建请求体
        val requestBody = JSONObject().apply {
            put("task_type", taskType)
            put("title", title)
            put("platform", platform)
            put("url", url)
            put("source_type", sourceType)
            // 如果是更新笔记任务且提供了noteId，则添加note_id字段
            if (taskType == 4 && !noteId.isNullOrEmpty()) {
                put("note_id", noteId)
            }
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "发送创建任务请求: $requestBody")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper.postJson(
                    "$apiBaseUrl/task/create",
                    requestBody,
                    null
                )

                when (result) {
                    is RequestHelper.ApiResult.Success -> {
                        Log.d(TAG, "创建任务成功: ${result.data}")

                        // 解析响应
                        try {
                            val jsonResponse = JSONObject(result.data)
                            val code = jsonResponse.optInt("code", -1)
                            val message = jsonResponse.optString("message", "")

                            if (code == 0) {
                                val data = jsonResponse.optJSONObject("data")
                                Log.d(TAG, "任务创建成功，返回数据: $data")
                                callback?.invoke(true, data?.toString(), null)
                            } else {
                                android.os.Handler(android.os.Looper.getMainLooper()).post {
                                    CustomToastHelper.showToast(context, "$message")
                                }
                                callback?.invoke(false, null, "任务创建失败: $message")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解析创建任务响应失败", e)
                            callback?.invoke(false, null, "解析响应失败: ${e.message}")
                        }
                    }
                    is RequestHelper.ApiResult.Error -> {
                        Log.e(TAG, "创建任务请求失败: ${result.message}")
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            CustomToastHelper.showToast(context, "${result.message}")
                        }
                        callback?.invoke(false, null, "请求失败: ${result.message}")
                    }
                    is RequestHelper.ApiResult.Exception -> {
                        Log.e(TAG, "创建任务请求异常", result.throwable)
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            CustomToastHelper.showToast(context, "${result.throwable.message}")
                        }

                        callback?.invoke(false, null, "请求异常: ${result.throwable.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "创建任务时发生未知错误", e)
                callback?.invoke(false, null, "未知错误: ${e.message}")
            }
        }
    }

    /**
     * 检查是否有正在进行的笔记创建任务
     *
     * @param callback 回调函数，参数为(success: Boolean, hasTask: Boolean, taskId: String?, error: String?)
     */
    fun checkCreateNoteTask(
        callback: ((Boolean, Boolean, String?, String?) -> Unit)? = null
    ) {
        Log.d(TAG, "开始检查笔记创建任务")

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "发送检查笔记任务请求")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper.get(
                    "$apiBaseUrl/task/note/check",
                    null,
                    null
                )

                when (result) {
                    is RequestHelper.ApiResult.Success -> {
                        Log.d(TAG, "检查笔记任务成功: ${result.data}")

                        // 解析响应
                        try {
                            val jsonResponse = JSONObject(result.data)
                            val code = jsonResponse.optInt("code", -1)
                            val message = jsonResponse.optString("message", "")

                            if (code == 0) {
                                val data = jsonResponse.optJSONObject("data")
                                val hasTask =
                                    data?.optBoolean("has_create_note_task", false) ?: false
                                val taskId =
                                    if (data?.has("task_id") == true && !data.isNull("task_id")) {
                                        data.optString("task_id")
                                    } else {
                                        null
                                    }

                                Log.d(TAG, "检查笔记任务结果: hasTask=$hasTask, taskId=$taskId")
                                callback?.invoke(true, hasTask, taskId, null)
                            } else {
                                Log.e(TAG, "检查笔记任务失败，错误码: $code, 错误信息: $message")
                                callback?.invoke(false, false, null, "检查失败: $message")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解析检查笔记任务响应失败", e)
                            callback?.invoke(false, false, null, "解析响应失败: ${e.message}")
                        }
                    }
                    is RequestHelper.ApiResult.Error -> {
                        Log.e(TAG, "检查笔记任务请求失败: ${result.message}")
                        callback?.invoke(false, false, null, "请求失败: ${result.message}")
                    }
                    is RequestHelper.ApiResult.Exception -> {
                        Log.e(TAG, "检查笔记任务请求异常", result.throwable)
                        callback?.invoke(false, false, null, "请求异常: ${result.throwable.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查笔记任务时发生异常", e)
                callback?.invoke(false, false, null, "检查异常: ${e.message}")
            }
        }
    }

    /**
     * 查询任务状态
     *
     * @param taskId 任务ID
     * @param callback 回调函数，参数为(success: Boolean, result: String?, error: String?)
     */
    fun getTaskStatus(
        taskId: String,
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        Log.d(TAG, "开始查询任务状态: $taskId")

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 构建查询参数
                val queryParams = mutableMapOf<String, String>().apply {
                    put("task_id", taskId)
                }

                Log.d(TAG, "查询参数: $queryParams")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper.get(
                    "$apiBaseUrl/task/status",
                    queryParams,
                    null
                )

                when (result) {
                    is RequestHelper.ApiResult.Success -> {
                        Log.d(TAG, "查询任务状态成功: ${result.data}")

                        // 解析响应
                        try {
                            val jsonResponse = JSONObject(result.data)
                            val code = jsonResponse.optInt("code", -1)
                            val message = jsonResponse.optString("message", "")

                            if (code == 0) {
                                val data = jsonResponse.optJSONObject("data")
                                Log.d(TAG, "任务状态查询成功，返回数据: $data")
                                callback?.invoke(true, data?.toString(), null)
                            } else {
                                Log.e(TAG, "任务状态查询失败，错误码: $code, 错误信息: $message")
                                callback?.invoke(false, null, "查询任务状态失败: $message")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解析任务状态响应失败", e)
                            callback?.invoke(false, null, "解析响应失败: ${e.message}")
                        }
                    }
                    is RequestHelper.ApiResult.Error -> {
                        Log.e(TAG, "查询任务状态请求失败: ${result.message}")
                        callback?.invoke(false, null, "请求失败: ${result.message}")
                    }
                    is RequestHelper.ApiResult.Exception -> {
                        Log.e(TAG, "查询任务状态请求异常", result.throwable)
                        callback?.invoke(false, null, "请求异常: ${result.throwable.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "查询任务状态时发生未知错误", e)
                callback?.invoke(false, null, "未知错误: ${e.message}")
            }
        }
    }

    /**
     * 获取任务列表
     *
     * @param page 页码，从1开始
     * @param pageSize 每页数量，范围1-100
     * @param taskType 任务类型过滤，可选：1=提取文案，2=下载视频
     * @param platform 平台过滤，可选
     * @param status 状态过滤，可选：1=新创建，2=已完成，3=失败
     * @param callback 回调函数，参数为(success: Boolean, result: String?, error: String?)
     */
    fun getTaskList(
        page: Int = 1,
        pageSize: Int = 10,
        taskType: Int? = null,
        platform: String? = null,
        status: Int? = null,
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        Log.d(TAG, "开始获取任务列表")
        Log.d(TAG, "页码: $page")
        Log.d(TAG, "每页数量: $pageSize")
        Log.d(TAG, "任务类型: $taskType")
        Log.d(TAG, "平台: $platform")
        Log.d(TAG, "状态: $status")

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 构建查询参数
                val queryParams = mutableMapOf<String, String>().apply {
                    put("page", page.toString())
                    put("page_size", pageSize.toString())
                    taskType?.let { put("task_type", it.toString()) }
                    platform?.let { if (it.isNotEmpty()) put("platform", it) }
                    status?.let { put("status", it.toString()) }
                }

                Log.d(TAG, "查询参数: $queryParams")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper.get(
                    "$apiBaseUrl/task/list",
                    queryParams,
                    null
                )

                when (result) {
                    is RequestHelper.ApiResult.Success -> {
                        Log.d(TAG, "获取任务列表成功: ${result.data}")

                        // 解析响应
                        try {
                            val jsonResponse = JSONObject(result.data)
                            val code = jsonResponse.optInt("code", -1)
                            val message = jsonResponse.optString("message", "")

                            if (code == 0) {
                                val data = jsonResponse.optJSONObject("data")
                                Log.d(TAG, "任务列表获取成功，返回数据: $data")
                                callback?.invoke(true, data?.toString(), null)
                            } else {
                                Log.e(TAG, "任务列表获取失败，错误码: $code, 错误信息: $message")
                                callback?.invoke(false, null, "获取任务列表失败: $message")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解析任务列表响应失败", e)
                            callback?.invoke(false, null, "解析响应失败: ${e.message}")
                        }
                    }
                    is RequestHelper.ApiResult.Error -> {
                        Log.e(TAG, "获取任务列表请求失败: ${result.message}")
                        callback?.invoke(false, null, "请求失败: ${result.message}")
                    }
                    is RequestHelper.ApiResult.Exception -> {
                        Log.e(TAG, "获取任务列表请求异常", result.throwable)
                        callback?.invoke(false, null, "请求异常: ${result.throwable.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取任务列表时发生未知错误", e)
                callback?.invoke(false, null, "未知错误: ${e.message}")
            }
        }
    }
}
