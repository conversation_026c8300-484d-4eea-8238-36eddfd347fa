package com.xunhe.aishoucang.lib

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.graphics.RectF
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import com.xunhe.aishoucang.helpers.SourceToOssLinkHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.util.concurrent.Executors

/**
 * 截图捕获助手
 * 用于获取屏幕截图、裁剪选中区域、上传到OSS
 */
class ScreenshotCaptureHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "ScreenshotCaptureHelper"
        private const val VIRTUAL_DISPLAY_NAME = "screenshot_display"

        @Volatile
        private var instance: ScreenshotCaptureHelper? = null

        fun getInstance(context: Context): ScreenshotCaptureHelper {
            return instance ?: synchronized(this) {
                instance ?: ScreenshotCaptureHelper(context.applicationContext).also { instance = it }
            }
        }
    }

    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var imageReader: ImageReader? = null
    private val handler = Handler(Looper.getMainLooper())
    private val executor = Executors.newSingleThreadExecutor()

    // MediaProjection回调（Android 14+需要）
    private var mediaProjectionCallback: MediaProjection.Callback? = null

    private var screenWidth = 0
    private var screenHeight = 0
    private var screenDensity = 0

    init {
        // 获取屏幕尺寸和密度
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val metrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(metrics)
        screenWidth = metrics.widthPixels
        screenHeight = metrics.heightPixels
        screenDensity = metrics.densityDpi
    }

    /**
     * 开始截图（使用前台服务）
     *
     * @param resultCode 从onActivityResult获取的结果码
     * @param data 从onActivityResult获取的数据
     * @param selectionRect 选中区域
     * @param callback 回调函数，返回OSS链接
     */
    fun captureScreenshot(resultCode: Int, data: Intent, selectionRect: RectF, callback: (success: Boolean, ossUrl: String?, error: String?) -> Unit) {
        try {
            // 启动前台服务进行截图
            ScreenshotService.startScreenshotService(context, resultCode, data, selectionRect)

            // 由于截图操作在服务中进行，这里直接返回
            // 实际的回调会在服务中处理
            Log.d(TAG, "已启动截图服务")
        } catch (e: Exception) {
            Log.e(TAG, "启动截图服务失败", e)
            callback(false, null, "启动截图服务失败: ${e.message}")
        }
    }

    /**
     * 使用MediaProjection进行截图（在服务中调用）
     *
     * @param mediaProjection MediaProjection实例
     * @param selectionRect 选中区域
     * @param callback 回调函数，返回OSS链接
     */
    fun captureScreenshotWithProjection(mediaProjection: MediaProjection, selectionRect: RectF, callback: (success: Boolean, ossUrl: String?, error: String?) -> Unit) {
        try {
            this.mediaProjection = mediaProjection

            // Android 14+需要注册回调
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                mediaProjectionCallback = object : MediaProjection.Callback() {
                    override fun onStop() {
                        Log.d(TAG, "MediaProjection已停止")
                        releaseResources()
                    }
                }
                this.mediaProjection?.registerCallback(mediaProjectionCallback!!, handler)
                Log.d(TAG, "已注册MediaProjection回调（Android 14+）")
            }

            // 创建ImageReader
            imageReader = ImageReader.newInstance(
                screenWidth, screenHeight, PixelFormat.RGBA_8888, 2
            )

            // 创建虚拟显示
            virtualDisplay = this.mediaProjection?.createVirtualDisplay(
                VIRTUAL_DISPLAY_NAME,
                screenWidth, screenHeight, screenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader?.surface, null, handler
            )

            // 延迟一段时间后获取图像，确保虚拟显示已经准备好
            handler.postDelayed({
                captureImage(selectionRect, callback)
            }, 300)
        } catch (e: Exception) {
            Log.e(TAG, "截图失败", e)
            callback(false, null, "截图失败: ${e.message}")
            releaseResources()
        }
    }

    /**
     * 捕获图像
     */
    private fun captureImage(selectionRect: RectF, callback: (success: Boolean, ossUrl: String?, error: String?) -> Unit) {
        executor.execute {
            try {
                val image = imageReader?.acquireLatestImage()
                if (image == null) {
                    handler.post {
                        callback(false, null, "无法获取图像")
                    }
                    releaseResources()
                    return@execute
                }

                // 将Image转换为Bitmap
                val bitmap = imageToBitmap(image)
                image.close()

                if (bitmap == null) {
                    handler.post {
                        callback(false, null, "无法转换图像")
                    }
                    releaseResources()
                    return@execute
                }

                // 裁剪选中区域
                val croppedBitmap = cropBitmap(bitmap, selectionRect)
                bitmap.recycle()

                if (croppedBitmap == null) {
                    handler.post {
                        callback(false, null, "无法裁剪图像")
                    }
                    releaseResources()
                    return@execute
                }

                // 保存裁剪后的图像到文件
                val file = saveBitmapToFile(croppedBitmap)
                croppedBitmap.recycle()

                if (file == null) {
                    handler.post {
                        callback(false, null, "无法保存图像")
                    }
                    releaseResources()
                    return@execute
                }

                // 返回文件路径，由调用者处理上传
                handler.post {
                    callback(true, file.absolutePath, null)
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理图像失败", e)
                handler.post {
                    callback(false, null, "处理图像失败: ${e.message}")
                }
                releaseResources()
            }
        }
    }

    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * screenWidth

            // 创建Bitmap
            val bitmap = Bitmap.createBitmap(
                screenWidth + rowPadding / pixelStride,
                screenHeight,
                Bitmap.Config.ARGB_8888
            )

            buffer.rewind()
            bitmap.copyPixelsFromBuffer(buffer)

            return bitmap
        } catch (e: Exception) {
            Log.e(TAG, "转换图像失败", e)
            return null
        }
    }

    /**
     * 裁剪Bitmap
     */
    private fun cropBitmap(bitmap: Bitmap, rect: RectF): Bitmap? {
        try {
            // 确保裁剪区域在有效范围内
            val left = rect.left.coerceIn(0f, bitmap.width.toFloat()).toInt()
            val top = rect.top.coerceIn(0f, bitmap.height.toFloat()).toInt()
            val right = rect.right.coerceIn(0f, bitmap.width.toFloat()).toInt()
            val bottom = rect.bottom.coerceIn(0f, bitmap.height.toFloat()).toInt()

            // 计算裁剪区域的宽高
            val width = right - left
            val height = bottom - top

            // 确保宽高大于0
            if (width <= 0 || height <= 0) {
                Log.e(TAG, "裁剪区域无效: $rect")
                return null
            }

            // 裁剪Bitmap
            return Bitmap.createBitmap(bitmap, left, top, width, height)
        } catch (e: Exception) {
            Log.e(TAG, "裁剪图像失败", e)
            return null
        }
    }

    /**
     * 保存Bitmap到文件
     */
    private fun saveBitmapToFile(bitmap: Bitmap): File? {
        try {
            // 创建临时文件
            val fileName = "screenshot_${System.currentTimeMillis()}.jpg"
            val file = File(context.cacheDir, fileName)

            // 保存Bitmap到文件
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)
                out.flush()
            }

            return file
        } catch (e: Exception) {
            Log.e(TAG, "保存图像失败", e)
            return null
        }
    }

    /**
     * 释放资源
     */
    private fun releaseResources() {
        try {
            virtualDisplay?.release()
            virtualDisplay = null

            imageReader?.close()
            imageReader = null

            // 取消注册MediaProjection回调（Android 14+）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE &&
                mediaProjectionCallback != null && mediaProjection != null) {
                try {
                    mediaProjection?.unregisterCallback(mediaProjectionCallback!!)
                    Log.d(TAG, "已取消注册MediaProjection回调")
                } catch (e: Exception) {
                    Log.w(TAG, "取消注册MediaProjection回调失败: ${e.message}")
                }
                mediaProjectionCallback = null
            }

            // 注意：不要在这里停止mediaProjection，因为它是由服务管理的
            // mediaProjection?.stop()
            // mediaProjection = null
        } catch (e: Exception) {
            Log.e(TAG, "释放资源失败", e)
        }
    }

    /**
     * 请求截图权限
     *
     * @param activity 当前Activity
     * @param requestCode 请求码
     */
    fun requestScreenshotPermission(activity: Activity, requestCode: Int) {
        val projectionManager = activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        activity.startActivityForResult(projectionManager.createScreenCaptureIntent(), requestCode)
    }
}
