package com.xunhe.aishoucang.lib

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log

/**
 * 剪贴板助手类
 * 负责处理剪贴板相关操作
 */
class ClipboardHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "ClipboardHelper"
        
        @Volatile
        private var instance: ClipboardHelper? = null
        
        fun getInstance(context: Context): ClipboardHelper {
            return instance ?: synchronized(this) {
                instance ?: ClipboardHelper(context.applicationContext).also { instance = it }
            }
        }
    }
    
    private val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    private val mainHandler = Handler(Looper.getMainLooper())
    private var isMonitoring = false
    private var onClipboardChangedListener: ((String?) -> Unit)? = null
    
    // 剪贴板变化监听器
    private val clipboardChangeListener = ClipboardManager.OnPrimaryClipChangedListener {
        val text = getClipboardText()
        Log.d(TAG, "剪贴板内容变化: $text")
        
        // 在主线程中回调
        mainHandler.post {
            onClipboardChangedListener?.invoke(text)
        }
    }
    
    /**
     * 开始监听剪贴板变化
     */
    fun startMonitor() {
        if (!isMonitoring) {
            clipboardManager.addPrimaryClipChangedListener(clipboardChangeListener)
            isMonitoring = true
            Log.d(TAG, "开始监听剪贴板变化")
        }
    }
    
    /**
     * 停止监听剪贴板变化
     */
    fun stopMonitor() {
        if (isMonitoring) {
            clipboardManager.removePrimaryClipChangedListener(clipboardChangeListener)
            isMonitoring = false
            Log.d(TAG, "停止监听剪贴板变化")
        }
    }
    
    /**
     * 获取剪贴板文本内容
     */
    fun getClipboardText(): String? {
        if (!clipboardManager.hasPrimaryClip()) {
            return null
        }
        
        val clip = clipboardManager.primaryClip
        if (clip != null && clip.itemCount > 0) {
            val item = clip.getItemAt(0)
            return item.text?.toString()
        }
        
        return null
    }
    
    /**
     * 设置剪贴板文本内容
     */
    fun setClipboardText(text: String) {
        val clip = ClipData.newPlainText("text", text)
        clipboardManager.setPrimaryClip(clip)
        Log.d(TAG, "设置剪贴板内容: $text")
    }
    
    /**
     * 设置剪贴板变化监听器
     */
    fun setOnClipboardChangedListener(listener: ((String?) -> Unit)?) {
        onClipboardChangedListener = listener
    }
}
