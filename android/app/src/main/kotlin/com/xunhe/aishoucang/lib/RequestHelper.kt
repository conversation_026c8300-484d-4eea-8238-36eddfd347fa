package com.xunhe.aishoucang.lib

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

/**
 * 网络请求辅助类，用于封装HTTP的GET和POST请求
 */
class RequestHelper private constructor(private val context: Context) {
    companion object {
        private const val TAG = "RequestHelper"
        private const val TIMEOUT = 15000 // 15秒超时
        
        @Volatile
        private var instance: RequestHelper? = null
        
        fun getInstance(context: Context): RequestHelper {
            return instance ?: synchronized(this) {
                instance ?: RequestHelper(context.applicationContext).also { instance = it }
            }
        }
    }
    
    /**
     * API结果封装类
     */
    sealed class ApiResult<out T> {
        data class Success<T>(val data: T) : ApiResult<T>()
        data class Error(val code: Int, val message: String, val errorBody: String? = null) : ApiResult<Nothing>()
        data class Exception(val throwable: Throwable) : ApiResult<Nothing>()
    }
    
    /**
     * 发送GET请求
     * @param url 请求URL
     * @param params 请求参数，会被拼接到URL后
     * @param headers 请求头
     * @return ApiResult封装的响应结果
     */
    suspend fun get(
        url: String,
        params: Map<String, String>? = null,
        headers: Map<String, String>? = null
    ): ApiResult<String> = withContext(Dispatchers.IO) {
        try {
            val urlWithParams = buildUrlWithParams(url, params)
            Log.d(TAG, "GET请求: $urlWithParams")
            
            val connection = createConnection(urlWithParams, "GET", headers)
            executeRequest(connection)
        } catch (e: Exception) {
            Log.e(TAG, "GET请求失败", e)
            ApiResult.Exception(e)
        }
    }
    
    /**
     * 发送POST请求（JSON格式）
     * @param url 请求URL
     * @param jsonBody 请求体JSON对象
     * @param headers 请求头
     * @return ApiResult封装的响应结果
     */
    suspend fun postJson(
        url: String,
        jsonBody: JSONObject,
        headers: Map<String, String>? = null
    ): ApiResult<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "POST JSON请求: $url, 内容: $jsonBody")
            
            val connection = createConnection(url, "POST", headers).apply {
                setRequestProperty("Content-Type", "application/json; charset=UTF-8")
                setRequestProperty("Accept", "application/json")
                doOutput = true
            }
            
            // 写入请求体
            OutputStreamWriter(connection.outputStream, StandardCharsets.UTF_8).use { writer ->
                writer.write(jsonBody.toString())
                writer.flush()
            }
            
            executeRequest(connection)
        } catch (e: Exception) {
            Log.e(TAG, "POST请求失败", e)
            ApiResult.Exception(e)
        }
    }
    
    /**
     * 发送POST请求（表单格式）
     * @param url 请求URL
     * @param formData 表单数据
     * @param headers 请求头
     * @return ApiResult封装的响应结果
     */
    suspend fun postForm(
        url: String,
        formData: Map<String, String>,
        headers: Map<String, String>? = null
    ): ApiResult<String> = withContext(Dispatchers.IO) {
        try {
            val formDataString = encodeFormData(formData)
            Log.d(TAG, "POST表单请求: $url, 内容: $formDataString")
            
            val connection = createConnection(url, "POST", headers).apply {
                setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
                doOutput = true
            }
            
            // 写入请求体
            OutputStreamWriter(connection.outputStream, StandardCharsets.UTF_8).use { writer ->
                writer.write(formDataString)
                writer.flush()
            }
            
            executeRequest(connection)
        } catch (e: Exception) {
            Log.e(TAG, "POST请求失败", e)
            ApiResult.Exception(e)
        }
    }
    
    /**
     * 解析JSON响应
     * @param response API响应结果
     * @return ApiResult封装的JSONObject对象
     */
    fun parseJsonResponse(response: ApiResult<String>): ApiResult<JSONObject> {
        return when (response) {
            is ApiResult.Success -> {
                try {
                    ApiResult.Success(JSONObject(response.data))
                } catch (e: Exception) {
                    Log.e(TAG, "解析JSON失败", e)
                    ApiResult.Exception(e)
                }
            }
            is ApiResult.Error -> ApiResult.Error(response.code, response.message, response.errorBody)
            is ApiResult.Exception -> ApiResult.Exception(response.throwable)
        }
    }
    
    // 私有辅助方法
    
    /**
     * 构建带参数的URL
     */
    private fun buildUrlWithParams(url: String, params: Map<String, String>?): String {
        return if (params != null && params.isNotEmpty()) {
            val queryString = params.entries.joinToString("&") { (key, value) ->
                "${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value, "UTF-8")}"
            }
            "$url${if (url.contains("?")) "&" else "?"}$queryString"
        } else {
            url
        }
    }
    
    /**
     * 编码表单数据
     */
    private fun encodeFormData(formData: Map<String, String>): String {
        return formData.entries.joinToString("&") { (key, value) ->
            "${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value, "UTF-8")}"
        }
    }
    
    /**
     * 创建HTTP连接
     */
    private fun createConnection(
        url: String,
        method: String,
        headers: Map<String, String>?
    ): HttpURLConnection {
        return (URL(url).openConnection() as HttpURLConnection).apply {
            requestMethod = method
            connectTimeout = TIMEOUT
            readTimeout = TIMEOUT
            
            // 添加认证令牌
            getAuthToken()?.let { token ->
                setRequestProperty("Authorization", "Bearer $token")
            }
            
            // 添加自定义请求头
            headers?.forEach { (key, value) ->
                setRequestProperty(key, value)
            }
        }
    }
    
    /**
     * 从 SharedPreferencesHelper 获取认证令牌
     */
    private fun getAuthToken(): String? {
        val token = SharedPreferencesHelper.getInstance(context).getAuthToken()
        return if (token.isNotEmpty()) token else null
    }
    
    /**
     * 执行请求并处理响应
     */
    private fun executeRequest(connection: HttpURLConnection): ApiResult<String> {
        val responseCode = connection.responseCode
        
        return if (responseCode == HttpURLConnection.HTTP_OK) {
            val reader = BufferedReader(InputStreamReader(connection.inputStream))
            val response = reader.use { it.readText() }
            Log.d(TAG, "响应: $response")
            ApiResult.Success(response)
        } else {
            // 读取错误信息
            val errorStream = connection.errorStream
            val errorBody = if (errorStream != null) {
                BufferedReader(InputStreamReader(errorStream)).use { it.readText() }
            } else {
                null
            }
            
            val errorMessage = "HTTP错误: $responseCode"
            Log.e(TAG, "$errorMessage, 错误内容: $errorBody")
            ApiResult.Error(responseCode, errorMessage, errorBody)
        }
    }
}