package com.xunhe.aishoucang.api

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.lib.RequestHelper
import com.xunhe.aishoucang.helpers.ConfigHelper
import com.xunhe.aishoucang.helpers.CustomToastModalHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import org.json.JSONArray
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.nio.charset.StandardCharsets

/**
 * 笔记数据类
 */
data class NoteItem(
    val id: String,
    val parent_id: String?,
    val user_id: String,
    val title: String,
    val cover: String?,
    val desc: String?,
    val content: String?,
    val html: String,
    val create_time: String,
    val update_time: String
)

/**
 * 笔记列表响应数据类
 */
data class NoteListResponse(
    val notes: List<NoteItem>,
    val total: Int,
    val page: Int,
    val page_size: Int
)

/**
 * OpenAI方法调用参数数据类
 */
data class OpenAiMethodParams(
    val context: Context,
    val model: String,
    val promptId: String,
    val placeholderData: List<Map<String, String>> = emptyList()
)

/**
 * 笔记相关API
 */
class NoteApi {
    companion object {
        private const val TAG = "NoteApi"
        private var requestHelper: RequestHelper? = null

        /**
         * 当前笔记标题（用于临时保存用户输入的标题）
         */
        var currentNoteTitle: String = ""

        /**
         * 当前笔记封面（用于临时保存从 extractVideo 提取的封面信息）
         */
        var currentNoteCover: String = ""

        /**
         * 当前更新的笔记ID（用于临时保存要更新的笔记ID）
         */
        var currentUpdateNoteId: String = ""

        /**
         * 当前更新的笔记内容（用于临时保存从草稿或详情获取的内容）
         */
        var currentUpdateNoteContent: String = ""

        /**
         * 是否正在更新笔记的状态标记
         */
        var isUpdating: Boolean = false

        /**
         * 初始化RequestHelper
         */
        private fun init(context: Context) {
            if (requestHelper == null) {
                requestHelper = RequestHelper.getInstance(context)
            }
        }
    }

    /**
     * 创建笔记
     *
     * @param context 上下文
     * @param title 笔记标题，必填，最大长度255字符
     * @param content 笔记内容，必填
     * @param parent_id 合集ID，可选，如果笔记属于某个合集
     * @param cover 笔记封面图片URL，可选
     * @param desc 笔记描述，可选
     * @param html HTML格式的笔记内容，可选
     * @param callback 回调函数，成功返回true和笔记ID，失败返回false和错误信息
     */
    fun createNote(
        context: Context,
        title: String,
        content: String,
        parent_id: String? = null,
        cover: String? = null,
        desc: String? = null,
        html: String? = null,
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        // 确保RequestHelper已初始化
        init(context)

        // 参数验证
        if (title.isEmpty()) {
            callback?.invoke(false, null, "笔记标题不能为空")
            return
        }

        if (title.length > 255) {
            callback?.invoke(false, null, "笔记标题长度不能超过255字符")
            return
        }

        // 创建请求体
        val requestBody = JSONObject().apply {
            put("title", title)
            put("content", content)
            parent_id?.let { if (it.isNotEmpty()) put("parent_id", it) }
            cover?.let { if (it.isNotEmpty()) put("cover", it) }
            desc?.let { if (it.isNotEmpty()) put("desc", it) }
            html?.let { if (it.isNotEmpty()) put("html", it) }
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "发送创建笔记请求: $title")
                Log.d(TAG, "封面参数: $cover")
                Log.d(TAG, "请求体: $requestBody")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper?.postJson("$apiBaseUrl/note/create",requestBody,null)

                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestHelper.ApiResult.Success<String> -> {
                            try {
                                val jsonResponse = JSONObject(result.data)
                                val code = jsonResponse.optInt("code", -1)
                                val message = jsonResponse.optString("message", "未知错误")

                                if (code == 0) {
                                    // 成功创建笔记，获取笔记ID
                                    val dataObject = jsonResponse.optJSONObject("data")
                                    val noteId = dataObject?.optString("id", "")

                                    Log.d(TAG, "笔记创建成功，ID: $noteId")
                                    callback?.invoke(true, noteId, null)
                                } else {
                                    Log.w(TAG, "笔记创建失败: $message")
                                    callback?.invoke(false, null, message)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析响应失败", e)
                                callback?.invoke(false, null, "解析响应失败: ${e.message}")
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            Log.e(TAG, "服务器错误: ${result.message}")
                            callback?.invoke(false, null, "服务器错误: ${result.message}")
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "网络请求异常", result.throwable)
                            callback?.invoke(false, null, "网络请求异常: ${result.throwable.message}")
                        }
                        null -> {
                            Log.e(TAG, "RequestHelper未初始化")
                            callback?.invoke(false, null, "系统错误: RequestHelper未初始化")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "创建笔记时发生异常", e)
                withContext(Dispatchers.Main) {
                    callback?.invoke(false, null, "创建笔记失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 通过AI生成笔记内容
     *
     * @param context 上下文
     * @param noteContent 笔记内容，作为AI的输入
     * @param noteTitle 笔记标题，用于AI提示词中的标题替换
     * @param callback 回调函数，成功返回true和AI生成的内容，失败返回false和错误信息
     */
    fun generateNoteByAi(
        context: Context,
        noteContent: String,
        noteTitle: String = "",
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        // 参数验证
        if (noteContent.isEmpty()) {
            callback?.invoke(false, null, "笔记内容不能为空")
            return
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始AI生成笔记内容: ${noteContent.take(100)}...")

                // 调用OpenAI API
                val aiResponse = callOpenAiApi(noteContent, noteTitle, context)

                withContext(Dispatchers.Main) {
                    if (aiResponse.isNotEmpty()) {
                        Log.d(TAG, "AI生成内容成功: ${aiResponse.take(200)}...")
                        callback?.invoke(true, aiResponse, null)
                    } else {
                        Log.w(TAG, "AI生成内容为空")
                        callback?.invoke(false, null, "AI生成内容为空")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "AI生成笔记内容时发生异常", e)
                withContext(Dispatchers.Main) {
                    callback?.invoke(false, null, "AI生成失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 通过AI生成笔记内容草稿
     *
     * @param context 上下文
     * @param noteContent 笔记内容，作为AI的输入
     * @param noteTitle 笔记标题，用于AI提示词中的标题替换
     * @param callback 回调函数，成功返回true和AI生成的内容，失败返回false和错误信息
     */
    fun generateNoteDraftByAi(
        context: Context,
        noteContent: String,
        noteTitle: String = "",
        taskData: JSONObject? = null,
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        // 参数验证
        if (noteContent.isEmpty()) {
            callback?.invoke(false, null, "笔记内容不能为空")
            return
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始AI生成笔记内容草稿: ${noteContent.take(100)}...")

                // 调用OpenAI API
                val aiResponse = callOpenAiGenerateDraft(noteContent, noteTitle, context, taskData)

                withContext(Dispatchers.Main) {
                    if (aiResponse.isNotEmpty()) {
                        Log.d(TAG, "草稿生成成功: ${aiResponse.take(200)}...")
                        callback?.invoke(true, aiResponse, null)
                    } else {
                        Log.w(TAG, "草稿生成内容为空")
                        callback?.invoke(false, null, "AI生成内容为空")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "AI生成笔记内容时发生异常", e)
                withContext(Dispatchers.Main) {callback?.invoke(false, null, "AI生成失败: ${e.message}")}
            }
        }
    }

    private suspend fun callOpenAiGenerateDraft (noteContent: String, noteTitle: String = "", context: Context? = null, taskData: JSONObject? = null):String {
        return try {
            // 打印taskData信息
            if (taskData != null) {
                Log.d(TAG, "=== callOpenAiGenerateDraft 接收到的taskData ===")
                Log.d(TAG, "taskData中的note_id: ${taskData.optString("note_id", "")}")
                Log.d(TAG, "使用currentUpdateNoteId: ${currentUpdateNoteId}")
                Log.d(TAG, "============================================")
            } else {
                Log.d(TAG, "callOpenAiGenerateDraft 未接收到taskData，使用currentUpdateNoteId: ${currentUpdateNoteId}")
            }

            // 1. 获取配置中的update_prompt_id
            val updatePromptId = ConfigHelper.getString("update_prompt_id", "")

            if (updatePromptId.isEmpty()) {
                Log.e(TAG, "配置中未找到update_prompt_id")
                throw Exception("配置中未找到update_prompt_id")
            }

            Log.d(TAG, "使用更新提示词ID: $updatePromptId")

            // 2. 获取笔记内容（优先获取草稿，如果没有草稿则获取笔记详情）
            val noteContentForAi = getNoteDraftOrDetailAsync(context ?: throw Exception("Context不能为空"), currentUpdateNoteId)

            // 存储获取到的内容，供后续直接更新笔记使用
            currentUpdateNoteContent = noteContentForAi

            Log.d(TAG, "获取到的笔记内容用于AI处理: ${noteContentForAi.take(200)}...")
            Log.d(TAG, "已存储到currentUpdateNoteContent: ${currentUpdateNoteContent.take(100)}...")

            // 准备占位符数据
            val placeholderData = listOf(
                mapOf("content" to noteContent),
                mapOf("title" to noteTitle),
                mapOf("HTML" to noteContentForAi)
            )

            // 调用commonOpenAiMethodAsync
            val params = OpenAiMethodParams(
                context = context ?: throw Exception("Context不能为空"),
                model = "gpt-4.1-mini", // 默认模型
                promptId = updatePromptId,
                placeholderData = placeholderData
            )

            return commonOpenAiMethodAsync(params)
        } catch (e: Exception) {
            Log.e(TAG, "callOpenAiGenerateDraft执行失败", e)
            throw e
        }
    }

    /**
     * 调用OpenAI API进行AI对话
     *
     * @param noteContent 笔记内容
     * @param noteTitle 笔记标题
     * @param context 上下文，用于创建笔记
     * @return AI生成的内容
     */
    private suspend fun callOpenAiApi(noteContent: String, noteTitle: String = "", context: Context? = null): String {
        return try {
            // OpenAI API配置
            val apiUrl = "https://api.gptsapi.net/v1/chat/completions"
            val apiKey = "sk-gY862e5b9a04ee26941ac843faead2bfbcc3e10c9eelGS92"

            // 构建请求体
            val requestBody = JSONObject().apply {
                put("model", "gpt-4.1-mini")
                put("stream", false) // 使用非流式响应

                // 构建消息数组
                val messages = JSONArray().apply {
                    // 系统提示词
                    put(JSONObject().apply {
                        put("role", "system")
                        put(
                            "content", """你是一位专业H5网页生成 AI，任务是根据用户提供的内容，生成 H5 HTML 页面代码。

请严格遵循以下规则生成代码：

.用户设置了笔记标题，根据用户的标题，提取并总结用户提供的内容中的相关信息，用户此次提供的标题是: $noteTitle
.页面的内容设计一定要适合笔记内容，能够方便阅读和理解，不要像流水账
.生成的页面中的JavaScript 必须使用标准 DOM API（如：document.querySelector、innerHTML、appendChild 等）。
.设计的时候假设用户只会使用手机查看，设计的整体必须考虑是手机的使用场景
.生成的代码中，所有的js行为必须封装到script标签中，并且script带上id属性，类似于data-id="behavior"，标识当前script标签的作用
.不要使用 window.alert 或 console.log
.返回的内容严禁包含其他任何内容，只需要html代码。我需要直接复制并展示，不需要任何解释
.笔记的内容要简洁精炼，但是不要遗漏内容。
.内容中多使用emoji图标装饰
.使用模板生成，模板是note.html，模板内容如下：
<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{note_title}}</title>
  <style>
    body {
      margin: 0;
      background: #f4f5f7;
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
    }
    .header {
      background-color: #ffffff;
      padding: 40px 20px 20px;
      font-size: 22px;
      font-weight: 600;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 16px;
    }
    .table-of-contents {
      background: #fff;
      margin-bottom: 24px;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    .toc-title {
      font-size: 18px;
      font-weight: 600;
      color: #222;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
    .toc-title::before {
      content: "📋";
      margin-right: 8px;
    }
    .toc-item {
      padding: 10px 0;
      font-size: 14px;
      color: #2d72ff;
      cursor: pointer;
      border-bottom: 1px solid #f8f9fa;
      transition: all 0.2s ease;
      position: relative;
    }
    .toc-item:last-child {
      border-bottom: none;
    }
    .toc-item:hover {
      color: #1a5ce6;
      background-color: #f8f9ff;
      padding-left: 8px;
    }
    .toc-item.level-1 {
      font-weight: 500;
      padding-left: 0;
    }
    .toc-item.level-2 {
      padding-left: 20px;
      font-size: 13px;
      color: #5a6c7d;
    }
    .toc-item.level-2:hover {
      color: #2d72ff;
      padding-left: 28px;
    }
    .toc-item.level-3 {
      padding-left: 40px;
      font-size: 12px;
      color: #8a9ba8;
    }
    .toc-item.level-3:hover {
      color: #2d72ff;
      padding-left: 48px;
    }
    .content-area {
      background: #fff;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    .card {
      margin-bottom: 32px;
      padding-bottom: 24px;
      border-bottom: 1px solid #f0f2f5;
    }
    .card:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    .card h1, .card h2, .card h3, .card h4 {
      margin: 0 0 12px;
      color: #222;
      scroll-margin-top: 80px;
      line-height: 1.4;
    }
    .card h1 {
      font-size: 18px;
      font-weight: 700;
      border-bottom: 2px solid #2d72ff;
      padding-bottom: 6px;
    }
    .card h2 {
      font-size: 16px;
      font-weight: 600;
    }
    .card h3 {
      font-size: 15px;
      font-weight: 600;
    }
    .card h4 {
      font-size: 14px;
      font-weight: 500;
    }
    .card p {
      margin: 0 0 14px;
      color: #444;
      font-size: 14px;
      line-height: 1.6;
    }
    .card p:last-child {
      margin-bottom: 0;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
      .container {
        padding: 12px;
      }
      .header {
        padding: 30px 16px 16px;
        font-size: 20px;
      }
      .table-of-contents {
        padding: 16px;
        margin-bottom: 16px;
      }
      .toc-title {
        font-size: 16px;
        margin-bottom: 12px;
      }
      .content-area {
        padding: 16px;
      }
      .card {
        margin-bottom: 20px;
        padding-bottom: 16px;
      }
      .card h1 {
        font-size: 16px;
        padding-bottom: 4px;
      }
      .card h2 {
        font-size: 15px;
      }
      .card h3 {
        font-size: 14px;
      }
      .card h4 {
        font-size: 13px;
      }
      .card p {
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 12px;
      }
    }
  </style>
</head>
<body>
  <div class="header">{{note_title}}</div>

  <div class="container">
    <div class="table-of-contents" id="tableOfContents">
      <div class="toc-title">目录</div>
      <div class="toc-item level-1" data-target="section-1">1. 商业模式解析</div>
      <div class="toc-item level-2" data-target="section-1-1">1.1 营收结构分析</div>
      <div class="toc-item level-2" data-target="section-1-2">1.2 盈利模式探讨</div>
      <div class="toc-item level-3" data-target="section-1-2-1">1.2.1 订阅模式</div>
      <div class="toc-item level-3" data-target="section-1-2-2">1.2.2 增值服务</div>
      <div class="toc-item level-1" data-target="section-2">2. 用户画像与痛点</div>
      <div class="toc-item level-2" data-target="section-2-1">2.1 目标用户分析</div>
      <div class="toc-item level-2" data-target="section-2-2">2.2 用户痛点识别</div>
      <div class="toc-item level-1" data-target="section-3">3. 市场竞争分析</div>
      <div class="toc-item level-2" data-target="section-3-1">3.1 竞品对比</div>
      <div class="toc-item level-2" data-target="section-3-2">3.2 差异化优势</div>
    </div>

    <div class="content-area">
      <div class="card" id="section-1">
        <h1>商业模式解析</h1>
        <p>本章节深入分析SaaS平台的商业模式，探讨其营收结构和盈利模式的核心要素。</p>
      </div>

      <div class="card" id="section-1-1">
        <h2>营收结构分析</h2>
        <p>SaaS平台的营收结构主要包括订阅费用、增值服务费用以及合作分成等多个维度。通过对这些收入来源的详细分析，我们可以更好地理解平台的盈利能力。</p>
      </div>

      <div class="card" id="section-1-2">
        <h2>盈利模式探讨</h2>
        <p>深入探讨SaaS平台的核心盈利模式，分析不同模式的优劣势和适用场景。</p>
      </div>

      <div class="card" id="section-1-2-1">
        <h3>订阅模式</h3>
        <p>订阅模式是SaaS平台最主要的盈利方式，通过提供稳定的服务获得持续的收入流。这种模式的优势在于收入的可预测性和用户粘性的建立。</p>
      </div>

      <div class="card" id="section-1-2-2">
        <h3>增值服务</h3>
        <p>在基础订阅服务之外，平台还可以通过提供定制化服务、高级功能、专业咨询等增值服务来获得额外收入。</p>
      </div>

      <div class="card" id="section-2">
        <h1>用户画像与痛点</h1>
        <p>基于市场调研和用户行为数据，构建详细的用户画像，识别核心痛点和需求。</p>
      </div>

      <div class="card" id="section-2-1">
        <h2>目标用户分析</h2>
        <p>通过数据分析和用户访谈，我们识别出平台的核心用户群体主要包括中小企业管理者、创业者以及专业服务提供商。</p>
      </div>

      <div class="card" id="section-2-2">
        <h2>用户痛点识别</h2>
        <p>用户在使用传统解决方案时面临的主要痛点包括：成本高昂、操作复杂、数据孤岛、缺乏定制化等问题。</p>
      </div>

      <div class="card" id="section-3">
        <h1>市场竞争分析</h1>
        <p>全面分析市场竞争格局，识别主要竞争对手和差异化机会。</p>
      </div>

      <div class="card" id="section-3-1">
        <h2>竞品对比</h2>
        <p>对市场上主要竞争产品进行功能、价格、用户体验等多维度对比分析，找出各自的优势和不足。</p>
      </div>

      <div class="card" id="section-3-2">
        <h2>差异化优势</h2>
        <p>基于竞品分析结果，明确我们产品的核心差异化优势和市场定位策略。</p>
      </div>
    </div>
  </div>
  <script data-id="toc-navigation">
    // 目录点击跳转功能
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('toc-item')) {
        const targetId = e.target.getAttribute('data-target');
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          // 平滑滚动到目标位置
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });

          // 添加高亮效果
          targetElement.style.backgroundColor = '#f8f9ff';
          targetElement.style.transition = 'background-color 0.3s ease';

          setTimeout(() => {
            targetElement.style.backgroundColor = '';
          }, 2000);
        }
      }
    });

    // 滚动时高亮当前章节对应的目录项
    function highlightCurrentSection() {
      const sections = document.querySelectorAll('.card[id^="section-"]');
      const tocItems = document.querySelectorAll('.toc-item');
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      let currentSection = null;

      sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        if (scrollTop >= sectionTop) {
          currentSection = section;
        }
      });

      // 清除所有高亮
      tocItems.forEach(item => {
        item.style.fontWeight = '';
        item.style.color = '';
      });

      // 高亮当前章节
      if (currentSection) {
        const currentId = currentSection.getAttribute('id');
        const currentTocItem = document.querySelector(`[data-target="${'$'}{currentId}"]`);
        if (currentTocItem) {
          currentTocItem.style.fontWeight = '600';
          currentTocItem.style.color = '#1a5ce6';
        }
      }
    }

    // 监听滚动事件
    let ticking = false;
    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          highlightCurrentSection();
          ticking = false;
        });
        ticking = true;
      }
    });

    // 页面加载完成后初始化高亮
    document.addEventListener('DOMContentLoaded', highlightCurrentSection);
  </script>
</body>
</html>
"""
                        )
                    })

                    // 用户消息
                    put(JSONObject().apply {
                        put("role", "user")
                        put("content", """【用户提供的内容如下】：$noteContent""")
                    })
                }
                put("messages", messages)
            }

            Log.d(TAG, "OpenAI请求体: $requestBody")

            // 创建HTTP连接
            val url = URL(apiUrl)
            val connection = url.openConnection() as HttpURLConnection

            connection.apply {
                requestMethod = "POST"
                connectTimeout = 30000 // 30秒超时
                readTimeout = 30000
                setRequestProperty("Content-Type", "application/json; charset=UTF-8")
                setRequestProperty("Authorization", "Bearer $apiKey")
                setRequestProperty("Accept", "application/json")
                doOutput = true
            }

            // 发送请求
            OutputStreamWriter(connection.outputStream, StandardCharsets.UTF_8).use { writer ->
                writer.write(requestBody.toString())
                writer.flush()
            }

            // 读取响应
            val responseCode = connection.responseCode
            Log.d(TAG, "OpenAI响应码: $responseCode")

            if (responseCode == HttpURLConnection.HTTP_OK) {
                val response =
                    BufferedReader(InputStreamReader(connection.inputStream)).use { reader ->
                        reader.readText()
                    }

                Log.d(TAG, "OpenAI响应: $response")

                // 解析响应
                parseOpenAiResponse(response, context)
            } else {
                // 读取错误信息
                val errorResponse = connection.errorStream?.let { errorStream ->
                    BufferedReader(InputStreamReader(errorStream)).use { reader ->
                        reader.readText()
                    }
                } ?: "未知错误"

                Log.e(TAG, "OpenAI API错误: $responseCode, 错误内容: $errorResponse")
                throw Exception("OpenAI API调用失败: $responseCode - $errorResponse")
            }
        } catch (e: Exception) {
            Log.e(TAG, "调用OpenAI API失败", e)
            throw e
        }
    }

    /**
     * 解析OpenAI API响应
     *
     * @param response API响应字符串
     * @param context 上下文，用于创建笔记和显示Modal
     * @return 解析出的AI生成内容
     */
    private fun parseOpenAiResponse(response: String, context: Context? = null): String {
        try {
            val jsonResponse = JSONObject(response)

            // 检查是否有错误
            if (jsonResponse.has("error")) {
                val error = jsonResponse.getJSONObject("error")
                val errorMessage = error.optString("message", "未知错误")
                throw Exception("OpenAI API错误: $errorMessage")
            }

            // 解析choices数组
            val choices = jsonResponse.optJSONArray("choices")
            if (choices != null && choices.length() > 0) {
                val firstChoice = choices.getJSONObject(0)
                val message = firstChoice.optJSONObject("message")
                if (message != null) {
                    val content = message.optString("content", "")
                    if (content.isNotEmpty()) {
                        // 过滤HTML内容，去除markdown代码块标记
                        val cleanedHtml = cleanHtmlContent(content)

                        // 如果有上下文且有保存的笔记标题，则创建笔记
                        if (context != null && currentNoteTitle.isNotEmpty()) {
                            Log.d(TAG, "开始创建笔记，标题: $currentNoteTitle")
                            Log.d(TAG, "当前保存的封面: $currentNoteCover")

                            // 调用createNote创建笔记
                            createNote(
                                context = context,
                                title = currentNoteTitle,
                                content = "",
                                cover = if (currentNoteCover.isNotEmpty()) currentNoteCover else null, // 使用提取的封面
                                html = cleanedHtml     // 使用过滤后的HTML内容
                            ) { success, noteId, error ->
                                // 确保在主线程中显示Modal
                                CoroutineScope(Dispatchers.Main).launch {
                                    if (success && noteId != null) {
                                        Log.d(TAG, "笔记创建成功，ID: $noteId")

                                        // 清空临时保存的标题和封面
                                        currentNoteTitle = ""
                                        currentNoteCover = ""

                                        // 显示成功提示Modal
                                        CustomToastModalHelper.showModal(
                                            context = context,
                                            title = "操作提示",
                                            content = "笔记已创建成功",
                                            cancelText = "知道了",
                                            confirmText = "查看笔记",
                                            callback = object : CustomToastModalHelper.ModalCallback {
                                                override fun onConfirm() {
                                                    Log.d(TAG, "用户选择查看笔记，笔记ID: $noteId")
                                                    // TODO: 这里可以添加跳转到笔记详情页面的逻辑
                                                }

                                                override fun onCancel() {
                                                    Log.d(TAG, "用户确认笔记创建成功")
                                                }
                                            }
                                        )
                                    } else {
                                        Log.e(TAG, "笔记创建失败: $error")

                                        // 清空临时保存的标题和封面
                                        currentNoteTitle = ""
                                        currentNoteCover = ""

                                        // 显示失败提示Modal
                                        CustomToastModalHelper.showModal(
                                            context = context,
                                            title = "笔记创建失败",
                                            content = error ?: "创建笔记时发生未知错误",
                                            cancelText = "知道了",
                                            confirmText = "重试",
                                            callback = object : CustomToastModalHelper.ModalCallback {
                                                override fun onConfirm() {
                                                    Log.d(TAG, "用户选择重试创建笔记")
                                                    // TODO: 这里可以添加重试逻辑
                                                }

                                                override fun onCancel() {
                                                    Log.d(TAG, "用户确认笔记创建失败")
                                                }
                                            }
                                        )
                                    }
                                }
                            }
                        }

                        // 返回过滤后的AI生成内容
                        return cleanedHtml
                    }
                }
            }

            Log.w(TAG, "OpenAI响应中未找到有效内容")
            throw Exception("OpenAI响应中未找到有效内容")

        } catch (e: Exception) {
            Log.e(TAG, "解析OpenAI响应失败", e)
            throw Exception("解析AI响应失败: ${e.message}")
        }
    }

    /**
     * 更新笔记
     *
     * @param context 上下文
     * @param noteId 笔记ID，必填
     * @param title 笔记标题，可选，最大长度255字符
     * @param content 笔记内容，可选
     * @param parent_id 合集ID，可选
     * @param cover 笔记封面图片URL，可选
     * @param desc 笔记描述，可选
     * @param html HTML格式的笔记内容，可选
     * @param callback 回调函数，成功返回true和笔记ID，失败返回false和错误信息
     */
    fun updateNote(
        context: Context,
        noteId: String,
        title: String? = null,
        content: String? = null,
        parent_id: String? = null,
        cover: String? = null,
        desc: String? = null,
        html: String? = null,
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        // 确保RequestHelper已初始化
        init(context)

        // 参数验证
        if (noteId.isEmpty()) {
            callback?.invoke(false, null, "笔记ID不能为空")
            return
        }

        if (title != null && title.length > 255) {
            callback?.invoke(false, null, "笔记标题长度不能超过255字符")
            return
        }

        // 创建请求体，只包含非空参数
        val requestBody = JSONObject().apply {
            title?.let { if (it.isNotEmpty()) put("title", it) }
            content?.let { put("content", it) }
            parent_id?.let { if (it.isNotEmpty()) put("parent_id", it) }
            cover?.let { if (it.isNotEmpty()) put("cover", it) }
            desc?.let { if (it.isNotEmpty()) put("desc", it) }
            html?.let { if (it.isNotEmpty()) put("html", it) }
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "发送更新笔记请求: $noteId")
                Log.d(TAG, "更新参数: $requestBody")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper?.postJson(
                    "$apiBaseUrl/note/update/$noteId",
                    requestBody,
                    null
                )

                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestHelper.ApiResult.Success<String> -> {
                            try {
                                val jsonResponse = JSONObject(result.data)
                                val code = jsonResponse.optInt("code", -1)
                                val message = jsonResponse.optString("message", "未知错误")

                                if (code == 0) {
                                    Log.d(TAG, "笔记更新成功: $noteId")
                                    callback?.invoke(true, noteId, null)
                                } else {
                                    Log.w(TAG, "笔记更新失败: $message")
                                    callback?.invoke(false, null, message)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析响应失败", e)
                                callback?.invoke(false, null, "解析响应失败: ${e.message}")
                            }
                        }

                        is RequestHelper.ApiResult.Error -> {
                            Log.e(TAG, "更新笔记请求失败: ${result.message}")
                            callback?.invoke(false, null, result.message)
                        }

                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "更新笔记请求异常", result.throwable)
                            callback?.invoke(false, null, "请求异常: ${result.throwable.message}")
                        }

                        null -> {
                            Log.e(TAG, "更新笔记请求返回null")
                            callback?.invoke(false, null, "请求失败")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "更新笔记时发生未知错误", e)
                withContext(Dispatchers.Main) {
                    callback?.invoke(false, null, "未知错误: ${e.message}")
                }
            }
        }
    }

    /**
     * 获取笔记列表
     *
     * @param context 上下文
     * @param page 页码，从1开始，默认为1
     * @param pageSize 每页数量，默认为10
     * @param callback 回调函数，成功返回true和笔记列表数据，失败返回false和错误信息
     */
    fun getNoteList(
        context: Context,
        page: Int = 1,
        pageSize: Int = 10,
        callback: ((Boolean, NoteListResponse?, String?) -> Unit)? = null
    ) {
        // 确保RequestHelper已初始化
        init(context)

        // 参数验证
        if (page < 1) {
            callback?.invoke(false, null, "页码必须大于0")
            return
        }

        if (pageSize < 1 || pageSize > 100) {
            callback?.invoke(false, null, "每页数量必须在1-100之间")
            return
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "获取笔记列表: page=$page, pageSize=$pageSize")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                // 构建查询参数
                val params = mapOf(
                    "page" to page.toString(),
                    "page_size" to pageSize.toString()
                )

                val result = requestHelper?.get(
                    "$apiBaseUrl/note/list",
                    params,
                    null
                )

                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestHelper.ApiResult.Success<String> -> {
                            try {
                                val jsonResponse = JSONObject(result.data)
                                val code = jsonResponse.optInt("code", -1)
                                val message = jsonResponse.optString("message", "未知错误")

                                if (code == 0) {
                                    // 成功获取笔记列表，解析数据
                                    val dataObject = jsonResponse.optJSONObject("data")
                                    if (dataObject != null) {
                                        val noteListResponse = parseNoteListResponse(dataObject)
                                        Log.d(TAG, "笔记列表获取成功，共${noteListResponse.total}条记录")
                                        callback?.invoke(true, noteListResponse, null)
                                    } else {
                                        Log.w(TAG, "响应数据格式错误：缺少data字段")
                                        callback?.invoke(false, null, "响应数据格式错误")
                                    }
                                } else {
                                    Log.w(TAG, "获取笔记列表失败: $message")
                                    callback?.invoke(false, null, message)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析笔记列表响应失败", e)
                                callback?.invoke(false, null, "解析响应失败: ${e.message}")
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            Log.e(TAG, "服务器错误: ${result.message}")
                            callback?.invoke(false, null, "服务器错误: ${result.message}")
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "网络请求异常", result.throwable)
                            callback?.invoke(false, null, "网络请求异常: ${result.throwable.message}")
                        }
                        null -> {
                            Log.e(TAG, "RequestHelper未初始化")
                            callback?.invoke(false, null, "系统错误: RequestHelper未初始化")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取笔记列表时发生异常", e)
                withContext(Dispatchers.Main) {
                    callback?.invoke(false, null, "获取笔记列表失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 解析笔记列表响应数据
     *
     * @param dataObject 响应中的data对象
     * @return 解析后的笔记列表响应对象
     */
    private fun parseNoteListResponse(dataObject: JSONObject): NoteListResponse {
        val notesArray = dataObject.optJSONArray("notes") ?: JSONArray()
        val total = dataObject.optInt("total", 0)
        val page = dataObject.optInt("page", 1)
        val pageSize = dataObject.optInt("page_size", 10)

        val noteList = mutableListOf<NoteItem>()
        for (i in 0 until notesArray.length()) {
            val noteObject = notesArray.optJSONObject(i)
            if (noteObject != null) {
                val note = NoteItem(
                    id = noteObject.optString("id", ""),
                    parent_id = noteObject.optString("parent_id").takeIf { it.isNotEmpty() },
                    user_id = noteObject.optString("user_id", ""),
                    title = noteObject.optString("title", ""),
                    cover = noteObject.optString("cover").takeIf { it.isNotEmpty() },
                    desc = noteObject.optString("desc").takeIf { it.isNotEmpty() },
                    content = noteObject.optString("content").takeIf { it.isNotEmpty() },
                    html = noteObject.optString("html", ""),
                    create_time = noteObject.optString("create_time", ""),
                    update_time = noteObject.optString("update_time", "")
                )
                noteList.add(note)
            }
        }

        return NoteListResponse(
            notes = noteList,
            total = total,
            page = page,
            page_size = pageSize
        )
    }

    /**
     * 测试获取笔记列表功能
     * 这是一个用于测试的公共方法
     */
    fun testGetNoteList(context: Context) {
        Log.d(TAG, "开始测试获取笔记列表功能...")

        getNoteList(context, page = 1, pageSize = 10) { success, noteListResponse, error ->
            if (success && noteListResponse != null) {
                Log.d(TAG, "=== 获取笔记列表测试成功 ===")
                Log.d(TAG, "总记录数: ${noteListResponse.total}")
                Log.d(TAG, "当前页: ${noteListResponse.page}")
                Log.d(TAG, "每页数量: ${noteListResponse.page_size}")
                Log.d(TAG, "当前页笔记数量: ${noteListResponse.notes.size}")

                noteListResponse.notes.forEachIndexed { index, note ->
                    Log.d(TAG, "笔记${index + 1}: ID=${note.id}, 标题=${note.title}, 创建时间=${note.create_time}")
                }
                Log.d(TAG, "========================")
            } else {
                Log.e(TAG, "=== 获取笔记列表测试失败 ===")
                Log.e(TAG, "错误信息: $error")
                Log.e(TAG, "========================")
            }
        }
    }

    /**
     * 清理HTML内容，去除markdown代码块标记
     *
     * @param htmlContent 原始HTML内容
     * @return 清理后的HTML内容
     */
    private fun cleanHtmlContent(htmlContent: String): String {
        var cleaned = htmlContent.trim()

        // 去除开头的```html标记
        if (cleaned.startsWith("```html")) {
            cleaned = cleaned.removePrefix("```html").trim()
        }

        // 去除结尾的```标记
        if (cleaned.endsWith("```")) {
            cleaned = cleaned.removeSuffix("```").trim()
        }

        // 去除可能存在的其他markdown代码块标记
        cleaned = cleaned.replace(Regex("^```html\\s*", RegexOption.MULTILINE), "")
        cleaned = cleaned.replace(Regex("^```\\s*$", RegexOption.MULTILINE), "")

        Log.d(TAG, "HTML内容清理完成，原始长度: ${htmlContent.length}, 清理后长度: ${cleaned.length}")

        return cleaned
    }

    /**
     * 获取系统提示词详情
     *
     * @param context 上下文
     * @param promptId 提示词ID
     * @param callback 回调函数，成功返回true和提示词内容，失败返回false和错误信息
     */
    fun getSystemPromptDetail(
        context: Context,
        promptId: String,
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        // 确保RequestHelper已初始化
        init(context)

        // 参数验证
        if (promptId.isEmpty()) {
            callback?.invoke(false, null, "提示词ID不能为空")
            return
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始获取提示词详情: $promptId")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper?.get("$apiBaseUrl/system_prompt/detail",mapOf("id" to promptId),null)

                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestHelper.ApiResult.Success<String> -> {
                            try {
                                val jsonResponse = JSONObject(result.data)
                                val code = jsonResponse.optInt("code", -1)
                                val message = jsonResponse.optString("message", "未知错误")

                                if (code == 0) {
                                    val data = jsonResponse.optJSONObject("data")
                                    val promptContent = data?.optString("content", "") ?: ""
                                    val promptTitle = data?.optString("title", "") ?: ""
                                    val promptId = data?.optString("id", "") ?: ""

                                    Log.d(TAG, "获取提示词成功:")
                                    Log.d(TAG, "  ID: $promptId")
                                    Log.d(TAG, "  标题: $promptTitle")
                                    Log.d(TAG, "  内容: $promptContent")
                                    callback?.invoke(true, promptContent, null)
                                } else {
                                    Log.e(TAG, "获取提示词失败: $message")
                                    callback?.invoke(false, null, message)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析提示词响应失败", e)
                                callback?.invoke(false, null, "解析响应失败: ${e.message}")
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            Log.e(TAG, "服务器错误: ${result.message}")
                            callback?.invoke(false, null, "服务器错误: ${result.message}")
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "网络请求异常", result.throwable)
                            callback?.invoke(false, null, "网络请求异常: ${result.throwable.message}")
                        }
                        null -> {
                            Log.e(TAG, "RequestHelper未初始化")
                            callback?.invoke(false, null, "系统错误: RequestHelper未初始化")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取提示词时发生异常", e)
                withContext(Dispatchers.Main) {
                    callback?.invoke(false, null, "获取提示词异常: ${e.message}")
                }
            }
        }
    }

    /**
     * 获取系统提示词详情 (使用suspend函数，支持async/await语法)
     *
     * @param context 上下文
     * @param promptId 提示词ID
     * @return 提示词内容，失败时抛出异常
     */
    private suspend fun getSystemPromptDetailAsync(
        context: Context,
        promptId: String
    ): String {
        // 确保RequestHelper已初始化
        init(context)

        // 参数验证
        if (promptId.isEmpty()) {
            throw IllegalArgumentException("提示词ID不能为空")
        }

        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始获取提示词详情: $promptId")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper?.get("$apiBaseUrl/system_prompt/detail",mapOf("id" to promptId),null) ?: throw Exception("RequestHelper未初始化")

                when (result) {
                    is RequestHelper.ApiResult.Success<String> -> {
                        val jsonResponse = JSONObject(result.data)
                        val code = jsonResponse.optInt("code", -1)
                        val message = jsonResponse.optString("message", "未知错误")

                        if (code == 0) {
                            val data = jsonResponse.optJSONObject("data")
                            val promptContent = data?.optString("content", "") ?: ""
                            val promptTitle = data?.optString("title", "") ?: ""
                            val promptIdFromResponse = data?.optString("id", "") ?: ""

                            Log.d(TAG, "获取提示词成功:")
                            Log.d(TAG, "  ID: $promptIdFromResponse")
                            Log.d(TAG, "  标题: $promptTitle")
                            Log.d(TAG, "  内容: $promptContent")

                            promptContent
                        } else {
                            throw Exception("获取提示词失败: $message")
                        }
                    }
                    is RequestHelper.ApiResult.Error -> {
                        throw Exception("服务器错误: ${result.message}")
                    }
                    is RequestHelper.ApiResult.Exception -> {
                        throw Exception("网络请求异常: ${result.throwable.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取提示词时发生异常", e)
                throw e
            }
        }
    }

    /**
     * 创建笔记草稿
     *
     * @param context 上下文
     * @param noteId 笔记ID
     * @param content 草稿内容
     * @param callback 回调函数，成功返回true和草稿ID，失败返回false和错误信息
     */
    fun createNoteDraft(
        context: Context,
        noteId: String,
        content: String,
        callback: ((Boolean, String?, String?) -> Unit)? = null
    ) {
        // 确保RequestHelper已初始化
        init(context)

        // 参数验证
        if (noteId.isEmpty()) {
            callback?.invoke(false, null, "笔记ID不能为空")
            return
        }

        if (content.isEmpty()) {
            callback?.invoke(false, null, "草稿内容不能为空")
            return
        }

        // 创建请求体
        val requestBody = JSONObject().apply {
            put("note_id", noteId)
            put("content", content)
        }

        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "发送创建笔记草稿请求: noteId=$noteId")
                Log.d(TAG, "草稿内容长度: ${content.length}")
                Log.d(TAG, "请求体: $requestBody")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                val result = requestHelper?.postJson("$apiBaseUrl/note/draft/create", requestBody, null)
                    ?: throw Exception("RequestHelper未初始化")

                withContext(Dispatchers.Main) {
                    when (result) {
                        is RequestHelper.ApiResult.Success<String> -> {
                            try {
                                val jsonResponse = JSONObject(result.data)
                                val code = jsonResponse.optInt("code", -1)
                                val message = jsonResponse.optString("message", "未知错误")

                                if (code == 0) {
                                    val data = jsonResponse.optJSONObject("data")
                                    val draftId = data?.optString("id", "") ?: ""
                                    Log.d(TAG, "创建笔记草稿成功: draftId=$draftId")
                                    callback?.invoke(true, draftId, null)
                                } else {
                                    Log.e(TAG, "创建笔记草稿失败: $message")
                                    callback?.invoke(false, null, message)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析创建草稿响应失败", e)
                                callback?.invoke(false, null, "解析响应失败: ${e.message}")
                            }
                        }
                        is RequestHelper.ApiResult.Error -> {
                            Log.e(TAG, "创建笔记草稿请求失败: ${result.message}")
                            callback?.invoke(false, null, result.message)
                        }
                        is RequestHelper.ApiResult.Exception -> {
                            Log.e(TAG, "创建笔记草稿网络异常", result.throwable)
                            callback?.invoke(false, null, "网络异常: ${result.throwable.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "创建笔记草稿时发生异常", e)
                withContext(Dispatchers.Main) {
                    callback?.invoke(false, null, "创建草稿失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 获取笔记草稿或详情内容 (优先获取草稿，如果没有草稿则获取笔记详情)
     *
     * @param context 上下文
     * @param noteId 笔记ID
     * @return 笔记内容，失败时抛出异常
     */
    private suspend fun getNoteDraftOrDetailAsync(
        context: Context,
        noteId: String
    ): String {
        // 确保RequestHelper已初始化
        init(context)

        // 参数验证
        if (noteId.isEmpty()) {
            throw IllegalArgumentException("笔记ID不能为空")
        }

        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始获取笔记草稿或详情: $noteId")

                // 使用ConfigHelper属性访问API基地址
                val apiBaseUrl = ConfigHelper.getString("api_base_url")

                // 1. 先尝试获取草稿
                Log.d(TAG, "尝试获取笔记草稿: $noteId")
                val draftResult = requestHelper?.get("$apiBaseUrl/note/draft/detail", mapOf("note_id" to noteId), null) ?: throw Exception("RequestHelper未初始化")

                when (draftResult) {
                    is RequestHelper.ApiResult.Success<String> -> {
                        val draftJsonResponse = JSONObject(draftResult.data)
                        val draftCode = draftJsonResponse.optInt("code", -1)
                        val draftMessage = draftJsonResponse.optString("message", "未知错误")

                        if (draftCode == 0) {
                            val draftData = draftJsonResponse.optJSONObject("data")
                            val draftContent = draftData?.optString("content", "") ?: ""

                            if (draftContent.isNotEmpty()) {
                                Log.d(TAG, "成功获取笔记草稿内容: ${draftContent.take(100)}...")
                                return@withContext draftContent
                            } else {
                                Log.d(TAG, "草稿内容为空，尝试获取笔记详情")
                            }
                        } else {
                            Log.d(TAG, "获取草稿失败: $draftMessage，尝试获取笔记详情")
                        }
                    }
                    is RequestHelper.ApiResult.Error -> {
                        Log.d(TAG, "获取草稿请求失败: ${draftResult.message}，尝试获取笔记详情")
                    }
                    else -> {
                        Log.d(TAG, "未知的草稿请求结果类型，尝试获取笔记详情")
                    }
                }

                // 2. 如果草稿获取失败或内容为空，则获取笔记详情
                Log.d(TAG, "获取笔记详情: $noteId")
                val detailResult = requestHelper?.get("$apiBaseUrl/note/detail", mapOf("id" to noteId), null) ?: throw Exception("RequestHelper未初始化")

                when (detailResult) {
                    is RequestHelper.ApiResult.Success<String> -> {
                        val detailJsonResponse = JSONObject(detailResult.data)
                        val detailCode = detailJsonResponse.optInt("code", -1)
                        val detailMessage = detailJsonResponse.optString("message", "未知错误")

                        if (detailCode == 0) {
                            val detailData = detailJsonResponse.optJSONObject("data")
                            val htmlContent = detailData?.optString("html", "") ?: ""

                            if (htmlContent.isNotEmpty()) {
                                Log.d(TAG, "成功获取笔记详情HTML内容: ${htmlContent.take(100)}...")
                                htmlContent
                            } else {
                                Log.w(TAG, "笔记详情HTML内容为空")
                                throw Exception("笔记详情HTML内容为空")
                            }
                        } else {
                            throw Exception("获取笔记详情失败: $detailMessage")
                        }
                    }
                    is RequestHelper.ApiResult.Error -> {
                        throw Exception("获取笔记详情请求失败: ${detailResult.message}")
                    }
                    else -> {
                        throw Exception("未知的笔记详情请求结果类型")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取笔记草稿或详情失败", e)
                throw e
            }
        }
    }

    /**
     * 通用的调用OpenAI的方法 (使用async/await语法，避免回调嵌套)
     * 使用对象传参方式，类似JavaScript的 method({a: xxx, b: xxx})
     *
     * @param params OpenAI方法调用参数对象
     * @return AI生成的内容
     */
    suspend fun commonOpenAiMethodAsync(params: OpenAiMethodParams): String {
        return withContext(Dispatchers.IO) {
            try {
                // 从配置中获取OpenAI的URL和Key
                val openaiUrl = ConfigHelper.getString("openai_api_url", "")
                val openaiKey = ConfigHelper.getString("openai_api_key", "")

                // 打印参数和配置信息
                Log.d(TAG, "=== commonOpenAiMethodAsync 调用信息 ===")
                Log.d(TAG, "模型名称: ${params.model}")
                Log.d(TAG, "提示词ID: ${params.promptId}")
                Log.d(TAG, "占位符数据: ${params.placeholderData}")
                Log.d(TAG, "OpenAI URL: $openaiUrl")
                Log.d(TAG, "OpenAI Key: $openaiKey")
                Log.d(TAG, "================================")

                // 使用await语法获取提示词内容，避免回调嵌套
                val originalPromptContent = getSystemPromptDetailAsync(params.context, params.promptId)

                Log.d(TAG, "=== 原始提示词内容 ===")
                Log.d(TAG, originalPromptContent)
                Log.d(TAG, "=====================")

                // 替换占位符
                val processedPromptContent = replacePlaceholders(originalPromptContent, params.placeholderData)

                Log.d(TAG, "=== 处理后的提示词内容 ===")
                Log.d(TAG, processedPromptContent)
                Log.d(TAG, "========================")

                // 调用OpenAI API
                callOpenAiApiWithPrompt(params.model, processedPromptContent, openaiUrl, openaiKey)

            } catch (e: Exception) {
                Log.e(TAG, "commonOpenAiMethodAsync 执行失败: ${e.message}", e)
                throw e
            }
        }
    }

    /**
     * 替换提示词中的占位符
     *
     * @param promptContent 原始提示词内容
     * @param placeholderData 占位符数据数组
     * @return 替换后的提示词内容
     */
    private fun replacePlaceholders(
        promptContent: String,
        placeholderData: List<Map<String, String>>
    ): String {
        var result = promptContent

        // 遍历占位符数据数组
        placeholderData.forEach { dataItem ->
            // 遍历每个数据项中的键值对
            dataItem.forEach { (key, value) ->
                // 替换占位符，格式为 {{key}}
                val placeholder = "{{$key}}"
                result = result.replace(placeholder, value)
                Log.d(TAG, "替换占位符: $placeholder -> $value")
            }
        }

        return result
    }

    /**
     * 调用OpenAI API (通用版本，使用自定义提示词)
     *
     * @param model 模型名称
     * @param systemPrompt 系统提示词
     * @param apiUrl OpenAI API地址
     * @param apiKey OpenAI API密钥
     * @return AI生成的内容
     */
    private suspend fun callOpenAiApiWithPrompt(model: String,systemPrompt: String,apiUrl: String,apiKey: String): String {
        return try {
            Log.d(TAG, "开始调用OpenAI API")
            Log.d(TAG, "模型: $model")
            Log.d(TAG, "API地址: $apiUrl")

            // 构建请求体
            val requestBody = JSONObject().apply {
                put("model", model)
                put("stream", false) // 使用非流式响应

                // 构建消息数组
                val messages = JSONArray().apply {
                    // 系统提示词
                    put(JSONObject().apply {
                        put("role", "system")
                        put("content", systemPrompt)
                    })
                }
                put("messages", messages)
            }

            Log.d(TAG, "请求体: $requestBody")

            // 创建HTTP连接
            val connection = URL(apiUrl).openConnection() as HttpURLConnection
            connection.apply {
                requestMethod = "POST"
                connectTimeout = 30000 // 30秒超时
                readTimeout = 30000
                setRequestProperty("Content-Type", "application/json; charset=UTF-8")
                setRequestProperty("Authorization", "Bearer $apiKey")
                setRequestProperty("Accept", "application/json")
                doOutput = true
            }

            // 发送请求
            OutputStreamWriter(connection.outputStream, StandardCharsets.UTF_8).use { writer ->
                writer.write(requestBody.toString())
                writer.flush()
            }

            // 读取响应
            val responseCode = connection.responseCode
            Log.d(TAG, "OpenAI响应码: $responseCode")

            if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = BufferedReader(InputStreamReader(connection.inputStream)).use { reader ->
                    reader.readText()
                }

                Log.d(TAG, "OpenAI响应: $response")

                // 解析响应并返回内容
                parseOpenAiResponseContent(response)
            } else {
                // 读取错误信息
                val errorResponse = connection.errorStream?.let { errorStream ->
                    BufferedReader(InputStreamReader(errorStream)).use { reader ->
                        reader.readText()
                    }
                } ?: "未知错误"

                Log.e(TAG, "OpenAI API错误: $responseCode, 错误内容: $errorResponse")
                throw Exception("OpenAI API调用失败: $responseCode - $errorResponse")
            }
        } catch (e: Exception) {
            Log.e(TAG, "调用OpenAI API失败", e)
            throw e
        }
    }

    /**
     * 解析OpenAI API响应内容 (纯内容版本，不创建笔记)
     *
     * @param response API响应字符串
     * @return 解析出的AI生成内容
     */
    private fun parseOpenAiResponseContent(response: String): String {
        try {
            val jsonResponse = JSONObject(response)

            // 检查是否有错误
            if (jsonResponse.has("error")) {
                val error = jsonResponse.getJSONObject("error")
                val errorMessage = error.optString("message", "未知错误")
                throw Exception("OpenAI API错误: $errorMessage")
            }

            // 解析choices数组
            val choices = jsonResponse.optJSONArray("choices")
            if (choices != null && choices.length() > 0) {
                val firstChoice = choices.getJSONObject(0)
                val message = firstChoice.optJSONObject("message")
                if (message != null) {
                    val content = message.optString("content", "")
                    if (content.isNotEmpty()) {
                        Log.d(TAG, "AI生成内容成功: ${content.take(200)}...")
                        return content
                    }
                }
            }

            Log.w(TAG, "OpenAI响应中未找到有效内容")
            throw Exception("OpenAI响应中未找到有效内容")

        } catch (e: Exception) {
            Log.e(TAG, "解析OpenAI响应失败", e)
            throw Exception("解析AI响应失败: ${e.message}")
        }
    }
}
