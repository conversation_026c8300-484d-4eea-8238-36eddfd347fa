package com.xunhe.aishoucang.helpers

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.views.CircularProgressView
import com.xunhe.aishoucang.lib.ClipboardHelper

/**
 * 任务进度管理器
 * 负责管理创建笔记时的任务进度显示
 */
object TaskProgressHelper {
    private const val TAG = "TaskProgressHelper"

    // 进度步骤枚举
    enum class ProgressStep(val stepIndex: Int, val stepName: String) {
        EXTRACT_VIDEO(0, "提取视频"),
        PARSE_CONTENT(1, "解析文案"),
        COMPLETE(2, "解析完成")
    }

    // 任务状态数据类
    data class TaskState(
        val currentStep: ProgressStep = ProgressStep.EXTRACT_VIDEO,
        val isProgressVisible: Boolean = false,
        val stepProgresses: Map<ProgressStep, Float> = mapOf(),
        val isTaskRunning: Boolean = false
    )

    // 当前任务状态
    private var taskState = TaskState()

    // UI组件引用
    private var progressContainer: LinearLayout? = null
    private var progressViews = mutableMapOf<ProgressStep, CircularProgressView>()
    private var percentTexts = mutableMapOf<ProgressStep, TextView>()
    private var stepLines = mutableMapOf<ProgressStep, View>()
    
    /**
     * 初始化进度组件
     * @param rootView 包含进度组件的根视图
     */
    fun initProgressComponents(rootView: View) {
        try {
            progressContainer = rootView.findViewById(R.id.task_progress_container)

            // 初始化各步骤的进度视图和百分比文本
            progressViews[ProgressStep.EXTRACT_VIDEO] = rootView.findViewById(R.id.progress_extract_video)
            progressViews[ProgressStep.PARSE_CONTENT] = rootView.findViewById(R.id.progress_parse_content)
            progressViews[ProgressStep.COMPLETE] = rootView.findViewById(R.id.progress_complete)

            percentTexts[ProgressStep.EXTRACT_VIDEO] = rootView.findViewById(R.id.percent_extract_video)
            percentTexts[ProgressStep.PARSE_CONTENT] = rootView.findViewById(R.id.percent_parse_content)
            percentTexts[ProgressStep.COMPLETE] = rootView.findViewById(R.id.percent_complete)

            Log.d(TAG, "任务进度组件初始化完成")

            // 检查是否需要恢复任务进度状态
            if (taskState.isTaskRunning) {
                Log.d(TAG, "检测到正在运行的任务，开始恢复进度显示")
                taskState = taskState.copy(isProgressVisible = true)
                restoreProgressState()
            } else if (!taskState.isTaskRunning && taskState.currentStep == ProgressStep.COMPLETE) {
                // 任务已完成但还没有重置，立即隐藏并重置状态
                Log.d(TAG, "检测到已完成的任务，立即隐藏并重置状态")
                hideProgress()
                resetTaskState()
            }
        } catch (e: Exception) {
            Log.e(TAG, "初始化任务进度组件失败", e)
        }
    }
    
    /**
     * 显示任务进度
     */
    fun showProgress() {
        // 确保在主线程中执行
        Handler(Looper.getMainLooper()).post {
            if (progressContainer == null) {
                Log.w(TAG, "进度容器未初始化，检查是否需要恢复任务状态")
                // 如果任务正在运行，标记UI为可见状态，等待UI组件初始化后恢复
                if (taskState.isTaskRunning) {
                    Log.d(TAG, "任务正在运行，标记进度为可见，等待UI初始化")
                    taskState = taskState.copy(isProgressVisible = true)
                }
                return@post
            }

            if (taskState.isProgressVisible) {
                Log.d(TAG, "进度已经显示，检查是否需要恢复状态")
                // 如果任务正在运行，恢复进度显示
                if (taskState.isTaskRunning) {
                    restoreProgressState()
                }
                return@post
            }

            Log.d(TAG, "显示任务进度")
            taskState = taskState.copy(
                isProgressVisible = true,
                currentStep = ProgressStep.EXTRACT_VIDEO,
                isTaskRunning = true
            )

            // 重置所有进度条
            resetAllProgress()

            // 显示容器并添加动画
            progressContainer?.let { container ->
                container.visibility = View.VISIBLE
                container.alpha = 0f
                container.animate()
                    .alpha(1f)
                    .setDuration(300)
                    .setInterpolator(AccelerateDecelerateInterpolator())
                    .start()
            }

            // 开始第一步进度
            startStepProgress(ProgressStep.EXTRACT_VIDEO)
        }
    }
    
    /**
     * 隐藏任务进度
     */
    fun hideProgress(force:Boolean = false) {
        // 确保在主线程中执行
        Handler(Looper.getMainLooper()).post {
            if (!force && !taskState.isProgressVisible) {
                Log.d(TAG, "进度已经隐藏，无需重复隐藏")
                return@post
            }

            Log.d(TAG, "隐藏任务进度")
            taskState = taskState.copy(isProgressVisible = false)

            // 如果UI组件存在，执行隐藏动画
            progressContainer?.let { container ->
                container.animate()
                    .alpha(0f)
                    .setDuration(300)
                    .setInterpolator(AccelerateDecelerateInterpolator())
                    .withEndAction {
                        container.visibility = View.GONE
                    }
                    .start()
            } ?: run {
                Log.d(TAG, "UI组件不存在，仅更新状态")
            }
        }
    }
    
    /**
     * 更新到下一步
     */
    fun nextStep() {
        // 确保在主线程中执行
        Handler(Looper.getMainLooper()).post {
            if (!taskState.isProgressVisible && !taskState.isTaskRunning) {
                Log.w(TAG, "进度未显示且任务未运行，无法更新步骤")
                return@post
            }

            // 如果任务在运行但UI未显示，仍然更新状态
            if (!taskState.isProgressVisible && taskState.isTaskRunning) {
                Log.d(TAG, "任务在运行但UI未显示，更新内部状态")
            }

            when (taskState.currentStep) {
                ProgressStep.EXTRACT_VIDEO -> {
                    // 确保当前步骤完成到100%
                    forceCompleteCurrentStep()
                    taskState = taskState.copy(currentStep = ProgressStep.PARSE_CONTENT)
                    startStepProgress(ProgressStep.PARSE_CONTENT)
                }
                ProgressStep.PARSE_CONTENT -> {
                    // 确保当前步骤完成到100%
                    forceCompleteCurrentStep()
                    taskState = taskState.copy(currentStep = ProgressStep.COMPLETE)
                    startStepProgress(ProgressStep.COMPLETE)
                    // 对于COMPLETE步骤，直接完成到100%而不是重置为0%
                    // completeStepProgress(ProgressStep.COMPLETE)

                    // // 解析完成步骤特殊处理：立即隐藏任务并重置状态
                    // Log.d(TAG, "解析完成，立即隐藏任务并重置状态")
                    // taskState = taskState.copy(isTaskRunning = false)

                    // if (taskState.isProgressVisible) {
                    //     hideProgress()
                    //     // 任务完全完成，重置状态
                    //     resetTaskState()
                    // } else {
                    //     // 如果进度不可见（面板已关闭），直接重置状态
                    //     Log.d(TAG, "解析完成但进度不可见，直接重置状态")
                    //     resetTaskState()
                    // }
                }
                ProgressStep.COMPLETE -> {
                    completeStepProgress(ProgressStep.COMPLETE)
                    hideProgress(true)
                    taskState = TaskState()
                    Log.w(TAG, "COMPLETE步骤不应该再调用nextStep")
                }
            }
        }
    }

    /**
     * 强制完成当前步骤到100%
     */
    private fun forceCompleteCurrentStep() {
        val progressView = progressViews[taskState.currentStep] ?: return
        val percentText = percentTexts[taskState.currentStep] ?: return

        // 如果当前进度小于100%，则立即设置为100%
        if (progressView.getProgress() < 100f) {
            Log.d(TAG, "强制完成步骤 ${taskState.currentStep.stepName} 到100%")
            progressView.setProgressImmediate(100f)
            percentText.text = "100%"

            // 更新状态中的进度
            val updatedProgresses = taskState.stepProgresses.toMutableMap()
            updatedProgresses[taskState.currentStep] = 100f
            taskState = taskState.copy(stepProgresses = updatedProgresses)
        }
    }
    
    /**
     * 开始某个步骤的进度
     */
    private fun startStepProgress(step: ProgressStep) {
        Log.d(TAG, "开始步骤: ${step.stepName}")

        val progressView = progressViews[step] ?: return
        val percentText = percentTexts[step] ?: return

        // 对于COMPLETE步骤，直接设置为100%
        if (step == ProgressStep.COMPLETE) {
            progressView.setProgressImmediate(100f)
            percentText.text = "100%"
            Log.d(TAG, "COMPLETE步骤直接设置为100%")
            return
        }

        // 其他步骤重置进度
        progressView.reset()
        percentText.text = "0%"

        // 不再使用模拟进度，等待真实进度回调
        Log.d(TAG, "等待步骤 ${step.stepName} 的真实进度回调...")
    }
    
    /**
     * 完成某个步骤的进度
     */
    private fun completeStepProgress(step: ProgressStep) {
        Log.d(TAG, "完成步骤: ${step.stepName}")

        // 立即设置进度到100%
        val progressView = progressViews[step] ?: return
        val percentText = percentTexts[step] ?: return

        progressView.setProgressImmediate(100f)
        percentText.text = "100%"

        // 更新状态中的进度
        val updatedProgresses = taskState.stepProgresses.toMutableMap()
        updatedProgresses[step] = 100f
        taskState = taskState.copy(stepProgresses = updatedProgresses)

        Log.d(TAG, "步骤 ${step.stepName} 完成，进度设置为100%")
    }
    
    /**
     * 重置所有进度
     */
    private fun resetAllProgress() {
        progressViews.values.forEach { progressView ->
            progressView.reset()
        }

        percentTexts.values.forEach { percentText ->
            percentText.text = "0%"
        }
    }
    
    /**
     * 检查进度是否正在显示
     */
    fun isProgressShowing(): Boolean {
        return taskState.isProgressVisible
    }

    /**
     * 获取当前步骤
     */
    fun getCurrentStep(): ProgressStep {
        return taskState.currentStep
    }

    /**
     * 检查任务是否正在运行
     */
    fun isTaskRunning(): Boolean {
        return taskState.isTaskRunning
    }

    /**
     * 更新当前步骤的真实进度
     * @param progress 进度值 (0-100)
     */
    fun updateCurrentProgress(progress: Float) {
        Handler(Looper.getMainLooper()).post {
            if (!taskState.isProgressVisible) {
                Log.d(TAG, "进度未显示，但任务可能在运行，保存进度状态")
                // 即使UI未显示，也要保存进度状态
                val updatedProgresses = taskState.stepProgresses.toMutableMap()
                updatedProgresses[taskState.currentStep] = progress
                taskState = taskState.copy(stepProgresses = updatedProgresses)
                return@post
            }

            val progressView = progressViews[taskState.currentStep] ?: return@post
            val percentText = percentTexts[taskState.currentStep] ?: return@post

            // 更新进度视图
            progressView.setProgressImmediate(progress)

            // 更新百分比文本
            percentText.text = "${progress.toInt()}%"

            // 更新状态中的进度
            val updatedProgresses = taskState.stepProgresses.toMutableMap()
            updatedProgresses[taskState.currentStep] = progress
            taskState = taskState.copy(stepProgresses = updatedProgresses)

            Log.d(TAG, "更新步骤 ${taskState.currentStep.stepName} 进度: ${progress.toInt()}%")
        }
    }

    /**
     * 动画更新进度
     * @param step 步骤
     * @param targetProgress 目标进度 (0-100)
     * @param duration 动画持续时间
     */
    private fun animateProgress(step: ProgressStep, targetProgress: Float, duration: Long) {
        val progressView = progressViews[step] ?: return
        val percentText = percentTexts[step] ?: return

        // 创建进度动画
        val currentProgress = progressView.getProgress()
        val animator = ValueAnimator.ofFloat(currentProgress, targetProgress)
        animator.duration = duration
        animator.interpolator = AccelerateDecelerateInterpolator()

        animator.addUpdateListener { animation ->
            val animatedProgress = animation.animatedValue as Float
            progressView.setProgressImmediate(animatedProgress)
            percentText.text = "${animatedProgress.toInt()}%"
        }

        animator.start()
    }

    /**
     * 恢复进度状态
     * 当面板重新打开时，恢复之前的进度显示
     */
    private fun restoreProgressState() {
        Log.d(TAG, "恢复进度状态，当前步骤: ${taskState.currentStep.stepName}")

        // 显示容器
        progressContainer?.let { container ->
            container.visibility = View.VISIBLE
            container.alpha = 1f
        }

        // 恢复各步骤的进度
        taskState.stepProgresses.forEach { (step, progress) ->
            val progressView = progressViews[step]
            val percentText = percentTexts[step]

            if (progressView != null && percentText != null) {
                progressView.setProgressImmediate(progress)
                percentText.text = "${progress.toInt()}%"
                Log.d(TAG, "恢复步骤 ${step.stepName} 进度: ${progress.toInt()}%")
            }
        }

        // 如果当前步骤没有进度记录，开始该步骤
        if (!taskState.stepProgresses.containsKey(taskState.currentStep)) {
            startStepProgress(taskState.currentStep)
        }
    }

    /**
     * 重置任务状态
     * 用于任务完成或取消时清理状态
     */
    fun resetTaskState() {
        Log.d(TAG, "重置任务状态")
        taskState = TaskState()
    }

    /**
     * 面板关闭时的处理
     * 保持任务状态，但标记UI为不可见
     */
    fun onPanelClosed() {
        Log.d(TAG, "面板关闭，保持任务状态")
        taskState = taskState.copy(isProgressVisible = false)
        // 清理UI组件引用，但保持任务状态
        progressContainer = null
        progressViews.clear()
        percentTexts.clear()
        stepLines.clear()
    }
}
