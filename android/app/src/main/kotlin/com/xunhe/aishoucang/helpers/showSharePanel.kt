package com.xunhe.aishoucang.helpers

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.*
import android.view.accessibility.AccessibilityNodeInfo
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.Toast
import android.widget.ProgressBar
import android.widget.LinearLayout
import android.widget.TextView
import android.view.View.VISIBLE
import android.view.View.GONE
import android.widget.RelativeLayout
import com.xunhe.aishoucang.R
import com.xunhe.aishoucang.lib.AccessibilityHelper
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.helpers.AppDetector
import com.xunhe.aishoucang.helpers.AppHandlerFactory
import com.xunhe.aishoucang.views.share_panel.SharePanelAdapter
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import com.xunhe.aishoucang.views.share_panel.SharePanelManager
import com.xunhe.aishoucang.helpers.DouyinAppHandler
import com.xunhe.aishoucang.helpers.KuaishouAppHandler
import com.xunhe.aishoucang.helpers.XiaohongshuAppHandler
import com.xunhe.aishoucang.helpers.BilibiliAppHandler
import com.xunhe.aishoucang.helpers.WechatAppHandler
import com.xunhe.aishoucang.helpers.MeituanAppHandler
import com.xunhe.aishoucang.helpers.DoubanAppHandler
import com.xunhe.aishoucang.helpers.FavoriteItemHandler
import com.xunhe.aishoucang.helpers.hooks.BeforePanelShowHookManager
import com.xunhe.aishoucang.helpers.ContentTypeConstants
import com.xunhe.aishoucang.helpers.DouyinAppItemHandler
import com.xunhe.aishoucang.helpers.PlatformTypeHelper

/**
 * 显示分享面板的辅助类
 * 从FloatingWindowHelper中提取出来，方便复用
 */
object SharePanelHelper {
    private const val TAG = "SharePanelHelper"

    // 分享面板相关变量
    private var sharePanelView: View? = null
    private var sharePanelParams: WindowManager.LayoutParams? = null
    internal var _isSharePanelShowing = false

    // 收藏面板开启状态标记位
    var isOpen: Boolean = false
        private set

    // 当前选中的分享项
    var currentShareItem: SharePanelItem? = null

    // 获取分享面板是否显示的属性
    val isSharePanelShowing: Boolean
        get() = _isSharePanelShowing

    // 当前前台APP包名
    private var currentPackage: String? = null

    // 当前内容类型，默认为未知类型
    private var currentContentType: Int = ContentTypeConstants.TYPE_UNKNOWN

    // 记录处理状态
    internal var isProcessing = false

    // 记录内容是否已准备好的标志
    private var isContentReady = false

    // 是否自动处理，默认为true
    private var isAuto = true

    // 用于防止多次处理的锁
    private val processingLock = Object()

    // 添加一个处理超时时间
    private const val CONTENT_PROCESSING_TIMEOUT = 10000L // 10秒

    // 临时数据存储，用于在不同方法之间传递数据
    private val tempDataMap = mutableMapOf<String, Any?>()

    // 前台Activity变化监听器ID，用于自动关闭面板
    private var activityChangeListenerId: String? = null

    /**
     * 设置临时数据
     *
     * @param key 数据键
     * @param value 数据值
     */
    fun setTempData(key: String, value: Any?) {
        tempDataMap[key] = value
        Log.d(TAG, "设置临时数据: $key")
    }

    /**
     * 获取临时数据
     *
     * @param key 数据键
     * @return 数据值，如果不存在则返回null
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> getTempData(key: String): T? {
        val value = tempDataMap[key]
        Log.d(TAG, "获取临时数据: $key, 是否存在: ${value != null}")
        return value as? T
    }

    /**
     * 移除临时数据
     *
     * @param key 数据键
     */
    fun removeTempData(key: String) {
        tempDataMap.remove(key)
        Log.d(TAG, "移除临时数据: $key")
    }

    /**
     * 清除所有临时数据
     */
    fun clearTempData() {
        tempDataMap.clear()
        Log.d(TAG, "清除所有临时数据")
    }

    /**
     * 设置是否自动处理
     *
     * @param auto 是否自动处理
     */
    fun setAuto(auto: Boolean) {
        isAuto = auto
        Log.d(TAG, "设置自动处理状态: $isAuto")
    }

    /**
     * 获取当前自动处理状态
     *
     * @return 是否自动处理
     */
    fun getAuto(): Boolean {
        return isAuto
    }

    // 添加一个回调接口
    interface ShareContentCallback {
        fun onContentReady()
        fun onContentTimeout()
    }

    // 存储注册的回调
    private var contentReadyCallback: ShareContentCallback? = null

    // 注册回调
    fun registerContentReadyCallback(callback: ShareContentCallback) {
        contentReadyCallback = callback
    }

    // 清除回调
    private fun clearCallback() {
        contentReadyCallback = null
    }

    // 供应用处理器调用，表示内容已准备好
    fun notifyContentReady() {
        synchronized(processingLock) {
            if (!isContentReady) {
                Log.d(TAG, "内容已准备好，通知UI更新")
                isProcessing = false
                isContentReady = true
                Handler(Looper.getMainLooper()).post {
                    contentReadyCallback?.onContentReady()
                    // 在内容准备好后设置面板可以获取焦点
                    val windowManager = sharePanelView?.context?.getSystemService(Context.WINDOW_SERVICE) as? WindowManager
                    setPanelCanFouce(windowManager)
                }


            }
        }
    }

    // 重置状态
    private fun resetState() {
        synchronized(processingLock) {
            isProcessing = false
            isContentReady = false
            clearCallback()
            // 移除前台Activity变化监听器
            unregisterActivityChangeListener()
        }
    }

    // 设置处理超时
    private fun setupProcessingTimeout(context: Context) {
        Handler(Looper.getMainLooper()).postDelayed({
            synchronized(processingLock) {
                if (isProcessing && !isContentReady) {
                    Log.w(TAG, "内容处理超时")
                    isProcessing = false
                    contentReadyCallback?.onContentTimeout()
                    Toast.makeText(context, "获取分享内容超时，请重试", Toast.LENGTH_SHORT).show()
                }
            }
        }, CONTENT_PROCESSING_TIMEOUT)
    }

    // 获取当前内容类型
    fun getCurrentContentType(): Int {
        return currentContentType
    }

    // 设置当前内容类型
    fun setCurrentContentType(type: Int) {
        currentContentType = type
        Log.d(TAG, "设置当前内容类型: $currentContentType")
    }

    /**
     * 设置当前前台APP包名
     */
    fun setCurrentPackage(context: Context) {
        val helper = AccessibilityHelper.getInstance(context)
        val rootNode = helper.getRootNode()
        if (rootNode != null) {
            currentPackage = rootNode.packageName?.toString() ?: "未知"
        }
    }

    /**
     * 获取当前平台类型
     *
     * @param schemeUrl 原生跳转链接（可选）
     * @return 平台类型的拼音小写，如果无法识别则返回null
     */
    fun getCurrentPlatformType(schemeUrl: String? = null): String? {
        return PlatformTypeHelper.getPlatformType(schemeUrl, currentPackage)
    }

    /**
     * 创建面板UI
     * 根据isAuto的值决定创建标准面板还是自定义面板
     */
    fun createPanelUI(
        context: Context,
        windowManager: WindowManager?,
        unSupport: Boolean = false,
        onComplete: () -> Unit = {}
    ) {
        // 注册内容就绪回调
        registerContentReadyCallback(object : ShareContentCallback {
            override fun onContentReady() {
                Handler(Looper.getMainLooper()).post {
                    // 内容准备好后，隐藏loading，显示内容
                    sharePanelView?.findViewById<LinearLayout>(R.id.loading_container)?.visibility = View.GONE
                    sharePanelView?.findViewById<LinearLayout>(R.id.share_panel_content)?.visibility = View.VISIBLE

                    // 更新分享面板上的按钮状态
                    sharePanelView?.findViewById<Button>(R.id.new_folder_button)?.isEnabled = true
                }
            }

            override fun onContentTimeout() {
                // 超时处理
                Handler(Looper.getMainLooper()).post {
                    sharePanelView?.findViewById<LinearLayout>(R.id.loading_container)?.visibility = View.GONE
                    sharePanelView?.findViewById<LinearLayout>(R.id.share_panel_content)?.visibility = View.VISIBLE

                    // 更新分享面板上的按钮状态
                    sharePanelView?.findViewById<Button>(R.id.new_folder_button)?.isEnabled = true
                }
            }
        })

        // 根据isAuto的值决定创建标准面板还是自定义面板
        if (isAuto) {
            Log.d(TAG, "创建标准面板UI")
            val result = PanelUICreator.createPanelUI(
                context,
                windowManager,
                sharePanelView,
                sharePanelParams,
                ::hideSharePanel,
                unSupport,
                onComplete
            )
            sharePanelView = result.first
            sharePanelParams = result.second
        } else {
            Log.d(TAG, "创建自定义面板UI")
            val result = CustomPanelUICreator.createPanelCustomUI(
                context,
                windowManager,
                sharePanelView,
                sharePanelParams,
                ::hideSharePanel,
                onComplete
            )
            sharePanelView = result.first
            sharePanelParams = result.second
        }
    }

    /**
     * 在创建面板UI前执行钩子函数
     * 用于执行特定应用的预处理逻辑
     *
     * @param context 上下文
     */
    private fun beforeCreatePanelUI(context: Context) {
        val service = AccessibilityHelper.AppAccessibilityService.getInstance()
        if (service != null) {
            val rootNode = service.rootInActiveWindow
            if (rootNode != null) {
                // 执行所有适用的钩子
                BeforePanelShowHookManager.executeHooks(context, rootNode)
            } else {
                Log.w(TAG, "无法获取当前活动窗口的根节点")
            }
        } else {
            Log.w(TAG, "无障碍服务未运行")
        }
    }

    /**
     * 注册前台Activity变化监听器
     * 当前台Activity变化且面板不是开启状态时，自动关闭面板
     *
     * @param context 上下文
     * @param windowManager 窗口管理器
     */
    private fun registerActivityChangeListener(context: Context, windowManager: WindowManager?) {
        // 先移除之前的监听器（如果存在）
        unregisterActivityChangeListener()

        // 生成唯一的监听器ID
        activityChangeListenerId = "share_panel_auto_close_${System.currentTimeMillis()}"

        val accessibilityHelper = AccessibilityHelper.getInstance(context)
        accessibilityHelper.addForegroundActivityChangeListener(activityChangeListenerId!!) { newActivity ->
            Log.d(TAG, "前台Activity变化: $newActivity, 面板开启状态: $isOpen, 面板显示状态: $isSharePanelShowing")

            // 从newActivity中提取包名（格式为"packageName/className"）
            val activityPackageName = newActivity.split("/").getOrNull(0) ?: ""

            // 检查收藏面板状态，如果不是com.xunhe.aishoucang的Activity就关闭收藏面板
            if (isSharePanelShowing) {
                // 如果不是本应用的Activity，自动关闭面板
                if (activityPackageName != currentPackage && activityPackageName != "com.xunhe.aishoucang") {
                    Log.d(TAG, "前台Activity不是本应用($activityPackageName)，自动关闭收藏面板")
                    try {
                        hideSharePanelWithoutParam()
                    } catch (e: Exception) {
                        Log.e(TAG, "自动关闭收藏面板时出错", e)
                    }

                    // 关闭面板后，移除监听器
                    unregisterActivityChangeListener()
                }
            }
        }

        Log.d(TAG, "已注册前台Activity变化监听器，ID: $activityChangeListenerId")
    }

    /**
     * 注销前台Activity变化监听器
     */
    private fun unregisterActivityChangeListener() {
        activityChangeListenerId?.let { id ->
            val accessibilityHelper = AccessibilityHelper.getInstance(sharePanelView?.context ?: return)
            val removed = accessibilityHelper.removeForegroundActivityChangeListener(id)
            Log.d(TAG, "移除前台Activity变化监听器，ID: $id, 结果: ${if (removed) "成功" else "失败"}")
            activityChangeListenerId = null
        }
    }

    /**
     * 显示分享面板
     *
     * @param context 上下文
     * @param windowManager 窗口管理器
     * @param unSupport 是否为不支持的应用，默认为false
     */
    fun showSharePanel(context: Context, windowManager: WindowManager?, unSupport: Boolean = false) {
        // 检查是否有正在运行的任务
        val hasRunningTask = TaskProgressHelper.isTaskRunning()

        // 重置状态（但不影响正在运行的任务）
        resetState()

        // 每次显示面板前，先重置isAuto为true
        isAuto = true

        // 标记为处理中状态
        isProcessing = true

        // 设置收藏面板为开启状态
        isOpen = true

        // 设置处理超时
        setupProcessingTimeout(context)

        // 执行面板显示前的钩子函数
        beforeCreatePanelUI(context)

        // 获取当前前台应用信息
        setCurrentPackage(context)

        // 注册前台Activity变化监听器，用于自动关闭面板
        registerActivityChangeListener(context, windowManager)

        // processSelectedShareItem(context)
        // 创建面板，并在动画完成后执行后续逻辑
        createPanelUI(context, windowManager, unSupport) {
            // 检查是否有正在运行的任务
            if (hasRunningTask) {
                Log.d(TAG, "检测到正在运行的任务，跳过重新处理")
                // 直接通知内容就绪，不重新启动处理逻辑
                notifyContentReady()
            } else {
                // 没有正在运行的任务，正常执行APP分发逻辑
                processSelectedShareItem(context)
            }
            // 注意：焦点设置已移至notifyContentReady方法中
        }
    }

    /**
     * 设置面板可以获取焦点，这样就可以获取剪切板
     */
    fun setPanelCanFouce(windowManager: WindowManager?) {
        try {
            // 添加安全检查，确保面板和参数都存在
            if (sharePanelView == null || sharePanelParams == null) {
                Log.d(TAG, "面板不存在或参数为空，无法设置可获取焦点")
                return
            }

            // 再次检查面板是否有父视图
            if (sharePanelView?.parent == null) {
                Log.d(TAG, "面板已从窗口移除，无法设置可获取焦点")
                return
            }

            sharePanelParams?.let { params ->
                // 清除FLAG_NOT_FOCUSABLE标志
                params.flags = params.flags and WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE.inv()
                Log.d(TAG, "动态移除FLAG_NOT_FOCUSABLE标志")
                // 更新窗口布局参数
                windowManager?.updateViewLayout(sharePanelView, params)
            }
        } catch (e: Exception) {
            Log.e(TAG, "动态修改窗口参数失败", e)
        }
    }

    /**
     * 设置面板不可以获取焦点，使无障碍服务可以操作后台应用
     */
    fun setPanelCannotFocus(windowManager: WindowManager?) {
        try {
            // 添加安全检查，确保面板和参数都存在
            if (sharePanelView == null || sharePanelParams == null) {
                Log.d(TAG, "面板不存在或参数为空，无法设置不可获取焦点")
                return
            }

            // 再次检查面板是否有父视图
            if (sharePanelView?.parent == null) {
                Log.d(TAG, "面板已从窗口移除，无法设置不可获取焦点")
                return
            }

            sharePanelParams?.let { params ->
                // 添加FLAG_NOT_FOCUSABLE标志
                params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                Log.d(TAG, "动态添加FLAG_NOT_FOCUSABLE标志")
                // 更新窗口布局参数
                windowManager?.updateViewLayout(sharePanelView, params)
            }
        } catch (e: Exception) {
            Log.e(TAG, "动态修改窗口参数失败", e)
        }
    }

    /**
     * 隐藏分享面板
     * @param context 上下文
     * @param windowManager 窗口管理器
     * @param shouldAdd 是否处理选中的分享项，默认为true
     */
    fun hideSharePanel(context: Context, windowManager: WindowManager?, shouldAdd: Boolean = true) {
        if (sharePanelView == null) {
            Log.d(TAG, "面板不存在，无需隐藏")
            return
        }

        _isSharePanelShowing = false
        // 设置收藏面板为关闭状态
        isOpen = false

        // 通知TaskProgressHelper面板已关闭
        TaskProgressHelper.onPanelClosed()

        val containerView = sharePanelView?.findViewById<View>(R.id.share_panel_container)
        if (containerView == null) {
            Log.w(TAG, "找不到容器 share_panel_container，直接移除整个面板")
            safelyRemoveSharePanel(windowManager, sharePanelView)
            return
        }

        try {
            val animation = AnimationUtils.loadAnimation(context, R.anim.slide_down)
            animation.setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation?) {
                    Log.d(TAG, "容器动画开始")
                }

                override fun onAnimationEnd(animation: Animation?) {
                    Log.d(TAG, "容器动画结束，移除整个悬浮窗")
                    Handler(Looper.getMainLooper()).post {
                        safelyRemoveSharePanel(windowManager, sharePanelView)

                        // 根据shouldAdd参数决定是否处理选中的分享项
                        if (shouldAdd && currentShareItem != null) {
                            // 获取剪贴板内容
                            val clipboardHelper = ClipboardHelper.getInstance(context)
                            val clipText = clipboardHelper.getClipboardText()

                            // 记录剪贴板内容，方便调试
                            Log.d(TAG, "剪贴板内容: $clipText")

                            // 清除剪切板内容
                            clipboardHelper.setClipboardText("")

                            // 处理收藏夹点击事件
                            FavoriteItemHandler.handleFavoriteItemClick(
                                context,
                                currentPackage,
                                clipText,
                                currentShareItem
                            )
                        }

                        // 重置状态
                        resetState()
                    }
                }

                override fun onAnimationRepeat(animation: Animation?) {}
            })

            containerView.startAnimation(animation)

        } catch (e: Exception) {
            Log.e(TAG, "容器动画执行失败，直接移除", e)
            safelyRemoveSharePanel(windowManager, sharePanelView)
            // 重置状态
            resetState()
        }
    }

    /**
     * 隐藏分享面板（无参数版本）
     * 自动从当前面板视图获取所需的 context 和 windowManager
     * @param shouldAdd 是否处理选中的分享项，默认为true
     */
    fun hideSharePanelWithoutParam(shouldAdd: Boolean = false) {
        Log.d(TAG, "隐藏分享面板（无参数版本）")

        // 检查面板是否存在
        if (sharePanelView == null) {
            Log.d(TAG, "面板不存在，无需隐藏")
            return
        }

        // 从面板视图获取 context
        val context = sharePanelView?.context
        if (context == null) {
            Log.e(TAG, "无法从面板视图获取 context")
            return
        }

        // 从 context 获取 windowManager
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as? WindowManager
        if (windowManager == null) {
            Log.e(TAG, "无法获取 WindowManager")
            return
        }

        Log.d(TAG, "成功获取 context 和 windowManager，调用原始 hideSharePanel 方法")
        // 调用原始的 hideSharePanel 方法
        hideSharePanel(context, windowManager, shouldAdd)
    }

    /**
     * 关闭收藏面板
     * @param context 上下文
     * @param windowManager 窗口管理器
     */
    fun closeSharePanel(context: Context, windowManager: WindowManager?) {
        Log.d(TAG, "关闭收藏面板")
        hideSharePanel(context, windowManager, false)
    }

    /**
     * 处理选中的分享项
     */
    fun processSelectedShareItem(context: Context) {
        Log.d(TAG, "使用包名处理应用: $currentPackage")

        // 使用工厂根据包名获取应用处理器
        val handler = AppHandlerFactory.getHandlerByPackage(currentPackage)

        try {
            // 启动处理
            handler.handle(context)

            // 注意：在各个AppHandler的处理完成后，应调用 notifyContentReady() 方法通知界面更新
            // 例如在DouyinAppHandler.handleCommonVideo()方法的最后调用 SharePanelHelper.notifyContentReady()
        } catch (e: Exception) {
            Log.e(TAG, "处理应用时出错: ${e.message}", e)
            // 出错时自动标记为完成，不要一直等待
            notifyContentReady()
        }
    }

    /**
     * 安全地移除分享面板
     */
    private fun safelyRemoveSharePanel(windowManager: WindowManager?, view: View?) {
        try {
            if (view != null && view.parent != null) {
                windowManager?.removeView(view)
                Log.d(TAG, "悬浮窗移除成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "悬浮窗移除失败", e)
        } finally {
            sharePanelView = null
            Log.d(TAG, "sharePanelView 置空")
        }
    }



}
