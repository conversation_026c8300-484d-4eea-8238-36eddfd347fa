package com.xunhe.aishoucang.helpers.hooks

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.xunhe.aishoucang.helpers.DouyinAppItemHandler
import com.xunhe.aishoucang.lib.AccessibilityHelper

/**
 * 抖音应用面板显示前的钩子实现
 */
class DouyinBeforePanelShow : BeforePanelShowHook {
    companion object {
        private const val TAG = "DouyinBeforePanelShow"
        private const val PACKAGE_DOUYIN = "com.ss.android.ugc.aweme"
    }

    /**
     * 判断当前应用是否是抖音
     */
    override fun isApplicable(packageName: String): Boolean {
        return packageName.startsWith(PACKAGE_DOUYIN)
    }

    /**
     * 执行抖音特定的预处理逻辑
     * 主要是提前获取作者名称
     */
    override fun execute(context: Context, rootNode: AccessibilityNodeInfo?) {
        if (rootNode == null) return

        Log.d(TAG, "当前应用是抖音，尝试提前获取作者名称")

        // 获取无障碍服务实例
        val service = com.xunhe.aishoucang.lib.AccessibilityHelper.AppAccessibilityService.getInstance()
        if (service == null) {
            Log.e(TAG, "无障碍服务未运行")
            return
        }

        // 调用DouyinAppItemHandler中的方法提前获取作者名称
        val authorName = DouyinAppItemHandler.extractAuthorNameFromUI(service as AccessibilityService)
        Log.d(TAG, "从UI获取的作者名称: $authorName")

        // 将作者名称保存到DouyinAppItemHandler中供后续使用
        authorName?.let {
            DouyinAppItemHandler.setAuthorNameCache(it)
            Log.d(TAG, "已缓存作者名称: $it")
        }
    }
}
