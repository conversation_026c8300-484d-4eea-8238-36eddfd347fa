package com.xunhe.aishoucang.services

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import androidx.core.content.FileProvider
import okhttp3.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * 应用下载管理器
 *
 * 负责处理应用的静默下载和安装功能，兼容Android各版本
 */
class AppDownloadManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "AppDownloadManager"
        private const val DOWNLOAD_TIMEOUT = 30L // 下载超时时间（秒）
        private const val CONNECT_TIMEOUT = 10L // 连接超时时间（秒）

        @Volatile
        private var instance: AppDownloadManager? = null

        fun getInstance(context: Context): AppDownloadManager {
            return instance ?: synchronized(this) {
                instance ?: AppDownloadManager(context.applicationContext).also { instance = it }
            }
        }
    }

    // OkHttp客户端
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
        .readTimeout(DOWNLOAD_TIMEOUT, TimeUnit.SECONDS)
        .writeTimeout(DOWNLOAD_TIMEOUT, TimeUnit.SECONDS)
        .build()

    // 当前下载任务
    private var currentCall: Call? = null

    /**
     * 下载应用
     *
     * @param url 下载地址
     * @param callback 下载结果回调
     */
    fun downloadApp(url: String, callback: DownloadCallback) {
        Log.d(TAG, "开始下载应用: $url")

        // 取消之前的下载任务
        cancelDownload()

        try {
            // 创建下载目录
            val downloadDir = getDownloadDirectory()
            if (!downloadDir.exists()) {
                downloadDir.mkdirs()
            }

            // 生成文件名
            val fileName = "app-update-${System.currentTimeMillis()}.apk"
            val targetFile = File(downloadDir, fileName)

            // 创建下载请求
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "AiShouCang-Android")
                .build()

            currentCall = httpClient.newCall(request)

            // 异步下载
            currentCall?.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG, "下载失败: ${e.message}", e)
                    callback.onError("下载失败: ${e.message}")
                }

                override fun onResponse(call: Call, response: Response) {
                    if (!response.isSuccessful) {
                        Log.e(TAG, "下载失败，HTTP状态码: ${response.code}")
                        callback.onError("下载失败，HTTP状态码: ${response.code}")
                        return
                    }

                    try {
                        val contentLength = response.body?.contentLength() ?: -1L
                        Log.d(TAG, "开始下载文件，大小: $contentLength 字节")

                        response.body?.byteStream()?.use { inputStream ->
                            FileOutputStream(targetFile).use { outputStream ->
                                val buffer = ByteArray(8192)
                                var downloadedBytes = 0L
                                var bytesRead: Int

                                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                                    // 检查是否被取消
                                    if (call.isCanceled()) {
                                        Log.d(TAG, "下载被取消")
                                        targetFile.delete()
                                        return
                                    }

                                    outputStream.write(buffer, 0, bytesRead)
                                    downloadedBytes += bytesRead

                                    // 每1MB记录一次下载进度
                                    if (contentLength > 0 && downloadedBytes % (1024 * 1024) == 0L) {
                                        val progress = (downloadedBytes.toDouble() / contentLength * 100).toInt()
                                        Log.d(TAG, "下载进度: $progress% ($downloadedBytes/$contentLength)")
                                    }
                                }

                                outputStream.flush()
                            }
                        }

                        Log.d(TAG, "下载完成: ${targetFile.absolutePath}")
                        callback.onSuccess(targetFile.absolutePath)

                    } catch (e: Exception) {
                        Log.e(TAG, "保存文件失败: ${e.message}", e)
                        targetFile.delete()
                        callback.onError("保存文件失败: ${e.message}")
                    }
                }
            })

        } catch (e: Exception) {
            Log.e(TAG, "启动下载失败: ${e.message}", e)
            callback.onError("启动下载失败: ${e.message}")
        }
    }

    /**
     * 取消下载
     */
    fun cancelDownload() {
        currentCall?.cancel()
        currentCall = null
        Log.d(TAG, "下载任务已取消")
    }

    /**
     * 安装应用
     *
     * @param filePath APK文件路径
     * @return 是否成功启动安装流程
     */
    fun installApp(filePath: String): Boolean {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                Log.e(TAG, "APK文件不存在: $filePath")
                return false
            }

            // Android 8.0+ 需要先检查安装权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (!context.packageManager.canRequestPackageInstalls()) {
                    Log.w(TAG, "没有安装未知来源应用权限，需要用户授权")
                    return false
                }
            }

            Log.d(TAG, "开始安装应用: $filePath")

            val intent = Intent(Intent.ACTION_VIEW)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0+ 使用FileProvider
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )
                intent.setDataAndType(uri, "application/vnd.android.package-archive")
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            } else {
                // Android 7.0以下直接使用文件URI
                intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive")
            }

            context.startActivity(intent)
            Log.d(TAG, "成功启动安装流程")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "安装应用失败: ${e.message}", e)
            return false
        }
    }

    /**
     * 检查是否有安装权限
     */
    fun hasInstallPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Android 8.0+ 需要检查安装未知来源权限
            context.packageManager.canRequestPackageInstalls()
        } else {
            // Android 8.0以下默认有权限
            true
        }
    }

    /**
     * 请求安装权限
     */
    fun requestInstallPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
                intent.data = Uri.parse("package:${context.packageName}")
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
                Log.d(TAG, "已打开安装权限设置页面")
            } catch (e: Exception) {
                Log.e(TAG, "打开安装权限设置失败: ${e.message}", e)
            }
        }
    }

    /**
     * 获取下载目录
     */
    fun getDownloadDirectory(): File {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 使用应用专用目录
            File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "updates")
        } else {
            // Android 10以下使用外部存储
            File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "AiShouCang")
        }
    }

    /**
     * 清理下载缓存
     */
    fun cleanDownloadCache() {
        try {
            val downloadDir = getDownloadDirectory()
            if (downloadDir.exists()) {
                downloadDir.listFiles()?.forEach { file ->
                    if (file.isFile && file.name.endsWith(".apk")) {
                        val deleted = file.delete()
                        Log.d(TAG, "删除缓存文件 ${file.name}: ${if (deleted) "成功" else "失败"}")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理下载缓存失败: ${e.message}", e)
        }
    }

    /**
     * 下载回调接口
     */
    interface DownloadCallback {
        fun onSuccess(filePath: String)
        fun onError(error: String)
    }
}
