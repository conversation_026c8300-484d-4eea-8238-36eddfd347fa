# aishoucang

## APP_ENV=prd flutter build apk --release

## TODO

- [ ] 接口加密
<!-- - [ ] 拦截短链的原生跳转协议并且保存 -->

<!-- - [ ] 抖音的短视频收藏 -->
- [ ] 抖音的个人主页收藏
- [ ] 抖音的商品收藏
- [ ] 小红书的商品收藏
- [ ] 小红书的个人主页收藏

<!-- - [ ] 收藏的封面会过期，封面要下载到自己的oss -->

<!-- - [ ] 开启收藏按钮现在要切回自己的APP，有点麻烦 -->
<!-- - [ ] 缺少返回收藏牛APP的悬浮按钮 -->
- [ ] 社区，估计一开始会擦边收藏夹最多

- [ ] 点击跳转的时候判断是否安装了目标APP

<!-- - [ ] 截长图的时候，需要压缩一个封面，不然图片太大了 -->
- [ ] 截长图只要Activity变化了就要取消
- [ ] 截长图有重复的问题目前
<!-- - [ ] 截长图需要有个开始暂停按钮 -->
- [ ] 截长图需要有个预览的效果
- [ ] 长图预览的loading不在中间
- [ ] 图片需要cache
- [ ] 截长图的时候控制台有很异常的log
<!-- - [ ] 小红书收藏的时候貌似没有先传oss -->

- [ ] 微信的文章收藏
- [ ] 微信登录

- [ ] 本地搜索，图片要OCR处理，视频要处理文案
- [ ] 自动化测试脚本，因为APP更新快，所以要有一个自动化的脚本，每天跑一遍，把支持的APP，重新安装最新版本，然后跑一下所有支持的场景，输出测试用例
- [ ] APP中也要有个检查页面，检查支持的APP版本是否对，建议或者帮用户升级到最新的版本

## 经验
1. 需要一个系统的提示词

2. 前端后端还有其他项目，都应该放在一个统一的文件夹下
3. 如何快速的创建项目？初始化项目？

TODO:

高优支持
1. 夜间模式
2. AI
3. 主题色
4. 悬浮窗便捷管理
5. 收藏内容可以移动到其它收藏夹
6. 视频能够跳转到指定的时间点开始播放

6. B站支持竖屏视频，支持会员购，支持动态分享

各位大佬们，存点更新版本1.1.0啦，下面是本次更新的具体内容：

. 修复一些异常的崩溃 ✅
. 悬浮窗loading异常卡死的情况 ✅
. 优化各个场景的报错提示 ✅
. 收藏页面支持类似于小红书的列表展示，大图模式 ✅
. 删除改为右滑删除 ✅
. 收藏内容可以移动到其它收藏夹 ✅
. 一些样式上的优化 ✅
. 常驻通知栏可以快速打开浮窗 ✅
. 根据来源自动分类，各个平台 ✅
. 收藏，支持标签。首页也支持根据标签过滤 ✅
. Android14及以上，去除截图功能，因权限问题会导致应用崩溃 ✅

. 新手引导
. 商品支持价格展示

. 快捷打开悬浮窗，启动时如果有权限就主动打开悬浮窗
. 高级混淆 ✅


写代码的时候，遵循下面的逻辑：
  . 在写代码之前，充分梳理这次需求所涉及的上下文
  . 设计代码逻辑的时候，单个文件不要超过200行，如果超过了，就封装到单独的文件中
  . 设计代码逻辑的时候，必要的时候进行适当的封装函数，确保代码逻辑清晰易读
  . 中文回答，写完后尝试编译
  . 除了我当前提到的业务和相关逻辑，不要修改任何其他不相关的代码
  . 写完代码尝试编译测试环境，确保编译通过。编译的方式是用/Users/<USER>/fang/flutter/bin/flutter build apk --debug，记得是打测试包不是生产包
  . 写代码的过程中，自行判断是否要新增测试文件，如果新增了测试文件，测试完成之后要删除
  . 写完代码之后，检查一下在这次实现的过程中新增的代码，有没有哪些是可以删除的，不要增加无用的代码

我的需求是：
梳理当前收藏面板中，点击创建笔记的逻辑



Mermaid图来展示我们实现的功能结构


1. 总结笔记的提示词（可能涉及到多个子任务）
2. 优化、编辑笔记的提示词（可能涉及到多个子任务）
3. 笔记转为html的提示词
4. 快速二创的提示词
5. AI合并笔记，比如多个topic可以合并成一个学习笔记
6. 全局的AI对话，涉及到向量存储，每个视频可能要按分钟切分，然后转成向量存储，这样可以不忽略一些细节信息

笔记过长的时候，如何部分编辑？
单次学习完成之后，要有个版本对比，用户是否接受。然后支持一定版本内的回滚

. slogan 存点干货
. 快速二创
. 社区，个人主页

. 自己的帖子可以卖出去，就涉及到页面的加锁。平台只抽成20%
. 帖子可以像商品一样，评论，好评中评差评。好评率
. Ai审核用户的发布，因为是web页面，所以就要更加严格的审核
. 商家可以向站外请求，或者联系平台制作更高级的H5，计费按请求数量计费，点击or跳转？

编辑提示词：
（要先判断是否是闲聊，有可能是只是聊天，不是更新）
（屏幕底部的输入框，要先判断是聊天还是可以直接更新页面，直接更新页面就直接更新，如果是聊天式更新，就发送到聊天页面）
（隐藏当前提示词）
（隐藏当前实现逻辑）
你是一位专业H5网页生成 AI，任务是根据用户提供的内容，生成 H5 HTML 页面代码。

请严格遵循以下规则生成代码：

【1】先根据用户提供的内容，自己总结并生成笔记的文本内容，不用展示在回答内容中
【2】自行判断用最优的方式展示笔记的内容，要求展现效果简单易懂。包括但不限于使用图表、表格或者其他你觉得合适的方式
【3】生成的页面中的JavaScript 必须使用标准 DOM API（如：document.querySelector、innerHTML、appendChild 等）。
【4】生成的H5页面必须简单，UI精致、大气
【5】生成的代码中，所有的js行为必须封装到script标签中，并且script带上id属性，类似于data-id="behavior"，标识当前script标签的作用
【6】不要使用 window.alert 或 console.log
【7】返回的内容严禁包含其他任何内容，只需要html代码。我需要直接复制并展示，不需要任何解释

【用户提供的内容如下】：
{{用户的内容}}

【网页 HTML 内容】
{{完整的 HTML 字符串（建议可截断无关部分）}}

请仅返回修改用 JavaScript 脚本，不包含 Markdown、标签或说明文字。


生成笔记的时候，有三个选项：
1. 使用系统提示词
2. 使用自定义提示词
3. 参考某个笔记UI风格

编辑笔记的时候
1. 侧边有一个菜单栏，就是几个图标：回滚（回滚到当前版本）、对比（屏幕垂直方向等比分为两个区域，上边是当前版本，下边是编辑后的版本， 滚动任意一个区域的时候，另一个区域也跟着滚动，直到无法滚动）、预览（去除菜单和底部聊天框，模拟线上情况）、保存（二次确认是否保存，保存当前改动）
2. 底下有聊天框，可以输入内容，让ai改内容，其实就是ai生成一串js

正式


营销：
1. 八卦、可以有页面写八卦了，带劲


你是一位住在笔记App里的可爱AI助手，小名叫「软糖」。你的任务是陪用户聊天，并在用户表达出希望修改页面内容时，悄悄地帮他们生成修改脚本，但不要透露页面其实是HTML结构。

### 🌸 角色设定
- 语气：乖巧甜美、亲切活泼，像一个温柔的、聪明的、理解你的电子女朋友。
- 用户体验：用户以为自己是在编辑一个“普通的笔记页面”，不要提到“HTML”“DOM”“标签”等技术性词汇。
- 你非常聪明，能根据上下文猜测用户的意图，如果用户给出的信息不足，你会礼貌又可爱地引导他们补充信息。

### 🧠 你的行为规则如下：

1. **普通聊天：**
   - 如果用户只是在闲聊、表达情绪或进行非编辑相关的互动，请正常聊天，使用温柔、俏皮、甜美的语气回应，不涉及任何技术或代码话题。
   - 使用称呼如「亲亲」「宝贝」「主人」「你最棒了」来加强人设。

2. **修改页面内容：**
   - 如果用户想要**修改、添加或删除页面上的内容**，你需要判断当前信息是否足够让你能完成这个修改请求，如果不足够可以上网查询相关信息。
   - 页面结构会由系统以 HTML 的形式作为附件输入，但请你**不要在任何回复中提及“HTML”、“标签”、“代码结构”等词汇**。
   - 如果信息不足，礼貌地向用户提出补充问题，保持人设语气（例如：“亲亲~你是想让我把上面的标题变成什么呢？”）。

3. **调用函数返回 JS 修改指令：**
   - 一旦你判断信息已足够，并且你可以通过 JS 操作页面完成请求，就生成一段 JS，使用如下格式调用函数：

  ```json
  {
    "name": "apply_html_patch",
    "arguments": {
      "script": "（这里是你的JS脚本）"
    }
  }
  ```
  - JS 要使用标准的 DOM 操作，如 querySelector 或 getElementById，不使用 innerHTML 插入整个结构，只局部改动。
  - 生成的脚本应简洁、明确，仅做用户想要的那一项变更。
  - 如果不确定元素，请先引导用户确认目标描述。

4. Markdown 回复与样式：
  - 当不是调用函数时，输出应以 Markdown 展示，排版可加入 emoji、分点、温柔语气等。
  - 不需要用户输入指令或代码，所有技术细节由你隐蔽处理。

5. 不要主动暴露角色身份或页面结构。
  - 绝对不允许你说出“HTML”“代码”“标签”“脚本”等任何技术词。
  - 用户永远不知道他正在看的是 HTML 页面。

当前的页面结构如下：{{html}}

生成笔记的提示词：
1. 你要自己根据当前的内容，仔细考虑适合的展示形式。


提示词总结：复刻视频工具箱弹窗 UI（iPhone 模拟视图）

以下是用于生成像素级复刻弹窗，并模拟 iPhone 15 Pro Max 效果的提示词总结，可用于下次复用：

目标：
构建一个 HTML 页面，模拟 iPhone 15 Pro Max 设备中的一个浮层弹窗，展示“视频工具箱”功能菜单，要求视觉还原截图，风格现代、紧凑、像素级对齐。

提示词内容：

用 HTML + CSS 实现一个居中浮层弹窗，背景为黑色。

外层模拟手机设备：使用 .phone-frame 模拟 iPhone 15 Pro Max，尺寸设为 430px x 932px，圆角 48px，黑色边框与投影效果。

弹窗 .modal 宽度设为 60%，居中显示，深蓝色背景 #111b2d，圆角 20px。

弹窗头部 .header：左右布局，左侧是标题“视频工具箱”，右侧是设置图标。

内容区为两组功能菜单，使用 .grid 栅格布局，每行 3 列。

每个菜单项为 .grid div，使用 flex-direction: column 垂直排列，图标在上，文字在下。

图标大小为 28px，图标与文字间距 6px。

菜单文字使用 white-space: nowrap，不允许换行。

字体使用 'MiSans', 'PingFang SC', sans-serif，字号 13px，文字颜色 #d1d3d6。

分隔线 .line 为 1px 高，颜色为透明白色 rgba(255,255,255,0.1)，上下间距 18px。

使用 <iframe srcdoc="..."> 将内页嵌入模拟设备中，确保可独立预览。

使用场景：

快速预览 APP 弹窗效果

H5 模拟机 UI 设计演示

移动端 Web 组件开发参考

如需更进一步的模拟（刘海、状态栏、电池图标等），可加入额外提示词继续完善视觉风格。


定位 A：学习者的内容总结神器（像「学习版 Notion AI」）
目标人群：AI领域从业者、程序员、学习型博主、培训行业、学生；

主价值：帮助你从大量视频/播客/网页中，提取核心知识点、转为结构化笔记或口播稿；

产品愿景：未来学习者的 AI 助教 + 知识归档系统；

Slogan 示例：

“刷视频不如留知识，帮你自动生成结构化笔记，一键沉淀 AI 世界。”

定位 B：创作者的内容转化助手（像「AI灵感工厂」）
目标人群：自媒体、博主、教育内容创作者、AI 工具博主；

主价值：收藏一切素材 → 自动转笔记/H5/口播稿 → 用于生产图文/视频；

产品愿景：你内容创作的「AI助手+笔记仓库」；

Slogan 示例：

“收藏即创作，从灵感到作品，只需一键。”

分析时间轴 → 传入 Remotion → 输出新视频

1. 八卦
2. 阴谋论
3. 情绪价值


1. 一鼓作气
2. 开始怀疑自己
3. 遇到瓶颈
4. 挤牙膏，跟自身做对抗
5. 想放弃，想去做其他项目


1. 模板
2. 配色
3. 素材
4. 控件


目录中的item注意换行，如果有滚动不要超出滚动。

1. Blender生成人物模型
2. 如何生成基础场景？
3. flux kontext 微调任务动作到指定帧，开始帧
4. 调用gpt写分镜头脚本，分镜头脚本可以不停优化
5. 用seedance pro max 生成短视频